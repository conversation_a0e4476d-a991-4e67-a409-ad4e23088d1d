// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M340.771 768c0.563 10.308 0.563 24.171 0.563 45.756 0 36.864 0 55.296 5.6 69.982 8.664 22.72 26.61 40.666 49.331 49.331 14.685 5.598 33.116 5.598 69.98 5.598h91.511c36.864 0 55.296 0 69.982-5.598 22.72-8.666 40.666-26.611 49.331-49.331 5.598-14.686 5.598-33.118 5.598-69.982 0-21.585 0-35.447 0.563-45.756M340.771 768c-0.224-4.1-0.536-7.633-0.974-10.871-4.139-30.635-1.765-22.665-15.066-50.577-4.937-10.359-30.213-49.020-80.765-126.34-32.862-50.266-51.967-110.34-51.967-174.878 0-176.731 143.269-320 320-320 176.73 0 320 143.269 320 320 0 64.538-19.106 124.612-51.968 174.878-50.551 77.321-75.827 115.981-80.764 126.34-13.299 27.913-10.927 19.942-15.066 50.577-0.435 3.238-0.751 6.771-0.973 10.871M340.771 768h171.229M683.23 768h-171.23M512 768v-170.667"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "light-bulb"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1731,
        "id": 289,
        "name": "light-bulb",
        "prevSize": 32,
        "code": 60007
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 360
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface LightBulbIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const LightBulbIcon = ({ size = 16, ...props }: LightBulbIconProps) => (
  <IcoMoon iconSet={iconSet} icon="light-bulb" size={size} {...props} />
);

LightBulbIcon.displayName = 'LightBulbIcon';

export default LightBulbIcon;
