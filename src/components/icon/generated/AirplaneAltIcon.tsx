// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M247.995 776.004c-27.121-27.119-29.030-70.468-4.397-99.87l48.246-57.583M247.995 776.004c27.122 27.123 70.47 29.030 99.87 4.399l57.584-48.247M247.995 776.004l-128.311-128.307c-17.357-17.357-17.357-45.5 0-62.857 10.328-10.33 25.002-14.942 39.224-11.618 29.914 6.989 84.397 21.692 132.936 45.329M247.995 776.004l128.311 128.311c17.358 17.357 45.5 17.357 62.858 0 10.325-10.33 14.942-25.003 11.618-39.223-6.993-29.914-21.696-84.395-45.332-132.937M694.72 297.335c5.751 5.034 11.268 10.177 16.521 15.43s10.394 10.769 15.428 16.518M610.372 238.643l-0.149-0.086M610.223 238.557c-102.793-58.599-238.353-115.302-294.999-130.118-19.272-5.041-39.515 0.896-53.6 14.981-22.803 22.802-22.803 59.772 0 82.575l215.176 191.808M610.223 238.557l-133.423 159.245M610.223 238.557l9.566-11.419c49.493-59.074 113.088-112.191 190.14-113.576 29.76-0.535 58.594 5.061 77.022 23.491 18.432 18.43 24.026 47.263 23.492 77.022-1.382 77.054-54.502 140.646-113.574 190.14l-11.422 9.567M785.446 413.782c58.603 102.792 115.298 238.344 130.116 294.988 5.039 19.273-0.896 39.518-14.98 53.602-22.805 22.801-59.776 22.801-82.577 0l-191.804-215.168M785.446 413.782l-0.085-0.151M785.446 413.782l-159.245 133.422M405.45 732.156l220.752-184.951M291.844 618.551l184.956-220.75"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "airplane-alt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1380,
        "id": 640,
        "name": "airplane-alt",
        "prevSize": 32,
        "code": 59656
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 9
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface AirplaneAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const AirplaneAltIcon = ({ size = 16, ...props }: AirplaneAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="airplane-alt" size={size} {...props} />
);

AirplaneAltIcon.displayName = 'AirplaneAltIcon';

export default AirplaneAltIcon;
