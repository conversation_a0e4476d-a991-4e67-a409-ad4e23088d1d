// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M490.010 119.708c9.527-17.388 34.453-17.388 43.981 0l114.5 208.944c7.245 13.216 20.019 22.478 34.812 25.234l234.837 43.766c19.58 3.649 27.315 27.605 13.585 42.062l-163.844 172.467c-10.449 10.999-15.377 26.138-13.414 41.195l30.707 235.511c2.569 19.716-17.63 34.479-35.58 26.001l-216.196-102.135c-13.551-6.4-29.244-6.4-42.795 0l-216.198 102.135c-17.946 8.478-38.148-6.285-35.577-26.001l30.707-235.511c1.963-15.057-2.967-30.195-13.414-41.195l-163.845-172.467c-13.732-14.457-5.994-38.413 13.585-42.062l234.838-43.766c14.791-2.757 27.568-12.019 34.81-25.234l114.502-208.944z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "star"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1913,
        "id": 107,
        "name": "star",
        "prevSize": 32,
        "code": 60189
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 542
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface StarIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const StarIcon = ({ size = 16, ...props }: StarIconProps) => (
  <IcoMoon iconSet={iconSet} icon="star" size={size} {...props} />
);

StarIcon.displayName = 'StarIcon';

export default StarIcon;
