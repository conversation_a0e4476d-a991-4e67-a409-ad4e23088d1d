// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 938.667c-94.825 0-186.948-31.59-261.821-89.779s-128.227-139.661-151.638-231.548c-23.411-91.891-15.544-188.962 22.359-275.882 37.903-86.921 103.68-158.738 186.946-204.111s179.271-61.714 272.861-46.445c93.589 15.27 179.422 61.281 243.947 130.767s104.064 158.487 112.371 252.946c8.311 94.46-15.087 188.996-66.492 268.681M512 796.446c-59.435 0-117.373-18.62-165.682-53.235-48.31-34.62-84.562-83.499-103.667-139.78-19.104-56.277-20.099-117.124-2.847-173.999s51.887-106.915 99.037-143.096c47.151-36.18 104.453-56.683 163.853-58.627s117.918 14.766 167.334 47.785c49.417 33.019 87.249 80.688 108.186 136.311 20.932 55.625 23.919 116.408 8.538 173.816M512 654.221c-24.964 0-49.489-6.571-71.113-19.051-21.619-12.484-39.573-30.438-52.055-52.058-12.483-21.623-19.054-46.148-19.054-71.113s6.572-49.489 19.054-71.113c12.483-21.619 30.436-39.572 52.055-52.055 21.623-12.483 46.148-19.054 71.113-19.054s49.489 6.572 71.113 19.054c21.619 12.483 39.573 30.436 52.053 52.055"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "chart-alt-1"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1533,
        "id": 487,
        "name": "chart-alt-1",
        "prevSize": 32,
        "code": 59809
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 162
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ChartAlt1IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ChartAlt1Icon = ({ size = 16, ...props }: ChartAlt1IconProps) => (
  <IcoMoon iconSet={iconSet} icon="chart-alt-1" size={size} {...props} />
);

ChartAlt1Icon.displayName = 'ChartAlt1Icon';

export default ChartAlt1Icon;
