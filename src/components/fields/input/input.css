.rt-InputRoot {
  display: flex;
  align-items: stretch;

  font-family: var(--default-font-family);
  font-weight: var(--font-weight-regular);
  font-style: normal;
  text-align: start;

  &:hover {
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.04) 0%, rgba(0, 0, 0, 0.04) 100%), var(--fills-secondary);
    transition: width 0.3s;
    transition-timing-function: cubic-bezier(0.44, 0, 0.56, 1);
  }

  @supports selector(:has(*)) {
    &:where(:has(.rt-Input:focus)) {
      outline: 2px solid var(--input-focus-color);
      outline-offset: -1px;
    }
  }
  @supports not selector(:has(*)) {
    &:where(:focus-within) {
      outline: 2px solid var(--input-focus-color);
      outline-offset: -1px;
    }
  }

  &::selection {
    background-color: var(--input-selection-color);
  }
}

.rt-Input {
  /*
   * Flex layout edge case: when the parent container of InputRoot
   * is smaller than the intrinsic width of Input with all the slots,
   * this actually causes the input to shrink the available width.
   */
  width: 100%;

  /* Fix date inputs alignment in Chrome and Safari */
  display: flex;
  align-items: center;

  /* Inherit alignment from root in case it's overriden */
  text-align: inherit;

  &::placeholder {
    color: var(--labels-secondary);
  }

  /*
   * Hide type="number" input stepper because it's small, ugly, hard to use, and if
   * needed, a nicer one can be easily implemented with own buttons in the Slot part.
   */
  &:where([type='number']) {
    -moz-appearance: textfield;
  }
  &::-webkit-inner-spin-button {
    appearance: none;
  }

  /* Remove the native cancel button */
  &::-webkit-search-cancel-button {
    appearance: none;
  }

  &::selection {
    background-color: var(--input-selection-color);
  }

  /*
   * Style the date inputs:
   * https://codepen.io/andresdamelio/pen/KKbvdYb
   */

  /* Chrome’s calendar and time icons */
  &::-webkit-calendar-picker-indicator {
    box-sizing: content-box;
    width: var(--input-native-icon-size);
    height: var(--input-native-icon-size);
    padding: var(--spatial-1);
    margin-left: 0;
    margin-right: calc(var(--spatial-1) * -1);
    border-radius: calc(var(--input-border-radius) - 2px);
  }
  /* Chrome’s calendar icon */
  &:where(:not([type='time']))::-webkit-calendar-picker-indicator {
    margin-left: var(--spatial-1);
  }
  &::-webkit-calendar-picker-indicator:where(:hover) {
    background-color: var(--brand-gray300);
  }
  &::-webkit-calendar-picker-indicator:where(:focus-visible) {
    outline: 2px solid var(--input-focus-color);
  }

  /* Remove focus ring from date fields and use the selection color */
  &::-webkit-datetime-edit-ampm-field,
  &::-webkit-datetime-edit-day-field,
  &::-webkit-datetime-edit-hour-field,
  &::-webkit-datetime-edit-millisecond-field,
  &::-webkit-datetime-edit-minute-field,
  &::-webkit-datetime-edit-month-field,
  &::-webkit-datetime-edit-second-field,
  &::-webkit-datetime-edit-week-field,
  &::-webkit-datetime-edit-year-field {
    &:where(:focus) {
      background-color: var(--input-selection-color);
      color: inherit;
      outline: none;
    }
  }

  @supports selector(:has(*)) {
    &:where(:autofill, [data-com-onepassword-filled]) {
      /* Reliably removes native autofill colors */
      background-clip: text;
      -webkit-text-fill-color: var(--brand-gray950);
    }
  }
}

.rt-InputSlot {
  box-sizing: border-box;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  cursor: text;

  /* A slot that is not a right-side slot goes on the left */
  &:where(:not([data-side='right'])) {
    order: -1;
    margin-left: calc(var(--input-border-width) * -1);
    margin-right: 0;
  }

  &:where([data-side='right']),
  /*
   * A slot followed by a left-side slot that is not a left-side slot itself goes on the right.
   * In simple terms, this makes it so that two slots without an explicit side work automatically.
   */
  :where(&:not([data-side='right'])) ~ &:where(:not([data-side='left'])) {
    order: 0;
    margin-left: 0;
    margin-right: calc(var(--input-border-width) * -1);
  }
}

/***************************************************************************************************
 *                                                                                                 *
 * SIZES                                                                                           *
 *                                                                                                 *
 ***************************************************************************************************/

.rt-InputRoot {
  box-sizing: border-box;
  height: var(--input-height);
  padding: var(--input-border-width);
  border-radius: var(--input-border-radius);
}
.rt-Input {
  /* Clip text to the border radius of the Root */
  /* border-radius: calc(var(--input-border-radius) - var(--input-border-width)); */

  /* Equivalent to padding-left, but doesn't cut off long values when cursor is at the end. */
  /* text-indent: var(--input-padding); */

  &:where([type='date'], [type='datetime-local'], [type='time'], [type='week'], [type='month']) {
    /* Safari is buggy with text-indent for these input types */
    text-indent: 0;
    padding-left: var(--input-padding);
    padding-right: var(--input-padding);
  }

  /* Remove border-radius and text-indent/padding on the left if there’s a left-side slot */
  &:where(:has(~ .rt-InputSlot:not([data-side='right']))) {
    text-indent: 0;
    padding-left: 0;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  /* Remove border-radius and padding on the right if there’s a right-side slot */
  /* prettier-ignore */
  &:where(:has(
    ~ .rt-InputSlot[data-side='right'],
    ~ .rt-InputSlot:not([data-side='right']) ~ .rt-InputSlot:not([data-side='left'])
  )) {
    padding-right: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
}

@breakpoints {
  .rt-InputRoot {
    &:where(.rt-r-size-1) {
      --input-height: var(--spatial-9);
      --input-padding: var(--spatial-4);
      --input-border-radius: var(--radius-5);
      --input-native-icon-size: var(--spatial-6);
      font-size: var(--font-size-3);
      letter-spacing: var(--letter-spacing-2);
      gap: var(--spatial-3);
      padding: var(--spatial-4);

      & :where(.rt-Input) {
        /* Reset size 2 padding bottom */
        padding-bottom: 0px;

        /* Safari credentials autofill icon */
        &::-webkit-textfield-decoration-container {
          padding-right: 0px;
          margin-right: -2px;
        }
      }
    }

    &:where(.rt-r-size-2) {
      --input-height: var(--spatial-10);
      --input-padding: var(--spatial-5);
      --input-border-radius: var(--radius-6);
      --input-native-icon-size: var(--spatial-7);
      font-size: var(--font-size-4);
      letter-spacing: var(--letter-spacing-3);
      gap: var(--spatial-4);
      padding: var(--spatial-5);
      & :where(.rt-Input) {
        /* Avoid 1px baseline jitter when layout around the text field is subpixel-sized (e.g. vh units). */
        /* Works because as of Nov 2023, Chrome computes input text bounding box height as 16.5px on @2x screens. */
        padding-bottom: 0.5px;

        /* Safari credentials autofill icon */
        &::-webkit-textfield-decoration-container {
          padding-right: 2px;
          margin-right: 0px;
        }
      }
    }

    &:where(.rt-r-size-3) {
      --input-height: var(--spatial-11);
      --input-padding: var(--spatial-6);
      --input-border-radius: var(--radius-6);
      --input-native-icon-size: var(--spatial-8);
      font-size: var(--font-size-5);
      letter-spacing: var(--letter-spacing-4);
      gap: var(--spatial-5);
      padding: var(--spatial-6);

      & :where(.rt-Input) {
        /* Reset size 2 padding bottom */
        padding-bottom: 0px;

        /* Safari credentials autofill icon */
        &::-webkit-textfield-decoration-container {
          padding-right: 5px;
          margin-right: 0px;
        }
      }
    }
  }
}

/***************************************************************************************************
	*                                                                                                 *
	* VARIANTS                                                                                        *
 *                                                                                                 *
 ***************************************************************************************************/

/* surface */

.rt-InputRoot:where(.rt-variant-surface) {
  --input-selection-color: var(--brand-accent-button-bezeled-fill-hover);
  --input-focus-color: var(--brand-accent-40);
  --input-border-width: 1px;

  /* Blend inner shadow with page background */
  background: var(--fills-tertiary);
  box-shadow: inset 0 0 0 var(--input-border-width) var(--brand-gray700);
  color: var(--labels-primary);

  /* prettier-ignore */
  &:where(:has(.rt-Input:where(:autofill, [data-com-onepassword-filled]):not(:disabled, :read-only))) {
    /* Blend with focus color */
    background-image: linear-gradient(var(--brand-accent-button-bezeled-fill), var(--brand-accent-button-bezeled-fill));
    box-shadow: inset 0 0 0 1px var(--brand-accent-button-bezeled-fill-hover), inset 0 0 0 1px var(--brand-gray500);
  }

  &:where(:has(.rt-Input:where(:disabled, :read-only))) {
    /* Blend with grey */
    background: var(--fills-tertiary);
    box-shadow: inset 0 0 0 var(--input-border-width) var(--brand-gray600);
  }
}

/* classic */

.rt-InputRoot:where(.rt-variant-classic) {
  --input-selection-color: var(--brand-accent-button-bezeled-fill-hover);
  --input-focus-color: var(--brand-accent-40);
  --input-border-width: 1px;

  /* Blend inner shadow with page background */
  background: var(--fills-tertiary);
  box-shadow:
    inset 0 0 0 1px var(--brand-gray500),
    inset 0 1.5px 2px 0 var(--brand-gray200),
    inset 0 1.5px 2px 0 hexToRgba(var(--grays-black), 0.2);
  color: var(--labels-primary);

  /* prettier-ignore */
  &:where(:has(.rt-Input:where(:autofill, [data-com-onepassword-filled]):not(:disabled, :read-only))) {
    /* Blend with focus color */
    background-image: linear-gradient(var(--brand-accent-button-bezeled-fill), var(--brand-accent-button-bezeled-fill));
    box-shadow: inset 0 0 0 1px var(--brand-accent-button-bezeled-fill-hover), inset 0 0 0 1px var(--brand-gray500);
  }

  &:where(:has(.rt-Input:where(:disabled, :read-only))) {
    /* Blend with grey */
    background: var(--fills-tertiary);
  }
}

/* soft */

.rt-InputRoot:where(.rt-variant-soft) {
  --input-selection-color: var(--brand-accent-button-bezeled-fill-hover);
  --input-focus-color: var(--brand-accent-40);
  --input-border-width: 0px;

  background: var(--fills-tertiary);
  color: var(--labels-primary);

  /* prettier-ignore */
  &:where(:has(.rt-Input:where(:autofill, [data-com-onepassword-filled]):not(:disabled, :read-only))) {
    /* Use gray autofill color when component color is gray */
    box-shadow: inset 0 0 0 1px var(--brand-accent-button-bezeled-fill-hover), inset 0 0 0 1px var(--brand-gray400);
  }

  &:where(:has(.rt-Input:where(:disabled, :read-only))) {
    background: var(--fills-tertiary);
  }
}

/* all disabled and read-only text fields */

.rt-Input {
  &:where(:disabled, :read-only) {
    cursor: text;
    color: var(--labels-tertiary);
    /* Safari */
    -webkit-text-fill-color: var(--labels-tertiary);

    &::placeholder {
      opacity: 0.5;
    }
    &:where(:placeholder-shown) {
      cursor: var(--cursor-disabled);

      & ~ :where(.rt-InputSlot) {
        cursor: var(--cursor-disabled);
      }
    }

    .rt-InputRoot:where(:has(&)) {
      --input-selection-color: var(--brand-gray500);
      --input-focus-color: var(--brand-accent-40);
    }
  }
}
