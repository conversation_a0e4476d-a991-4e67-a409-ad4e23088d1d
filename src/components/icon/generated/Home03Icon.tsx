// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M397.812 512h228.381M456.102 102.768l-311.408 216.934c-38.92 27.112-61.165 71.232-59.247 117.504l15.296 368.849c3.072 74.082 66.229 132.612 143.094 132.612h101.086c13.182 0 23.868-10.308 23.868-23.023v-104.977c0-70.694 57.308-128 128.002-128h30.413c70.694 0 128 57.306 128 128v104.977c0 12.715 10.688 23.023 23.868 23.023h101.086c76.868 0 140.023-58.53 143.095-132.612l15.296-368.849c1.92-46.272-20.326-90.391-59.247-117.504l-311.407-216.934c-33.37-23.246-78.426-23.245-111.795 0z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "home-03"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1713,
        "id": 307,
        "name": "home-03",
        "prevSize": 32,
        "code": 59989
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 342
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface Home03IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const Home03Icon = ({ size = 16, ...props }: Home03IconProps) => (
  <IcoMoon iconSet={iconSet} icon="home-03" size={size} {...props} />
);

Home03Icon.displayName = 'Home03Icon';

export default Home03Icon;
