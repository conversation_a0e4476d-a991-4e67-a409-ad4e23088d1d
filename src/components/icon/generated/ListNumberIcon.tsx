// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M369.778 229.216h568.889M369.778 524.425h568.889M369.778 819.631h568.889M85.333 207.567l71.111-36.901v258.308M90.222 634.697c3.276-8.422 8.227-16.030 14.528-22.327 6.301-6.302 13.812-11.149 22.042-14.234 8.23-3.081 16.993-4.331 25.719-3.657 8.726 0.67 17.217 3.243 24.919 7.548 7.702 4.309 14.443 10.253 19.782 17.446s9.156 15.475 11.202 24.303c2.046 8.828 2.275 18.005 0.672 26.931-1.603 8.93-5.002 17.404-9.975 24.875l-113.778 157.751h124.445"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "round",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "list-number"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "round",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1740,
        "id": 280,
        "name": "list-number",
        "prevSize": 32,
        "code": 60016
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 369
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ListNumberIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ListNumberIcon = ({ size = 16, ...props }: ListNumberIconProps) => (
  <IcoMoon iconSet={iconSet} icon="list-number" size={size} {...props} />
);

ListNumberIcon.displayName = 'ListNumberIcon';

export default ListNumberIcon;
