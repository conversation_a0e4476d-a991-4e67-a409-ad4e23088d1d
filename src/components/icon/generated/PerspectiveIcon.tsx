// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 512h118.519M203.852 512h616.295M203.852 512v-129.47c0-55.953 42.68-103.537 100.497-112.045l379.261-55.809c71.791-10.565 136.538 42.566 136.538 112.045v185.279M203.852 512v129.468c0 55.953 42.68 103.539 100.497 112.047l379.261 55.808c71.791 10.564 136.538-42.564 136.538-112.043v-185.28M820.147 512h118.519"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "perspective"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1821,
        "id": 199,
        "name": "perspective",
        "prevSize": 32,
        "code": 60097
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 450
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface PerspectiveIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const PerspectiveIcon = ({ size = 16, ...props }: PerspectiveIconProps) => (
  <IcoMoon iconSet={iconSet} icon="perspective" size={size} {...props} />
);

PerspectiveIcon.displayName = 'PerspectiveIcon';

export default PerspectiveIcon;
