// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M210.093 210.509c-77.089 77.195-124.76 183.776-124.76 301.491 0 235.639 191.025 426.667 426.667 426.667 117.713 0 224.294-47.671 301.491-124.762M210.093 210.509c77.228-77.334 183.98-125.176 301.907-125.176 235.639 0 426.667 191.025 426.667 426.667 0 117.926-47.842 224.678-125.175 301.905M210.093 210.509l603.398 603.396"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "forbidden"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1674,
        "id": 346,
        "name": "forbidden",
        "prevSize": 32,
        "code": 59950
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 303
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ForbiddenIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ForbiddenIcon = ({ size = 16, ...props }: ForbiddenIconProps) => (
  <IcoMoon iconSet={iconSet} icon="forbidden" size={size} {...props} />
);

ForbiddenIcon.displayName = 'ForbiddenIcon';

export default ForbiddenIcon;
