// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M251.258 256h687.408M251.258 512h687.408M251.258 768h687.404M85.807 256h-0.474M85.807 512h-0.474M85.807 768h-0.474"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "list-bullet"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1737,
        "id": 283,
        "name": "list-bullet",
        "prevSize": 32,
        "code": 60013
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 366
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ListBulletIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ListBulletIcon = ({ size = 16, ...props }: ListBulletIconProps) => (
  <IcoMoon iconSet={iconSet} icon="list-bullet" size={size} {...props} />
);

ListBulletIcon.displayName = 'ListBulletIcon';

export default ListBulletIcon;
