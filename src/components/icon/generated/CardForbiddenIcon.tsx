// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 384v341.333c0 70.694 57.308 128 128 128h298.667M85.333 384v-85.333c0-70.692 57.308-128 128-128h597.333c70.694 0 128 57.308 128 128v85.333M85.333 384h853.333M938.667 384v106.667M234.667 704h106.667M647.019 604.958c-30.707 30.861-49.685 73.399-49.685 120.375 0 94.255 76.412 170.667 170.667 170.667 46.976 0 89.515-18.978 120.375-49.685M647.019 604.958c30.908-31.061 73.698-50.291 120.981-50.291 94.255 0 170.667 76.412 170.667 170.667 0 47.283-19.226 90.074-50.291 120.981M647.019 604.958l241.357 241.357"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "card-forbidden"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1523,
        "id": 497,
        "name": "card-forbidden",
        "prevSize": 32,
        "code": 59799
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 152
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CardForbiddenIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CardForbiddenIcon = ({ size = 16, ...props }: CardForbiddenIconProps) => (
  <IcoMoon iconSet={iconSet} icon="card-forbidden" size={size} {...props} />
);

CardForbiddenIcon.displayName = 'CardForbiddenIcon';

export default CardForbiddenIcon;
