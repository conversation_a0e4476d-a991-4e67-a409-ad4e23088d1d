// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M938.667 621.355v19.917c0 93.555-72.674 169.395-162.321 169.395h-37.653c-82.807 0-151.049-67.802-155.115-154.116-4.16-88.414 19.537-175.817 67.469-248.846l124.791-190.125c5.611-8.55 18.436-3.161 16.802 7.062l-32.533 203.72c-3.639 22.775 13.193 43.511 35.319 43.511 79.113 0 143.241 66.927 143.241 149.483z",
          "M440.887 621.355v19.917c0 93.555-72.672 169.395-162.32 169.395h-37.654c-82.807 0-151.049-67.802-155.112-154.116-4.162-88.414 19.535-175.817 67.469-248.846l124.79-190.125c5.612-8.55 18.435-3.161 16.802 7.062l-32.535 203.72c-3.638 22.775 13.194 43.511 35.322 43.511 79.109 0 143.238 66.927 143.238 149.483z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "quote-up"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1843,
        "id": 177,
        "name": "quote-up",
        "prevSize": 32,
        "code": 60119
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 472
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface QuoteUpIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const QuoteUpIcon = ({ size = 16, ...props }: QuoteUpIconProps) => (
  <IcoMoon iconSet={iconSet} icon="quote-up" size={size} {...props} />
);

QuoteUpIcon.displayName = 'QuoteUpIcon';

export default QuoteUpIcon;
