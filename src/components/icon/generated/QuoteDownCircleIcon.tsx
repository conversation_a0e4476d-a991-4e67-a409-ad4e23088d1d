// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M256 449.51v-11.379c0-53.46 43.604-96.798 97.393-96.798h22.592c49.684 0 90.63 38.745 93.067 88.068 2.5 50.522-11.721 100.463-40.482 142.195l-74.873 108.642c-3.367 4.885-11.060 1.809-10.081-4.032l19.521-116.412c2.183-13.018-7.917-24.866-21.193-24.866-47.465 0-85.943-38.242-85.943-85.419z",
          "M554.667 449.51v-11.379c0-53.46 43.605-96.798 97.395-96.798h22.592c49.681 0 90.628 38.745 93.065 88.068 2.5 50.522-11.721 100.463-40.482 142.195l-74.871 108.642c-3.366 4.885-11.063 1.809-10.082-4.032l19.52-116.412c2.185-13.018-7.915-24.866-21.193-24.866-47.467 0-85.943-38.242-85.943-85.419z",
          "M938.667 512c0 235.639-191.027 426.667-426.667 426.667-235.642 0-426.667-191.027-426.667-426.667 0-235.642 191.025-426.667 426.667-426.667 235.639 0 426.667 191.025 426.667 426.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "quote-down-circle"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1838,
        "id": 182,
        "name": "quote-down-circle",
        "prevSize": 32,
        "code": 60114
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 467
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface QuoteDownCircleIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const QuoteDownCircleIcon = ({ size = 16, ...props }: QuoteDownCircleIconProps) => (
  <IcoMoon iconSet={iconSet} icon="quote-down-circle" size={size} {...props} />
);

QuoteDownCircleIcon.displayName = 'QuoteDownCircleIcon';

export default QuoteDownCircleIcon;
