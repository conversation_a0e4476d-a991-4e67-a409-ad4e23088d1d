// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M192 642.863v-17.638c0-2.436-1.91-4.412-4.267-4.412h-98.133c-2.356 0-4.267 1.975-4.267 4.412v255.765c0 2.432 1.91 4.407 4.267 4.407h98.133c2.356 0 4.267-1.975 4.267-4.407v-17.643M192 642.863v220.484M192 642.863c17.988-8.265 40.057-22.379 64-36.919M192 863.347h128l236.907 30.609c66.901 8.644 134.4-10.338 187.84-52.826l178.138-141.623c9.95-7.91 15.782-20.151 15.782-33.126 0-31.066-31.633-51.268-58.517-37.376l-14.14 7.189M734.153 128h110.524c11.78 0 21.333 9.551 21.333 21.333v486.861M734.153 128v175.998M734.153 128h-456.819c-11.782 0-21.333 9.551-21.333 21.333v456.61M256 605.943c41.164-25.003 87.869-51.277 128.905-51.277h197.823c19.849 0 35.938 16.631 35.938 37.146 0 17.045-11.226 31.902-27.221 36.036l-124.369 32.132c-11.209 2.897-19.076 13.308-19.076 25.254 0 13.466 9.933 24.704 22.908 25.924l162.099 15.232c32.461 3.051 56.38-0.166 85.534-15.232l147.469-74.965"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "loyalty-1"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1750,
        "id": 270,
        "name": "loyalty-1",
        "prevSize": 32,
        "code": 60026
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 379
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface Loyalty-1IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const Loyalty-1Icon = ({ size = 16, ...props }: Loyalty-1IconProps) => (
  <IcoMoon iconSet={iconSet} icon="loyalty-1" size={size} {...props} />
);

Loyalty-1Icon.displayName = 'Loyalty-1Icon';

export default Loyalty-1Icon;
