// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M938.667 539.307c0 50.18 0 75.273-9.766 94.438-8.589 16.858-22.298 30.566-39.155 39.155-17.971 9.156-41.148 9.728-85.325 9.762M795.307 85.333c50.18 0 75.273 0 94.438 9.766 16.858 8.59 30.566 22.297 39.155 39.156 9.766 19.166 9.766 44.257 9.766 94.438M484.693 85.333c-50.18 0-75.271 0-94.438 9.766-16.859 8.59-30.566 22.297-39.156 39.156-9.156 17.969-9.728 41.145-9.763 85.323M580.267 85.333h119.467M938.667 324.267v119.467M228.693 938.667c-50.181 0-75.271 0-94.438-9.766-16.859-8.589-30.566-22.298-39.156-39.155-9.766-19.166-9.766-44.258-9.766-94.438v-310.613c0-50.18 0-75.271 9.766-94.438 8.59-16.859 22.297-30.566 39.156-39.156 19.166-9.766 44.257-9.766 94.438-9.766h310.613c50.18 0 75.273 0 94.438 9.766 16.858 8.59 30.566 22.297 39.155 39.156 9.766 19.166 9.766 44.257 9.766 94.438v310.613c0 50.18 0 75.273-9.766 94.438-8.589 16.858-22.298 30.566-39.155 39.155-18.453 9.404-42.394 9.754-88.926 9.766h-316.126z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "selection-background"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1869,
        "id": 151,
        "name": "selection-background",
        "prevSize": 32,
        "code": 60145
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 498
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SelectionBackgroundIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SelectionBackgroundIcon = ({ size = 16, ...props }: SelectionBackgroundIconProps) => (
  <IcoMoon iconSet={iconSet} icon="selection-background" size={size} {...props} />
);

SelectionBackgroundIcon.displayName = 'SelectionBackgroundIcon';

export default SelectionBackgroundIcon;
