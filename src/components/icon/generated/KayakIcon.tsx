// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M485.252 110.346v0zM503.543 87.059v0zM538.743 110.346v0zM520.452 87.059v0zM538.743 913.655v0zM520.452 936.943v0zM485.252 913.655v0zM503.543 936.943v0zM841.873 134.034v0zM890.142 182.306v0zM890.142 194.374v0zM830.66 253.856v0zM745.327 253.856v0zM770.321 193.516v0zM829.803 134.034v0zM182.302 889.967v0zM134.031 841.694v0zM134.031 829.628v0zM193.513 770.146v0zM253.853 745.152v0zM278.846 745.152h32v-32h-32v32zM278.846 770.146v0zM253.853 830.485v0zM194.371 889.967v0zM278.846 745.331v0zM657.126 367.051v0zM445.431 578.748v0zM436.309 749.538c-7.155-16.162-26.054-23.462-42.215-16.307-16.16 7.151-23.462 26.052-16.308 42.214l58.523-25.907zM713.685 485.474c-1.271-17.63-16.593-30.891-34.219-29.619s-30.886 16.589-29.619 34.219l63.838-4.599zM514.957 618.155c-17.408 3.034-29.065 19.61-26.031 37.018 3.038 17.412 19.61 29.069 37.022 26.031l-10.991-63.049zM598.626 641.293c12.497-12.497 12.497-32.755 0-45.252s-32.755-12.497-45.252 0l45.252 45.252zM682.662 512h-32c0 63.919-22.089 139.379-51.533 210.782-29.188 70.788-64.358 134.673-87.94 174.596l55.108 32.55c24.444-41.391 61.235-108.139 91.998-182.75 30.511-73.993 56.367-158.703 56.367-235.179h-32zM341.331 512h32c0-63.919 22.091-139.378 51.535-210.784 29.189-70.788 64.359-134.671 87.941-174.597l-55.108-32.547c-24.444 41.389-61.234 108.135-92 182.746-30.511 73.994-56.367 158.705-56.367 235.181h32zM485.252 110.346l27.554 16.273c2.065-3.498 3.657-6.189 5.065-8.469 1.412-2.286 2.325-3.642 2.944-4.476 1.246-1.679-0.149 0.769-4.134 2.564l-26.274-58.359c-10.167 4.576-16.67 11.833-21.005 17.685-4.023 5.425-8.017 12.268-11.703 18.509l27.554 16.273zM538.743 110.346l27.554-16.273c-3.686-6.241-7.68-13.084-11.703-18.509-4.335-5.852-10.842-13.108-21.005-17.685l-26.274 58.359c-3.985-1.795-5.38-4.243-4.134-2.564 0.619 0.834 1.532 2.191 2.944 4.476 1.408 2.28 2.999 4.971 5.065 8.469l27.554-16.273zM503.543 87.059l13.137 29.18c-2.057 0.925-3.661 1.095-4.685 1.095-1.020 0-2.624-0.17-4.681-1.095l26.274-58.359c-13.466-6.062-29.717-6.062-43.183 0l13.137 29.179zM538.743 913.655l-27.554-16.277c-2.065 3.499-3.657 6.191-5.065 8.469-1.412 2.287-2.325 3.644-2.944 4.476-1.246 1.681 0.149-0.768 4.134-2.56l26.274 58.359c10.163-4.578 16.67-11.836 21.005-17.685 4.023-5.427 8.017-12.271 11.703-18.509l-27.554-16.273zM485.252 913.655l-27.554 16.273c3.686 6.238 7.68 13.082 11.703 18.509 4.335 5.85 10.842 13.107 21.005 17.685l26.274-58.359c3.985 1.792 5.38 4.241 4.134 2.56-0.619-0.832-1.532-2.189-2.944-4.476-1.408-2.278-2.999-4.971-5.065-8.465l-27.554 16.273zM520.452 936.943l-13.137-29.18c2.057-0.926 3.661-1.097 4.681-1.097 1.024 0 2.628 0.171 4.685 1.097l-26.274 58.359c13.466 6.059 29.717 6.059 43.183 0l-13.137-29.18zM841.873 134.034l-22.63 22.627 48.273 48.272 45.257-45.255-48.273-48.272-22.626 22.627zM890.142 194.374l-22.626-22.627-59.482 59.482 45.257 45.255 59.482-59.482-22.63-22.627zM770.321 278.849v-32h-24.994v64h24.994v-32zM745.327 278.849h32v-24.993h-64v24.993h32zM770.321 193.516l22.626 22.627 59.486-59.482-45.257-45.255-59.482 59.482 22.626 22.627zM745.327 253.856h32c0-14.145 5.619-27.711 15.62-37.713l-45.252-45.255c-22.003 22.004-34.368 51.849-34.368 82.967h32zM830.66 253.856l-22.626-22.627c-10.001 10.002-23.569 15.621-37.713 15.621v64c31.121 0 60.962-12.362 82.97-34.366l-22.63-22.627zM890.142 182.306l-22.626 22.627c-9.165-9.164-9.165-24.023 0-33.187l45.257 45.255c15.829-15.829 15.829-41.493 0-57.323l-22.63 22.627zM841.873 134.034l22.626-22.627c-15.829-15.829-41.493-15.829-57.323 0l45.257 45.255c-9.165 9.164-24.026 9.164-33.19 0l22.63-22.627zM182.302 889.967l22.627-22.63-48.272-48.269-45.255 45.252 48.272 48.273 22.627-22.626zM134.031 829.628l22.627 22.626 59.482-59.482-45.255-45.257-59.482 59.482 22.627 22.63zM253.853 745.152v32h24.993v-64h-24.993v32zM253.853 830.485l-22.627-22.63-59.482 59.482 45.255 45.257 59.482-59.482-22.627-22.626zM278.846 770.146h-32c0 14.144-5.619 27.708-15.621 37.709l45.255 45.257c22.004-22.003 34.366-51.849 34.366-82.965h-32zM193.513 770.146l22.627 22.626c10.002-10.001 23.568-15.62 37.713-15.62v-64c-31.119 0-60.963 12.361-82.967 34.364l22.627 22.63zM134.031 841.694l22.627-22.626c9.164 9.165 9.164 24.021 0 33.186l-45.255-45.257c-15.829 15.829-15.829 41.493 0 57.323l22.627-22.626zM182.302 889.967l-22.627 22.626c15.829 15.829 41.494 15.829 57.323 0l-45.255-45.257c9.164-9.161 24.022-9.161 33.187 0l-22.627 22.63zM278.846 745.152h-32v0.179h64v-0.179h-32zM278.846 745.331h-32v24.815h64v-24.815h-32zM426.665 512h31.998c0-48.269 30.174-74.667 53.333-74.667v-64c-71.095 0-117.331 69.116-117.331 138.667h32zM745.327 278.849l-22.626-22.627-88.201 88.201 45.252 45.255 88.205-88.201-22.63-22.627zM538.743 110.346l-27.554 16.273c31.957 54.109 84.651 151.303 115.388 249.95l61.103-19.039c-32.934-105.694-88.563-207.89-121.382-263.458l-27.554 16.273zM657.126 367.051l-22.626-22.627-78.421 78.422 45.252 45.255 78.421-78.422-22.626-22.627zM511.996 405.333v32c13.756 0 28.612 7.957 39.744 25.374l53.926-34.466c-20.139-31.515-53.423-54.908-93.67-54.908v32zM578.705 445.474l-22.626-22.629-133.275 133.272 45.254 45.257 133.274-133.274-22.626-22.626zM445.431 578.748l26.923-17.301c-8.277-12.877-13.692-30.033-13.692-49.446h-63.998c0 31.113 8.637 60.378 23.846 84.045l26.921-17.297zM445.431 578.748l-22.627-22.63-78.477 78.477 45.255 45.257 78.476-78.477-22.626-22.626zM366.954 657.225l-22.627-22.63-88.108 88.111 45.255 45.252 88.108-88.107-22.627-22.626zM366.954 657.225l30.548-9.532c-14.884-47.706-24.171-94.302-24.171-135.693h-64c0 50.206 11.122 103.62 27.075 154.756l30.548-9.532zM485.252 913.655l27.554-16.273c-20.544-34.782-49.937-87.838-76.497-147.844l-58.523 25.907c27.887 62.997 58.575 118.353 79.912 154.483l27.554-16.273zM681.766 487.774l-31.919 2.3c0.542 7.479 0.815 14.793 0.815 21.926h64c0-8.781-0.337-17.634-0.977-26.526l-31.919 2.3zM520.452 649.681l5.495 31.522c25.233-4.395 46.852-14.080 72.678-39.91l-45.252-45.252c-16.841 16.836-26.615 20.058-38.417 22.114l5.495 31.526z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "kayak"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1726,
        "id": 294,
        "name": "kayak",
        "prevSize": 32,
        "code": 60002
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 355
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface KayakIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const KayakIcon = ({ size = 16, ...props }: KayakIconProps) => (
  <IcoMoon iconSet={iconSet} icon="kayak" size={size} {...props} />
);

KayakIcon.displayName = 'KayakIcon';

export default KayakIcon;
