// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M576 512v285.867c0 11.947 0 17.92 2.325 22.485 2.044 4.015 5.308 7.279 9.323 9.323 4.565 2.325 10.539 2.325 22.485 2.325h145.067c11.947 0 17.92 0 22.485-2.325 4.015-2.044 7.279-5.308 9.323-9.323 2.325-4.565 2.325-10.539 2.325-22.485v-285.867M576 512v-285.867c0-11.948 0-17.922 2.325-22.485 2.044-4.014 5.308-7.278 9.323-9.323 4.565-2.325 10.539-2.325 22.485-2.325h145.067c11.947 0 17.92 0 22.485 2.325 4.015 2.045 7.279 5.309 9.323 9.323 2.325 4.564 2.325 10.537 2.325 22.485v285.867M576 512h-128M789.333 512h149.333M448 512v-179.2c0-11.948 0-17.922-2.325-22.485-2.044-4.014-5.308-7.278-9.323-9.323-4.565-2.325-10.537-2.325-22.485-2.325h-145.067c-11.948 0-17.922 0-22.485 2.325-4.014 2.045-7.278 5.309-9.323 9.323-2.325 4.563-2.325 10.537-2.325 22.485v179.2M448 512v179.2c0 11.947 0 17.92-2.325 22.485-2.044 4.015-5.308 7.279-9.323 9.323-4.565 2.325-10.537 2.325-22.485 2.325h-145.067c-11.948 0-17.922 0-22.485-2.325-4.014-2.044-7.278-5.308-9.323-9.323-2.325-4.565-2.325-10.539-2.325-22.485v-179.2M234.667 512h-149.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "align-vertical-center"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1394,
        "id": 626,
        "name": "align-vertical-center",
        "prevSize": 32,
        "code": 59670
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 23
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface AlignVerticalCenterIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const AlignVerticalCenterIcon = ({ size = 16, ...props }: AlignVerticalCenterIconProps) => (
  <IcoMoon iconSet={iconSet} icon="align-vertical-center" size={size} {...props} />
);

AlignVerticalCenterIcon.displayName = 'AlignVerticalCenterIcon';

export default AlignVerticalCenterIcon;
