// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 938.667h71.111M156.445 938.667v-734.815c0-65.456 53.062-118.519 118.518-118.519h474.076c65.455 0 118.515 53.062 118.515 118.519v734.815M156.445 938.667h711.11M867.554 938.667h71.113M298.667 938.667v-497.779c0-39.272 31.837-71.11 71.111-71.11h142.222M725.333 938.667v-497.779c0-39.272-31.838-71.11-71.113-71.11h-142.221M512 369.778v568.889M440.887 227.555h142.225"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "lift"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1730,
        "id": 290,
        "name": "lift",
        "prevSize": 32,
        "code": 60006
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 359
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface LiftIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const LiftIcon = ({ size = 16, ...props }: LiftIconProps) => (
  <IcoMoon iconSet={iconSet} icon="lift" size={size} {...props} />
);

LiftIcon.displayName = 'LiftIcon';

export default LiftIcon;
