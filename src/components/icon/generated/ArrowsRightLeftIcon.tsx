// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M722.961 106.667l214.123 208.692c1.054 1.282 1.583 2.962 1.583 4.642M722.961 533.333l214.123-208.692c1.054-1.282 1.583-2.962 1.583-4.642M938.667 320h-597.333M301.037 490.667l-214.123 208.691c-1.054 1.284-1.581 2.961-1.581 4.642M301.037 917.333l-214.123-208.691c-1.054-1.284-1.581-2.961-1.581-4.642M85.333 704h597.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "arrows-right-left"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1440,
        "id": 580,
        "name": "arrows-right-left",
        "prevSize": 32,
        "code": 59716
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 69
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ArrowsRightLeftIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ArrowsRightLeftIcon = ({ size = 16, ...props }: ArrowsRightLeftIconProps) => (
  <IcoMoon iconSet={iconSet} icon="arrows-right-left" size={size} {...props} />
);

ArrowsRightLeftIcon.displayName = 'ArrowsRightLeftIcon';

export default ArrowsRightLeftIcon;
