import type { Meta, StoryObj } from '@storybook/react-vite';

import { Avatar } from '@components';
import Icon from '@components/icon';

import { avatarPropDefs } from './avatar.props';

// 🔧 Shared Controls for Avatar
const avatarArgTypes = {
  size: {
    control: { type: 'inline-radio' as const },
    options: avatarPropDefs.size.values,
  },
  variant: {
    control: { type: 'inline-radio' as const },
    options: avatarPropDefs.variant.values,
  },
  color: {
    control: { type: 'select' as const },
    options: avatarPropDefs.color.values,
  },
  highContrast: { control: { type: 'boolean' as const } },
  isVerify: { control: { type: 'boolean' as const } },
  isOnline: { control: { type: 'boolean' as const } },
  radius: {
    control: { type: 'select' as const },
    options: avatarPropDefs.radius.values,
  },
  src: { control: { type: 'text' as const } },
  alt: { control: { type: 'text' as const } },
};

// 🧱 Avatar Meta
const meta: Meta<typeof Avatar> = {
  title: 'DesignSystem/Avatar',
  component: Avatar,
  parameters: { layout: 'centered' },
};
export default meta;

type AvatarStory = StoryObj<typeof Avatar>;
type AvatarGroupStory = StoryObj<typeof Avatar.Group>;

// 🔹 Avatar: Default
export const _avatar: AvatarStory = {
  args: {
    size: avatarPropDefs.size.default,
    variant: avatarPropDefs.variant.default,
    color: avatarPropDefs.color.default,
    highContrast: false,
    radius: avatarPropDefs.radius.default,
    fallback: 'AB',
    src: 'https://i.pravatar.cc/128?u=default',
    alt: 'Avatar',
    isVerify: false,
    isOnline: false,
  },
  argTypes: avatarArgTypes,
};

// 🔹 Avatar: With Icon
export const _avatarIcon: AvatarStory = {
  args: {
    size: avatarPropDefs.size.default,
    variant: avatarPropDefs.variant.default,
    color: avatarPropDefs.color.default,
    highContrast: false,
    radius: avatarPropDefs.radius.default,
    fallback: <Icon icon="home-01" />,
    src: undefined,
    alt: 'Avatar Icon',
    isVerify: false,
    isOnline: false,
  },
  argTypes: avatarArgTypes,
};

// 🔸 Avatar Group
export const _avatarGroup: AvatarGroupStory = {
  render: (args) => (
    <Avatar.Group {...args}>
      <Avatar fallback="JD" />
      <Avatar fallback="MK" />
      <Avatar fallback="TS" />
      <Avatar fallback="AL" />
    </Avatar.Group>
  ),
  args: {
    size: '1',
    variant: 'solid',
    radius: 'full',
    maxOptions: {
      count: 3,
      variant: 'soft',
      style: { backgroundColor: '#f0f0f0' },
    },
    spacing: undefined,
  },
  argTypes: {
    size: {
      control: { type: 'inline-radio' },
      options: avatarPropDefs.size.values,
    },
    variant: {
      control: { type: 'inline-radio' },
      options: avatarPropDefs.variant.values,
    },
    radius: {
      control: { type: 'select' },
      options: avatarPropDefs.radius.values,
    },
    spacing: { control: { type: 'number' } },
    maxOptions: {
      control: { type: 'object' },
      description: 'Controls overflow avatars. Includes count, style, and variant.',
    },
  },
};
