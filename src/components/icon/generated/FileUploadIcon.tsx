// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M648.533 906.667c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM375.467 970.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM613.491 122.824v0zM815.842 325.176v0zM240.556 924.715v0zM184.618 868.779v0zM783.445 924.715v0zM839.381 868.779v0zM184.618 155.223v0zM240.556 99.284v0zM841.835 362.667v0zM360.969 663.138c-12.27 12.719-11.906 32.977 0.814 45.248s32.977 11.904 45.248-0.815l-46.061-44.433zM508.71 556.079v0zM515.29 556.079v0zM616.969 707.571c12.271 12.719 32.529 13.086 45.248 0.815s13.086-32.529 0.815-45.248l-46.063 44.433zM480 938.667c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM170.667 733.867h32v-443.733h-64v443.733h32zM853.333 415.686h-32v318.181h64v-318.181h-32zM613.491 122.824l-22.63 22.627 202.355 202.353 45.252-45.255-202.351-202.353-22.626 22.627zM375.467 85.333v32h147.516v-64h-147.516v32zM375.467 938.667v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM170.667 733.867h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM240.556 924.715l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM648.533 938.667v32c35.315 0 63.727 0.026 86.677-1.852 23.322-1.903 43.806-5.931 62.763-15.586l-29.056-57.024c-8.427 4.292-19.554 7.241-38.921 8.823-19.738 1.613-45.090 1.638-81.463 1.638v32zM853.333 733.867h-32c0 36.373-0.026 61.726-1.638 81.463-1.583 19.366-4.531 30.494-8.823 38.921l57.024 29.056c9.655-18.957 13.683-39.441 15.586-62.763 1.877-22.95 1.852-51.362 1.852-86.677h-32zM783.445 924.715l14.528 28.514c30.106-15.343 54.579-39.817 69.922-69.922l-57.024-29.056c-9.203 18.061-23.893 32.747-41.954 41.954l14.528 28.51zM170.667 290.133h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM375.467 85.333v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM184.618 155.223l28.512 14.528c9.204-18.063 23.89-32.75 41.953-41.953l-29.055-57.024c-30.106 15.34-54.583 39.817-69.923 69.923l28.512 14.528zM613.491 122.824l22.626-22.627c-13.679-13.681-29.585-24.629-46.861-32.492l-26.513 58.252c10.368 4.718 19.908 11.285 28.117 19.495l22.63-22.627zM576 96.83l13.257-29.126c-20.617-9.381-43.191-14.371-66.274-14.371v64c13.85 0 27.392 2.993 39.761 8.623l13.257-29.126zM576 96.83h-32v137.836h64v-137.836h-32zM853.333 415.686h32c0-23.087-4.992-45.66-14.37-66.274l-58.253 26.509c5.632 12.372 8.623 25.913 8.623 39.765h32zM841.835 362.667l29.129-13.254c-7.863-17.277-18.812-33.183-32.495-46.863l-45.252 45.255c8.209 8.209 14.775 17.75 19.494 28.117l29.124-13.254zM704 362.667v32h137.835v-64h-137.835v32zM576 234.667h-32c0 88.366 71.633 160 160 160v-64c-53.018 0-96-42.981-96-96h-32zM384 685.355l23.031 22.217 124.707-129.276-46.059-44.433-124.71 129.276 23.031 22.217zM515.29 556.079l-23.027 22.217 124.706 129.276 46.063-44.433-124.71-129.276-23.031 22.217zM512 554.667h-32v384h64v-384h-32zM508.71 556.079l23.027 22.217c-5.239 5.436-12.416 8.371-19.738 8.371v-64c-9.702 0-19.264 3.878-26.321 11.196l23.031 22.217zM512 554.667v32c-7.322 0-14.498-2.935-19.738-8.371l46.059-44.433c-7.057-7.317-16.619-11.196-26.321-11.196v32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "file-upload"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1644,
        "id": 376,
        "name": "file-upload",
        "prevSize": 32,
        "code": 59920
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 273
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FileUploadIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FileUploadIcon = ({ size = 16, ...props }: FileUploadIconProps) => (
  <IcoMoon iconSet={iconSet} icon="file-upload" size={size} {...props} />
);

FileUploadIcon.displayName = 'FileUploadIcon';

export default FileUploadIcon;
