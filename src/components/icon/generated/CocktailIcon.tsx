// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 554.667v384M512 554.667l189.359-189.36M512 554.667l-291.383-291.383c-2.688-2.688-0.784-7.284 3.017-7.284h576.733c3.802 0 5.705 4.596 3.017 7.284l-102.025 102.023M512 938.667h-128M512 938.667h128M640 256c0-70.692 57.306-128 128-128s128 57.308 128 128c0 70.692-57.306 128-128 128-24.414 0-47.228-6.834-66.641-18.694M128 85.333h168.030c1.616 0 3.093 0.913 3.816 2.359l84.154 168.308"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "cocktail"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1576,
        "id": 444,
        "name": "cocktail",
        "prevSize": 32,
        "code": 59852
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 205
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CocktailIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CocktailIcon = ({ size = 16, ...props }: CocktailIconProps) => (
  <IcoMoon iconSet={iconSet} icon="cocktail" size={size} {...props} />
);

CocktailIcon.displayName = 'CocktailIcon';

export default CocktailIcon;
