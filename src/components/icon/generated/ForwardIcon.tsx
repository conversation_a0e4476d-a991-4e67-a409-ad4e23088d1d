// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 733.077v-442.153c0-10.38 11.351-16.917 20.538-11.828l399.106 221.077c9.365 5.188 9.365 18.466 0 23.654l-399.106 221.077c-9.187 5.090-20.538-1.451-20.538-11.827z",
          "M512 733.077v-442.153c0-10.38 11.349-16.917 20.535-11.828l399.108 221.077c9.365 5.188 9.365 18.466 0 23.654l-399.108 221.077c-9.186 5.090-20.535-1.451-20.535-11.827z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "forward"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1676,
        "id": 344,
        "name": "forward",
        "prevSize": 32,
        "code": 59952
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 305
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ForwardIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ForwardIcon = ({ size = 16, ...props }: ForwardIconProps) => (
  <IcoMoon iconSet={iconSet} icon="forward" size={size} {...props} />
);

ForwardIcon.displayName = 'ForwardIcon';

export default ForwardIcon;
