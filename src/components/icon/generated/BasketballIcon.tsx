// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M557.056 87.684c1.553 14.806 2.351 29.839 2.351 45.056 0 94.409-30.66 181.656-82.573 252.326M557.056 87.684c-14.805-1.554-29.837-2.351-45.056-2.351-120.579 0-229.476 50.019-307.073 130.439M557.056 87.684c199.654 20.954 358.306 179.604 379.26 379.26M204.927 215.773c-74.047 76.741-119.593 181.165-119.593 296.227 0 15.219 0.797 30.251 2.351 45.056M204.927 215.773c93.347 40.827 185.587 97.523 271.907 169.294M87.684 557.056c14.806 1.553 29.839 2.351 45.056 2.351 141.233 0 266.438-68.621 344.093-174.34M87.684 557.056c20.954 199.654 179.604 358.306 379.26 379.26M466.944 936.316c14.805 1.553 29.837 2.351 45.056 2.351 115.063 0 219.486-45.547 296.226-119.595M466.944 936.316c-1.553-14.805-2.351-29.837-2.351-45.056 0-141.235 68.621-266.441 174.34-344.094M808.226 819.072c80.422-77.598 130.441-186.492 130.441-307.072 0-15.219-0.798-30.251-2.351-45.056M808.226 819.072c-40.828-93.346-97.523-185.587-169.293-271.906M638.933 547.166c-24.094-28.979-49.886-57.289-77.346-84.753-27.465-27.46-55.774-53.253-84.753-77.346M638.933 547.166c70.669-51.913 157.918-82.573 252.326-82.573 15.219 0 30.251 0.798 45.056 2.351"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "basketball"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1457,
        "id": 563,
        "name": "basketball",
        "prevSize": 32,
        "code": 59733
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 86
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BasketballIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BasketballIcon = ({ size = 16, ...props }: BasketballIconProps) => (
  <IcoMoon iconSet={iconSet} icon="basketball" size={size} {...props} />
);

BasketballIcon.displayName = 'BasketballIcon';

export default BasketballIcon;
