import clsx from 'clsx';
import { ScrollArea as ScrollAreaPrimitive } from 'radix-ui';
import * as React from 'react';

import { scrollAreaPropDefs } from '@components/scroll-area/scroll-area.props';
import {
  type ComponentPropsWithout,
  type RemovedProps,
  extractMarginProps,
  getMarginStyles,
  getResponsiveClassNames,
  getSubtree,
  mergeStyles,
} from '@helpers';
import type { GetPropDefTypes, MarginProps } from '@props';

type ScrollAreaElement = React.ComponentRef<typeof ScrollAreaPrimitive.Viewport>;
type ScrollAreaOwnProps = GetPropDefTypes<typeof scrollAreaPropDefs>;
interface ScrollAreaProps
  extends ComponentPropsWithout<typeof ScrollAreaPrimitive.Root, RemovedProps>,
    ComponentPropsWithout<typeof ScrollAreaPrimitive.Viewport, RemovedProps | 'dir'>,
    MarginProps,
    ScrollAreaOwnProps {}
const ScrollArea = React.forwardRef<ScrollAreaElement, ScrollAreaProps>((props, forwardedRef) => {
  const { rest: marginRest, ...marginProps } = extractMarginProps(props);
  const [marginClassNames, marginCustomProperties] = getMarginStyles(marginProps);

  const {
    asChild,
    children,
    className,
    style,
    type,
    scrollHideDelay = type !== 'scroll' ? 0 : undefined,
    dir,
    size = scrollAreaPropDefs.size.default,
    radius = scrollAreaPropDefs.radius.default,
    scrollbars = scrollAreaPropDefs.scrollbars.default,
    ...viewportProps
  } = marginRest;

  return (
    <ScrollAreaPrimitive.Root
      type={type}
      scrollHideDelay={scrollHideDelay}
      className={clsx('rt-ScrollAreaRoot', marginClassNames, className)}
      style={mergeStyles(marginCustomProperties, style)}
      asChild={asChild}
    >
      {getSubtree({ asChild, children }, (children) => (
        <>
          <ScrollAreaPrimitive.Viewport {...viewportProps} ref={forwardedRef} className="rt-ScrollAreaViewport">
            {children}
          </ScrollAreaPrimitive.Viewport>

          <div className="rt-ScrollAreaViewportFocusRing" />

          {scrollbars !== 'vertical' ? (
            <ScrollAreaPrimitive.Scrollbar
              data-radius={radius}
              orientation="horizontal"
              className={clsx(
                'rt-ScrollAreaScrollbar',
                getResponsiveClassNames({
                  className: 'rt-r-size',
                  value: size,
                  propValues: scrollAreaPropDefs.size.values,
                }),
              )}
            >
              <ScrollAreaPrimitive.Thumb className="rt-ScrollAreaThumb" />
            </ScrollAreaPrimitive.Scrollbar>
          ) : null}

          {scrollbars !== 'horizontal' ? (
            <ScrollAreaPrimitive.Scrollbar
              data-radius={radius}
              orientation="vertical"
              className={clsx(
                'rt-ScrollAreaScrollbar',
                getResponsiveClassNames({
                  className: 'rt-r-size',
                  value: size,
                  propValues: scrollAreaPropDefs.size.values,
                }),
              )}
            >
              <ScrollAreaPrimitive.Thumb className="rt-ScrollAreaThumb" />
            </ScrollAreaPrimitive.Scrollbar>
          ) : null}

          {scrollbars === 'both' ? <ScrollAreaPrimitive.Corner className="rt-ScrollAreaCorner" /> : null}
        </>
      ))}
    </ScrollAreaPrimitive.Root>
  );
});
ScrollArea.displayName = 'ScrollArea';

export { ScrollArea };
export type { ScrollAreaProps };
