// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M131.926 876.032v0zM94.634 838.741v0zM929.365 838.741v0zM892.075 876.032v0zM892.075 595.968v0zM929.365 633.259v0zM131.926 595.968v0zM94.634 633.259v0zM277.333 704c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM490.24 768c17.677 0 32-14.327 32-32s-14.323-32-32-32v64zM618.671 768c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM618.244 704c-17.677 0-32 14.327-32 32s14.323 32 32 32v-64zM746.671 768c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM746.244 704c-17.677 0-32 14.327-32 32s14.323 32 32 32v-64zM131.926 428.032v0zM94.634 390.74v0zM929.365 390.74v0zM892.075 428.032v0zM892.075 147.968v0zM929.365 185.26v0zM131.926 147.968v0zM94.634 185.26v0zM277.333 256c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM490.24 320c17.677 0 32-14.327 32-32s-14.323-32-32-32v64zM618.671 320c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM618.244 256c-17.677 0-32 14.327-32 32s14.323 32 32 32v-64zM746.671 320c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM746.244 256c-17.677 0-32 14.327-32 32s14.323 32 32 32v-64zM221.867 586.667v32h580.267v-64h-580.267v32zM938.667 723.2h-32v25.6h64v-25.6h-32zM802.133 885.333v-32h-580.267v64h580.267v-32zM85.333 748.8h32v-25.6h-64v25.6h32zM221.867 885.333v-32c-24.424 0-40.817-0.026-53.441-1.058-12.251-0.998-18.108-2.786-21.972-4.753l-29.055 57.024c14.39 7.33 29.607 10.193 45.815 11.52 15.835 1.293 35.285 1.267 58.653 1.267v-32zM85.333 748.8h-32c0 23.369-0.025 42.816 1.269 58.654 1.324 16.209 4.188 31.424 11.52 45.815l57.024-29.056c-1.969-3.866-3.756-9.724-4.756-21.973-1.032-12.625-1.056-29.018-1.056-53.44h-32zM131.926 876.032l14.528-28.51c-10.035-5.116-18.194-13.274-23.308-23.309l-57.024 29.056c11.249 22.076 29.199 40.026 51.277 51.277l14.528-28.514zM938.667 748.8h-32c0 24.422-0.026 40.815-1.058 53.44-0.998 12.25-2.786 18.108-4.753 21.973l57.024 29.056c7.33-14.391 10.193-29.606 11.52-45.815 1.293-15.838 1.267-35.285 1.267-58.654h-32zM802.133 885.333v32c23.369 0 42.816 0.026 58.654-1.267 16.209-1.327 31.424-4.19 45.815-11.52l-29.056-57.024c-3.866 1.967-9.724 3.755-21.973 4.753-12.625 1.033-29.018 1.058-53.44 1.058v32zM929.365 838.741l-28.51-14.528c-5.116 10.035-13.274 18.193-23.309 23.309l29.056 57.024c22.076-11.251 40.026-29.201 51.277-51.277l-28.514-14.528zM802.133 586.667v32c24.422 0 40.815 0.026 53.44 1.058 12.25 0.998 18.108 2.786 21.973 4.753l29.056-57.024c-14.391-7.33-29.606-10.193-45.815-11.52-15.838-1.293-35.285-1.267-58.654-1.267v32zM938.667 723.2h32c0-23.369 0.026-42.816-1.267-58.654-1.327-16.209-4.19-31.424-11.52-45.815l-57.024 29.056c1.967 3.866 3.755 9.724 4.753 21.973 1.033 12.625 1.058 29.018 1.058 53.44h32zM892.075 595.968l-14.528 28.51c10.035 5.116 18.193 13.274 23.309 23.309l57.024-29.056c-11.251-22.076-29.201-40.026-51.277-51.277l-14.528 28.514zM221.867 586.667v-32c-23.368 0-42.818-0.026-58.653 1.267-16.209 1.327-31.426 4.19-45.815 11.52l29.055 57.024c3.864-1.967 9.721-3.755 21.972-4.753 12.624-1.033 29.017-1.058 53.441-1.058v-32zM85.333 723.2h32c0-24.422 0.025-40.815 1.056-53.44 1.001-12.25 2.788-18.108 4.756-21.973l-57.024-29.056c-7.332 14.391-10.195 29.606-11.52 45.815-1.294 15.838-1.269 35.285-1.269 58.654h32zM131.926 595.968l-14.528-28.514c-22.078 11.251-40.028 29.201-51.277 51.277l57.024 29.056c5.113-10.035 13.272-18.193 23.308-23.309l-14.528-28.51zM277.333 736v32h212.907v-64h-212.907v32zM618.671 736v-32h-0.427v64h0.427v-32zM746.671 736v-32h-0.427v64h0.427v-32zM221.867 138.667v32h580.267v-64h-580.267v32zM938.667 275.2h-32v25.6h64v-25.6h-32zM802.133 437.333v-32h-580.267v64h580.267v-32zM85.333 300.8h32v-25.6h-64v25.6h32zM221.867 437.333v-32c-24.424 0-40.817-0.025-53.441-1.056-12.251-1.001-18.108-2.788-21.972-4.756l-29.055 57.026c14.39 7.33 29.607 10.193 45.815 11.52 15.835 1.293 35.285 1.267 58.653 1.267v-32zM85.333 300.8h-32c0 23.368-0.025 42.818 1.269 58.653 1.324 16.208 4.188 31.426 11.52 45.815l57.024-29.055c-1.969-3.864-3.756-9.721-4.756-21.972-1.032-12.624-1.056-29.017-1.056-53.441h-32zM131.926 428.032l14.528-28.512c-10.035-5.113-18.194-13.272-23.308-23.308l-57.024 29.055c11.249 22.077 29.199 40.027 51.277 51.278l14.528-28.514zM938.667 300.8h-32c0 24.424-0.026 40.817-1.058 53.441-0.998 12.251-2.786 18.108-4.753 21.972l57.024 29.055c7.33-14.39 10.193-29.607 11.52-45.815 1.293-15.835 1.267-35.285 1.267-58.653h-32zM802.133 437.333v32c23.369 0 42.816 0.026 58.654-1.267 16.209-1.327 31.424-4.19 45.815-11.52l-29.056-57.026c-3.866 1.969-9.724 3.756-21.973 4.756-12.625 1.032-29.018 1.056-53.44 1.056v32zM929.365 390.74l-28.51-14.528c-5.116 10.035-13.274 18.194-23.309 23.308l29.056 57.026c22.076-11.251 40.026-29.201 51.277-51.278l-28.514-14.528zM802.133 138.667v32c24.422 0 40.815 0.025 53.44 1.056 12.25 1.001 18.108 2.788 21.973 4.756l29.056-57.024c-14.391-7.332-29.606-10.195-45.815-11.52-15.838-1.294-35.285-1.269-58.654-1.269v32zM938.667 275.2h32c0-23.368 0.026-42.818-1.267-58.653-1.327-16.208-4.19-31.426-11.52-45.815l-57.024 29.055c1.967 3.864 3.755 9.721 4.753 21.972 1.033 12.624 1.058 29.017 1.058 53.441h32zM892.075 147.968l-14.528 28.512c10.035 5.113 18.193 13.272 23.309 23.308l57.024-29.055c-11.251-22.078-29.201-40.028-51.277-51.277l-14.528 28.512zM221.867 138.667v-32c-23.368 0-42.818-0.025-58.653 1.269-16.209 1.324-31.426 4.188-45.815 11.52l29.055 57.024c3.864-1.969 9.721-3.756 21.972-4.756 12.624-1.032 29.017-1.056 53.441-1.056v-32zM85.333 275.2h32c0-24.424 0.025-40.817 1.056-53.441 1.001-12.251 2.788-18.108 4.756-21.972l-57.024-29.055c-7.332 14.39-10.195 29.607-11.52 45.815-1.294 15.835-1.269 35.285-1.269 58.653h32zM131.926 147.968l-14.528-28.512c-22.078 11.249-40.028 29.199-51.277 51.277l57.024 29.055c5.113-10.035 13.272-18.194 23.308-23.308l-14.528-28.512zM277.333 288v32h212.907v-64h-212.907v32zM618.671 288v-32h-0.427v64h0.427v-32zM746.671 288v-32h-0.427v64h0.427v-32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "driver"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1611,
        "id": 409,
        "name": "driver",
        "prevSize": 32,
        "code": 59887
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 240
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface DriverIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const DriverIcon = ({ size = 16, ...props }: DriverIconProps) => (
  <IcoMoon iconSet={iconSet} icon="driver" size={size} {...props} />
);

DriverIcon.displayName = 'DriverIcon';

export default DriverIcon;
