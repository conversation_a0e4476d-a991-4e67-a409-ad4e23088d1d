// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M248 322.371c66.274 0 120-53.063 120-118.519s-53.726-118.519-120-118.519c-66.274 0-120 53.062-120 118.519s53.726 118.519 120 118.519zM248 322.371v379.257M248 701.628c-66.274 0-120 53.065-120 118.519s53.726 118.519 120 118.519c66.274 0 120-53.065 120-118.519s-53.726-118.519-120-118.519zM248 701.628l31.121-81.963c24.589-64.759 87.273-107.665 157.304-107.665h219.575c66.274 0 120-53.065 120-118.519v-71.111M776 322.371c66.274 0 120-53.063 120-118.519s-53.726-118.519-120-118.519c-66.274 0-120 53.062-120 118.519s53.726 118.519 120 118.519z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "git-branch"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1687,
        "id": 333,
        "name": "git-branch",
        "prevSize": 32,
        "code": 59963
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 316
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface GitBranchIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const GitBranchIcon = ({ size = 16, ...props }: GitBranchIconProps) => (
  <IcoMoon iconSet={iconSet} icon="git-branch" size={size} {...props} />
);

GitBranchIcon.displayName = 'GitBranchIcon';

export default GitBranchIcon;
