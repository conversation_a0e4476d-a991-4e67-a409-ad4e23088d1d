// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M701.628 535.706c0 65.455 53.065 118.515 118.519 118.515s118.519-53.060 118.519-118.515v-23.706c0-235.642-191.027-426.667-426.667-426.667-235.642 0-426.667 191.025-426.667 426.667 0 235.639 191.025 426.667 426.667 426.667 91.23 0 175.77-28.634 245.129-77.402M701.628 512c0 104.73-84.898 189.628-189.628 189.628s-189.629-84.898-189.629-189.628c0-104.73 84.9-189.629 189.629-189.629s189.628 84.9 189.628 189.629z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "at"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1442,
        "id": 578,
        "name": "at",
        "prevSize": 32,
        "code": 59718
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 71
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface AtIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const AtIcon = ({ size = 16, ...props }: AtIconProps) => (
  <IcoMoon iconSet={iconSet} icon="at" size={size} {...props} />
);

AtIcon.displayName = 'AtIcon';

export default AtIcon;
