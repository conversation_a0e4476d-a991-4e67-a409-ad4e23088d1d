// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M483.733 85.333h-352.907c-25.125 0-45.493 20.368-45.493 45.493v352.907c0 36.198 14.379 70.912 39.974 96.508l318.452 318.451c53.299 53.299 139.712 53.299 193.011 0l261.922-261.922c53.299-53.299 53.299-139.712 0-193.011l-318.451-318.452c-25.596-25.595-60.309-39.974-96.508-39.974z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "tag"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1930,
        "id": 90,
        "name": "tag",
        "prevSize": 32,
        "code": 60206
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 559
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface TagIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const TagIcon = ({ size = 16, ...props }: TagIconProps) => (
  <IcoMoon iconSet={iconSet} icon="tag" size={size} {...props} />
);

TagIcon.displayName = 'TagIcon';

export default TagIcon;
