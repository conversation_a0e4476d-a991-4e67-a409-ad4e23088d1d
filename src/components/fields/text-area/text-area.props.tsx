import { type PropDef, colorPropDef, radiusPropDef } from '@props';

const sizes = ['1', '2', '3'] as const;
const variants = ['classic', 'surface', 'soft'] as const;
const resizeValues = ['none', 'vertical', 'horizontal', 'both'] as const;

const textAreaPropDefs = {
  size: { type: 'enum', className: 'rt-r-size', values: sizes, default: '2', responsive: true },
  variant: { type: 'enum', className: 'rt-variant', values: variants, default: 'soft' },
  resize: { type: 'enum', className: 'rt-r-resize', values: resizeValues, default: 'both', responsive: true },
  ...colorPropDef,
  ...radiusPropDef,
} satisfies {
  size: PropDef<(typeof sizes)[number]>;
  variant: PropDef<(typeof variants)[number]>;
  resize: PropDef<(typeof resizeValues)[number]>;
};

export { textAreaPropDefs };
