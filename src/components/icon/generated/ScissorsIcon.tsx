// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M524.548 512l414.118-259.81M524.548 512l-225.882-141.714M524.548 512l414.118 259.81M298.667 653.713l112.941-70.857M322.371 299.429c0 65.222-53.063 118.095-118.519 118.095s-118.519-52.873-118.519-118.095c0-65.222 53.062-118.095 118.519-118.095s118.519 52.873 118.519 118.095zM322.371 724.57c0 65.225-53.063 118.097-118.519 118.097s-118.519-52.873-118.519-118.097c0-65.22 53.062-118.093 118.519-118.093s118.519 52.873 118.519 118.093z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "scissors"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1864,
        "id": 156,
        "name": "scissors",
        "prevSize": 32,
        "code": 60140
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 493
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ScissorsIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ScissorsIcon = ({ size = 16, ...props }: ScissorsIconProps) => (
  <IcoMoon iconSet={iconSet} icon="scissors" size={size} {...props} />
);

ScissorsIcon.displayName = 'ScissorsIcon';

export default ScissorsIcon;
