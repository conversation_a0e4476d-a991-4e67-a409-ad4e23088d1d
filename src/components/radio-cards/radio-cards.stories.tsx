import type { <PERSON>a, StoryObj } from '@storybook/react-vite';

import { RadioCardsGroup } from '@components/radio-cards';

import { radioCardsRootPropDefs } from './radio-cards.props';

const meta: Meta<typeof RadioCardsGroup> = {
  title: 'DesignSystem/Radio/RadioCards',
  component: RadioCardsGroup,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    variant: {
      control: 'inline-radio',
      options: radioCardsRootPropDefs.variant.values,
    },
    size: {
      control: 'inline-radio',
      options: radioCardsRootPropDefs.size.values,
    },
    highContrast: { control: 'boolean' },
    disabled: { control: 'boolean' },
    value: { control: 'text' },
    defaultValue: { control: 'text' },
    onValueChange: { action: 'onValueChange' },
  },
};

export default meta;
type Story = StoryObj<typeof RadioCardsGroup>;

const renderLabel = (opt: any) => (
  <div>
    <div>{opt.label}</div>
    <div style={{ fontSize: 12, color: '#888' }}>{opt.caption}</div>
  </div>
);

const OPTIONS = [
  { value: 'option-1', label: renderLabel({ label: 'Option 1', caption: 'Caption 1' }) },
  { value: 'option-2', label: renderLabel({ label: 'Option 2', caption: 'Caption 2' }) },
  { value: 'option-3', label: renderLabel({ label: 'Option 3', caption: 'Caption 3' }) },
  { value: 'option-4', label: renderLabel({ label: 'Option 4', caption: 'Caption 4' }) },
];

export const _radioCards: Story = {
  args: {
    variant: radioCardsRootPropDefs.variant.default,
    size: radioCardsRootPropDefs.size.default,
    highContrast: false,
    disabled: false,
    defaultValue: 'option-1',
    columns: '1',
    orientation: 'horizontal',
    options: OPTIONS,
  },
};
