// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M210.301 813.7c-166.624-166.626-166.624-436.775 0-603.399s436.773-166.624 603.399 0M210.301 813.7c89.353 89.353 208.478 130.79 325.443 124.309M210.301 813.7l100.567-100.57M813.7 210.301c89.353 89.353 130.79 208.478 124.309 325.443M813.7 210.301l-101.146 101.145M512 512l301.7 301.7M512 512l99.409-99.409M512 512l-100.566 100.565M813.7 813.7c34.829-34.833 62.379-74.185 82.645-116.173M813.7 813.7c-34.833 34.829-74.185 62.379-116.173 82.645M896.346 697.527l-284.937-284.937M896.346 697.527c24.7-51.157 38.588-106.227 41.664-161.783M611.409 412.591l101.146-101.145M411.434 612.565l286.094 283.78M411.434 612.565l-100.566 100.565M697.527 896.346c-51.157 24.7-106.227 38.588-161.783 41.664M938.010 535.744l-225.455-224.298M535.744 938.010l-224.876-224.879"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "mask"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1756,
        "id": 264,
        "name": "mask",
        "prevSize": 32,
        "code": 60032
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 385
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MaskIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MaskIcon = ({ size = 16, ...props }: MaskIconProps) => (
  <IcoMoon iconSet={iconSet} icon="mask" size={size} {...props} />
);

MaskIcon.displayName = 'MaskIcon';

export default MaskIcon;
