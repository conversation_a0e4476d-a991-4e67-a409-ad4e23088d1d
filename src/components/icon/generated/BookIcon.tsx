// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M306.819 847.979c0 23.77-19.833 43.034-44.297 43.034h-132.891c-24.465 0-44.297-19.264-44.297-43.034v-122.645M306.819 847.979c0 23.77 19.832 43.034 44.297 43.034h132.891c24.465 0 44.297-19.264 44.297-43.034v-122.645M306.819 847.979v-122.645M306.819 159.452c0-23.767-19.833-43.033-44.297-43.033h-132.891c-24.465 0-44.297 19.267-44.297 43.033v139.215M306.819 159.452c0-23.767 19.832-43.033 44.297-43.033h132.891c24.465 0 44.297 19.267 44.297 43.033v139.215M306.819 159.452v139.215M85.333 298.667h221.485M85.333 298.667v426.667M306.819 298.667h221.484M306.819 298.667v426.667M528.303 298.667v426.667M85.333 725.333h221.485M306.819 725.333h221.484M690.82 746.667l32.388 117.427c6.332 22.959 30.622 36.582 54.251 30.43l128.363-33.412c23.633-6.153 37.658-29.751 31.326-52.706l-33.975-123.179M690.82 746.667l212.352-61.44M690.82 746.667l-121.958-442.162M903.172 685.227l-121.429-440.248M568.862 304.505l-36.292-131.577c-6.332-22.956 7.693-46.553 31.326-52.704l128.363-33.413c23.629-6.151 47.919 7.472 54.251 30.429l35.234 127.739M568.862 304.505l212.881-59.526"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "book"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1480,
        "id": 540,
        "name": "book",
        "prevSize": 32,
        "code": 59756
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 109
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BookIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BookIcon = ({ size = 16, ...props }: BookIconProps) => (
  <IcoMoon iconSet={iconSet} icon="book" size={size} {...props} />
);

BookIcon.displayName = 'BookIcon';

export default BookIcon;
