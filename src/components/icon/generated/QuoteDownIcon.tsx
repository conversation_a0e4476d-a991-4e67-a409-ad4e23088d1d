// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 402.645v-19.915c0-93.555 72.674-169.397 162.322-169.397h37.653c82.807 0 151.048 67.803 155.114 154.116 4.16 88.414-19.537 175.817-67.471 248.845l-124.791 190.127c-5.612 8.55-18.434 3.162-16.802-7.061l32.535-203.721c3.638-22.775-13.194-43.511-35.322-43.511-79.109 0-143.239-66.927-143.239-149.483z",
          "M583.113 402.645v-19.915c0-93.555 72.674-169.397 162.321-169.397h37.653c82.807 0 151.049 67.803 155.11 154.116 4.164 88.414-19.533 175.817-67.469 248.845l-124.787 190.127c-5.615 8.55-18.436 3.162-16.802-7.061l32.533-203.721c3.639-22.775-13.193-43.511-35.324-43.511-79.108 0-143.236-66.927-143.236-149.483z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "quote-down"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1840,
        "id": 180,
        "name": "quote-down",
        "prevSize": 32,
        "code": 60116
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 469
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface QuoteDownIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const QuoteDownIcon = ({ size = 16, ...props }: QuoteDownIconProps) => (
  <IcoMoon iconSet={iconSet} icon="quote-down" size={size} {...props} />
);

QuoteDownIcon.displayName = 'QuoteDownIcon';

export default QuoteDownIcon;
