// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M596.655 409.725v0zM570.462 401.671v0zM391.456 429.7v0zM368.607 446.963v0zM483.669 266.134c8.213 15.648 27.558 21.673 43.209 13.458 15.646-8.215 21.67-27.56 13.457-43.208l-56.666 29.75zM506.492 240.762v0zM511.147 169.516v0zM512.853 167.077v0zM517.513 95.83v0zM540.335 70.458c-8.218-15.648-27.558-21.673-43.209-13.458-15.646 8.215-21.675 27.56-13.457 43.208l56.666-29.75zM334.335 289.838c8.215 15.648 27.56 21.673 43.208 13.458s21.673-27.56 13.458-43.207l-56.666 29.749zM357.157 264.466v0zM361.815 193.219v0zM363.521 190.781v0zM396.512 104.659c-8.215-15.648-27.56-21.673-43.207-13.458s-21.673 27.56-13.458 43.208l56.666-29.749zM633.003 289.838c8.213 15.648 27.558 21.673 43.209 13.458 15.646-8.215 21.67-27.56 13.457-43.207l-56.666 29.749zM655.825 264.466v0zM660.48 193.219v0zM662.187 190.781v0zM666.846 119.534v0zM689.668 94.162c-8.218-15.648-27.558-21.673-43.209-13.458-15.646 8.215-21.675 27.56-13.457 43.207l56.666-29.749zM320 521.481v32h384v-64h-384v32zM533.333 938.667v-32h-42.667v64h42.667v-32zM490.667 938.667v-32c-139.141 0-245.333-99.482-245.333-214.519h-64c0 157.261 142.141 278.519 309.333 278.519v-32zM810.667 692.147h-32c0 115.038-106.193 214.519-245.333 214.519v64c167.194 0 309.333-121.259 309.333-278.519h-32zM704 521.481v32c44.885 0 74.667 31.565 74.667 62.814h64c0-73.476-65.732-126.814-138.667-126.814v32zM320 521.481v-32c-72.936 0-138.667 53.338-138.667 126.814h64c0-31.249 29.782-62.814 74.667-62.814v-32zM618.667 407.704v32.002c44.885 0 74.667 31.561 74.667 62.814h64c0-73.476-65.732-126.816-138.667-126.816v32zM391.456 429.7l29.231 13.022c10-22.45 36.308-40.944 69.981-40.944v-64c-56.427 0-107.17 31.151-128.441 78.898l29.23 13.024zM596.655 409.725l5.867 31.457c5.171-0.964 10.569-1.476 16.145-1.476v-64.002c-9.515 0-18.842 0.879-27.878 2.563l5.867 31.458zM490.667 369.778v32c23.501 0 43.789 9.126 57.152 22.506l45.286-45.226c-25.719-25.749-62.447-41.28-102.438-41.28v32zM725.333 502.519h-32c0 4.070-0.461 8.021-1.331 11.836l62.396 14.251c1.924-8.439 2.935-17.169 2.935-26.086h-32zM298.667 521.481h32c0-18.901 16.607-38.383 43.357-42.978l-10.833-63.077c-48.762 8.375-96.524 47.917-96.524 106.055h32zM596.655 409.725l-5.867-31.458c0.55-0.102 0.926-0.038 1.161 0.035 0.226 0.070 0.649 0.249 1.156 0.756l-45.286 45.226c14.178 14.197 34.85 20.601 54.703 16.898l-5.867-31.457zM391.456 429.7l-29.23-13.024c0.373-0.838 0.774-1.152 0.825-1.19s0.067-0.047 0.14-0.060l10.833 63.077c19.854-3.409 38.027-16.397 46.662-35.78l-29.231-13.022zM213.333 692.147h32v-37.926h-64v37.926h32zM213.333 654.221h32v-37.926h-64v37.926h32zM810.667 616.294h-32v37.926h64v-37.926h-32zM810.667 654.221h-32v37.926h64v-37.926h-32zM213.333 654.221v32h597.333v-64h-597.333v32zM512 251.259l28.335-14.875-5.513-10.496-56.666 29.749 5.513 10.497 28.331-14.875zM511.147 169.516l26.219 18.351 1.707-2.438-52.433-36.701-1.707 2.438 26.214 18.351zM517.513 95.83l28.331-14.875-5.508-10.497-56.666 29.75 5.508 10.496 28.335-14.874zM512.853 167.077l26.219 18.351c21.679-30.973 24.358-70.976 6.771-104.473l-56.666 29.749c6.383 12.153 5.508 26.529-2.539 38.022l26.214 18.351zM506.492 240.762l28.331-14.874c-6.379-12.153-5.504-26.529 2.543-38.022l-52.433-36.701c-21.679 30.973-24.358 70.976-6.775 104.473l28.335-14.875zM362.668 274.963l28.333-14.874-5.511-10.497-56.666 29.749 5.511 10.497 28.333-14.875zM361.815 193.219l26.216 18.351 1.707-2.438-52.431-36.701-1.707 2.438 26.215 18.351zM363.521 190.781l26.216 18.351c21.681-30.973 24.36-70.976 6.775-104.473l-56.666 29.749c6.38 12.153 5.505 26.529-2.54 38.022l26.215 18.351zM357.157 264.466l28.333-14.875c-6.38-12.153-5.505-26.529 2.54-38.022l-52.431-36.702c-21.681 30.973-24.361 70.976-6.775 104.473l28.333-14.874zM661.333 274.963l28.335-14.874-5.513-10.497-56.666 29.749 5.513 10.497 28.331-14.875zM660.48 193.219l26.219 18.351 1.707-2.438-52.433-36.701-1.707 2.438 26.214 18.351zM666.846 119.534l28.331-14.874-5.508-10.497-56.666 29.749 5.508 10.497 28.335-14.875zM662.187 190.781l26.219 18.351c21.679-30.973 24.358-70.976 6.771-104.473l-56.666 29.749c6.383 12.153 5.508 26.529-2.539 38.022l26.214 18.351zM655.825 264.466l28.331-14.875c-6.379-12.153-5.504-26.529 2.543-38.022l-52.433-36.702c-21.679 30.973-24.358 70.976-6.775 104.473l28.335-14.874z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "breakfast"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1490,
        "id": 530,
        "name": "breakfast",
        "prevSize": 32,
        "code": 59766
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 119
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BreakfastIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BreakfastIcon = ({ size = 16, ...props }: BreakfastIconProps) => (
  <IcoMoon iconSet={iconSet} icon="breakfast" size={size} {...props} />
);

BreakfastIcon.displayName = 'BreakfastIcon';

export default BreakfastIcon;
