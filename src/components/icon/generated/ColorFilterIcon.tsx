// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M295.358 442.001c-118.192 13.487-210.025 114.406-210.025 236.902 0 131.682 106.125 238.43 237.037 238.43 77.539 0 146.383-37.449 189.629-95.347M295.358 442.001c36.921 83.392 120.032 141.53 216.642 141.53 9.135 0 18.146-0.521 27.012-1.532M295.358 442.001c-13.109-29.608-20.395-62.4-20.395-96.903 0-131.682 106.125-238.432 237.037-238.432 130.91 0 237.039 106.749 237.039 238.432 0 34.503-7.287 67.295-20.399 96.903M539.012 581.999c13.107 29.606 20.395 62.4 20.395 96.905 0 53.683-17.638 103.228-47.407 143.083M539.012 581.999c84.902-9.69 156.198-64.491 189.628-139.998M728.64 442.001c118.195 13.487 210.027 114.406 210.027 236.902 0 131.682-106.125 238.43-237.039 238.43-77.538 0-146.381-37.449-189.628-95.347"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "color-filter"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1580,
        "id": 440,
        "name": "color-filter",
        "prevSize": 32,
        "code": 59856
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 209
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ColorFilterIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ColorFilterIcon = ({ size = 16, ...props }: ColorFilterIconProps) => (
  <IcoMoon iconSet={iconSet} icon="color-filter" size={size} {...props} />
);

ColorFilterIcon.displayName = 'ColorFilterIcon';

export default ColorFilterIcon;
