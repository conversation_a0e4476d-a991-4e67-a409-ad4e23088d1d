import { type PropDef, accentColorPropDef, asChildPropDef } from '@props';

const sizes = ['1', '2', '3', '4'] as const;
const variants = ['solid', 'soft', 'surface', 'text', 'ghost'] as const;
const statuses = ['info', 'success', 'warning', 'error'];

const badgePropDefs = {
  ...asChildPropDef,
  size: { type: 'enum', className: 'rt-r-size', values: sizes, default: '2', responsive: true },
  variant: { type: 'enum', className: 'rt-variant', values: variants, default: 'solid' },
  status: { type: 'enum', className: 'rt-status', values: statuses, default: 'info' },
  ...accentColorPropDef,
} satisfies {
  size: PropDef<(typeof sizes)[number]>;
  variant: PropDef<(typeof variants)[number]>;
  status: PropDef<(typeof statuses)[number]>;
};

export { badgePropDefs };
