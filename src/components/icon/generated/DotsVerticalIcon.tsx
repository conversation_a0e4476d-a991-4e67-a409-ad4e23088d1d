// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 234.667v0.427M512 512v0.427M512 788.907v0.427"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 85.33333333333333
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "dots-vertical"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 85.33333333333333
        }
      ],
      "properties": {
        "order": 1607,
        "id": 413,
        "name": "dots-vertical",
        "prevSize": 32,
        "code": 59883
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 236
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface DotsVerticalIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const DotsVerticalIcon = ({ size = 16, ...props }: DotsVerticalIconProps) => (
  <IcoMoon iconSet={iconSet} icon="dots-vertical" size={size} {...props} />
);

DotsVerticalIcon.displayName = 'DotsVerticalIcon';

export default DotsVerticalIcon;
