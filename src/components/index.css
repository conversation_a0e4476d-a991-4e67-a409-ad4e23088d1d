@import './reset.css';
@import './animations.css';
@import './layout.css';

/* base components css */
@import '../base-components/base-button.css';
@import '../base-components/base-card.css';
@import '../base-components/base-checkbox.css';
@import '../base-components/base-radio.css';
@import '../base-components/base-dialog.css';
@import '../base-components/base-menu.css';
/* Import Skeleton before other components so that its default border-radius doesn’t have a higher specificity */
@import './skeleton/skeleton.css';

/* Import Text before other components as it’s commonly extended */
@import './text/text.css';
@import './heading/heading.css';

@import './accordion/accordion.css';
@import './avatar/avatar.css';
@import './badge/badge.css';
@import './button/button.css';
@import './callout/callout.css';
@import './card/card.css';
@import './checkbox/checkbox.css';
@import './checkbox-cards/checkbox-cards.css';
@import './checkbox-group/checkbox-group.css';
@import './collapsible/collapsible.css';
@import './context-menu/context-menu.css';
@import './divider/divider.css';
@import './dots/dots.css';
@import './dropdown-menu/dropdown-menu.css';
@import './fields/index.css';
@import './icon-button/icon-button.css';
@import './link/link.css';
@import './radio/radio.css';
@import './radio-cards/radio-cards.css';
@import './radio-group/radio-group.css';
@import './scroll-area/scroll-area.css';
@import './segmented-control/segmented-control.css';
@import './sidebar/sidebar.css';
@import './spinner/spinner.css';
@import './strength-meter/strength-meter.css';
@import './switch/switch.css';
@import './tabs/tabs.css';
@import './tag/tag.css';
@import './tooltip/tooltip.css';
@import './toast/toast.css';

.herond-themes:where([data-is-root-theme='true']) {
  /* Create a new stacking context on the root `Theme` so layered components work out of the box */
  position: relative;
  z-index: 0;

  /* Make sure root `Theme` background covers the viewport */
  min-height: 100vh;
  width: 100%;

  @supports (min-height: 100dvh) {
    min-height: 100dvh;
  }
}
