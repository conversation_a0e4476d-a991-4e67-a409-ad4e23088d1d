// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M248.625 248.625v408.232c0 65.455 53.062 118.515 118.519 118.515h408.229M248.625 248.625h-163.292M248.625 248.625v-163.292M775.373 775.373h163.294M775.373 775.373v163.294M381.629 248.625h275.228c65.455 0 118.515 53.062 118.515 118.519v276.542"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "crop"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1590,
        "id": 430,
        "name": "crop",
        "prevSize": 32,
        "code": 59866
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 219
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CropIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CropIcon = ({ size = 16, ...props }: CropIconProps) => (
  <IcoMoon iconSet={iconSet} icon="crop" size={size} {...props} />
);

CropIcon.displayName = 'CropIcon';

export default CropIcon;
