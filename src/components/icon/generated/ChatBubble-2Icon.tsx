// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M484.224 899.78v0zM461.662 868.194v0zM308.634 782.886v0zM276.51 780.745v0zM747.49 780.745v0zM715.366 782.886v0zM562.338 868.194v0zM539.776 899.78v0zM645.926 792v0zM606.601 813.922v0zM417.399 813.922v0zM378.073 792v0zM504.081 921.775v0zM519.919 921.775v0zM98.147 704.073v0zM149.973 759.467v0zM925.854 704.073v0zM874.027 759.467v0zM868.779 99.284v0zM924.715 155.223v0zM155.223 99.284v0zM99.284 155.223v0zM341.333 330.667c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM682.667 394.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM341.333 480c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM597.333 544c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM290.133 85.333v32h443.733v-64h-443.733v32zM938.667 290.133h-32v286.263h64v-286.263h-32zM85.333 576.397h32v-286.263h-64v286.263h32zM484.224 899.78l26.039-18.598-22.562-31.586-52.079 37.201 22.562 31.582 26.039-18.598zM308.634 782.886l2.129-31.927-32.124-2.142-2.129 31.927-2.129 31.932 32.124 2.138 2.129-31.927zM747.49 780.745l-2.129-31.927-32.124 2.142 4.258 63.855 32.124-2.138-2.129-31.932zM562.338 868.194l-26.039-18.598-22.562 31.586 52.079 37.197 22.562-31.582-26.039-18.603zM715.366 782.886l-2.129-31.927c-33.783 2.253-56.61 3.511-77.734 10.786l20.847 60.51c11.162-3.844 23.868-4.954 61.146-7.441l-2.129-31.927zM562.338 868.194l26.039 18.603c21.713-30.404 29.338-40.627 38.477-48.098l-40.508-49.549c-17.297 14.14-30.37 32.892-50.048 60.446l26.039 18.598zM645.926 792l-10.423-30.255c-17.873 6.157-34.517 15.437-49.156 27.405l40.508 49.549c8.785-7.181 18.769-12.749 29.495-16.444l-10.423-30.255zM461.662 868.194l26.039-18.598c-19.678-27.554-32.751-46.306-50.048-60.446l-40.509 49.549c9.139 7.471 16.764 17.694 38.478 48.098l26.039-18.603zM308.634 782.886l-2.129 31.927c37.278 2.487 49.984 3.597 61.144 7.441l20.846-60.51c-21.122-7.275-43.948-8.533-77.733-10.786l-2.129 31.927zM417.399 813.922l20.254-24.772c-14.638-11.968-31.281-21.248-49.158-27.405l-20.846 60.51c10.726 3.695 20.712 9.263 29.495 16.444l20.254-24.777zM484.224 899.78l-26.039 18.598c4.335 6.067 8.811 12.386 13.039 17.306 4.326 5.035 11.008 11.814 20.983 15.804l23.753-59.43c4.169 1.668 5.602 4.011 3.797 1.907-0.798-0.926-1.916-2.342-3.575-4.582-1.651-2.231-3.529-4.855-5.918-8.201l-26.039 18.598zM539.776 899.78l-26.039-18.598c-2.389 3.345-4.267 5.969-5.918 8.201-1.66 2.24-2.778 3.657-3.575 4.582-1.805 2.103-0.371-0.239 3.797-1.907l23.753 59.43c9.975-3.989 16.657-10.769 20.983-15.804 4.228-4.919 8.704-11.238 13.039-17.306l-26.039-18.598zM504.081 921.775l-11.874 29.713c12.706 5.077 26.88 5.077 39.586 0l-23.753-59.43c2.543-1.015 5.376-1.015 7.919 0l-11.878 29.717zM85.333 576.397h-32c0 33.237-0.023 60.028 1.702 81.762 1.757 22.127 5.466 41.609 14.315 59.866l57.593-27.908c-3.965-8.183-6.662-18.79-8.109-37.022-1.478-18.615-1.501-42.441-1.501-76.698h-32zM276.51 780.745l2.129-31.927c-34.177-2.278-57.949-3.887-76.427-6.601-18.092-2.658-28.499-6.054-36.399-10.556l-31.68 55.612c17.629 10.044 36.822 15.040 58.782 18.266 21.574 3.166 48.307 4.924 81.467 7.138l2.129-31.932zM98.147 704.073l-28.797 13.952c14.101 29.099 36.687 53.244 64.783 69.248l31.68-55.612c-16.857-9.6-30.409-24.085-38.87-41.545l-28.797 13.956zM938.667 576.397h-32c0 34.257-0.021 58.082-1.502 76.698-1.446 18.231-4.143 28.838-8.107 37.022l57.591 27.908c8.849-18.257 12.557-37.739 14.315-59.866 1.724-21.734 1.702-48.525 1.702-81.762h-32zM747.49 780.745l2.129 31.932c33.161-2.214 59.891-3.972 81.468-7.138 21.956-3.226 41.152-8.222 58.782-18.266l-31.68-55.612c-7.902 4.501-18.308 7.898-36.399 10.556-18.479 2.714-42.253 4.322-76.429 6.601l2.129 31.927zM925.854 704.073l-28.796-13.956c-8.461 17.459-22.016 31.945-38.869 41.545l31.68 55.612c28.092-16.004 50.679-40.149 64.781-69.248l-28.796-13.952zM733.867 85.333v32c36.373 0 61.726 0.025 81.463 1.638 19.366 1.582 30.494 4.532 38.921 8.826l29.056-57.024c-18.957-9.658-39.441-13.683-62.763-15.589-22.95-1.875-51.362-1.85-86.677-1.85v32zM938.667 290.133h32c0-35.315 0.026-63.727-1.852-86.676-1.903-23.323-5.931-43.809-15.586-62.762l-57.024 29.055c4.292 8.428 7.241 19.553 8.823 38.919 1.613 19.739 1.638 45.093 1.638 81.464h32zM868.779 99.284l-14.528 28.512c18.061 9.204 32.747 23.89 41.954 41.953l57.024-29.055c-15.343-30.106-39.817-54.583-69.922-69.923l-14.528 28.512zM290.133 85.333v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM85.333 290.133h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM155.223 99.284l-14.528-28.512c-30.106 15.34-54.583 39.817-69.923 69.923l57.024 29.055c9.204-18.063 23.89-32.75 41.953-41.953l-14.528-28.512zM341.333 362.667v32h341.333v-64h-341.333v32zM341.333 512v32h256v-64h-256v32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "chat-bubble-2"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1537,
        "id": 483,
        "name": "chat-bubble-2",
        "prevSize": 32,
        "code": 59813
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 166
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ChatBubble-2IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ChatBubble-2Icon = ({ size = 16, ...props }: ChatBubble-2IconProps) => (
  <IcoMoon iconSet={iconSet} icon="chat-bubble-2" size={size} {...props} />
);

ChatBubble-2Icon.displayName = 'ChatBubble-2Icon';

export default ChatBubble-2Icon;
