// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 213.333h426.667M512 412.445h426.667M85.333 611.554h853.333M85.333 810.667h853.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "text-align-justify-right"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1942,
        "id": 78,
        "name": "text-align-justify-right",
        "prevSize": 32,
        "code": 60218
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 571
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface TextAlignJustifyRightIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const TextAlignJustifyRightIcon = ({ size = 16, ...props }: TextAlignJustifyRightIconProps) => (
  <IcoMoon iconSet={iconSet} icon="text-align-justify-right" size={size} {...props} />
);

TextAlignJustifyRightIcon.displayName = 'TextAlignJustifyRightIcon';

export default TextAlignJustifyRightIcon;
