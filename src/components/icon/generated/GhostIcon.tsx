// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M402.502 405.376h0.548M621.5 405.376h0.546M128.756 458.598c0-98.807 40.378-193.565 112.25-263.43s169.352-109.116 270.994-109.116c101.641 0 199.121 39.25 270.993 109.116s112.252 164.624 112.252 263.43v372.544c2.59 20.083-1.51 40.448-11.695 58.103-10.18 17.655-25.916 31.676-44.894 40.009s-40.201 10.534-60.561 6.289c-20.361-4.25-38.784-14.724-52.574-29.892-8.448-10.509-19.251-19.008-31.582-24.853s-25.873-8.883-39.59-8.883c-13.722 0-27.26 3.038-39.595 8.883-12.331 5.845-23.13 14.345-31.582 24.853-8.448 10.505-19.247 19.004-31.582 24.849-12.331 5.845-25.869 8.883-39.59 8.883s-27.26-3.038-39.59-8.883c-12.335-5.845-23.134-14.345-31.582-24.849-8.452-10.509-19.251-19.008-31.584-24.853s-25.872-8.883-39.592-8.883c-13.72 0-27.26 3.038-39.592 8.883s-23.133 14.345-31.582 24.853c-13.789 15.168-32.214 25.643-52.574 29.892-20.36 4.245-41.584 2.044-60.561-6.289s-34.711-22.353-44.895-40.009c-10.184-17.655-14.281-38.020-11.692-58.103v-372.544z",
          "M384 576c21.333 64 82.231 64.717 128 64.717s106.667-0.717 128-64.717"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "round",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "round",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "ghost"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "round",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "round",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1685,
        "id": 335,
        "name": "ghost",
        "prevSize": 32,
        "code": 59961
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 314
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface GhostIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const GhostIcon = ({ size = 16, ...props }: GhostIconProps) => (
  <IcoMoon iconSet={iconSet} icon="ghost" size={size} {...props} />
);

GhostIcon.displayName = 'GhostIcon';

export default GhostIcon;
