// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M370.902 410.551c5.235-16.88-4.204-34.807-21.084-40.043s-34.808 4.204-40.044 21.084l61.128 18.959zM131.185 803.281v0zM310.438 850.172v0zM400.495 522.048v0zM384.092 494.31v0zM221.242 475.157v0zM249.652 459.145v0zM713.562 850.172v0zM892.817 803.281v0zM751.94 465.003v0zM808.759 497.033v0zM662.315 488.448v0zM629.508 543.923v0zM400.495 522.048l-30.859-8.469-90.057 328.124 61.718 16.939 90.059-328.124-30.861-8.469zM131.185 803.281l30.859 8.469 90.058-328.124-61.718-16.939-90.057 328.124 30.859 8.469zM249.652 459.145l-8.098 30.959 67.22 17.583 16.197-61.918-67.22-17.583-8.099 30.959zM316.873 476.727l-8.099 30.959 67.22 17.583 16.197-61.918-67.22-17.583-8.099 30.959zM316.873 476.727l30.563 9.481 23.466-75.657-61.128-18.959-23.465 75.655 30.564 9.481zM196.796 914.223l8.099-30.959c-32.878-8.597-51.229-40.99-42.851-71.514l-61.717-16.939c-18.149 66.125 22.25 133.073 88.372 150.37l8.098-30.959zM310.438 850.172l-30.859-8.469c-8.463 30.835-41.539 50.236-74.685 41.562l-16.197 61.918c65.853 17.229 134.535-20.727 152.599-86.541l-30.859-8.469zM400.495 522.048l30.861 8.469c8.201-29.884-10.168-59.58-39.165-67.166l-16.197 61.918c-4.247-1.109-7.927-5.973-6.358-11.691l30.859 8.469zM221.242 475.157l30.859 8.469c-1.484 5.41-6.568 7.514-10.547 6.477l16.197-61.918c-28.728-7.516-59.252 8.93-67.368 38.502l30.859 8.469zM662.315 488.448l8.098 30.959 89.626-23.445-16.196-61.914-89.626 23.445 8.098 30.955zM808.759 497.033l-30.857 8.469 84.053 306.249 61.717-16.939-84.053-306.249-30.861 8.469zM713.562 850.172l30.861-8.469-84.053-306.249-61.717 16.939 84.053 306.249 30.857-8.469zM827.204 914.223l-8.098-30.959c-33.148 8.674-66.223-10.726-74.684-41.562l-61.717 16.939c18.061 65.813 86.746 103.77 152.597 86.541l-8.098-30.959zM892.817 803.281l-30.861 8.469c8.38 30.524-9.971 62.916-42.85 71.514l16.196 61.918c66.121-17.297 106.522-84.245 88.371-150.37l-30.857 8.469zM751.94 465.003l8.098 30.959c8.397-2.193 16.034 2.867 17.865 9.54l61.717-16.939c-11.43-41.651-54.673-65.269-95.778-54.515l8.098 30.955zM662.315 488.448l-8.098-30.955c-41.374 10.82-67.085 52.937-55.565 94.899l61.717-16.939c-1.749-6.362 1.916-13.918 10.044-16.047l-8.098-30.959zM604.787 219.899h-32c0 44.143-36.873 81.232-83.985 81.232v64c81.003 0 147.985-64.303 147.985-145.232h-32zM488.802 333.132v-32c-47.113 0-83.985-37.089-83.985-81.232h-64c0 80.93 66.984 145.232 147.985 145.232v-32zM372.817 219.899h32c0-44.143 36.873-81.233 83.985-81.233v-64c-81.001 0-147.985 64.303-147.985 145.233h32zM488.802 106.667v32c47.113 0 83.985 37.089 83.985 81.233h64c0-80.93-66.982-145.233-147.985-145.233v32zM604.787 219.899l-29.726 11.846 102.34 256.827 59.452-23.689-102.34-256.829-29.726 11.846z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "hand-grip"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1696,
        "id": 324,
        "name": "hand-grip",
        "prevSize": 32,
        "code": 59972
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 325
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface HandGripIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const HandGripIcon = ({ size = 16, ...props }: HandGripIconProps) => (
  <IcoMoon iconSet={iconSet} icon="hand-grip" size={size} {...props} />
);

HandGripIcon.displayName = 'HandGripIcon';

export default HandGripIcon;
