// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M312.973 91.875c-32.555 8.723-62.24 25.862-86.071 49.693s-40.97 53.516-49.693 86.071M711.027 91.875c32.555 8.723 62.238 25.862 86.071 49.693 23.829 23.831 40.969 53.516 49.694 86.071M389.433 874.667c26.986 38.686 71.819 64 122.567 64s95.582-25.314 122.569-64M731.443 334.214l65.749 263.119c5.35 13.717 13.879 25.975 24.887 35.759l5.888 5.235c16.132 14.34 25.365 34.897 25.365 56.482 0 41.737-33.835 75.575-75.575 75.575h-531.517c-41.738 0-75.574-33.839-75.574-75.575 0-21.585 9.231-42.142 25.365-56.482l5.89-5.235c11.006-9.783 19.535-22.042 24.887-35.759l65.748-263.119c28.941-97.039 118.18-163.548 219.443-163.548 101.261 0 190.502 66.509 219.443 163.548z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bell-ringing"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1470,
        "id": 550,
        "name": "bell-ringing",
        "prevSize": 32,
        "code": 59746
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 99
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BellRingingIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BellRingingIcon = ({ size = 16, ...props }: BellRingingIconProps) => (
  <IcoMoon iconSet={iconSet} icon="bell-ringing" size={size} {...props} />
);

BellRingingIcon.displayName = 'BellRingingIcon';

export default BellRingingIcon;
