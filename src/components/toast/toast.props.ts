import { type PropDef } from '@props';

const sweepDirections = ['right', 'left', 'up', 'down'] as const;
const positions = ['top', 'center', 'bottom'] as const;
const aligns = ['start', 'center', 'end'] as const;
const variants = ['default', 'success', 'error', 'warning'] as const;
const toastPropsDefs = {
  direction: { type: 'enum', className: 'rt-sweep-direction', values: sweepDirections, default: 'right' },
  align: { type: 'enum', className: 'rt-align', values: aligns, default: 'end' },
  variant: { type: 'enum', className: 'rt-variant', values: variants, default: 'default' },
  position: { type: 'enum', className: 'rt-position', values: positions, default: 'top' },
} satisfies {
  direction: PropDef<(typeof sweepDirections)[number]>;
  align: PropDef<(typeof aligns)[number]>;
  position?: PropDef<(typeof positions)[number]>;
  variant?: PropDef<(typeof variants)[number]>;
};

export { toastPropsDefs };
