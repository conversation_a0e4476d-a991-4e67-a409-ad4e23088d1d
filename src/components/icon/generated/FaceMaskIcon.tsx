// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M819.132 389.217c-5.461-30.835-32.23-54.938-64.934-61.325-73.485-14.355-130.876-42.301-209.933-66.98-20.979-6.549-43.55-6.549-64.529 0-79.057 24.679-136.447 52.625-209.935 66.98-32.701 6.387-59.473 30.49-64.931 61.325M819.132 389.217c0.666 3.781 1.015 7.664 1.015 11.627v167.527c0 13.367-2.394 26.368-6.865 38.528M819.132 389.217l62.908-12.086c29.312-5.632 56.627 16.1 56.627 45.055 0 47.92-2.445 103.788-44.271 136.359l-81.114 48.354M813.282 606.899c-10.517 28.599-32.538 52.565-61.986 65.732l-189.628 84.8c-31.514 14.093-67.823 14.093-99.337 0l-189.627-84.8c-29.449-13.167-51.467-37.133-61.986-65.732M204.87 389.217c-0.669 3.781-1.018 7.664-1.018 11.627v167.527c0 13.367 2.393 26.368 6.867 38.528M204.87 389.217l-62.908-12.086c-29.314-5.632-56.628 16.1-56.628 45.055 0 47.92 2.443 103.788 44.272 136.359l81.113 48.354M346.074 442.18c129.425-16.824 203.119-16.803 331.852 0M346.074 558.545c130.683 28.727 204.381 28.843 331.852 0"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "face-mask"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1627,
        "id": 393,
        "name": "face-mask",
        "prevSize": 32,
        "code": 59903
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 256
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FaceMaskIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FaceMaskIcon = ({ size = 16, ...props }: FaceMaskIconProps) => (
  <IcoMoon iconSet={iconSet} icon="face-mask" size={size} {...props} />
);

FaceMaskIcon.displayName = 'FaceMaskIcon';

export default FaceMaskIcon;
