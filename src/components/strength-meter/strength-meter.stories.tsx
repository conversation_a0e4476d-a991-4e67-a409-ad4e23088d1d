import type { Meta, StoryObj } from '@storybook/react-vite';

import { StrengthMeter } from '@components/strength-meter';
import { strengthMeterPropDefs } from '@components/strength-meter/strength-meter.props';

const meta: Meta<typeof StrengthMeter> = {
  title: 'DesignSystem/StrengthMeter',
  component: StrengthMeter,
  argTypes: {
    level: {
      control: 'inline-radio',
      options: strengthMeterPropDefs.level.values,
    },
    segments: {
      control: { type: 'number', min: 1, max: 10 },
      description: 'Total number of segments (default: 3)',
    },
    thresholds: {
      control: 'object',
      description: 'Custom thresholds for weak/medium/strong (e.g. { weak: 2, medium: 4 })',
    },
  },
};

export default meta;

type Story = StoryObj<typeof StrengthMeter>;

export const _strengthMeter: Story = {
  args: {
    level: 'weak',
    segments: 6,
    thresholds: {
      weak: 2,
      medium: 4,
    },
  },
};
