// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M218.262 312.674l27.476 16.403 0.306-0.512 0.287-0.524-28.070-15.366zM805.743 312.674l-28.066 15.366 0.341 0.629 0.371 0.613 27.354-16.608zM704.738 128.168v0zM806.985 314.717l27.558-16.266-0.205-0.342-27.354 16.608zM216.304 315.954l-27.476-16.403-0.163 0.273-0.158 0.277 27.797 15.852zM319.269 128.168v0zM328.594 243.817l-24.611 20.452 0.274 0.329 0.282 0.322 24.055-21.103zM695.411 243.817l24.081 21.075 0.269-0.308 0.265-0.314-24.614-20.452zM222.060 846.225v0zM303.774 926.353v0zM796.352 434.022l23.561 21.658 0.32-0.358-23.881-21.299zM794.547 435.985l-23.561-21.652-0.235 0.254-0.23 0.259 24.026 21.139zM229.461 435.985l24.055-21.102-0.403-0.459-0.42-0.443-23.232 22.004zM718.848 926.724v0zM802.022 846.234v0zM378.278 605.619v0zM219.878 785.591v0zM657.745 924.173v0zM365.034 924.049v0zM645.632 605.197l-24.051 21.103 0.043 0.051 24.009-21.154zM512.226 453.129v0zM804.395 785.382v0zM388.419 85.333v32h247.17v-64h-247.17v32zM317.717 131.002l-28.070-15.366-99.455 181.673 56.139 30.732 99.455-181.673-28.069-15.366zM706.291 131.002l-28.070 15.366 99.456 181.673 56.137-30.732-99.456-181.673-28.066 15.366zM635.588 85.333v32c15.68 0 31.398 9.213 41.344 26.675l55.612-31.68c-19.43-34.103-54.677-58.995-96.956-58.995v32zM704.738 128.168l-27.806 15.84c0.354 0.623 0.704 1.257 1.041 1.899l56.631-29.809c-0.666-1.269-1.353-2.526-2.061-3.769l-27.806 15.839zM706.291 131.002l-28.318 14.904c4.988 9.48 8.004 20.865 8.004 33.34h64c0-22.804-5.517-44.427-15.373-63.148l-28.314 14.904zM706.291 131.002l28.062-15.373-1.549-2.833-56.132 30.746 1.553 2.834 28.066-15.373zM806.985 314.717l-27.558 16.265c5.897 9.989 9.54 22.377 9.54 36.089h64c0-25.056-6.665-48.691-18.423-68.618l-27.558 16.265zM805.743 312.674l-27.354 16.608 1.242 2.042 54.707-33.216-1.242-2.042-27.354 16.608zM203.042 367.071h32c0-13.334 3.445-25.42 9.059-35.264l-55.595-31.705c-11.164 19.575-17.465 42.599-17.465 66.969h32zM218.262 312.674l-27.476-16.403-1.958 3.28 54.953 32.806 1.958-3.28-27.476-16.403zM306.029 179.246h32c0-12.474 3.016-23.86 8.006-33.34l-56.635-29.807c-9.853 18.72-15.371 40.343-15.371 63.148h32zM317.717 131.002l28.317 14.903c0.337-0.641 0.684-1.274 1.039-1.897l-55.61-31.68c-0.708 1.243-1.396 2.5-2.065 3.77l28.317 14.904zM319.269 128.168l27.805 15.84c9.947-17.461 25.666-26.675 41.345-26.675v-64c-42.278 0-77.527 24.892-96.954 58.995l27.805 15.84zM317.717 131.002l28.065 15.373 1.552-2.833-56.131-30.746-1.552 2.833 28.066 15.373zM328.594 243.817l24.611-20.452c-9.096-10.945-15.176-26.394-15.176-44.119h-64c0 32.3 11.077 62.308 29.954 85.024l24.611-20.452zM717.978 179.246h-32c0 17.725-6.080 33.174-15.177 44.119l49.225 40.905c18.876-22.716 29.952-52.724 29.952-85.024h-32zM222.060 846.225l-22.404 22.852 81.714 80.124 44.809-45.696-81.714-80.128-22.404 22.848zM820.966 367.071h-32c0 18.586-6.686 34.648-16.499 45.653l47.765 42.598c20.574-23.070 32.734-54.413 32.734-88.251h-32zM794.547 435.985l23.561 21.653 1.805-1.963-47.121-43.308-1.805 1.964 23.561 21.652zM229.461 435.985l23.232-22.004c-10.432-11.014-17.651-27.587-17.651-46.91h-64c0 35.148 13.123 67.63 35.187 90.922l23.232-22.007zM718.848 926.724l22.251 22.997 83.179-80.491-44.506-45.995-83.179 80.491 22.255 22.997zM229.461 435.985l-24.055 21.103 148.817 169.634 48.11-42.21-148.818-169.629-24.055 21.102zM378.278 605.619l-24.021-21.146-158.401 179.977 48.042 42.283 158.401-179.972-24.021-21.141zM378.278 605.619l-24.055 21.103 133.286 151.923 48.111-42.206-133.287-151.927-24.055 21.107zM511.565 757.542l-24.055 21.103 146.18 166.63 48.111-42.21-146.18-166.626-24.055 21.103zM365.034 924.049l24.022 21.141 146.53-166.507-48.043-42.278-146.532 166.502 24.023 21.141zM645.632 605.197l24.026 21.141 148.911-169.212-48.047-42.281-148.911 169.21 24.021 21.141zM512.226 453.129l-24.055 21.103 133.41 152.068 48.107-42.206-133.41-152.068-24.051 21.103zM645.632 605.197l-24.009 21.154 158.758 180.186 48.021-42.308-158.758-180.186-24.013 21.154zM303.774 926.353l-22.404 22.848c30.532 29.939 79.48 28.041 107.686-4.011l-48.045-42.283c-4.029 4.578-10.581 4.766-14.833 0.597l-22.404 22.848zM222.060 846.225l22.404-22.848c-4.447-4.361-4.765-11.874-0.566-16.644l-48.042-42.283c-26.608 30.229-24.993 76.39 3.799 104.627l22.404-22.852zM718.848 926.724l-22.255-22.997c-4.258 4.122-10.782 3.913-14.793-0.661l-48.111 42.21c28.058 31.979 76.787 34.078 107.409 4.446l-22.251-22.997zM802.022 846.234l22.255 22.997c29.129-28.19 30.886-74.633 4.126-105.003l-48.021 42.308c4.228 4.796 3.883 12.352-0.61 16.7l22.251 22.997zM328.594 243.817l-24.055 21.103 78.294 89.244 48.109-42.207-78.293-89.244-24.055 21.103zM406.888 333.061l-24.055 21.104 105.338 120.067 48.107-42.206-105.335-120.068-24.054 21.103zM512.226 453.129l24.077 21.077 105.084-120.070-48.158-42.15-105.084 120.069 24.081 21.073zM617.306 333.061l24.081 21.075 78.106-89.244-48.158-42.15-78.106 89.244 24.077 21.075zM452.109 286.839v32h119.974v-64h-119.974v32zM572.083 286.839v32c6.647 0 13.222 5.704 13.222 14.222h64c0-42.537-33.916-78.222-77.222-78.222v32zM406.888 333.061h31.999c0-8.518 6.579-14.222 13.222-14.222v-64c-43.304 0-77.221 35.685-77.221 78.222h32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "HIV"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1710,
        "id": 310,
        "name": "HIV",
        "prevSize": 32,
        "code": 59986
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 339
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface HivIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const HivIcon = ({ size = 16, ...props }: HivIconProps) => (
  <IcoMoon iconSet={iconSet} icon="HIV" size={size} {...props} />
);

HivIcon.displayName = 'HivIcon';

export default HivIcon;
