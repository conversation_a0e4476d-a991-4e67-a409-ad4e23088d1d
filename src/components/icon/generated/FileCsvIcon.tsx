// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M717.538 730.253c-6.797-16.316-25.532-24.030-41.847-17.233-16.311 6.801-24.026 25.536-17.229 41.847l59.076-24.614zM768 934.558l-29.538 12.309c4.966 11.925 16.619 19.691 29.538 19.691s24.572-7.765 29.538-19.691l-29.538-12.309zM877.538 754.867c6.797-16.311-0.917-35.046-17.229-41.847-16.316-6.797-35.051 0.917-41.847 17.233l59.076 24.614zM341.591 936.179c13.044-11.925 13.951-32.166 2.027-45.21s-32.166-13.952-45.21-2.027l43.183 47.236zM264 934.558v32.004l0.258-0.004-0.258-32zM264 742.558l0.258-32h-0.258v32zM298.409 788.177c13.044 11.925 33.285 11.017 45.21-2.027s11.017-33.284-2.027-45.21l-43.183 47.236zM467.132 892.911c-14.165-10.569-34.214-7.65-44.783 6.515-10.566 14.17-7.648 34.219 6.519 44.783l38.263-51.298zM507.998 938.56l-0.081 32h0.081v-32zM507.998 738.56l0.085-32h-0.085v32zM548.868 784.209c14.165 10.569 34.214 7.65 44.783-6.515 10.564-14.17 7.646-34.219-6.519-44.783l-38.263 51.298zM613.491 122.824v0zM815.842 325.176v0zM184.618 155.223v0zM240.556 99.284v0zM841.835 362.667v0zM138.667 617.442c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM821.333 617.442c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM688 742.558l-29.538 12.309 80 192 59.076-24.614-80-192-29.538 12.305zM768 934.558l29.538 12.309 80-192-59.076-24.614-80 192 29.538 12.305zM320 912.559l-21.591-23.616c-9.478 8.661-21.826 13.513-34.667 13.619l0.258 31.996 0.258 32c28.645-0.23 56.191-11.051 77.333-30.379l-21.591-23.62zM264 934.558v-32c-28.556 0-56-25.783-56-64h-64c0 68.19 50.956 128 120 128v-32zM176 838.558h32c0-38.212 27.444-64 56-64v-64c-69.044 0-120 59.814-120 128h32zM264 742.558l-0.258 32c12.841 0.107 25.189 4.954 34.667 13.619l43.183-47.236c-21.142-19.328-48.689-30.148-77.333-30.379l-0.258 31.996zM448 918.558l-19.132 25.651c22.839 17.037 50.551 26.274 79.049 26.351l0.081-32 0.085-32c-14.758-0.038-29.12-4.826-40.951-13.649l-19.132 25.647zM507.998 938.56v32c20.582 0 44.77-3.26 64.751-16.047 22.302-14.272 35.251-37.786 35.251-67.955h-64c0 5.67-1.109 8.636-1.865 10.065-0.717 1.357-1.796 2.645-3.887 3.985-5.018 3.213-14.831 5.952-30.251 5.952v32zM576 886.558h32c0-29.803-16.316-49.289-35.349-60.885-16.704-10.18-37.525-15.578-52.437-19.541-17.472-4.642-28.813-7.77-36.416-12.087-3.247-1.843-3.972-2.953-3.908-2.859 0.341 0.499 0.111 0.755 0.111-0.627h-64c0 13.623 3.768 26.125 11.115 36.83 7.061 10.295 16.333 17.344 25.088 22.315 16.397 9.306 37.056 14.426 51.584 18.287 17.088 4.535 28.267 7.889 35.563 12.335 4.966 3.025 4.651 4.041 4.651 6.234h32zM448 790.558h32c0-7.403 2.466-11.251 5.325-13.7 3.388-2.901 10.381-6.298 22.673-6.298v-64c-23.706 0-46.707 6.605-64.324 21.705-18.138 15.548-27.674 37.7-27.674 62.293h32zM507.998 738.56l-0.081 32c14.758 0.038 29.12 4.826 40.951 13.649l38.263-51.298c-22.839-17.037-50.551-26.274-79.049-26.351l-0.085 32zM613.491 122.824l-22.63 22.627 202.355 202.353 45.252-45.255-202.351-202.353-22.626 22.627zM375.467 85.333v32h147.516v-64h-147.516v32zM170.667 290.133h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM375.467 85.333v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM184.618 155.223l28.512 14.528c9.204-18.063 23.89-32.75 41.953-41.953l-29.055-57.024c-30.106 15.34-54.583 39.817-69.923 69.923l28.512 14.528zM613.491 122.824l22.626-22.627c-13.679-13.681-29.585-24.629-46.861-32.492l-26.513 58.252c10.368 4.718 19.908 11.285 28.117 19.495l22.63-22.627zM576 96.83l13.257-29.126c-20.617-9.381-43.191-14.371-66.274-14.371v64c13.85 0 27.392 2.993 39.761 8.623l13.257-29.126zM576 96.83h-32v137.836h64v-137.836h-32zM853.333 415.686h32c0-23.087-4.992-45.66-14.37-66.274l-58.253 26.509c5.632 12.372 8.623 25.913 8.623 39.765h32zM841.835 362.667l29.129-13.254c-7.863-17.277-18.812-33.183-32.495-46.863l-45.252 45.255c8.209 8.209 14.775 17.75 19.494 28.117l29.124-13.254zM704 362.667v32h137.835v-64h-137.835v32zM576 234.667h-32c0 88.366 71.633 160 160 160v-64c-53.018 0-96-42.981-96-96h-32zM170.667 617.442h32v-327.309h-64v327.309h32zM853.333 415.686h-32v201.756h64v-201.756h-32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "file-csv"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1630,
        "id": 390,
        "name": "file-csv",
        "prevSize": 32,
        "code": 59906
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 259
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FileCsvIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FileCsvIcon = ({ size = 16, ...props }: FileCsvIconProps) => (
  <IcoMoon iconSet={iconSet} icon="file-csv" size={size} {...props} />
);

FileCsvIcon.displayName = 'FileCsvIcon';

export default FileCsvIcon;
