// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M597.333 853.333c0 47.13-38.204 85.333-85.333 85.333s-85.333-38.204-85.333-85.333M597.333 853.333c0-47.13-38.204-85.333-85.333-85.333M597.333 853.333h192M426.667 853.333c0-47.13 38.204-85.333 85.333-85.333M426.667 853.333h-192M512 768v-170.667M512 597.333h241.779c66.765 0 120.887-57.306 120.887-128 0-55.77-33.685-103.209-80.687-120.752 3.588-136.745-102.033-263.248-241.685-263.248-97.728 0-181.915 61.396-220.032 149.748-101.196-7.21-182.929 81.37-182.929 180.919 0 100.147 76.675 181.333 171.259 181.333h191.407z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "cloud-connection"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1563,
        "id": 457,
        "name": "cloud-connection",
        "prevSize": 32,
        "code": 59839
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 192
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CloudConnectionIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CloudConnectionIcon = ({ size = 16, ...props }: CloudConnectionIconProps) => (
  <IcoMoon iconSet={iconSet} icon="cloud-connection" size={size} {...props} />
);

CloudConnectionIcon.displayName = 'CloudConnectionIcon';

export default CloudConnectionIcon;
