import { type PropDef, asChildPropDef, colorPropDef, highContrastPropDef } from '@props';

const sizes = ['1', '2', '3'] as const;
const variants = ['classic', 'surface', 'soft'] as const;
const labelAligns = ['left', 'right'] as const;

const radioGroupRootPropDefs = {
  ...asChildPropDef,
  size: { type: 'enum', className: 'rt-r-size', values: sizes, default: '2', responsive: true },
  variant: { type: 'enum', className: 'rt-variant', values: variants, default: 'surface' },
  labelAlign: { type: 'enum', values: labelAligns, default: 'right' },
  ...colorPropDef,
  ...highContrastPropDef,
} satisfies {
  size: PropDef<(typeof sizes)[number]>;
  variant: PropDef<(typeof variants)[number]>;
  labelAlign: PropDef<(typeof labelAligns)[number]>;
};

export { radioGroupRootPropDefs };
