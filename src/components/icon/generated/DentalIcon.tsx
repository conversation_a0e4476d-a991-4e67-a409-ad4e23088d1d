// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M259.413 874.667l-163.413-512-1.321-4.659c-6.067-18.688-9.346-38.631-9.346-59.341 0-106.039 85.961-192 192-192 35.457 0 68.669 9.612 97.174 26.371l9.493 5.629 128 83.692M764.587 874.667l163.413-512 2.057-6.971c5.594-18.017 8.61-37.171 8.61-57.029 0-106.039-85.961-192-192-192-37.38 0-72.269 10.683-101.773 29.163l-4.894 2.837-128 83.692M380.587 874.667l32.558-82.807c15.86-39.019 54.145-66.526 98.856-66.526s82.995 27.507 98.854 66.526l32.559 82.807 2.249 5.018c10.044 22.204 32.388 37.649 58.338 37.649 27.268 0 50.556-17.054 59.772-41.079M379.739 876.343c-9.241 23.979-32.503 40.99-59.739 40.99s-50.498-17.011-59.739-40.99M512 222.359l98.854 64.637"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "dental"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1597,
        "id": 423,
        "name": "dental",
        "prevSize": 32,
        "code": 59873
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 226
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface DentalIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const DentalIcon = ({ size = 16, ...props }: DentalIconProps) => (
  <IcoMoon iconSet={iconSet} icon="dental" size={size} {...props} />
);

DentalIcon.displayName = 'DentalIcon';

export default DentalIcon;
