// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M597.333 853.333c0 47.13-38.204 85.333-85.333 85.333s-85.333-38.204-85.333-85.333M597.333 853.333c0-47.13-38.204-85.333-85.333-85.333M597.333 853.333h192M426.667 853.333c0-47.13 38.204-85.333 85.333-85.333M426.667 853.333h-192M512 768v-170.667M512 597.333h144.213c46.596 0 69.897 0 87.693-9.301 15.654-8.179 28.382-21.235 36.361-37.291 9.067-18.253 9.067-42.15 9.067-89.941v-132.239c0-47.791 0-71.687-9.067-89.94-7.979-16.057-20.706-29.111-36.361-37.292-17.796-9.3-41.097-9.3-87.693-9.3h-55.113c-17.365 0-26.048 0-33.933-2.455-6.976-2.174-13.47-5.737-19.102-10.487-6.362-5.365-11.179-12.776-20.813-27.599l-3.469-5.341c-4.608-7.090-8.482-13.055-11.934-18.145-7.36-10.868-12.783-17.772-19.285-23.253-8.448-7.124-18.185-12.47-28.655-15.731-11.823-3.683-24.845-3.683-50.897-3.683h-45.226c-46.596 0-69.894 0-87.692 9.301-15.655 8.181-28.383 21.235-36.36 37.292-9.068 18.254-9.068 42.15-9.068 89.94v238.933c0 47.791 0 71.689 9.068 89.941 7.977 16.055 20.704 29.111 36.36 37.291 17.798 9.301 41.096 9.301 87.692 9.301h144.213z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "folder-connection"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1664,
        "id": 356,
        "name": "folder-connection",
        "prevSize": 32,
        "code": 59940
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 293
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FolderConnectionIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FolderConnectionIcon = ({ size = 16, ...props }: FolderConnectionIconProps) => (
  <IcoMoon iconSet={iconSet} icon="folder-connection" size={size} {...props} />
);

FolderConnectionIcon.displayName = 'FolderConnectionIcon';

export default FolderConnectionIcon;
