// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 599.113c0 116.838 90.206 211.554 201.481 211.554h509.631c78.545 0 142.221-66.859 142.221-149.333 0-65.067-39.629-120.41-94.925-140.877 4.224-159.536-120.038-307.123-284.335-307.123-114.978 0-214.019 71.629-258.863 174.705-119.055-8.412-215.211 94.931-215.211 211.074z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "drive-cloud"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1609,
        "id": 411,
        "name": "drive-cloud",
        "prevSize": 32,
        "code": 59885
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 238
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface DriveCloudIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const DriveCloudIcon = ({ size = 16, ...props }: DriveCloudIconProps) => (
  <IcoMoon iconSet={iconSet} icon="drive-cloud" size={size} {...props} />
);

DriveCloudIcon.displayName = 'DriveCloudIcon';

export default DriveCloudIcon;
