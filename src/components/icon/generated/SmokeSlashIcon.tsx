// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 545.779v206.221M180.148 545.779v206.221M85.333 437.018l23.194-63.852c6.394-17.603 17.9-32.672 32.7-44.007 1.915-1.466 3.885-2.871 5.907-4.209M346.074 176l-41.749 76.623c-5.6 10.278-13.020 19.377-21.797 27.037M450.726 252.131c0 0-9.839 43.503-50.573 87.007-10.237 10.932-21.095 19.805-32.254 26.961M202.223 437.018c9.666-13.338 16.201-24.204 25.332-31.74M132.741 128l149.787 151.659M891.26 896l-142.221-144M282.528 279.659l85.372 86.44M367.9 366.099l177.461 179.68M545.361 545.779h383.825c5.235 0 9.481 3.895 9.481 8.7v188.821c0 4.804-4.245 8.7-9.481 8.7h-180.147M545.361 545.779l203.678 206.221M367.9 545.779h-83.455c-5.236 0-9.482 3.895-9.482 8.7v188.821c0 4.804 4.245 8.7 9.482 8.7h274.962"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "smoke-slash"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1904,
        "id": 116,
        "name": "smoke-slash",
        "prevSize": 32,
        "code": 60180
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 533
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SmokeSlashIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SmokeSlashIcon = ({ size = 16, ...props }: SmokeSlashIconProps) => (
  <IcoMoon iconSet={iconSet} icon="smoke-slash" size={size} {...props} />
);

SmokeSlashIcon.displayName = 'SmokeSlashIcon';

export default SmokeSlashIcon;
