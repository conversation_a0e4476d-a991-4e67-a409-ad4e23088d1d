import clsx from 'clsx';
import { Slot } from 'radix-ui';
import * as React from 'react';

import { cardPropDefs } from '@components/card/card.props';
import { type ComponentPropsWithout, type RemovedProps, extractProps } from '@helpers';
import { type GetPropDefTypes, type MarginProps, marginPropDefs } from '@props';

type CardElement = React.ComponentRef<'div'>;
type CardOwnProps = GetPropDefTypes<typeof cardPropDefs>;
interface CardProps extends ComponentPropsWithout<'div', RemovedProps | 'radioGroup'>, MarginProps, CardOwnProps {}
const Card = React.forwardRef<CardElement, CardProps>((props, forwardedRef) => {
  const { asChild, radius, className, ...cardProps } = extractProps(props, cardPropDefs, marginPropDefs);
  const Comp = asChild ? Slot.Root : 'div';

  return (
    <Comp
      ref={forwardedRef}
      data-radius={radius}
      {...cardProps}
      className={clsx('rt-reset', 'rt-BaseCard', 'rt-Card', className)}
    />
  );
});
Card.displayName = 'Card';

export { Card };
export type { CardProps };
