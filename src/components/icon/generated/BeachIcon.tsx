// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 896h830.404M269.867 742.148l-69.2 153.852M269.867 742.148l-115.334-109.897M269.867 742.148h230.669M869.602 896l-59.059-98.483c-20.535-34.24-58.748-55.369-100.139-55.369h-209.869M108.399 588.297l46.134 43.955M154.533 632.252h198.301c35.037 0 68.173 15.177 90.059 41.246l57.643 68.651M433.532 349.28c4.1-31.508 16.444-61.73 36.335-88.939s46.938-50.872 79.595-69.64c28.489-16.372 60.762-28.738 95.39-36.586M433.532 349.28l-114.457-13.511c-12.643-1.492-21.753-12.515-18.741-24.31 6.052-23.702 16.887-46.407 32.183-67.331 19.891-27.209 46.936-50.873 79.593-69.64s70.284-32.27 110.736-39.738c18.202-3.36 36.813-5.469 55.616-6.32 22.985-1.040 46.255-0.199 69.402 2.533s45.926 7.328 67.951 13.68c18.018 5.197 35.529 11.57 52.318 19.061 37.316 16.648 70.349 38.491 97.212 64.285s47.031 55.029 59.349 86.041c9.476 23.848 14.17 48.386 13.969 72.779-0.102 12.138-11.759 20.71-24.401 19.217l-114.462-13.511M433.532 349.28l183.134 21.618M799.799 392.516c4.1-31.508-0.132-63.687-12.454-94.699s-32.491-60.248-59.354-86.041c-23.433-22.502-51.563-41.998-83.14-57.661M799.799 392.516l-183.134-21.618M644.851 154.116l-28.186 216.783M616.666 370.898l-60.535 525.102"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "beach"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1460,
        "id": 560,
        "name": "beach",
        "prevSize": 32,
        "code": 59736
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 89
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BeachIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BeachIcon = ({ size = 16, ...props }: BeachIconProps) => (
  <IcoMoon iconSet={iconSet} icon="beach" size={size} {...props} />
);

BeachIcon.displayName = 'BeachIcon';

export default BeachIcon;
