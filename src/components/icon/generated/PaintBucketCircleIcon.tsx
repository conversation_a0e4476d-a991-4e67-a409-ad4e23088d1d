// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M388.667 408.546l-92.324 89.447c-25.346 24.559-25.346 64.375 0 88.93l123.85 119.991c25.345 24.559 66.442 24.559 91.786 0l201.933-195.644c5.069-4.911 5.069-12.873 0-17.783l-197.278-191.137c-5.069-4.911-13.286-4.911-18.359 0l-109.607 106.196zM388.667 408.546l144.666 39.454M388.667 408.546l-90-24.546M938.667 512c0 235.639-191.027 426.667-426.667 426.667-235.642 0-426.667-191.027-426.667-426.667 0-235.642 191.025-426.667 426.667-426.667 235.639 0 426.667 191.025 426.667 426.667zM746.667 685.261c0 22.127-17.51 40.068-39.113 40.068s-39.113-17.941-39.113-40.068c0-18.189 26.415-49.011 35.827-59.418 1.459-1.609 4.083-0.956 5.013 1.020 8.755 18.628 37.385 37.943 37.385 58.398z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "paint-bucket-circle"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1810,
        "id": 210,
        "name": "paint-bucket-circle",
        "prevSize": 32,
        "code": 60086
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 439
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface PaintBucketCircleIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const PaintBucketCircleIcon = ({ size = 16, ...props }: PaintBucketCircleIconProps) => (
  <IcoMoon iconSet={iconSet} icon="paint-bucket-circle" size={size} {...props} />
);

PaintBucketCircleIcon.displayName = 'PaintBucketCircleIcon';

export default PaintBucketCircleIcon;
