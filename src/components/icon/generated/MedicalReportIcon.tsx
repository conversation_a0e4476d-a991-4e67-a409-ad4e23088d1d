// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M725.333 149.333l32 0.119v-0.119h-32zM384 149.333h-32l0.001 0.119 32-0.119zM858.347 873.212v0zM809.212 922.347v0zM858.347 214.79v0zM809.212 165.654v0zM250.987 214.79v0zM300.123 165.654v0zM294.217 936.576v0zM285.826 928.183v0zM396.841 928.183v0zM388.45 936.576v0zM388.45 556.757v0zM396.841 565.15v0zM294.217 556.757v0zM285.826 565.15v0zM159.817 802.176v0zM151.426 793.783v0zM531.243 793.783v0zM522.85 802.176v0zM522.85 691.157v0zM531.243 699.55v0zM159.817 691.157v0zM151.426 699.55v0zM409.417 806.357v0zM401.026 814.75v0zM281.641 814.75v0zM273.25 806.357v0zM409.417 686.976v0zM401.026 678.583v0zM273.25 686.976v0zM281.641 678.583v0zM554.667 906.667c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM202.667 448c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM448 85.333v32h213.333v-64h-213.333v32zM661.333 213.333v-32h-213.333v64h213.333v-32zM661.333 85.333v32c17.673 0 32 14.327 32 32h64c0-53.019-42.982-96-96-96v32zM448 85.333v-32c-53.019 0-96 42.981-96 96h64c0-17.673 14.327-32 32-32v-32zM874.667 360.533h-32v366.933h64v-366.933h-32zM874.667 727.467h-32c0 39.441-0.030 66.935-1.916 88.277-1.847 20.915-5.291 32.841-10.325 41.83l55.842 31.27c11.285-20.151 16.004-42.231 18.236-67.465 2.193-24.815 2.163-55.62 2.163-93.914h-32zM663.467 938.667v32c38.293 0 69.099 0.030 93.914-2.163 25.233-2.231 47.313-6.95 67.465-18.236l-31.27-55.842c-8.99 5.035-20.915 8.478-41.83 10.325-21.342 1.886-48.836 1.916-88.277 1.916v32zM858.347 873.212l-27.921-15.637c-8.649 15.445-21.406 28.203-36.851 36.851l31.27 55.842c25.745-14.421 47.002-35.678 61.423-61.423l-27.921-15.633zM874.667 360.533h32c0-38.293 0.030-69.099-2.163-93.911-2.231-25.235-6.95-47.315-18.236-67.468l-55.842 31.272c5.035 8.989 8.478 20.914 10.325 41.832 1.886 21.34 1.916 48.835 1.916 88.276h32zM809.212 165.654l-15.637 27.92c15.445 8.651 28.203 21.405 36.851 36.852l55.842-31.272c-14.421-25.745-35.678-47.002-61.423-61.42l-15.633 27.92zM234.667 360.533h32c0-39.441 0.029-66.935 1.916-88.276 1.849-20.918 5.29-32.843 10.324-41.832l-55.84-31.272c-11.286 20.153-16.006 42.233-18.236 67.468-2.193 24.812-2.164 55.618-2.164 93.911h32zM300.123 165.654l-15.636-27.92c-25.745 14.418-47.002 35.675-61.42 61.42l55.84 31.272c8.651-15.447 21.405-28.201 36.852-36.852l-15.636-27.92zM384.002 149.817l-0.885-31.988c-39.314 1.088-71.101 4.489-98.629 19.905l31.272 55.84c12.72-7.123 30.455-10.698 69.128-11.769l-0.885-31.988zM448 213.333v-32c-17.591 0-31.869-14.197-31.999-31.753l-63.998 0.474c0.39 52.691 43.219 95.279 95.997 95.279v-32zM384.002 149.817l32-0.119-0.002-0.484-63.999 0.237 0.002 0.483 32-0.118zM725.333 149.817l-0.887 31.988c38.673 1.071 56.41 4.646 69.129 11.769l31.27-55.84c-27.529-15.416-59.315-18.816-98.628-19.905l-0.883 31.988zM725.333 149.333l-32-0.119v0.484l64 0.237v-0.483l-32-0.119zM725.333 149.817l-32-0.237c-0.132 17.556-14.409 31.753-32 31.753v64c52.779 0 95.607-42.589 95.996-95.279l-31.996-0.237zM314.453 554.667v32h53.76v-64h-53.76v32zM368.213 938.667v-32h-53.76v64h53.76v-32zM314.453 938.667v-32c-2.761 0-4.841 0-6.618-0.030-1.778-0.026-2.795-0.077-3.386-0.124-1.223-0.102 1.086-0.085 4.296 1.549l-29.056 57.024c7.316 3.729 14.368 4.791 19.548 5.214 4.807 0.393 10.368 0.367 15.216 0.367v-32zM283.733 907.947h-32c0 4.847-0.025 10.411 0.368 15.215 0.423 5.184 1.485 12.233 5.213 19.55l57.024-29.056c1.635 3.209 1.65 5.517 1.551 4.297-0.048-0.593-0.099-1.609-0.127-3.388s-0.029-3.857-0.029-6.618h-32zM294.217 936.576l14.528-28.514c2.409 1.229 4.367 3.187 5.594 5.594l-57.024 29.056c4.909 9.634 12.742 17.468 22.375 22.374l14.528-28.51zM398.933 907.947h-32c0 2.761-0.001 4.838-0.029 6.618s-0.079 2.795-0.127 3.388c-0.1 1.22-0.085-1.088 1.551-4.297l57.024 29.056c3.729-7.317 4.791-14.366 5.213-19.55 0.393-4.804 0.367-10.368 0.367-15.215h-32zM368.213 938.667v32c4.849 0 10.409 0.026 15.216-0.367 5.181-0.422 12.232-1.485 19.548-5.214l-29.056-57.024c3.209-1.634 5.519-1.651 4.296-1.549-0.592 0.047-1.608 0.098-3.386 0.124-1.777 0.030-3.857 0.030-6.618 0.030v32zM396.841 928.183l-28.512-14.528c1.227-2.406 3.185-4.365 5.594-5.594l29.056 57.024c9.634-4.907 17.466-12.74 22.375-22.374l-28.512-14.528zM368.213 554.667v32c2.761 0 4.841 0 6.618 0.030 1.778 0.026 2.795 0.077 3.386 0.124 1.223 0.102-1.086 0.085-4.296-1.549l29.056-57.024c-7.316-3.729-14.368-4.791-19.548-5.214-4.807-0.393-10.368-0.367-15.216-0.367v32zM398.933 585.387h32c0-4.847 0.026-10.411-0.367-15.215-0.422-5.184-1.485-12.233-5.213-19.55l-57.024 29.056c-1.635-3.209-1.65-5.517-1.551-4.297 0.048 0.593 0.099 1.609 0.127 3.388s0.029 3.857 0.029 6.618h32zM388.45 556.757l-14.528 28.514c-2.409-1.229-4.367-3.187-5.594-5.594l57.024-29.056c-4.909-9.634-12.742-17.468-22.375-22.374l-14.528 28.51zM314.453 554.667v-32c-4.849 0-10.409-0.026-15.216 0.367-5.181 0.422-12.232 1.485-19.548 5.214l29.056 57.024c-3.209 1.634-5.519 1.651-4.296 1.549 0.592-0.047 1.608-0.098 3.386-0.124 1.777-0.030 3.857-0.030 6.618-0.030v-32zM283.733 585.387h32c0-2.761 0.001-4.838 0.029-6.618s0.079-2.795 0.127-3.388c0.1-1.22 0.085 1.088-1.551 4.297l-57.024-29.056c-3.728 7.317-4.789 14.366-5.213 19.55-0.393 4.804-0.368 10.368-0.368 15.215h32zM294.217 556.757l-14.528-28.51c-9.634 4.907-17.466 12.74-22.375 22.374l57.024 29.056c-1.227 2.406-3.185 4.365-5.594 5.594l-14.528-28.514zM533.333 719.787h-32v53.76h64v-53.76h-32zM149.333 773.547h32v-53.76h-64v53.76h32zM180.053 804.267v-32c-2.761 0-4.841 0-6.618-0.030-1.778-0.026-2.795-0.077-3.386-0.124-1.223-0.102 1.086-0.085 4.296 1.549l-29.056 57.024c7.316 3.729 14.368 4.791 19.548 5.214 4.807 0.393 10.368 0.367 15.216 0.367v-32zM149.333 773.547h-32c0 4.847-0.025 10.411 0.368 15.215 0.423 5.184 1.485 12.233 5.213 19.55l57.024-29.056c1.635 3.209 1.65 5.517 1.551 4.297-0.048-0.593-0.099-1.609-0.127-3.388s-0.029-3.857-0.029-6.618h-32zM159.817 802.176l14.528-28.514c2.409 1.229 4.367 3.187 5.594 5.594l-57.024 29.056c4.909 9.634 12.742 17.468 22.375 22.374l14.528-28.51zM533.333 773.547h-32c0 2.761 0 4.838-0.030 6.618-0.026 1.779-0.077 2.795-0.124 3.388-0.102 1.22-0.085-1.088 1.549-4.297l57.024 29.056c3.729-7.317 4.791-14.366 5.214-19.55 0.393-4.804 0.367-10.368 0.367-15.215h-32zM502.613 804.267v32c4.847 0 10.411 0.026 15.215-0.367 5.184-0.422 12.233-1.485 19.55-5.214l-29.056-57.024c3.209-1.634 5.517-1.651 4.297-1.549-0.593 0.047-1.609 0.098-3.388 0.124-1.779 0.030-3.857 0.030-6.618 0.030v32zM531.243 793.783l-28.514-14.528c1.229-2.406 3.187-4.365 5.594-5.594l29.056 57.024c9.634-4.907 17.468-12.74 22.374-22.374l-28.51-14.528zM502.613 689.067v32c2.761 0 4.838 0 6.618 0.030 1.779 0.026 2.795 0.077 3.388 0.124 1.22 0.102-1.088 0.085-4.297-1.549l29.056-57.024c-7.317-3.729-14.366-4.791-19.55-5.214-4.804-0.393-10.368-0.367-15.215-0.367v32zM533.333 719.787h32c0-4.847 0.026-10.411-0.367-15.215-0.422-5.184-1.485-12.233-5.214-19.55l-57.024 29.056c-1.634-3.209-1.651-5.517-1.549-4.297 0.047 0.593 0.098 1.609 0.124 3.388 0.030 1.779 0.030 3.857 0.030 6.618h32zM522.85 691.157l-14.528 28.514c-2.406-1.229-4.365-3.187-5.594-5.594l57.024-29.056c-4.907-9.634-12.74-17.468-22.374-22.374l-14.528 28.51zM180.053 689.067v-32c-4.849 0-10.409-0.026-15.216 0.367-5.181 0.422-12.232 1.485-19.548 5.214l29.056 57.024c-3.209 1.634-5.519 1.651-4.296 1.549 0.592-0.047 1.608-0.098 3.386-0.124 1.777-0.030 3.857-0.030 6.618-0.030v-32zM149.333 719.787h32c0-2.761 0.001-4.838 0.029-6.618s0.079-2.795 0.127-3.388c0.1-1.22 0.085 1.088-1.551 4.297l-57.024-29.056c-3.728 7.317-4.789 14.366-5.213 19.55-0.393 4.804-0.368 10.368-0.368 15.215h32zM159.817 691.157l-14.528-28.51c-9.634 4.907-17.466 12.74-22.375 22.374l57.024 29.056c-1.227 2.406-3.185 4.365-5.594 5.594l-14.528-28.514zM180.053 689.067v32h72.96v-64h-72.96v32zM283.733 658.347h32v-72.96h-64v72.96h32zM429.653 689.067v32h72.96v-64h-72.96v32zM398.933 585.387h-32v72.96h64v-72.96h-32zM283.733 907.947h32v-72.96h-64v72.96h32zM253.013 804.267v-32h-72.96v64h72.96v-32zM502.613 804.267v-32h-72.96v64h72.96v-32zM398.933 834.987h-32v72.96h64v-72.96h-32zM429.653 804.267v-32c-4.849 0-10.409-0.026-15.216 0.367-5.181 0.422-12.232 1.485-19.548 5.214l29.056 57.024c-3.209 1.634-5.519 1.651-4.296 1.549 0.592-0.047 1.608-0.098 3.386-0.124 1.777-0.030 3.857-0.030 6.618-0.030v-32zM398.933 834.987h32c0-2.761 0-4.838 0.030-6.618 0.026-1.779 0.077-2.795 0.124-3.388 0.102-1.22 0.085 1.088-1.549 4.297l-57.024-29.056c-3.728 7.317-4.789 14.366-5.213 19.55-0.393 4.804-0.368 10.368-0.368 15.215h32zM409.417 806.357l-14.528-28.51c-9.634 4.907-17.466 12.74-22.375 22.374l57.024 29.056c-1.229 2.411-3.185 4.365-5.594 5.594l-14.528-28.514zM283.733 834.987h32c0-4.847 0.025-10.411-0.368-15.215-0.423-5.184-1.485-12.233-5.213-19.55l-57.024 29.056c-1.635-3.209-1.65-5.517-1.551-4.297 0.048 0.593 0.099 1.609 0.127 3.388s0.029 3.857 0.029 6.618h32zM253.013 804.267v32c2.761 0 4.841 0 6.618 0.030 1.778 0.026 2.795 0.077 3.386 0.124 1.223 0.102-1.086 0.085-4.296-1.549l29.056-57.024c-7.316-3.729-14.368-4.791-19.548-5.214-4.807-0.393-10.368-0.367-15.216-0.367v32zM281.641 814.75l28.512-14.528c-4.909-9.634-12.742-17.468-22.375-22.374l-29.056 57.024c-2.408-1.229-4.367-3.183-5.594-5.594l28.512-14.528zM429.653 689.067v-32c-2.761 0-4.841 0-6.618-0.030-1.778-0.026-2.795-0.077-3.387-0.124-1.222-0.102 1.087-0.085 4.296 1.549l-29.056 57.024c7.316 3.729 14.368 4.791 19.548 5.214 4.807 0.393 10.368 0.367 15.216 0.367v-32zM398.933 658.347h-32c0 4.847-0.025 10.411 0.368 15.215 0.423 5.184 1.485 12.233 5.213 19.55l57.024-29.056c1.634 3.209 1.651 5.517 1.549 4.297-0.047-0.593-0.098-1.609-0.124-3.388-0.030-1.779-0.030-3.857-0.030-6.618h-32zM409.417 686.976l14.528-28.514c2.408 1.229 4.365 3.187 5.594 5.594l-57.024 29.056c4.909 9.634 12.742 17.468 22.375 22.374l14.528-28.51zM253.013 689.067v32c4.848 0 10.409 0.026 15.216-0.367 5.181-0.422 12.232-1.485 19.548-5.214l-29.056-57.024c3.209-1.634 5.519-1.651 4.296-1.549-0.592 0.047-1.609 0.098-3.387 0.124-1.777 0.030-3.857 0.030-6.618 0.030v32zM283.733 658.347h-32c0 2.761-0.001 4.838-0.029 6.618s-0.079 2.795-0.127 3.388c-0.1 1.22-0.085-1.088 1.551-4.297l57.024 29.056c3.728-7.317 4.789-14.37 5.213-19.55 0.393-4.804 0.368-10.368 0.368-15.215h-32zM273.25 686.976l14.528 28.51c9.634-4.907 17.466-12.74 22.375-22.374l-57.024-29.056c1.227-2.406 3.185-4.365 5.594-5.594l14.528 28.514zM663.467 938.667v-32h-108.8v64h108.8v-32zM234.667 448h32v-87.467h-64v87.467h32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "medical-report"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1762,
        "id": 258,
        "name": "medical-report",
        "prevSize": 32,
        "code": 60038
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 391
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MedicalReportIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MedicalReportIcon = ({ size = 16, ...props }: MedicalReportIconProps) => (
  <IcoMoon iconSet={iconSet} icon="medical-report" size={size} {...props} />
);

MedicalReportIcon.displayName = 'MedicalReportIcon';

export default MedicalReportIcon;
