import React, { createContext, useContext, useReducer } from 'react';

import { menuReducer } from '@components/sidebar/menu-reducer';
import type { MenuItem } from '@components/sidebar/type';

type MenuContextProps = {
  openKeys?: string;
  mergeKeys: (keys: string[]) => string;
  onOpenKeyChange: (openKeys: string) => void;
  onMenuSelected?: (key: string, data: MenuItem) => void;
};

const MenuContext = createContext<MenuContextProps | null>(null);

export function useMenu() {
  const context = useContext(MenuContext);
  if (!context) {
    throw new Error('useMenu must be used within a MenuProvider.');
  }

  return context;
}

export interface MenuProviderProps {
  defaultOpenKeys?: Array<string>;
  onMenuSelected?: (key: string, data: MenuItem) => void;
}

interface Props extends MenuProviderProps {
  children?: React.ReactNode;
}

export const MenuProvider: React.FC<Props> = ({ children, defaultOpenKeys = [], onMenuSelected }) => {
  const mergeKeys = (keys: string[]) => keys.join('.');

  const [state, dispatch] = useReducer(menuReducer, { openKeys: mergeKeys(defaultOpenKeys) });
  const onOpenKeyChange = (openKeys: string) => {
    if (openKeys !== state.openKeys) {
      dispatch({ type: 'SET_OPEN_KEYS', payload: { openKeys } });
    }
  };

  return (
    <MenuContext.Provider
      value={{
        openKeys: state.openKeys,
        mergeKeys,
        onOpenKeyChange,
        onMenuSelected,
      }}
    >
      {children}
    </MenuContext.Provider>
  );
};
