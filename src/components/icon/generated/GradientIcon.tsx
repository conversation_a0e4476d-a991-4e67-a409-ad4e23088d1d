// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 213.333h853.333M85.333 412.445h355.554M583.113 412.445h355.554M85.333 810.667h106.667M334.222 810.667h106.665M583.113 810.667h106.667M832 810.667h106.667M85.333 611.554h189.629M417.185 611.554h189.629M749.039 611.554h189.628"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "gradient"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1694,
        "id": 326,
        "name": "gradient",
        "prevSize": 32,
        "code": 59970
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 323
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface GradientIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const GradientIcon = ({ size = 16, ...props }: GradientIconProps) => (
  <IcoMoon iconSet={iconSet} icon="gradient" size={size} {...props} />
);

GradientIcon.displayName = 'GradientIcon';

export default GradientIcon;
