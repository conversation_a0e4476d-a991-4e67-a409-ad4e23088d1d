// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M256 85.333l91.251 15.692c28.081 4.829 50.395 26.973 56.163 55.736l128.277 639.685M671.356 580.574l-49.404 33.024c-37.001 24.832-84.518 25.596-122.253 1.958l-40.661-25.472c-30.839-19.315-68.681-22.562-102.189-8.768l-49.344 20.314-5.747 2.432M671.356 580.574l8.068-116.523M671.356 580.574l-20.22 292.049c-2.577 37.214-32.589 66.044-68.749 66.044h-193.289c-36.16 0-66.172-28.83-68.748-66.044l-18.593-268.561M533.321 274.963c11.149-80.41 78.174-142.222 159.194-142.222 88.819 0 160.819 74.288 160.819 165.926s-72 165.926-160.819 165.926c-4.407 0-8.772-0.183-13.090-0.542M533.321 274.963h-229.672c-13.333 0-23.866 11.671-22.916 25.393l21.026 303.706M533.321 274.963h134.519c13.333 0 23.863 11.671 22.916 25.393l-11.332 163.696"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "juice"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1725,
        "id": 295,
        "name": "juice",
        "prevSize": 32,
        "code": 60001
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 354
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface JuiceIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const JuiceIcon = ({ size = 16, ...props }: JuiceIconProps) => (
  <IcoMoon iconSet={iconSet} icon="juice" size={size} {...props} />
);

JuiceIcon.displayName = 'JuiceIcon';

export default JuiceIcon;
