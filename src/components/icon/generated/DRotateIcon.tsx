// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M630.443 906.487v0zM954.854 397.139c-4.74-17.026-22.383-26.986-39.411-22.247-17.024 4.739-26.982 22.383-22.246 39.409l61.658-17.162zM393.555 117.513v0zM69.146 626.859c4.739 17.028 22.383 26.987 39.409 22.251 17.026-4.74 26.986-22.383 22.247-39.411l-61.656 17.161zM399.327 123.22v0zM343.901 199.807c-4.904 16.979 4.886 34.719 21.865 39.622s34.719-4.885 39.622-21.865l-61.487-17.758zM624.674 900.779v0zM680.098 824.192c4.907-16.977-4.885-34.718-21.862-39.62-16.981-4.902-34.722 4.885-39.625 21.862l61.487 17.758zM500.489 280.329v0zM310.859 386.149v0zM310.859 637.85v0zM500.489 743.671v0zM523.511 743.671v0zM713.139 637.85v0zM713.139 386.149v0zM523.511 280.329v0zM302.072 394.667v0zM630.443 906.487l8.567 30.831c238.729-66.33 381.107-305.711 315.844-540.179l-61.658 17.162c55.241 198.461-64.678 403.939-271.317 461.356l8.563 30.831zM393.555 117.513l-8.567-30.832c-238.727 66.331-381.106 305.71-315.843 540.178l61.656-17.161c-55.24-198.461 64.681-403.939 271.32-461.354l-8.567-30.832zM399.327 123.22l-30.743-8.879-24.683 85.466 61.487 17.758 24.684-85.466-30.744-8.879zM624.674 900.779l30.741 8.879 24.683-85.466-61.487-17.758-24.683 85.466 30.746 8.879zM393.555 117.513l8.567 30.832c-19.725 5.481-39.732-12.556-33.538-34.004l61.488 17.758c8.201-28.396-18.294-52.861-45.082-45.419l8.566 30.832zM630.443 906.487l-8.563-30.831c19.725-5.483 39.731 12.557 33.536 34.001l-61.487-17.758c-8.201 28.399 18.295 52.864 45.082 45.419l-8.567-30.831zM500.489 280.329l-15.595-27.943-189.629 105.819 31.187 55.887 189.631-105.82-15.595-27.944zM298.667 406.962h-32v210.074h64v-210.074h-32zM310.859 637.85l-15.593 27.947 189.629 105.818 31.189-55.889-189.631-105.818-15.594 27.942zM523.511 743.671l15.595 27.942 189.628-105.818-31.185-55.889-189.632 105.818 15.595 27.947zM725.333 617.037h32v-210.074h-64v210.074h32zM713.139 386.149l15.595-27.943-189.628-105.82-31.189 55.887 189.632 105.82 15.59-27.944zM713.139 637.85l15.595 27.947c17.698-9.877 28.599-28.561 28.599-48.759h-64c0-2.901 1.566-5.653 4.215-7.13l15.59 27.942zM298.667 617.037h-32c0 20.198 10.901 38.882 28.599 48.759l31.187-55.889c2.647 1.476 4.214 4.228 4.214 7.13h-32zM500.489 280.329l15.595 27.944c-2.534 1.414-5.632 1.414-8.166 0l31.189-55.887c-16.853-9.404-37.359-9.404-54.212 0l15.595 27.943zM725.333 406.962h32c0-10.313-2.842-20.228-7.97-28.767l-54.869 32.943c-0.747-1.243-1.161-2.688-1.161-4.176h32zM721.929 394.667l27.435-16.471c-4.919-8.196-11.964-15.154-20.629-19.99l-31.185 55.887c-1.297-0.723-2.338-1.761-3.055-2.955l27.435-16.471zM512 512l15.612 27.934 209.929-117.334-31.223-55.866-209.929 117.332 15.612 27.934zM310.859 386.149l-15.593-27.944c-8.665 4.836-15.708 11.794-20.628 19.99l54.871 32.943c-0.717 1.194-1.76 2.232-3.055 2.955l-15.594-27.944zM302.072 394.667l-27.435-16.471c-5.126 8.539-7.97 18.455-7.97 28.767h64c0 1.488-0.413 2.933-1.159 4.176l-27.436-16.471zM512 512l15.612-27.934-209.927-117.332-31.225 55.866 209.929 117.334 15.612-27.934zM500.489 743.671l-15.595 27.942c8.418 4.698 17.766 7.053 27.106 7.053v-64c1.408 0 2.82 0.358 4.083 1.058l-15.595 27.947zM512 746.667v32c9.34 0 18.688-2.355 27.106-7.053l-31.189-55.889c1.263-0.7 2.675-1.058 4.083-1.058v32zM512 512h-32v234.667h64v-234.667h-32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "d-rotate"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1373,
        "id": 647,
        "name": "d-rotate",
        "prevSize": 32,
        "code": 59649
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 2
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface DRotateIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const DRotateIcon = ({ size = 16, ...props }: DRotateIconProps) => (
  <IcoMoon iconSet={iconSet} icon="d-rotate" size={size} {...props} />
);

DRotateIcon.displayName = 'DRotateIcon';

export default DRotateIcon;
