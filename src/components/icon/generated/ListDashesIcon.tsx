// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 213.333h71.111M85.333 512h71.111M85.333 810.667h71.111M298.667 512h640M298.667 810.667h640M298.667 213.333h640"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "list-dashes"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1739,
        "id": 281,
        "name": "list-dashes",
        "prevSize": 32,
        "code": 60015
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 368
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ListDashesIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ListDashesIcon = ({ size = 16, ...props }: ListDashesIconProps) => (
  <IcoMoon iconSet={iconSet} icon="list-dashes" size={size} {...props} />
);

ListDashesIcon.displayName = 'ListDashesIcon';

export default ListDashesIcon;
