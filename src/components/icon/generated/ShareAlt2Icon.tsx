// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M582.729 180.39v109.744c0 5.737-4.907 10.313-10.615 10.188-64.222-1.405-400.94 16.653-486.643 541.355-1.821 11.153 15.083 16.294 20.445 6.362 143.631-266.133 415.831-269.278 468.038-267.319 4.996 0.188 8.777 4.343 8.777 9.37v111.573c0 8.055 9.199 12.604 15.548 7.689l336.627-260.638c5.018-3.883 5.018-11.49 0-15.373l-336.627-260.638c-6.349-4.916-15.548-0.367-15.548 7.688z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "share-alt2"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1880,
        "id": 140,
        "name": "share-alt2",
        "prevSize": 32,
        "code": 60156
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 509
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ShareAlt2IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ShareAlt2Icon = ({ size = 16, ...props }: ShareAlt2IconProps) => (
  <IcoMoon iconSet={iconSet} icon="share-alt2" size={size} {...props} />
);

ShareAlt2Icon.displayName = 'ShareAlt2Icon';

export default ShareAlt2Icon;
