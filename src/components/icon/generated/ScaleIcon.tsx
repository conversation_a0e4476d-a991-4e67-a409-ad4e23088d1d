// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M469.333 85.333h-179.2c-71.687 0-107.53 0-134.911 13.951-24.085 12.272-43.666 31.853-55.938 55.938-13.951 27.38-13.951 63.224-13.951 134.911v443.733c0 71.689 0 107.529 13.951 134.912 12.272 24.085 31.853 43.665 55.938 55.936 27.38 13.952 63.224 13.952 134.911 13.952h451.61c66.475-0.017 100.676-0.521 127.036-13.952 24.085-12.271 43.665-31.851 55.936-55.936 13.952-27.383 13.952-63.223 13.952-134.912v-179.2M645.333 85.333h285.79c2.082 0 3.968 0.844 5.333 2.209M938.667 378.667v-285.791c0-2.083-0.845-3.968-2.21-5.333M936.457 87.543l-550.248 550.247M677.333 640h-285.791c-2.083 0-3.968-0.845-5.333-2.21M384 346.667v285.79c0 2.082 0.844 3.968 2.209 5.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "scale"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1862,
        "id": 158,
        "name": "scale",
        "prevSize": 32,
        "code": 60138
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 491
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ScaleIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ScaleIcon = ({ size = 16, ...props }: ScaleIconProps) => (
  <IcoMoon iconSet={iconSet} icon="scale" size={size} {...props} />
);

ScaleIcon.displayName = 'ScaleIcon';

export default ScaleIcon;
