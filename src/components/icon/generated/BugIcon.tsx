// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 938.667v-663.704M512 938.667c-116.61 0-216.832-70.17-260.741-170.594M512 938.667c116.612 0 216.836-70.17 260.745-170.594M701.632 274.963c0-104.729-84.902-189.629-189.632-189.629-104.728 0-189.628 84.9-189.628 189.629M512 274.963h-183.881c-55.538 0-100.561 45.023-100.561 100.561l-0 26.7M512 274.963h183.885c55.539 0 100.561 45.023 100.561 100.561v26.7M227.555 583.113l0.003 71.108c0 40.482 8.457 78.993 23.701 113.852M227.555 583.113h-142.222M227.555 583.113l0.003-180.888M796.446 583.113v71.108c0 40.482-8.457 78.993-23.701 113.852M796.446 583.113h142.221M796.446 583.113v-180.888M251.259 768.073l-118.518 123.187M772.745 768.073l118.511 123.187M227.558 402.224l-118.521-103.558M796.446 402.224l118.515-103.558"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bug"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1495,
        "id": 525,
        "name": "bug",
        "prevSize": 32,
        "code": 59771
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 124
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BugIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BugIcon = ({ size = 16, ...props }: BugIconProps) => (
  <IcoMoon iconSet={iconSet} icon="bug" size={size} {...props} />
);

BugIcon.displayName = 'BugIcon';

export default BugIcon;
