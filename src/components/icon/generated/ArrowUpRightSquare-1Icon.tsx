// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M384 388.002l-31.832-3.274-0.18 1.75 0.013 1.759 31.999-0.234zM353.327 569.493c0.13 17.673 14.561 31.893 32.233 31.765 17.673-0.132 31.895-14.562 31.765-32.235l-63.998 0.469zM388.002 384l0.234-31.999-1.759-0.013-1.75 0.18 3.274 31.832zM569.024 417.326c17.673 0.13 32.102-14.092 32.235-31.765 0.128-17.673-14.093-32.104-31.765-32.233l-0.469 63.998zM385.327 385.327v0zM617.374 662.626c12.497 12.497 32.755 12.497 45.252 0s12.497-32.755 0-45.252l-45.252 45.252zM155.223 924.715v0zM99.284 868.779v0zM924.715 868.779v0zM868.779 924.715v0zM868.779 99.284v0zM924.715 155.223v0zM155.223 99.284v0zM99.284 155.223v0zM384 388.002l-31.999 0.234 1.327 181.257 63.998-0.469-1.327-181.257-31.999 0.234zM388.002 384l-0.234 31.999 181.257 1.327 0.469-63.998-181.257-1.327-0.234 31.999zM384 388.002l31.832 3.274c-0.752 7.31-4.154 12.955-7.878 16.678l-45.255-45.254c-5.171 5.171-9.573 12.714-10.531 22.028l31.832 3.274zM385.327 385.327l22.627 22.628c-3.723 3.723-9.368 7.126-16.678 7.878l-6.548-63.664c-9.314 0.958-16.857 5.36-22.028 10.531l22.627 22.627zM385.327 385.327l-22.627 22.627 254.675 254.672 45.252-45.252-254.672-254.675-22.627 22.627zM290.133 85.333v32h443.733v-64h-443.733v32zM938.667 290.133h-32v443.733h64v-443.733h-32zM733.867 938.667v-32h-443.733v64h443.733v-32zM85.333 733.867h32v-443.733h-64v443.733h32zM290.133 938.667v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM85.333 733.867h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM155.223 924.715l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM938.667 733.867h-32c0 36.373-0.026 61.726-1.638 81.463-1.583 19.366-4.531 30.494-8.823 38.921l57.024 29.056c9.655-18.957 13.683-39.441 15.586-62.763 1.877-22.95 1.852-51.362 1.852-86.677h-32zM733.867 938.667v32c35.315 0 63.727 0.026 86.677-1.852 23.322-1.903 43.806-5.931 62.763-15.586l-29.056-57.024c-8.427 4.292-19.554 7.241-38.921 8.823-19.738 1.613-45.090 1.638-81.463 1.638v32zM924.715 868.779l-28.51-14.528c-9.207 18.061-23.893 32.747-41.954 41.954l29.056 57.024c30.106-15.343 54.579-39.817 69.922-69.922l-28.514-14.528zM733.867 85.333v32c36.373 0 61.726 0.025 81.463 1.638 19.366 1.582 30.494 4.532 38.921 8.826l29.056-57.024c-18.957-9.658-39.441-13.683-62.763-15.589-22.95-1.875-51.362-1.85-86.677-1.85v32zM938.667 290.133h32c0-35.315 0.026-63.727-1.852-86.676-1.903-23.323-5.931-43.809-15.586-62.762l-57.024 29.055c4.292 8.428 7.241 19.553 8.823 38.919 1.613 19.739 1.638 45.093 1.638 81.464h32zM868.779 99.284l-14.528 28.512c18.061 9.204 32.747 23.89 41.954 41.953l57.024-29.055c-15.343-30.106-39.817-54.583-69.922-69.923l-14.528 28.512zM290.133 85.333v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM85.333 290.133h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM155.223 99.284l-14.528-28.512c-30.106 15.34-54.583 39.817-69.923 69.923l57.024 29.055c9.204-18.063 23.89-32.75 41.953-41.953l-14.528-28.512z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "arrow-up-right-square-1"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1431,
        "id": 589,
        "name": "arrow-up-right-square-1",
        "prevSize": 32,
        "code": 59707
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 60
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ArrowUpRightSquare-1IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ArrowUpRightSquare-1Icon = ({ size = 16, ...props }: ArrowUpRightSquare-1IconProps) => (
  <IcoMoon iconSet={iconSet} icon="arrow-up-right-square-1" size={size} {...props} />
);

ArrowUpRightSquare-1Icon.displayName = 'ArrowUpRightSquare-1Icon';

export default ArrowUpRightSquare-1Icon;
