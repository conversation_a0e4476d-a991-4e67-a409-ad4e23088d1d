// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M379.715 510.908l82.979 87.070c2.782 2.918 7.684 2.953 10.509 0.073l171.081-174.24M395.070 150.15l-65.984-12.76c-21.551-4.167-42.885 8.15-50.051 28.897l-21.941 63.524c-4.422 12.801-14.481 22.861-27.283 27.283l-63.524 21.941c-20.747 7.166-33.064 28.5-28.897 50.051l12.76 65.984c2.572 13.297-1.111 27.039-9.986 37.267l-44.043 50.765c-14.384 16.58-14.384 41.216 0 57.796l44.043 50.765c8.876 10.227 12.558 23.97 9.986 37.265l-12.76 65.984c-4.167 21.551 8.15 42.889 28.897 50.052l63.524 21.943c12.801 4.42 22.861 14.481 27.283 27.281l21.941 63.522c7.166 20.749 28.5 33.067 50.051 28.898l65.984-12.757c13.297-2.573 27.039 1.109 37.267 9.984l50.765 44.045c16.58 14.383 41.216 14.383 57.796 0l50.765-44.045c10.227-8.875 23.97-12.557 37.265-9.984l65.984 12.757c21.551 4.169 42.889-8.149 50.052-28.898l21.943-63.522c4.42-12.8 14.481-22.861 27.281-27.281l63.522-21.943c20.749-7.164 33.067-28.501 28.898-50.052l-12.757-65.984c-2.573-13.295 1.109-27.038 9.984-37.265l44.045-50.765c14.383-16.58 14.383-41.216 0-57.796l-44.045-50.765c-8.875-10.228-12.557-23.969-9.984-37.267l12.757-65.984c4.169-21.551-8.149-42.885-28.898-50.051l-63.522-21.941c-12.8-4.422-22.861-14.481-27.281-27.283l-21.943-63.524c-7.164-20.747-28.501-33.064-50.052-28.897l-65.984 12.76c-13.295 2.572-27.038-1.111-37.265-9.986l-50.765-44.043c-16.58-14.384-41.216-14.384-57.796 0l-50.765 44.043c-10.228 8.876-23.969 12.558-37.267 9.986z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "verify"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1986,
        "id": 34,
        "name": "verify",
        "prevSize": 32,
        "code": 60262
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 615
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface VerifyIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const VerifyIcon = ({ size = 16, ...props }: VerifyIconProps) => (
  <IcoMoon iconSet={iconSet} icon="verify" size={size} {...props} />
);

VerifyIcon.displayName = 'VerifyIcon';

export default VerifyIcon;
