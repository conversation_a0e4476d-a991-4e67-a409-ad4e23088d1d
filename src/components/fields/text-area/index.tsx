import clsx from 'clsx';
import * as React from 'react';

import { textAreaPropDefs } from '@components/fields/text-area/text-area.props';
import { type ComponentPropsWithout, type RemovedProps, extractProps } from '@helpers';
import { type GetPropDefTypes, type MarginProps, marginPropDefs } from '@props';

type TextAreaElement = React.ComponentRef<'textarea'>;
type TextAreaOwnProps = GetPropDefTypes<typeof textAreaPropDefs> & {
  defaultValue?: string;
  value?: string;
};
interface TextAreaProps
  extends ComponentPropsWithout<'textarea', RemovedProps | 'size' | 'value'>,
    MarginProps,
    TextAreaOwnProps {
  label?: React.ReactNode;
  helperText?: React.ReactNode;
  error?: boolean;
}

const TextArea = React.forwardRef<TextAreaElement, TextAreaProps>((props, forwardedRef) => {
  const { label, helperText, error, id, className, color, radius, style, ...textAreaProps } = extractProps(
    props,
    textAreaPropDefs,
    marginPropDefs,
  );

  const inputId = id ?? React.useId();
  const describedById = helperText ? `${inputId}-helper` : undefined;

  return (
    <div className="rt-InputFieldWrapper">
      {label && <label className="rt-InputFieldLabel">{label}</label>}
      <div
        data-accent-color={color}
        data-radius={radius}
        className={clsx('rt-TextAreaRoot', { ['rt-error']: error }, className)}
        style={style}
      >
        <textarea
          className="rt-reset rt-TextAreaInput"
          ref={forwardedRef}
          {...textAreaProps}
          id={inputId}
          aria-invalid={error || undefined}
          aria-describedby={describedById}
        />
      </div>
      {helperText && (
        <div id={describedById} className="rt-InputFieldHelperText">
          {helperText}
        </div>
      )}
    </div>
  );
});
TextArea.displayName = 'Input.TextArea';

export { TextArea };
export type { TextAreaProps };
