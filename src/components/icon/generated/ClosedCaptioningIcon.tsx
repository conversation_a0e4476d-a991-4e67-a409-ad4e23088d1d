// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M455.458 429.449c-9.681-7.432-20.557-13.388-32.282-17.533-12.239-4.326-25.408-6.679-39.127-6.679-64.828 0-117.382 52.556-117.382 117.383s52.553 117.38 117.382 117.38c13.719 0 26.889-2.355 39.127-6.677 11.726-4.147 22.601-10.103 32.282-17.536M757.534 429.449c-9.681-7.432-20.553-13.388-32.282-17.533-12.237-4.326-25.408-6.679-39.125-6.679-64.828 0-117.38 52.556-117.38 117.383s52.553 117.38 117.38 117.38c13.717 0 26.889-2.355 39.125-6.677 11.729-4.147 22.601-10.103 32.282-17.536M290.133 810.667h443.733c71.689 0 107.529 0 134.912-13.952 24.085-12.271 43.665-31.851 55.936-55.936 13.952-27.383 13.952-63.223 13.952-134.912v-187.733c0-71.687 0-107.53-13.952-134.911-12.271-24.085-31.851-43.666-55.936-55.938-27.383-13.951-63.223-13.951-134.912-13.951h-443.733c-71.687 0-107.53 0-134.911 13.951-24.085 12.272-43.666 31.853-55.938 55.938-13.951 27.38-13.951 63.224-13.951 134.911v187.733c0 71.689 0 107.529 13.951 134.912 12.272 24.085 31.853 43.665 55.938 55.936 27.38 13.952 63.224 13.952 134.911 13.952z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "closed-captioning"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1561,
        "id": 459,
        "name": "closed-captioning",
        "prevSize": 32,
        "code": 59837
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 190
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ClosedCaptioningIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ClosedCaptioningIcon = ({ size = 16, ...props }: ClosedCaptioningIconProps) => (
  <IcoMoon iconSet={iconSet} icon="closed-captioning" size={size} {...props} />
);

ClosedCaptioningIcon.displayName = 'ClosedCaptioningIcon';

export default ClosedCaptioningIcon;
