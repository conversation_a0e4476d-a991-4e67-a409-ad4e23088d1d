// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M407.165 488.866c-12.192-12.791-32.448-13.282-45.242-1.088-12.794 12.19-13.282 32.448-1.089 45.239l46.331-44.151zM464.29 595.196v0zM474.462 595.264v0zM662.835 449.088c12.382-12.612 12.194-32.873-0.414-45.255-12.612-12.382-32.875-12.197-45.257 0.414l45.67 44.841zM694.963 829.884v0zM544.576 924.983v0zM479.424 924.983v0zM329.039 829.884v0zM222.197 178.454v0zM341.333 146.959v0zM488.486 91.948v0zM535.514 91.948v0zM682.667 146.959v0zM801.805 178.454v0zM506.176 85.803v0zM517.824 85.803v0zM520.478 937.694v0zM503.522 937.694v0zM180.343 672.486v0zM221.478 749.798v0zM843.657 672.486v0zM802.522 749.798v0zM850.061 208.245v0zM836.574 190.236v0zM173.941 208.245v0zM187.425 190.236v0zM384 510.942l-23.165 22.076 80.292 84.254 46.327-44.151-80.288-84.254-23.165 22.076zM474.462 595.264l22.831 22.421 165.542-168.597-45.67-44.841-165.538 168.599 22.835 22.417zM464.29 595.196l-23.164 22.076c15.194 15.945 40.785 16.081 56.166 0.414l-45.666-44.838c9.911-10.099 26.014-10.027 35.827 0.273l-23.164 22.076zM853.333 246.772h-32v285.444h64v-285.444h-32zM170.667 532.215h32v-285.443h-64v285.443h32zM694.963 829.884l-17.105-27.046-150.383 95.1 34.206 54.089 150.383-95.095-17.101-27.046zM479.424 924.983l17.101-27.046-150.383-95.1-34.206 54.093 150.383 95.095 17.105-27.042zM222.197 178.454l7.327 31.15c35.892-8.442 84.367-20.482 121.4-32.117l-19.182-61.057c-34.647 10.885-81.185 22.48-116.872 30.874l7.327 31.15zM341.333 146.959l9.591 30.528c49.164-15.446 115.345-41.872 149.679-55.923l-24.239-59.232c-34.406 14.081-98.324 39.552-144.623 54.097l9.591 30.529zM535.514 91.948l-12.117 29.616c34.334 14.051 100.514 40.477 149.679 55.923l19.183-61.057c-46.298-14.546-110.217-40.017-144.623-54.097l-12.122 29.616zM682.667 146.959l-9.591 30.528c37.035 11.635 85.508 23.674 121.399 32.117l7.33-31.15 7.326-31.15c-35.686-8.394-82.227-19.989-116.873-30.874l-9.591 30.529zM488.486 91.948l12.117 29.616c4.693-1.921 7.292-2.977 9.263-3.685 1.745-0.628 1.856-0.555 1.289-0.465l-9.958-63.22c-8.926 1.407-17.451 5.119-24.832 8.14l12.122 29.616zM535.514 91.948l12.122-29.616c-7.381-3.021-15.906-6.733-24.832-8.14l-9.958 63.22c-0.567-0.089-0.457-0.163 1.289 0.465 1.971 0.708 4.57 1.764 9.263 3.685l12.117-29.616zM506.176 85.803l4.979 31.61c0.678-0.106 1.011-0.106 1.69 0l9.958-63.22c-7.275-1.146-14.332-1.146-21.606 0l4.979 31.61zM544.576 924.983l-17.101-27.046c-6.259 3.955-9.732 6.135-12.407 7.603-2.355 1.289-2.569 1.156-1.826 0.981l14.473 62.345c12.868-2.987 24.124-10.615 33.967-16.836l-17.105-27.046zM479.424 924.983l-17.105 27.046c9.843 6.221 21.099 13.85 33.967 16.836l14.473-62.345c0.742 0.175 0.529 0.307-1.826-0.981-2.675-1.468-6.148-3.648-12.407-7.603l-17.101 27.046zM520.478 937.694l-7.236-31.172c-0.828 0.192-1.655 0.192-2.483 0l-14.473 62.345c10.347 2.402 21.082 2.402 31.428 0l-7.236-31.172zM170.667 532.215h-32c0 69.986-0.321 111.612 11.024 149.457l61.305-18.377c-8.008-26.714-8.329-57.079-8.329-131.081h-32zM329.039 829.884l17.103-27.046c-59.488-37.615-83.043-52.949-100.017-73.446l-49.294 40.819c24.98 30.165 59.149 51.337 115.106 86.72l17.103-27.046zM180.343 672.486l-30.653 9.186c9.762 32.567 25.777 62.737 47.14 88.538l49.294-40.819c-15.761-19.034-27.766-41.527-35.129-66.095l-30.653 9.19zM853.333 532.215h-32c0 74.001-0.32 104.367-8.329 131.081l61.303 18.377c11.345-37.845 11.025-79.471 11.025-149.457h-32zM694.963 829.884l17.101 27.046c55.957-35.383 90.125-56.555 115.106-86.72l-49.293-40.819c-16.977 20.497-40.529 35.831-100.019 73.446l17.105 27.046zM843.657 672.486l-30.652-9.19c-7.364 24.567-19.366 47.061-35.127 66.095l49.293 40.819c21.363-25.801 37.376-55.97 47.138-88.538l-30.652-9.186zM853.333 246.772h32c0-9.688 0.017-18.531-0.482-25.935-0.521-7.718-1.694-16.022-5.086-24.49l-59.413 23.798c-0.115-0.294 0.346 0.547 0.644 4.99 0.32 4.757 0.337 11.090 0.337 21.637h32zM801.805 178.454l-7.33 31.15c9.822 2.31 15.45 3.655 19.537 4.951 3.708 1.175 3.802 1.623 3.029 1.027l39.070-50.691c-7.505-5.782-15.45-9.027-22.767-11.346-6.942-2.2-15.339-4.152-24.213-6.24l-7.326 31.15zM850.061 208.245l29.705-11.899c-4.975-12.416-12.937-23.196-23.654-31.456l-39.070 50.691c1.314 1.013 2.432 2.362 3.311 4.564l29.709-11.9zM170.667 246.772h32c0-10.547 0.017-16.88 0.337-21.637 0.299-4.443 0.76-5.285 0.643-4.99l-59.411-23.798c-3.392 8.468-4.568 16.772-5.087 24.49-0.498 7.404-0.482 16.247-0.482 25.935h32zM222.197 178.454l-7.327-31.15c-8.877 2.088-17.272 4.041-24.214 6.24-7.32 2.319-15.264 5.564-22.767 11.346l39.070 50.691c-0.774 0.597-0.681 0.149 3.030-1.027 4.088-1.296 9.715-2.641 19.535-4.951l-7.327-31.15zM173.941 208.245l29.705 11.899c0.881-2.201 1.999-3.551 3.313-4.564l-39.070-50.691c-10.717 8.26-18.681 19.040-23.654 31.456l29.705 11.899z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "shield-check"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1882,
        "id": 138,
        "name": "shield-check",
        "prevSize": 32,
        "code": 60158
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 511
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ShieldCheckIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ShieldCheckIcon = ({ size = 16, ...props }: ShieldCheckIconProps) => (
  <IcoMoon iconSet={iconSet} icon="shield-check" size={size} {...props} />
);

ShieldCheckIcon.displayName = 'ShieldCheckIcon';

export default ShieldCheckIcon;
