// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M675.273 132.741c76.352 0 138.249 61.906 138.249 138.272s-61.897 138.272-138.249 138.272c-7.189 0-14.251-0.549-21.146-1.607M715.836 525.606c12.71 0.491 25.532 1.207 38.566 2.155 94.494 6.874 167.070 83.546 180.608 177.335l2.633 18.244c7.799 54.054-29.747 104.508-84.045 110.332-5.961 0.64-11.87 1.25-17.724 1.83M567.262 251.259c0 91.638-74.274 165.926-165.899 165.926-91.623 0-165.898-74.288-165.898-165.926s74.275-165.926 165.898-165.926c91.625 0 165.899 74.288 165.899 165.926zM187.651 926.327c153.143 16.337 274.787 16.525 427.7 0.124 65.156-6.985 110.212-67.533 100.851-132.399l-3.157-21.892c-16.243-112.55-103.334-204.553-216.73-212.8-64.691-4.706-124.983-4.698-189.798 0.026-113.442 8.265-200.597 100.275-216.846 212.873l-3.109 21.542c-9.375 64.964 35.829 125.564 101.087 132.527z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "user-group"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1978,
        "id": 42,
        "name": "user-group",
        "prevSize": 32,
        "code": 60254
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 607
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface UserGroupIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const UserGroupIcon = ({ size = 16, ...props }: UserGroupIconProps) => (
  <IcoMoon iconSet={iconSet} icon="user-group" size={size} {...props} />
);

UserGroupIcon.displayName = 'UserGroupIcon';

export default UserGroupIcon;
