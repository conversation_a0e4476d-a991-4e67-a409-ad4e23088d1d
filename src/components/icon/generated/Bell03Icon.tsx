// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M343.45 837.141c19.274 47.006 66.241 80.192 121.122 80.192 72.060 0 130.475-57.216 130.475-127.791 0-2.615-0.081-5.214-0.239-7.791M343.45 837.141l-78.839 17.374c-41.507 9.148-82.726-16.397-92.065-57.050-4.83-21.026-0.249-43.072 12.587-60.574l4.686-6.391c8.756-11.942 14.497-25.745 16.75-40.282l9.821-333.573c8.068-52.058 47.909-93.94 100.358-105.497l165.489-36.468c52.45-11.558 106.765 9.576 136.819 53.233l154.743 297.309c8.388 12.19 19.614 22.259 32.747 29.376l7.031 3.806c19.251 10.436 33.033 28.433 37.862 49.459 9.34 40.657-16.742 81.028-58.249 90.172l-198.383 43.716M343.45 837.141l251.357-55.39M321.526 184.602c7.908 34.428 42.815 56.058 77.966 48.313 35.149-7.746 57.233-41.935 49.327-76.363-7.91-34.428-42.816-56.058-77.967-48.312s-57.235 41.935-49.327 76.363z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bell-03"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1464,
        "id": 556,
        "name": "bell-03",
        "prevSize": 32,
        "code": 59740
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 93
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface Bell03IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const Bell03Icon = ({ size = 16, ...props }: Bell03IconProps) => (
  <IcoMoon iconSet={iconSet} icon="bell-03" size={size} {...props} />
);

Bell03Icon.displayName = 'Bell03Icon';

export default Bell03Icon;
