/// <reference types="vitest/config" />
import react from '@vitejs/plugin-react-swc';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import { visualizer } from 'rollup-plugin-visualizer';
import { type UserConfig, defineConfig } from 'vite';
import dts from 'vite-plugin-dts';
import { viteStaticCopy } from 'vite-plugin-static-copy';

const dirname = typeof __dirname !== 'undefined' ? __dirname : path.dirname(fileURLToPath(import.meta.url));

const externals = [
  /^react(-dom)?$/, // react, react-dom
  /^react\/jsx-runtime$/, // react/jsx-runtime
  /^i18next$/, // i18next
  /^react-i18next$/, // react-i18next
  /^i18next-browser-languagedetector$/, // i18next-browser-languagedetector
  /^i18next-http-backend$/, // i18next-http-backend
  /^i18next-icu$/, // i18next-icu
  /^i18next-parser$/, // i18next-parser
  /^clsx$/, // clsx
  /^react-icomoon$/, // react-icomoon
  /^react-remove-scroll-bar$/, // react-remove-scroll-bar
  /^@radix-ui\/.*$/, // All Radix UI packages
  /^radix-ui$/, // radix-ui
];

export default defineConfig((env) => {
  const configs = {
    plugins: [
      react(),
      dts({
        tsconfigPath: './tsconfig.json',
        entryRoot: 'src',
        outDir: 'dist',
        copyDtsFiles: true,
        exclude: ['src/stories/**/*', '**/*.stories.tsx', '**/*.stories.ts', 'src/vite-env.d.ts'],
      }),
      viteStaticCopy({
        targets: [
          {
            src: 'src/styles/fonts/sf_pro',
            dest: 'fonts',
          },
          {
            src: 'styles.css',
            dest: '.',
          },
        ],
      }),
    ],

    resolve: {
      alias: {
        '@base-components': path.resolve(dirname, 'src/base-components'),
        '@components': path.resolve(dirname, 'src/components'),
        '@helpers': path.resolve(dirname, 'src/helpers'),
        '@props': path.resolve(dirname, 'src/props'),
      },
    },

    build: {
      lib: {
        entry: path.resolve(dirname, 'src/index.ts'),
        fileName: (format, entryName) => {
          const ext = format === 'es' ? 'js' : 'cjs';
          return entryName === 'index' ? `index.${ext}` : `${entryName}/index.${ext}`;
        },
        formats: ['es', 'cjs'],
      },
      rollupOptions: {
        external: (id) => externals.some((ext) => ext.test(id)) || id.includes('src/stories'),
        input: {
          index: path.resolve(dirname, 'src/index.ts'),
          components: path.resolve(dirname, 'src/components/index.ts'),
        },
        output: {
          globals: {
            react: 'React',
            'react-dom': 'ReactDOM',
            'react/jsx-runtime': 'jsxRuntime',
          },
          // Enable code splitting for better tree-shaking
          manualChunks: (id) => {
            // Put each icon in its own chunk for optimal loading
            if (id.includes('src/components/icon/icons/')) {
              const iconName = id.split('/').pop()?.replace('.json', '');
              return `icons/${iconName}`;
            }
            return undefined;
          },
        },
        treeshake: {
          moduleSideEffects: false,
          propertyReadSideEffects: false,
          // More aggressive tree-shaking
          unknownGlobalSideEffects: false,
        },
      },
      minify: true,
      sourcemap: true,
      emptyOutDir: true,
      // Optimize chunk size
      chunkSizeWarningLimit: 1000,
    },
  } as UserConfig;
  if (env.mode === 'development')
    configs.plugins?.push(
      visualizer({
        filename: 'dist/bundle-analysis.html',
        open: false,
        gzipSize: true,
        brotliSize: true,
      }),
    );
  return configs;
});
