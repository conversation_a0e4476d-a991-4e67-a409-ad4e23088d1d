// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 596.809v213.858M180.148 596.809v213.858M85.333 484.019l23.194-66.217c14.46-41.281 55.055-69.125 100.78-69.125 40.238 0 77.022-21.634 95.017-55.883l41.749-79.46M450.726 292.284c0 0-9.839 45.114-50.573 90.229-40.735 45.114-91.301 56.395-131.538 56.395s-47.037 17.412-66.392 45.111M274.963 605.828v195.814c0 4.983 4.245 9.024 9.482 9.024h644.742c5.235 0 9.481-4.041 9.481-9.024v-195.814c0-4.983-4.245-9.020-9.481-9.020h-644.742c-5.236 0-9.482 4.036-9.482 9.020z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "smoke"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1905,
        "id": 115,
        "name": "smoke",
        "prevSize": 32,
        "code": 60181
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 534
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SmokeIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SmokeIcon = ({ size = 16, ...props }: SmokeIconProps) => (
  <IcoMoon iconSet={iconSet} icon="smoke" size={size} {...props} />
);

SmokeIcon.displayName = 'SmokeIcon';

export default SmokeIcon;
