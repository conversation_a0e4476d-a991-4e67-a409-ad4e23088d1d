.rt-RadioCardsRoot {
  line-height: var(--line-height);
  letter-spacing: var(--letter-spacing);
  cursor: default;
  &.rt-horizontal {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  }
}

.rt-RadioCardsItem {
  --base-card-padding-top: var(--radio-cards-item-padding-y);
  --base-card-padding-right: var(--radio-cards-item-padding-x);
  --base-card-padding-bottom: var(--radio-cards-item-padding-y);
  --base-card-padding-left: var(--radio-cards-item-padding-x);
  --base-card-border-radius: var(--radio-cards-item-border-radius);
  --base-card-border-width: var(--radio-cards-item-border-width);

  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spatial-2);

  & > * {
    /* Avoid unintentional drag interactions (e.g. on images) */
    pointer-events: none;
  }
  /* stylelint-disable-next-line selector-max-type */
  & > :where(svg) {
    /* Don’t squish icons */
    flex-shrink: 0;
  }
  &::after {
    outline-offset: -1px;
  }
}

/* * * * * * * * * * * * * * * * * * * */
/*                                     */
/*                Sizes                */
/*                                     */
/* * * * * * * * * * * * * * * * * * * */

@breakpoints {
  .rt-RadioCardsRoot {
    /* 40px height for the card with one line of text */
    &:where(.rt-r-size-1) {
      font-size: var(--font-size-2);
      --line-height: var(--line-height-2);
      --letter-spacing: var(--letter-spacing-2);
      --radio-cards-item-padding-x: var(--spatial-3);
      --radio-cards-item-padding-y: calc(var(--spatial-3) / 1.2);
      --radio-cards-item-border-radius: var(--radius-3);
    }
    /* 48px height for the card with one line of text */
    &:where(.rt-r-size-2) {
      font-size: var(--font-size-2);
      --line-height: var(--line-height-2);
      --letter-spacing: var(--letter-spacing-2);
      --radio-cards-item-padding-x: var(--spatial-4);
      --radio-cards-item-padding-y: calc(var(--spatial-4) * 0.875);
      --radio-cards-item-border-radius: var(--radius-3);
    }
    /* 64px height for the card with one line of text */
    &:where(.rt-r-size-3) {
      font-size: var(--font-size-3);
      --line-height: var(--line-height-3);
      --letter-spacing: var(--letter-spacing-3);
      --radio-cards-item-padding-x: var(--spatial-5);
      --radio-cards-item-padding-y: calc(var(--spatial-5) / 1.2);
      --radio-cards-item-border-radius: var(--radius-4);
    }
  }
}

/* * * * * * * * * * * * * * * * * * * */
/*                                     */
/*         Variants / Surface          */
/*                                     */
/* * * * * * * * * * * * * * * * * * * */

:where(.rt-RadioCardsRoot.rt-variant-surface) {
  .rt-RadioCardsItem {
    --radio-cards-item-border-width: 1px;
    --radio-cards-item-background-color: var(--background-primary-base);

    &::before {
      background-color: var(--radio-cards-item-background-color);
    }
    &::after {
      box-shadow: var(--base-card-surface-box-shadow);
    }
    @media (hover: hover) {
      &:where(:not(:disabled):not([data-state='checked']):hover) {
        &::after {
          box-shadow: var(--base-card-surface-hover-box-shadow);
        }
      }
    }
  }
}

/* * * * * * * * * * * * * * * * * * * */
/*                                     */
/*         Variants / Classic          */
/*                                     */
/* * * * * * * * * * * * * * * * * * * */

:where(.rt-RadioCardsRoot.rt-variant-classic) {
  .rt-RadioCardsItem {
    --radio-cards-item-border-width: 1px;
    --radio-cards-item-background-color: var(--background-primary-base);

    transition: box-shadow 120ms;
    box-shadow: var(--base-card-classic-box-shadow-outer);

    &::before {
      background-color: var(--radio-cards-item-background-color);
    }
    &::after {
      box-shadow: var(--base-card-classic-box-shadow-inner);
    }
    @media (hover: hover) {
      &:where(:not(:disabled):not([data-state='checked']):hover) {
        transition-duration: 40ms;
        box-shadow: var(--base-card-classic-hover-box-shadow-outer);
        &::after {
          box-shadow: var(--base-card-classic-hover-box-shadow-inner);
        }
      }
    }
  }
}

/* * * * * * * * * * * * * * * * * * * */
/*                                     */
/*             All checked             */
/*                                     */
/* * * * * * * * * * * * * * * * * * * */

.rt-RadioCardsItem:where([data-state='checked']) {
  &::after {
    outline: 2px solid var(--brand-accent);
  }
  :where(.rt-RadioCardsRoot.rt-high-contrast) & {
    &::after {
      outline-color: var(--brand-accent-40);
    }
  }
}

/* * * * * * * * * * * * * * * * * * * */
/*                                     */
/*              All focus              */
/*                                     */
/* * * * * * * * * * * * * * * * * * * */

.rt-RadioCardsItem:where(:focus-visible) {
  &::after {
    outline: 2px solid var(--brand-accent-40);
  }
  &:where([data-state='checked']) {
    &::before {
      background-image: linear-gradient(rgba(var(--brand-accent), 0.3), rgba(var(--brand-accent), 0.3));
    }
    &::after {
      outline-color: var(--brand-accent);
    }
  }
}

/* * * * * * * * * * * * * * * * * * * */
/*                                     */
/*            All disabled             */
/*                                     */
/* * * * * * * * * * * * * * * * * * * */

.rt-RadioCardsItem:where(:disabled) {
  cursor: var(--cursor-disabled);
  color: var(--separators-non-opaque);

  &::selection {
    background-color: var(--labels-tertiary);
  }
  &::before {
    background-image: linear-gradient(var(--fills-tertiary), var(--fills-tertiary));
  }
  &::after {
    outline-color: var(--brand-gray800);
  }
}
