import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';

import { type MenuItem, Sidebar, sidebarMenuItemPropDefs, sidebarPropDefs } from '@components/sidebar';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: 'DesignSystem/Sidebar',
  component: Sidebar,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'left',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  // tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    type: {
      control: 'inline-radio',
      options: sidebarPropDefs.type.values,
    },
    variant: {
      control: 'inline-radio',
      options: sidebarMenuItemPropDefs.variant.values,
    },
    side: {
      control: 'inline-radio',
      options: sidebarPropDefs.side.values,
    },
    collapsible: {
      control: 'inline-radio',
      options: sidebarPropDefs.collapsible.values,
    },
    size: {
      control: 'inline-radio',
      options: sidebarMenuItemPropDefs.size.values,
    },
  },
} satisfies Meta<typeof Sidebar>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockData: Array<MenuItem> = [
  {
    title: 'Home',
    key: 'group1',
    path: '#',
    type: 'group',
    children: [
      {
        title: 'Submenu 1',
        key: 'submenu1',
        path: '#',
        icon: 'airdrop',
      },
      {
        title: 'Submenu 2',
        key: 'submenu2',
        path: '#',
        icon: 'DNA',
        children: [
          {
            title: 'Submenu 3',
            key: 'submenu3',
            path: '#',
            icon: 'airplane',
          },

          {
            title: 'Submenu 5',
            key: 'submenu5',
            path: '#',
            icon: 'activity',
            children: [
              {
                title: 'Submenu 6',
                key: 'submenu6',
                path: '#',
                icon: 'home-01',
              },
            ],
          },
        ],
      },
      {
        title: 'Submenu 4',
        key: 'submenu4',
        path: '#',
        icon: 'home-02',
      },
    ],
  },
  {
    title: 'Group2',
    key: 'group2',
    path: '#',
    type: 'group',
    children: [
      {
        title: 'Submenu 1',
        key: 'submenu21',
        path: '#',
        icon: 'home-02',
      },
      {
        title: 'Submenu 2',
        key: 'submenu22',
        path: '#',
        icon: 'home-03',
      },
    ],
  },
  {
    title: 'Dashboard',
    key: 'dashboard',
    path: '#',
    icon: 'camera',
  },
  {
    title: 'Settings',
    key: 'settings',
    path: '#',
    icon: 'app',
  },
  {
    title: 'Settings 2',
    key: 'setting2',
    path: '#',
    icon: 'airdrop',
  },
  {
    title: 'Settings 3',
    key: 'setting3',
    path: '#',
    icon: 'settings-01',
  },
  {
    title: 'Settings 4',
    key: 'setting4',
    path: '#',
    icon: 'settings-02',
  },
];

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args

const flatMenus: MenuItem[] = [
  {
    title: 'Dashboard',
    key: 'dashboard',
    path: '#',
    icon: 'camera',
  },
  {
    title: 'Settings',
    key: 'settings',
    path: '#',
    icon: 'app',
  },
  {
    title: 'Settings 2',
    key: 'setting2',
    path: '#',
    icon: 'airdrop',
  },
  {
    title: 'Settings 3',
    key: 'setting3',
    path: '#',
    icon: 'settings-01',
  },
  {
    title: 'Settings 4',
    key: 'setting4',
    path: '#',
    icon: 'settings-02',
  },
];

export const _flatSidebar: Story = {
  args: {
    data: flatMenus,
    collapsible: 'icon',
    type: 'sidebar',
  },
};
export const _treeSidebar: Story = {
  args: {
    data: mockData,
    collapsible: 'icon',
    type: 'sidebar',
    defaultOpenKeys: ['group1', 'submenu2'],
  },
};
