// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M938.667 384v-85.333c0-70.692-57.306-128-128-128h-597.333c-70.692 0-128 57.308-128 128v85.333M938.667 384v341.333c0 70.694-57.306 128-128 128h-597.333c-70.692 0-128-57.306-128-128v-341.333M938.667 384h-853.333M234.667 704h106.667M469.333 704h213.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "card"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1529,
        "id": 491,
        "name": "card",
        "prevSize": 32,
        "code": 59805
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 158
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CardIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CardIcon = ({ size = 16, ...props }: CardIconProps) => (
  <IcoMoon iconSet={iconSet} icon="card" size={size} {...props} />
);

CardIcon.displayName = 'CardIcon';

export default CardIcon;
