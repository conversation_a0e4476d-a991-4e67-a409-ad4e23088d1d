import { Input as InternalInput, Password as InternalPassword, Slot } from './input';
import { OTPInput } from './otp';
import { TextArea } from './text-area';

type CompoundedComponent = typeof InternalInput & {
  Slot: typeof Slot;
  TextArea: typeof TextArea;
  OTP: typeof OTPInput;
  Password: typeof InternalPassword;
};

const Input = InternalInput as CompoundedComponent;

Input.Slot = Slot;
Input.TextArea = TextArea;
Input.OTP = OTPInput;
Input.Password = InternalPassword;

export { Input };

export type { InputProps, PasswordProps, SlotProps } from './input';
export type { OTPInputProps } from './otp';
export * as Select from './select';
export type { TextAreaProps } from './text-area';
