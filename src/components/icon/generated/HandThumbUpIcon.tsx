// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M229.32 856.307h107.269c12.57 0 25.057 2.189 36.973 6.473l160.010 57.566c11.917 4.284 24.401 6.473 36.975 6.473h172.898c42.769 0 82.453-25.062 101.269-66.206 59.046-129.118 83.866-226.287 93.559-348.215 5.504-69.193-47.778-126.151-112.597-126.151h-165.227M660.45 386.246h-65.331M660.45 386.246c35.644-76.935 42.121-59.43 55.232-153.579 6.699-48.119-18.556-94.708-60.25-113.993l-40.201-18.593c-20.514-9.487-44.19 5.454-48.841 28.865-9.63 48.455-31.684 118.859-80.158 139.785-108.887 47.006-146.246 109.571-233.356 203.581M115.442 427.652l121.663-21.723c5.22-0.932 10.121 3.041 10.724 8.674 19.382 181.066 0.048 296.002-23.076 477.113-0.71 5.564-5.576 9.451-10.746 8.529l-120.909-21.589c-5.004-0.892-8.406-5.973-7.662-11.349 22.978-165.948 29.054-263.121 22.246-429.321-0.207-5.039 3.12-9.506 7.761-10.334z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "hand-thumb-up"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1699,
        "id": 321,
        "name": "hand-thumb-up",
        "prevSize": 32,
        "code": 59975
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 328
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface HandThumbUpIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const HandThumbUpIcon = ({ size = 16, ...props }: HandThumbUpIconProps) => (
  <IcoMoon iconSet={iconSet} icon="hand-thumb-up" size={size} {...props} />
);

HandThumbUpIcon.displayName = 'HandThumbUpIcon';

export default HandThumbUpIcon;
