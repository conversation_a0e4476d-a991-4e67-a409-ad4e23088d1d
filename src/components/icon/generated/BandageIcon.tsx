// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 206.060l-57.365-57.364c-84.482-84.483-221.456-84.483-305.939-0s-84.483 221.457 0 305.939l57.364 57.365M512 206.060l57.365-57.364c84.48-84.483 221.457-84.483 305.937 0 84.484 84.483 84.484 221.457 0 305.939l-57.361 57.365M512 206.060l-305.94 305.94M817.941 512l57.361 57.365c84.484 84.48 84.484 221.457 0 305.937-84.48 84.484-221.457 84.484-305.937 0l-57.365-57.361M817.941 512l-305.941 305.941M512 817.941l-57.365 57.361c-84.482 84.484-221.456 84.484-305.939 0-84.483-84.48-84.483-221.457 0-305.937l57.364-57.365M512 397.272l0.384 0.382M397.272 512l0.382 0.384M626.726 512l0.384 0.384M512 626.726l0.384 0.384"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bandage"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1450,
        "id": 570,
        "name": "bandage",
        "prevSize": 32,
        "code": 59726
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 79
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BandageIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BandageIcon = ({ size = 16, ...props }: BandageIconProps) => (
  <IcoMoon iconSet={iconSet} icon="bandage" size={size} {...props} />
);

BandageIcon.displayName = 'BandageIcon';

export default BandageIcon;
