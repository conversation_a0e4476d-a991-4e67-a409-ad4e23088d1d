// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M714.667 85.333h213.44c2.918 0 5.555 1.182 7.467 3.093M938.667 309.333v-213.44c0-2.916-1.182-5.556-3.093-7.467M935.573 88.426l-295.573 295.574M400.166 295.89l-138.252-188.525c-22.143-30.194-67.566-29.178-88.335 1.977l-52.25 78.375c-37.004 55.506-49.969 123.059-17.351 181.251 37.419 66.758 109.12 168.603 245.786 305.269s238.512 208.367 305.268 245.786c58.193 32.619 125.747 19.652 181.252-17.348l78.374-52.25c31.155-20.77 32.171-66.193 1.975-88.337l-188.523-138.253c-19.204-14.080-46.272-9.293-59.482 10.517-30.835 46.255-88.422 65.097-134.276 33.668-27.639-18.944-60.74-45.299-96.909-81.463-36.166-36.169-62.518-69.269-81.463-96.909-31.427-45.854-12.587-103.44 33.666-134.275 19.814-13.209 24.6-40.279 10.519-59.482z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "call-outgoing"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1507,
        "id": 513,
        "name": "call-outgoing",
        "prevSize": 32,
        "code": 59783
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 136
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CallOutgoingIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CallOutgoingIcon = ({ size = 16, ...props }: CallOutgoingIconProps) => (
  <IcoMoon iconSet={iconSet} icon="call-outgoing" size={size} {...props} />
);

CallOutgoingIcon.displayName = 'CallOutgoingIcon';

export default CallOutgoingIcon;
