// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M490.671 320.727c7.198-0.478 14.319-0.727 21.333-0.727s14.131 0.249 21.333 0.727M395.153 337.859c-107.802 29.501-236.29 82.195-284.032 110.141-16.235 9.502-25.788 26.982-25.788 45.794 0 30.455 24.687 55.142 55.14 55.142l271.765-15.603M628.851 337.859c107.806 29.501 236.284 82.195 284.028 110.141 16.235 9.502 25.788 26.982 25.788 45.794 0 30.455-24.687 55.142-55.138 55.142l-271.761-15.603M587.866 804.245l-6.238 70.669c-3.183 36.083-33.404 63.753-69.623 63.753-36.224 0-66.441-27.669-69.628-63.753l-6.234-70.669M587.866 804.245c48.196 16.627 94.391 43.191 119.036 58.5 11.716 7.275 18.436 20.156 18.436 33.95 0 23.181-18.795 41.971-41.975 41.971h-342.719c-23.181 0-41.973-18.79-41.973-41.971 0-13.794 6.718-26.675 18.434-33.95 24.644-15.309 70.842-41.873 119.039-58.5M587.866 804.245l23.902-270.912M436.143 804.245l-23.904-270.912M412.239 533.333l-18.48-209.445c-6.397-72.497 0.598-150.431 51.127-202.809 19.516-20.229 42.505-35.746 67.119-35.746s47.603 15.517 67.119 35.746c50.526 52.378 57.523 130.312 51.127 202.809l-18.483 209.445"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "airplane"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1383,
        "id": 637,
        "name": "airplane",
        "prevSize": 32,
        "code": 59659
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 12
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface AirplaneIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const AirplaneIcon = ({ size = 16, ...props }: AirplaneIconProps) => (
  <IcoMoon iconSet={iconSet} icon="airplane" size={size} {...props} />
);

AirplaneIcon.displayName = 'AirplaneIcon';

export default AirplaneIcon;
