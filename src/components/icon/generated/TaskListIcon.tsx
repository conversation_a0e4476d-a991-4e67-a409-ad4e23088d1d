// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M620.497 830.199c-12.19-12.791-32.448-13.282-45.239-1.088-12.796 12.19-13.282 32.448-1.088 45.239l46.327-44.151zM677.623 936.529v0zM687.795 936.597v0zM876.169 790.421c12.382-12.612 12.194-32.875-0.414-45.257-12.612-12.382-32.875-12.194-45.257 0.414l45.67 44.843zM682.667 149.333l32 0.119v-0.119h-32zM341.333 149.333h-32l0.001 0.119 32-0.119zM257.457 922.347v0zM208.32 873.212v0zM815.68 214.79v0zM766.545 165.654v0zM208.32 214.79v0zM257.457 165.654v0zM469.333 970.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM800 640c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM341.333 352c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM682.667 416c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM341.333 501.333c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM682.667 565.333c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM341.333 650.667c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM512 714.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM597.333 852.275l-23.164 22.076 80.29 84.254 46.327-44.151-80.29-84.254-23.164 22.076zM687.795 936.597l22.831 22.421 165.542-168.597-45.67-44.843-165.538 168.602 22.835 22.417zM677.623 936.529l-23.164 22.076c15.194 15.945 40.785 16.081 56.166 0.414l-45.666-44.838c9.911-10.099 26.014-10.027 35.827 0.273l-23.164 22.076zM405.333 85.333v32h213.333v-64h-213.333v32zM618.667 213.333v-32h-213.333v64h213.333v-32zM618.667 85.333v32c17.673 0 32 14.327 32 32h64c0-53.019-42.982-96-96-96v32zM405.333 85.333v-32c-53.019 0-96 42.981-96 96h64c0-17.673 14.327-32 32-32v-32zM192 727.467h32v-366.933h-64v366.933h32zM403.2 938.667v-32c-39.441 0-66.935-0.030-88.276-1.916-20.918-1.847-32.843-5.291-41.832-10.325l-31.272 55.842c20.153 11.285 42.233 16.004 67.468 18.236 24.812 2.193 55.618 2.163 93.911 2.163v-32zM192 727.467h-32c0 38.293-0.029 69.099 2.164 93.914 2.231 25.233 6.95 47.313 18.236 67.465l55.84-31.27c-5.034-8.99-8.475-20.915-10.324-41.83-1.887-21.342-1.916-48.836-1.916-88.277h-32zM257.457 922.347l15.636-27.921c-15.447-8.649-28.201-21.406-36.852-36.851l-55.84 31.27c14.418 25.745 35.675 47.002 61.42 61.423l15.636-27.921zM832 360.533h32c0-38.293 0.030-69.099-2.163-93.911-2.231-25.235-6.95-47.315-18.236-67.468l-55.842 31.272c5.035 8.989 8.478 20.914 10.325 41.832 1.886 21.34 1.916 48.835 1.916 88.276h32zM766.545 165.654l-15.637 27.92c15.445 8.651 28.203 21.405 36.851 36.852l55.842-31.272c-14.421-25.745-35.678-47.002-61.423-61.42l-15.633 27.92zM192 360.533h32c0-39.441 0.029-66.935 1.916-88.276 1.849-20.918 5.29-32.843 10.324-41.832l-55.84-31.272c-11.286 20.153-16.006 42.233-18.236 67.468-2.193 24.812-2.164 55.618-2.164 93.911h32zM257.457 165.654l-15.636-27.92c-25.745 14.418-47.002 35.675-61.42 61.42l55.84 31.272c8.651-15.447 21.405-28.201 36.852-36.852l-15.636-27.92zM341.335 149.817l-0.885-31.988c-39.314 1.088-71.101 4.489-98.629 19.905l31.272 55.84c12.72-7.123 30.455-10.698 69.128-11.769l-0.885-31.988zM405.333 213.333v-32c-17.591 0-31.869-14.197-31.999-31.753l-63.998 0.474c0.39 52.691 43.219 95.279 95.997 95.279v-32zM341.335 149.817l32-0.119-0.002-0.484-63.999 0.237 0.002 0.483 32-0.118zM682.667 149.817l-0.887 31.988c38.673 1.071 56.41 4.646 69.129 11.769l31.27-55.84c-27.529-15.416-59.315-18.816-98.628-19.905l-0.883 31.988zM682.667 149.333l-32-0.119v0.484l64 0.237v-0.483l-32-0.119zM682.667 149.817l-32-0.237c-0.132 17.556-14.409 31.753-32 31.753v64c52.779 0 95.607-42.589 95.996-95.279l-31.996-0.237zM469.333 938.667v-32h-66.133v64h66.133v-32zM832 360.533h-32v279.467h64v-279.467h-32zM341.333 384v32h341.333v-64h-341.333v32zM341.333 533.333v32h341.333v-64h-341.333v32zM341.333 682.667v32h170.667v-64h-170.667v32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "task-list"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1931,
        "id": 89,
        "name": "task-list",
        "prevSize": 32,
        "code": 60207
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 560
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface TaskListIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const TaskListIcon = ({ size = 16, ...props }: TaskListIconProps) => (
  <IcoMoon iconSet={iconSet} icon="task-list" size={size} {...props} />
);

TaskListIcon.displayName = 'TaskListIcon';

export default TaskListIcon;
