// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M649.481 827.409c0 37.884-31.838 68.591-71.108 68.591-39.275 0-71.113-30.707-71.113-68.591s31.838-68.591 71.113-68.591c39.27 0 71.108 30.707 71.108 68.591zM649.481 827.409v-236.352M933.926 827.409c0 37.884-31.838 68.591-71.113 68.591s-71.108-30.707-71.108-68.591c0-37.884 31.834-68.591 71.108-68.591s71.113 30.707 71.113 68.591zM933.926 827.409v-213.76M649.481 591.057v-44.484c0-33.438 29.632-59.738 64.183-56.96l165.922 13.338c30.716 2.47 54.34 27.234 54.34 56.96v53.739M649.481 591.057l284.446 22.592M85.333 128h853.333M85.333 353.882h853.333M85.333 579.763h374.519M85.333 805.649h237.037"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "music-filter"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1794,
        "id": 226,
        "name": "music-filter",
        "prevSize": 32,
        "code": 60070
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 423
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MusicFilterIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MusicFilterIcon = ({ size = 16, ...props }: MusicFilterIconProps) => (
  <IcoMoon iconSet={iconSet} icon="music-filter" size={size} {...props} />
);

MusicFilterIcon.displayName = 'MusicFilterIcon';

export default MusicFilterIcon;
