// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M618.667 640l-149.333 64M938.667 512c0 235.639-191.027 426.667-426.667 426.667-235.642 0-426.667-191.027-426.667-426.667 0-235.642 191.025-426.667 426.667-426.667 235.639 0 426.667 191.025 426.667 426.667zM704 416c0 29.457-23.876 53.333-53.333 53.333s-53.333-23.876-53.333-53.333c0-29.455 23.876-53.333 53.333-53.333s53.333 23.878 53.333 53.333zM426.667 416c0 29.457-23.878 53.333-53.333 53.333s-53.333-23.876-53.333-53.333c0-29.455 23.878-53.333 53.333-53.333s53.333 23.878 53.333 53.333z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "confused"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1582,
        "id": 438,
        "name": "confused",
        "prevSize": 32,
        "code": 59858
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 211
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ConfusedIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ConfusedIcon = ({ size = 16, ...props }: ConfusedIconProps) => (
  <IcoMoon iconSet={iconSet} icon="confused" size={size} {...props} />
);

ConfusedIcon.displayName = 'ConfusedIcon';

export default ConfusedIcon;
