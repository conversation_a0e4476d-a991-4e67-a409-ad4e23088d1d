// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M279.517 414.486c-17.107 0.134-34.265 0.848-51.961 2.142-69.31 5.069-129.613 43.050-139.54 112.234l-1.931 13.457c-5.722 39.876 21.817 77.094 61.641 81.391 41.367 4.459 78.991 6.925 116.196 7.415M739.191 414.463c18.863 0.015 37.73 0.737 57.25 2.165 69.312 5.069 129.613 43.050 139.541 112.234l1.933 13.457c5.722 39.876-21.82 77.094-61.641 81.391-44.19 4.766-84.105 7.253-123.806 7.488M618.607 450.684c0 62.208-50.133 112.64-111.979 112.64s-111.983-50.432-111.983-112.64c0-62.21 50.138-112.64 111.983-112.64s111.979 50.43 111.979 112.64zM367.726 908.954c103.374 11.093 185.482 11.221 288.701 0.090 43.981-4.745 74.389-45.845 68.075-89.882l-2.133-14.861c-10.965-76.407-69.751-138.863-146.295-144.461-43.665-3.196-84.361-3.191-128.111 0.017-76.574 5.611-135.404 68.070-146.371 144.508l-2.098 14.626c-6.328 44.1 24.185 85.239 68.234 89.963zM181.962 208.662c0 56.331 45.398 101.996 101.399 101.996s101.4-45.665 101.4-101.996c0-56.331-45.398-101.996-101.4-101.996s-101.399 45.665-101.399 101.996zM842.035 208.662c0 56.331-45.397 101.996-101.402 101.996-56 0-101.397-45.665-101.397-101.996s45.397-101.996 101.397-101.996c56.004 0 101.402 45.665 101.402 101.996z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "user-group-alt2"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1977,
        "id": 43,
        "name": "user-group-alt2",
        "prevSize": 32,
        "code": 60253
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 606
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface UserGroupAlt2IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const UserGroupAlt2Icon = ({ size = 16, ...props }: UserGroupAlt2IconProps) => (
  <IcoMoon iconSet={iconSet} icon="user-group-alt2" size={size} {...props} />
);

UserGroupAlt2Icon.displayName = 'UserGroupAlt2Icon';

export default UserGroupAlt2Icon;
