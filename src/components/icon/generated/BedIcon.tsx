// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M180.148 796.902l19.215 80.333c2.638 11.029 12.119 18.765 22.996 18.765h34.097c10.877 0 20.358-7.735 22.996-18.765l19.215-80.333M725.333 796.902l19.217 80.333c2.637 11.029 12.117 18.765 22.993 18.765h34.099c10.876 0 20.356-7.735 22.993-18.765l19.217-80.333M180.148 474.837v-297.289M180.148 177.548v-49.548M180.148 177.548h663.705M843.853 177.548v-49.548M843.853 177.548v297.289M85.333 685.419v37.163c0 41.045 31.837 74.321 71.111 74.321h711.11c39.275 0 71.113-33.276 71.113-74.321v-37.163M85.333 685.419v-86.707c0-68.416 53.062-123.874 118.519-123.874h616.295c65.455 0 118.519 55.462 118.519 123.874v86.707M85.333 685.419h853.333M349.037 350.968h31.735c40.91 0 74.076 34.662 74.076 77.418 0 25.655-19.9 46.451-44.446 46.451h-90.994c-24.546 0-44.445-20.796-44.445-46.451 0-42.757 33.164-77.418 74.074-77.418zM643.439 350.968h31.731c40.913 0 74.078 34.662 74.078 77.418 0 25.655-19.9 46.451-44.446 46.451h-90.995c-24.546 0-44.446-20.796-44.446-46.451 0-42.757 33.165-77.418 74.078-77.418z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bed"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1461,
        "id": 559,
        "name": "bed",
        "prevSize": 32,
        "code": 59737
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 90
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BedIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BedIcon = ({ size = 16, ...props }: BedIconProps) => (
  <IcoMoon iconSet={iconSet} icon="bed" size={size} {...props} />
);

BedIcon.displayName = 'BedIcon';

export default BedIcon;
