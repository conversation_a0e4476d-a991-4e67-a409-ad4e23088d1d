// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M938.667 384v-85.333c0-70.692-57.306-128-128-128h-597.333c-12.932 0-25.416 1.918-37.182 5.484M938.667 384v341.333c0 57.762-38.259 106.586-90.816 122.517M938.667 384h-554.667M85.333 384v341.333c0 70.694 57.308 128 128 128h495.953M85.333 384v-85.333c0-20.194 4.676-39.295 13.005-56.28M85.333 384h154.619M234.667 704h106.667M469.333 704h90.62M85.333 85.333l90.818 90.818M938.667 938.667l-90.816-90.816M176.151 176.151l207.849 207.849M384 384l463.851 463.851"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "card-slash"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1527,
        "id": 493,
        "name": "card-slash",
        "prevSize": 32,
        "code": 59803
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 156
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CardSlashIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CardSlashIcon = ({ size = 16, ...props }: CardSlashIconProps) => (
  <IcoMoon iconSet={iconSet} icon="card-slash" size={size} {...props} />
);

CardSlashIcon.displayName = 'CardSlashIcon';

export default CardSlashIcon;
