// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 768c199.637 0 339.883-130.846 403.614-205.449 11.511-13.474 17.267-20.207 21.056-33.126 2.662-9.084 2.662-25.766 0-34.85-3.789-12.919-9.545-19.652-21.056-33.126-63.731-74.601-203.977-205.449-403.614-205.449s-339.881 130.848-403.615 205.449c-11.51 13.474-17.264 20.207-21.053 33.126-2.664 9.084-2.664 25.766 0 34.85 3.789 12.919 9.544 19.652 21.053 33.126 63.735 74.603 203.977 205.449 403.615 205.449z",
          "M640 512c0 70.694-57.306 128-128 128s-128-57.306-128-128c0-70.694 57.306-128 128-128s128 57.306 128 128z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "eye"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1626,
        "id": 394,
        "name": "eye",
        "prevSize": 32,
        "code": 59902
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 255
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface EyeIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const EyeIcon = ({ size = 16, ...props }: EyeIconProps) => (
  <IcoMoon iconSet={iconSet} icon="eye" size={size} {...props} />
);

EyeIcon.displayName = 'EyeIcon';

export default EyeIcon;
