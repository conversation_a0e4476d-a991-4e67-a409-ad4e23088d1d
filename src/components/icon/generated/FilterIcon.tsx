// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M853.333 85.333h-682.667c-47.128 0-85.333 38.958-85.333 87.016v32.951c0 34.617 13.486 67.816 37.49 92.294l223.686 228.098c24.005 24.478 37.49 57.677 37.49 92.292v277.141c0 29.696 28.531 50.667 56.158 41.276l170.667-58.010c17.425-5.922 29.175-22.549 29.175-41.276v-219.132c0-34.615 13.487-67.814 37.491-92.292l223.684-228.098c24.004-24.478 37.491-57.677 37.491-92.294v-32.951c0-48.058-38.204-87.016-85.333-87.016z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "filter"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1652,
        "id": 368,
        "name": "filter",
        "prevSize": 32,
        "code": 59928
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 281
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FilterIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FilterIcon = ({ size = 16, ...props }: FilterIconProps) => (
  <IcoMoon iconSet={iconSet} icon="filter" size={size} {...props} />
);

FilterIcon.displayName = 'FilterIcon';

export default FilterIcon;
