// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M389.433 853.333c26.986 38.686 71.819 64 122.567 64s95.582-25.314 122.569-64M496.196 149.875c-94.64 6.508-176.214 71.049-203.639 163.006l-65.748 263.119c-5.352 13.717-13.881 25.975-24.887 35.759l-5.89 5.235c-16.134 14.34-25.365 34.897-25.365 56.482 0 41.737 33.836 75.575 75.574 75.575h531.517c41.741 0 75.575-33.839 75.575-75.575 0-21.585-9.233-42.142-25.365-56.482l-5.888-5.235c-11.008-9.783-19.537-22.042-24.887-35.759l-33.254-133.069M810.667 234.667c0 58.91-47.757 106.667-106.667 106.667s-106.667-47.756-106.667-106.667c0-58.91 47.757-106.667 106.667-106.667s106.667 47.756 106.667 106.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bell-notif"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1465,
        "id": 555,
        "name": "bell-notif",
        "prevSize": 32,
        "code": 59741
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 94
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BellNotifIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BellNotifIcon = ({ size = 16, ...props }: BellNotifIconProps) => (
  <IcoMoon iconSet={iconSet} icon="bell-notif" size={size} {...props} />
);

BellNotifIcon.displayName = 'BellNotifIcon';

export default BellNotifIcon;
