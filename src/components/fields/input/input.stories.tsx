import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react-vite';

import { Input } from '@components/fields';
import { inputPropDefs } from '@components/fields/input/input.props';
import Icon, { type IconType } from '@components/icon';
import { iconName } from '@components/icon/icon-name';
import { accentColors, radii } from '@props';

const prefixSuffixMapping: Record<IconType, React.ReactNode> = iconName.reduce(
  (map, name) => {
    map[name] = <Icon icon={name} size={16} />;
    return map;
  },
  {} as Record<IconType, React.ReactNode>,
);

const meta: Meta<typeof Input> = {
  title: 'DesignSystem/InputFields/Input',
  component: Input,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    label: { control: 'text' },
    variant: { control: 'inline-radio', options: inputPropDefs.variant.values },
    size: { control: 'inline-radio', options: inputPropDefs.size.values },
    color: { control: 'select', options: accentColors },
    radius: { control: 'select', options: radii },
    placeholder: { control: 'text', type: 'string' },
    value: { control: 'text', type: 'string' },
    defaultValue: { control: 'text', type: 'string' },
    disabled: { control: 'boolean', type: 'boolean' },
    readOnly: { control: 'boolean', type: 'boolean' },
    prefix: {
      control: { type: 'select' },
      options: iconName,
      mapping: prefixSuffixMapping,
    },
    suffix: {
      control: { type: 'select' },
      options: iconName,
      mapping: prefixSuffixMapping,
    },
    helperText: { control: 'text' },
    error: { control: 'boolean' },
  },
};

export default meta;
type Story = StoryObj<typeof Input>;

export const _input: Story = {
  args: {
    variant: inputPropDefs.variant.default,
    size: inputPropDefs.size.default,
    color: 'accent',
    placeholder: 'Search...',
    defaultValue: undefined,
    value: undefined,
    prefix: 'search',
    suffix: 'check',
    label: 'Label',
    helperText: 'Hint',
    readOnly: false,
    disabled: false,
    error: false,
  },
};

export const _password: StoryObj<typeof Input.Password> = {
  argTypes: {
    hideIcon: {
      control: { type: 'select' },
      options: iconName,
      mapping: prefixSuffixMapping,
    },
    showIcon: {
      control: { type: 'select' },
      options: iconName,
      mapping: prefixSuffixMapping,
    },
  },
  args: {
    placeholder: 'input your password',
    hideIcon: 'eye-off',
    showIcon: 'eye',
  },
  render: (args) => <Input.Password {...args} />,
};
