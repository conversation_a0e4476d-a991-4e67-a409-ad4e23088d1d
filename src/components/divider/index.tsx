import clsx from 'clsx';
import * as React from 'react';

import { dividerPropDefs } from '@components/divider/divider.props';
import { type ComponentPropsWithout, type RemovedProps, extractProps } from '@helpers';
import { type GetPropDefTypes, type MarginProps, marginPropDefs } from '@props';

type DividerElement = React.ComponentRef<'span'>;
type DividerOwnProps = GetPropDefTypes<typeof dividerPropDefs>;
interface DividerProps extends ComponentPropsWithout<'span', RemovedProps>, MarginProps, DividerOwnProps {}
const Divider = React.forwardRef<DividerElement, DividerProps>((props, forwardedRef) => {
  const { className, ...dividerProps } = extractProps(props, dividerPropDefs, marginPropDefs);
  return <span {...dividerProps} ref={forwardedRef} className={clsx('rt-divider', className)} />;
});
Divider.displayName = 'Divider';

export { Divider };
export type { DividerProps };
