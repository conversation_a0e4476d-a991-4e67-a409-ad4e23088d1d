// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M460.117 326.784v0zM611.883 478.549v0zM180.417 928.205v0zM138.464 886.251v0zM587.584 928.205v0zM629.538 886.251v0zM138.464 351.084v0zM180.417 309.13v0zM432 307.29v0zM631.377 506.667v0zM716.117 113.451v0zM867.883 265.216v0zM843.584 714.871v0zM885.538 672.917v0zM394.464 137.75v0zM436.416 95.797v0zM688 93.956v0zM887.377 293.333v0zM352 298.667c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM640 693.333c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM486.4 938.667v-32h-204.8v64h204.8v-32zM128 785.067h32v-332.8h-64v332.8h32zM640 546.432h-32v238.635h64v-238.635h-32zM460.117 326.784l-22.626 22.627 151.765 151.764 45.252-45.252-151.765-151.766-22.626 22.627zM281.6 298.667v32h110.636v-64h-110.636v32zM281.6 938.667v-32c-27.41 0-46.044-0.026-60.447-1.203-14.029-1.143-21.204-3.221-26.209-5.773l-29.055 57.024c15.531 7.915 32.065 11.068 50.052 12.54 17.614 1.438 39.304 1.412 65.658 1.412v-32zM128 785.067h-32c0 26.355-0.025 48.047 1.414 65.66 1.469 17.984 4.623 34.522 12.537 50.052l57.024-29.056c-2.55-5.005-4.628-12.181-5.774-26.21-1.177-14.4-1.201-33.037-1.201-60.446h-32zM180.417 928.205l14.528-28.514c-12.042-6.135-21.833-15.927-27.969-27.968l-57.024 29.056c12.272 24.085 31.853 43.665 55.938 55.936l14.528-28.51zM486.4 938.667v32c26.355 0 48.047 0.026 65.66-1.412 17.984-1.472 34.522-4.625 50.052-12.54l-29.056-57.024c-5.005 2.551-12.181 4.629-26.21 5.773-14.4 1.178-33.037 1.203-60.446 1.203v32zM640 785.067h-32c0 27.409-0.026 46.046-1.203 60.446-1.143 14.029-3.221 21.205-5.773 26.21l57.024 29.056c7.915-15.531 11.068-32.068 12.54-50.052 1.438-17.613 1.412-39.305 1.412-65.66h-32zM587.584 928.205l14.528 28.51c24.085-12.271 43.665-31.851 55.936-55.936l-57.024-29.056c-6.135 12.041-15.927 21.833-27.968 27.968l14.528 28.514zM128 452.267h32c0-27.41 0.025-46.044 1.201-60.447 1.146-14.029 3.224-21.204 5.774-26.209l-57.024-29.055c-7.913 15.531-11.067 32.065-12.537 50.052-1.439 17.614-1.414 39.304-1.414 65.658h32zM281.6 298.667v-32c-26.354 0-48.045-0.025-65.658 1.414-17.987 1.469-34.522 4.623-50.052 12.537l29.055 57.024c5.005-2.55 12.18-4.628 26.209-5.774 14.403-1.177 33.036-1.201 60.447-1.201v-32zM138.464 351.084l28.512 14.528c6.136-12.042 15.927-21.833 27.969-27.969l-29.055-57.024c-24.085 12.272-43.666 31.853-55.938 55.938l28.512 14.528zM460.117 326.784l22.626-22.627c-10.944-10.944-23.667-19.704-37.487-25.993l-26.511 58.252c6.912 3.145 13.271 7.524 18.746 12.997l22.626-22.627zM432 307.29l13.257-29.126c-16.495-7.505-34.552-11.497-53.021-11.497v64c9.234 0 18.262 1.995 26.51 5.748l13.254-29.126zM640 546.432h32c0-18.47-3.994-36.527-11.499-53.022l-58.249 26.513c3.75 8.247 5.747 17.271 5.747 26.509h32zM631.377 506.667l29.124-13.257c-6.289-13.82-15.049-26.543-25.993-37.487l-45.252 45.252c5.47 5.474 9.847 11.836 12.996 18.748l29.124-13.257zM512 570.667h-32v224h64v-224h-32zM272 810.667v-32c8.837 0 16 7.164 16 16h-64c0 26.509 21.49 48 48 48v-32zM512 794.667h-32c0-8.836 7.164-16 16-16v64c26.509 0 48-21.491 48-48h-32zM496 554.667v32c-8.836 0-16-7.164-16-16h64c0-26.509-21.491-48-48-48v32zM272 554.667v-32c-26.51 0-48 21.491-48 48h64c0 8.836-7.163 16-16 16v-32zM272 554.667v32h112v-64h-112v32zM384 554.667v32h112v-64h-112v32zM496 810.667v-32h-112v64h112v-32zM384 810.667v-32h-112v64h112v-32zM384 554.667h-32v128h64v-128h-32zM384 682.667h-32v128h64v-128h-32zM256 794.667h32v-112h-64v112h32zM256 682.667h32v-112h-64v112h32zM384 682.667v-32h-128v64h128v-32zM896 333.098h-32v238.636h64v-238.636h-32zM716.117 113.451l-22.626 22.627 151.765 151.764 45.252-45.255-151.765-151.764-22.626 22.627zM537.6 85.333v32h110.635v-64h-110.635v32zM742.4 725.333v32c26.355 0 48.047 0.026 65.66-1.412 17.984-1.472 34.522-4.625 50.052-12.54l-29.056-57.024c-5.005 2.551-12.181 4.629-26.21 5.773-14.4 1.178-33.037 1.203-60.446 1.203v32zM896 571.733h-32c0 27.409-0.026 46.046-1.203 60.446-1.143 14.029-3.221 21.205-5.773 26.21l57.024 29.056c7.915-15.531 11.068-32.068 12.54-50.052 1.438-17.613 1.412-39.305 1.412-65.66h-32zM843.584 714.871l14.528 28.51c24.085-12.271 43.665-31.851 55.936-55.936l-57.024-29.056c-6.135 12.041-15.927 21.833-27.968 27.968l14.528 28.514zM384 238.933h32c0-27.41 0.025-46.044 1.201-60.447 1.146-14.029 3.224-21.204 5.774-26.209l-57.024-29.055c-7.913 15.531-11.067 32.065-12.537 50.052-1.439 17.614-1.414 39.304-1.414 65.658h32zM537.6 85.333v-32c-26.355 0-48.047-0.025-65.66 1.414-17.984 1.469-34.522 4.623-50.051 12.537l29.055 57.024c5.005-2.55 12.181-4.628 26.21-5.774 14.4-1.177 33.037-1.201 60.446-1.201v-32zM394.464 137.75l28.512 14.528c6.136-12.042 15.928-21.833 27.968-27.969l-29.055-57.024c-24.085 12.272-43.666 31.853-55.938 55.938l28.512 14.528zM716.117 113.451l22.626-22.627c-10.944-10.944-23.667-19.704-37.487-25.993l-26.513 58.252c6.912 3.145 13.274 7.524 18.748 12.997l22.626-22.627zM688 93.956l13.257-29.126c-16.491-7.505-34.551-11.497-53.022-11.497v64c9.237 0 18.261 1.995 26.509 5.748l13.257-29.126zM896 333.098h32c0-18.469-3.994-36.528-11.499-53.019l-58.249 26.509c3.75 8.248 5.747 17.276 5.747 26.51h32zM887.377 293.333l29.124-13.254c-6.289-13.821-15.049-26.546-25.993-37.491l-45.252 45.255c5.47 5.473 9.847 11.833 12.996 18.745l29.124-13.254zM384 298.667h32v-59.733h-64v59.733h32zM742.4 725.333v-32h-102.4v64h102.4v-32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "sim-card-alt"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1894,
        "id": 126,
        "name": "sim-card-alt",
        "prevSize": 32,
        "code": 60170
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 523
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SimCardAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SimCardAltIcon = ({ size = 16, ...props }: SimCardAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="sim-card-alt" size={size} {...props} />
);

SimCardAltIcon.displayName = 'SimCardAltIcon';

export default SimCardAltIcon;
