// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M290.133 970.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM155.223 924.715v0zM117.333 733.867c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM99.284 868.779v0zM970.667 733.867c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM924.715 868.779v0zM733.867 906.667c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM868.779 924.715v0zM733.867 53.333c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM868.779 99.284v0zM906.667 290.133c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM924.715 155.223v0zM290.133 117.333c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM155.223 99.284v0zM53.333 290.133c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM99.284 155.223v0zM500.489 280.329v0zM310.859 386.149v0zM310.859 637.85v0zM500.489 743.671v0zM523.511 743.671v0zM713.139 637.85v0zM713.139 386.149v0zM523.511 280.329v0zM302.072 394.667v0zM290.133 938.667v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM85.333 733.867h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM155.223 924.715l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM938.667 733.867h-32c0 36.373-0.026 61.726-1.638 81.463-1.583 19.366-4.531 30.494-8.823 38.921l57.024 29.056c9.655-18.957 13.683-39.441 15.586-62.763 1.877-22.95 1.852-51.362 1.852-86.677h-32zM733.867 938.667v32c35.315 0 63.727 0.026 86.677-1.852 23.322-1.903 43.806-5.931 62.763-15.586l-29.056-57.024c-8.427 4.292-19.554 7.241-38.921 8.823-19.738 1.613-45.090 1.638-81.463 1.638v32zM924.715 868.779l-28.51-14.528c-9.207 18.061-23.893 32.747-41.954 41.954l29.056 57.024c30.106-15.343 54.579-39.817 69.922-69.922l-28.514-14.528zM733.867 85.333v32c36.373 0 61.726 0.025 81.463 1.638 19.366 1.582 30.494 4.532 38.921 8.826l29.056-57.024c-18.957-9.658-39.441-13.683-62.763-15.589-22.95-1.875-51.362-1.85-86.677-1.85v32zM938.667 290.133h32c0-35.315 0.026-63.727-1.852-86.676-1.903-23.323-5.931-43.809-15.586-62.762l-57.024 29.055c4.292 8.428 7.241 19.553 8.823 38.919 1.613 19.739 1.638 45.093 1.638 81.464h32zM868.779 99.284l-14.528 28.512c18.061 9.204 32.747 23.89 41.954 41.953l57.024-29.055c-15.343-30.106-39.817-54.583-69.922-69.923l-14.528 28.512zM290.133 85.333v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM85.333 290.133h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM155.223 99.284l-14.528-28.512c-30.106 15.34-54.583 39.817-69.923 69.923l57.024 29.055c9.204-18.063 23.89-32.75 41.953-41.953l-14.528-28.512zM500.489 280.329l-15.595-27.943-189.629 105.819 31.187 55.887 189.631-105.82-15.595-27.944zM298.667 406.962h-32v210.074h64v-210.074h-32zM310.859 637.85l-15.593 27.947 189.629 105.818 31.189-55.889-189.631-105.818-15.594 27.942zM523.511 743.671l15.595 27.942 189.628-105.818-31.185-55.889-189.632 105.818 15.595 27.947zM725.333 617.037h32v-210.074h-64v210.074h32zM713.139 386.149l15.595-27.943-189.628-105.82-31.189 55.887 189.632 105.82 15.59-27.944zM713.139 637.85l15.595 27.947c17.698-9.877 28.599-28.561 28.599-48.759h-64c0-2.901 1.566-5.653 4.215-7.13l15.59 27.942zM298.667 617.037h-32c0 20.198 10.901 38.882 28.599 48.759l31.187-55.889c2.647 1.476 4.214 4.228 4.214 7.13h-32zM500.489 280.329l15.595 27.944c-2.534 1.414-5.632 1.414-8.166 0l31.189-55.887c-16.853-9.404-37.359-9.404-54.212 0l15.595 27.943zM725.333 406.962h32c0-10.313-2.842-20.228-7.97-28.767l-54.869 32.943c-0.747-1.243-1.161-2.688-1.161-4.176h32zM721.929 394.667l27.435-16.471c-4.919-8.196-11.964-15.154-20.629-19.99l-31.185 55.887c-1.297-0.723-2.338-1.761-3.055-2.955l27.435-16.471zM512 512l15.612 27.934 209.929-117.334-31.223-55.866-209.929 117.332 15.612 27.934zM310.859 386.149l-15.593-27.944c-8.665 4.836-15.708 11.794-20.628 19.99l54.871 32.943c-0.717 1.194-1.76 2.232-3.055 2.955l-15.594-27.944zM302.072 394.667l-27.435-16.471c-5.126 8.539-7.97 18.455-7.97 28.767h64c0 1.488-0.413 2.933-1.159 4.176l-27.436-16.471zM512 512l15.612-27.934-209.927-117.332-31.225 55.866 209.929 117.334 15.612-27.934zM500.489 743.671l-15.595 27.942c8.418 4.698 17.766 7.053 27.106 7.053v-64c1.408 0 2.82 0.358 4.083 1.058l-15.595 27.947zM512 746.667v32c9.34 0 18.688-2.355 27.106-7.053l-31.189-55.889c1.263-0.7 2.675-1.058 4.083-1.058v32zM512 512h-32v234.667h64v-234.667h-32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "d-cube-scan"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1372,
        "id": 648,
        "name": "d-cube-scan",
        "prevSize": 32,
        "code": 59648
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 1
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface DCubeScanIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const DCubeScanIcon = ({ size = 16, ...props }: DCubeScanIconProps) => (
  <IcoMoon iconSet={iconSet} icon="d-cube-scan" size={size} {...props} />
);

DCubeScanIcon.displayName = 'DCubeScanIcon';

export default DCubeScanIcon;
