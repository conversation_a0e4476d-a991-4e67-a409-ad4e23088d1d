// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M203.852 345.098c65.456 0 118.519-53.375 118.519-119.216s-53.063-119.216-118.519-119.216c-65.456 0-118.519 53.375-118.519 119.216s53.062 119.216 118.519 119.216zM203.852 345.098v333.805M203.852 345.098l163.543 201.061c31.514 38.746 78.651 61.214 128.418 61.214h205.815M203.852 678.903c-65.456 0-118.519 53.372-118.519 119.215 0 65.839 53.062 119.215 118.519 119.215s118.519-53.376 118.519-119.215c0-65.843-53.063-119.215-118.519-119.215zM701.628 607.373c0 65.839 53.065 119.215 118.519 119.215s118.519-53.376 118.519-119.215c0-65.843-53.065-119.215-118.519-119.215s-118.519 53.372-118.519 119.215z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "git-merge"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1691,
        "id": 329,
        "name": "git-merge",
        "prevSize": 32,
        "code": 59967
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 320
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface GitMergeIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const GitMergeIcon = ({ size = 16, ...props }: GitMergeIconProps) => (
  <IcoMoon iconSet={iconSet} icon="git-merge" size={size} {...props} />
);

GitMergeIcon.displayName = 'GitMergeIcon';

export default GitMergeIcon;
