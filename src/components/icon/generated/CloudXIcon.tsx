// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M300.544 296.466c-4.537-0.309-9.115-0.466-13.729-0.466-111.275 0-201.481 91.334-201.481 204.002 0 112.666 90.206 203.998 201.481 203.998h154.073M300.544 296.466c44.844-99.396 143.885-168.466 258.863-168.466 157.094 0 284.446 128.942 284.446 288M300.544 296.466c15.177-0.156 50.271 4.334 69.234 23.534M681.993 636.117l67.046 67.883M749.039 704l67.042 67.883M749.039 704l67.042-67.883M749.039 704l-67.046 67.883M938.667 704c0 106.039-84.898 192-189.628 192s-189.632-85.961-189.632-192c0-106.039 84.902-192 189.632-192s189.628 85.961 189.628 192z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "cloud-x"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1573,
        "id": 447,
        "name": "cloud-x",
        "prevSize": 32,
        "code": 59849
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 202
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CloudXIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CloudXIcon = ({ size = 16, ...props }: CloudXIconProps) => (
  <IcoMoon iconSet={iconSet} icon="cloud-x" size={size} {...props} />
);

CloudXIcon.displayName = 'CloudXIcon';

export default CloudXIcon;
