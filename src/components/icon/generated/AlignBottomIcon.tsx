// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M246.315 808.341v0zM236.992 799.019v0zM445.675 799.019v0zM436.352 808.341v0zM436.352 87.659v0zM445.675 96.981v0zM246.315 87.659v0zM236.992 96.981v0zM587.648 808.341v0zM578.325 799.019v0zM787.008 799.019v0zM777.685 808.341v0zM777.685 300.992v0zM787.008 310.315v0zM587.648 300.992v0zM578.325 310.315v0zM170.667 906.667c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM853.333 970.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM268.8 85.333v32h145.067v-64h-145.067v32zM448 119.467h-32v657.067h64v-657.067h-32zM413.867 810.667v-32h-145.067v64h145.067v-32zM234.667 776.533h32v-657.067h-64v657.067h32zM268.8 810.667v-32c-3.060 0-5.4 0-7.409-0.034-2.010-0.030-3.227-0.085-3.997-0.149-1.578-0.128 0.467-0.175 3.449 1.344l-29.056 57.028c7.545 3.844 14.859 4.962 20.396 5.414 5.163 0.422 11.171 0.397 16.617 0.397v-32zM234.667 776.533h-32c0 5.444-0.025 11.456 0.397 16.619 0.452 5.534 1.572 12.851 5.416 20.395l57.024-29.056c1.519 2.982 1.476 5.026 1.347 3.447-0.063-0.768-0.12-1.988-0.152-3.994-0.032-2.010-0.032-4.352-0.032-7.411h-32zM246.315 808.341l14.528-28.51c2.007 1.020 3.639 2.654 4.661 4.659l-57.024 29.056c5.114 10.035 13.272 18.193 23.308 23.309l14.528-28.514zM448 776.533h-32c0 3.059-0.001 5.402-0.032 7.411-0.032 2.005-0.089 3.226-0.152 3.994-0.129 1.579-0.172-0.465 1.347-3.447l57.026 29.056c3.844-7.543 4.962-14.861 5.414-20.395 0.422-5.163 0.397-11.174 0.397-16.619h-32zM413.867 810.667v32c5.446 0 11.455 0.026 16.619-0.397 5.534-0.452 12.851-1.57 20.395-5.414l-29.056-57.028c2.982-1.519 5.026-1.472 3.449-1.344-0.77 0.064-1.987 0.119-3.997 0.149-2.009 0.034-4.349 0.034-7.409 0.034v32zM445.675 799.019l-28.512-14.528c1.023-2.005 2.655-3.639 4.661-4.659l29.056 57.024c10.035-5.116 18.193-13.274 23.309-23.309l-28.514-14.528zM413.867 85.333v32c3.060 0 5.4 0.001 7.409 0.032s3.227 0.089 3.997 0.152c1.577 0.129-0.467 0.172-3.449-1.347l29.056-57.024c-7.543-3.844-14.861-4.964-20.395-5.416-5.164-0.422-11.173-0.397-16.619-0.397v32zM448 119.467h32c0-5.446 0.026-11.455-0.397-16.617-0.452-5.536-1.57-12.851-5.414-20.396l-57.026 29.056c-1.519-2.982-1.476-5.027-1.347-3.449 0.063 0.77 0.12 1.987 0.152 3.997s0.032 4.349 0.032 7.409h32zM436.352 87.659l-14.528 28.512c-2.007-1.023-3.639-2.654-4.661-4.661l57.026-29.056c-5.116-10.035-13.274-18.194-23.309-23.308l-14.528 28.512zM268.8 85.333v-32c-5.446 0-11.455-0.025-16.617 0.397-5.536 0.452-12.851 1.572-20.396 5.416l29.056 57.024c-2.982 1.519-5.027 1.476-3.449 1.347 0.77-0.063 1.987-0.12 3.997-0.152s4.349-0.032 7.409-0.032v-32zM234.667 119.467h32c0-3.060 0.001-5.401 0.032-7.409 0.032-2.010 0.089-3.227 0.152-3.997 0.129-1.578 0.172 0.467-1.347 3.449l-57.024-29.056c-3.844 7.545-4.964 14.859-5.416 20.396-0.422 5.163-0.397 11.171-0.397 16.617h32zM246.315 87.659l-14.528-28.512c-10.035 5.113-18.194 13.272-23.308 23.308l57.024 29.056c-1.023 2.007-2.654 3.639-4.661 4.661l-14.528-28.512zM610.133 298.667v32h145.067v-64h-145.067v32zM789.333 332.8h-32v443.733h64v-443.733h-32zM755.2 810.667v-32h-145.067v64h145.067v-32zM576 776.533h32v-443.733h-64v443.733h32zM610.133 810.667v-32c-3.059 0-5.402 0-7.411-0.034-2.010-0.030-3.226-0.085-3.994-0.149-1.579-0.128 0.465-0.175 3.447 1.344l-29.056 57.028c7.543 3.844 14.861 4.962 20.395 5.414 5.163 0.422 11.174 0.397 16.619 0.397v-32zM576 776.533h-32c0 5.444-0.026 11.456 0.397 16.619 0.452 5.534 1.57 12.851 5.414 20.395l57.028-29.056c1.519 2.982 1.472 5.026 1.344 3.447-0.064-0.768-0.119-1.988-0.149-3.994-0.034-2.010-0.034-4.352-0.034-7.411h-32zM587.648 808.341l14.528-28.51c2.005 1.020 3.639 2.654 4.663 4.659l-57.028 29.056c5.116 10.035 13.274 18.193 23.309 23.309l14.528-28.514zM789.333 776.533h-32c0 3.059 0 5.402-0.034 7.411-0.030 2.005-0.085 3.226-0.149 3.994-0.128 1.579-0.175-0.465 1.344-3.447l57.028 29.056c3.844-7.543 4.962-14.861 5.414-20.395 0.422-5.163 0.397-11.174 0.397-16.619h-32zM755.2 810.667v32c5.444 0 11.456 0.026 16.619-0.397 5.534-0.452 12.851-1.57 20.395-5.414l-29.056-57.028c2.982-1.519 5.026-1.472 3.447-1.344-0.768 0.064-1.984 0.119-3.994 0.149-2.010 0.034-4.352 0.034-7.411 0.034v32zM787.008 799.019l-28.514-14.528c1.024-2.005 2.658-3.639 4.663-4.659l29.056 57.024c10.035-5.116 18.193-13.274 23.309-23.309l-28.514-14.528zM755.2 298.667v32c3.059 0 5.402 0.001 7.411 0.032s3.226 0.089 3.994 0.152c1.579 0.129-0.465 0.172-3.447-1.347l29.056-57.024c-7.543-3.844-14.861-4.964-20.395-5.416-5.163-0.422-11.174-0.397-16.619-0.397v32zM789.333 332.8h32c0-5.446 0.026-11.455-0.397-16.617-0.452-5.536-1.57-12.851-5.414-20.396l-57.028 29.056c-1.519-2.982-1.472-5.027-1.344-3.449 0.064 0.77 0.119 1.987 0.149 3.997 0.034 2.009 0.034 4.349 0.034 7.409h32zM777.685 300.992l-14.528 28.512c-2.005-1.023-3.639-2.654-4.663-4.661l57.028-29.056c-5.116-10.035-13.274-18.194-23.309-23.308l-14.528 28.512zM610.133 298.667v-32c-5.444 0-11.456-0.025-16.619 0.397-5.534 0.452-12.851 1.572-20.395 5.416l29.056 57.024c-2.982 1.519-5.026 1.476-3.447 1.347 0.768-0.063 1.984-0.12 3.994-0.152s4.352-0.032 7.411-0.032v-32zM576 332.8h32c0-3.060 0-5.4 0.034-7.409 0.030-2.010 0.085-3.227 0.149-3.997 0.128-1.578 0.175 0.467-1.344 3.449l-57.028-29.056c-3.844 7.545-4.962 14.859-5.414 20.396-0.422 5.163-0.397 11.171-0.397 16.617h32zM587.648 300.992l-14.528-28.512c-10.035 5.113-18.193 13.272-23.309 23.308l57.028 29.056c-1.024 2.007-2.658 3.639-4.663 4.661l-14.528-28.512zM170.667 938.667v32h682.667v-64h-682.667v32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "align-bottom"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1390,
        "id": 630,
        "name": "align-bottom",
        "prevSize": 32,
        "code": 59666
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 19
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface AlignBottomIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const AlignBottomIcon = ({ size = 16, ...props }: AlignBottomIconProps) => (
  <IcoMoon iconSet={iconSet} icon="align-bottom" size={size} {...props} />
);

AlignBottomIcon.displayName = 'AlignBottomIcon';

export default AlignBottomIcon;
