// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M277.333 405.333l128-128M362.667 490.667l128-128M130.827 85.333h352.907c36.198 0 70.912 14.379 96.508 39.974l318.451 318.452c53.299 53.299 53.299 139.712 0 193.011l-261.922 261.922c-53.299 53.299-139.712 53.299-193.011 0l-318.452-318.451c-25.595-25.596-39.974-60.309-39.974-96.508v-352.907c0-25.125 20.368-45.493 45.493-45.493z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "tag-1"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1929,
        "id": 91,
        "name": "tag-1",
        "prevSize": 32,
        "code": 60205
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 558
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface Tag1IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const Tag1Icon = ({ size = 16, ...props }: Tag1IconProps) => (
  <IcoMoon iconSet={iconSet} icon="tag-1" size={size} {...props} />
);

Tag1Icon.displayName = 'Tag1Icon';

export default Tag1Icon;
