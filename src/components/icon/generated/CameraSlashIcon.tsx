// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M813.252 284.732v0zM819.251 285.398v0zM307.296 217.409v0zM716.706 217.409v0zM155.223 860.715v0zM99.284 804.779v0zM924.715 804.779v0zM868.779 860.715v0zM938.586 408.677v0zM829.321 286.599v0zM85.414 408.677v0zM725.333 373.333c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM768 437.333c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM107.961 62.706c-12.497-12.497-32.758-12.497-45.255 0s-12.497 32.758 0 45.255l45.255-45.255zM916.041 961.293c12.497 12.497 32.755 12.497 45.252 0s12.497-32.755 0-45.252l-45.252 45.252zM169.752 327.676c15.747-8.024 22.006-27.294 13.982-43.041s-27.294-22.006-43.040-13.982l29.059 57.023zM403.798 530.688c6.868-16.282-0.766-35.051-17.050-41.92-16.284-6.865-35.052 0.768-41.92 17.054l58.97 24.866zM582.178 743.172c16.286-6.869 23.919-25.634 17.054-41.92-6.869-16.286-25.638-23.919-41.92-17.050l24.866 58.97zM730.726 906.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM938.667 418.818h-32v251.049h64v-251.049h-32zM85.333 669.867h32v-251.049h-64v251.049h32zM813.252 284.732l-3.533 31.805 5.999 0.666 7.066-63.608-5.995-0.666-3.537 31.804zM417.445 149.333v32h189.109v-64h-189.109v32zM307.296 217.409l28.622 14.311c15.44-30.88 47.002-50.387 81.527-50.387v-64c-58.767 0-112.49 33.202-138.771 85.765l28.622 14.311zM716.706 217.409l-28.625 14.311c23.539 47.072 69.333 79.004 121.638 84.816l3.533-31.805 3.537-31.804c-30.733-3.414-57.634-22.174-71.462-49.829l-28.621 14.311zM716.706 217.409l28.621-14.311c-26.283-52.563-80.004-85.765-138.773-85.765v64c34.526 0 66.091 19.506 81.527 50.387l28.625-14.311zM290.133 874.667v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM85.333 669.867h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM155.223 860.715l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM938.667 669.867h-32c0 36.373-0.026 61.726-1.638 81.463-1.583 19.366-4.531 30.494-8.823 38.921l57.024 29.056c9.655-18.957 13.683-39.441 15.586-62.763 1.877-22.95 1.852-51.362 1.852-86.677h-32zM924.715 804.779l-28.51-14.528c-9.207 18.061-23.893 32.747-41.954 41.954l29.056 57.024c30.106-15.343 54.579-39.817 69.922-69.922l-28.514-14.528zM938.667 418.818h32c0-4.977 0.004-8.292-0.102-11.279l-63.957 2.277c0.055 1.566 0.060 3.494 0.060 9.002h32zM819.251 285.398l-3.533 31.804c5.474 0.608 7.39 0.826 8.939 1.055l9.327-63.317c-2.957-0.436-6.255-0.797-11.2-1.346l-3.533 31.804zM938.586 408.677l31.979-1.139c-2.748-77.157-60.198-141.347-136.58-152.598l-9.327 63.317c45.828 6.751 80.299 45.265 81.95 91.559l31.979-1.139zM85.333 418.818h32c0-5.508 0.005-7.436 0.061-9.002l-63.959-2.278c-0.107 2.988-0.102 6.302-0.102 11.279h32zM725.333 405.333v32h42.667v-64h-42.667v32zM661.333 576h32c0-99.921-81.412-181.333-181.333-181.333v64c64.576 0 117.333 52.757 117.333 117.333h32zM362.667 576h-32c0 99.921 81.412 181.333 181.333 181.333v-64c-64.576 0-117.333-52.757-117.333-117.333h-32zM85.414 408.677l31.98 1.139c1.268-35.607 21.959-66.649 52.358-82.14l-29.059-57.023c-50.595 25.783-85.143 77.471-87.258 136.885l31.98 1.139zM374.313 518.255l-29.485-12.433c-9.122 21.628-14.161 45.363-14.161 70.178h64c0-16.090 3.257-31.381 9.131-45.312l-29.485-12.433zM512 725.333v32c24.815 0 48.55-5.039 70.178-14.161l-24.866-58.97c-13.931 5.875-29.222 9.131-45.312 9.131v32zM730.726 874.667v-32h-440.593v64h440.593v-32zM863.279 863.279l-22.626 22.63 75.388 75.383 45.252-45.252-75.383-75.388-22.63 22.626zM863.279 863.279l12.54 29.444c2.539-1.084 5.030-2.244 7.488-3.494l-29.056-57.024c-1.131 0.576-2.3 1.118-3.507 1.634l12.535 29.44zM644.591 644.591l-22.63 22.626 218.692 218.692 45.257-45.257-218.692-218.692-22.626 22.63zM644.591 644.591l28.386 14.767c13.001-24.994 20.356-53.376 20.356-83.358h-64c0 19.418-4.745 37.7-13.133 53.824l28.39 14.767zM443.409 443.409l-22.626 22.63 201.178 201.178 45.257-45.257-201.178-201.178-22.63 22.626zM512 426.667v-32c-29.982 0-58.364 7.354-83.358 20.355l29.534 56.778c16.124-8.388 34.406-13.133 53.824-13.133v-32zM85.333 85.333l-22.627 22.627 179.73 179.73 45.255-45.255-179.73-179.73-22.627 22.627zM265.064 265.064l-22.627 22.627 178.347 178.348 45.257-45.257-178.348-178.347-22.627 22.627zM265.064 265.064l17.649 26.693c22.347-14.775 40.847-35.32 53.205-60.037l-57.244-28.622c-7.26 14.521-18.121 26.586-31.258 35.272l17.649 26.693z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "camera-slash"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1515,
        "id": 505,
        "name": "camera-slash",
        "prevSize": 32,
        "code": 59791
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 144
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CameraSlashIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CameraSlashIcon = ({ size = 16, ...props }: CameraSlashIconProps) => (
  <IcoMoon iconSet={iconSet} icon="camera-slash" size={size} {...props} />
);

CameraSlashIcon.displayName = 'CameraSlashIcon';

export default CameraSlashIcon;
