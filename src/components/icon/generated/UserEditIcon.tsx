// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M628.868 569.903c-16.836-5.649-34.573-9.297-53.018-10.658-63.778-4.715-123.217-4.706-187.119 0.026-111.842 8.282-197.768 100.489-213.787 213.325l-3.065 21.589c-9.243 65.101 35.324 125.833 99.661 132.809 49.492 5.367 117.875 9.647 168.139 11.674M580.723 819.413l-5.197 86.259c-0.346 5.679 4.297 10.385 9.899 10.039l85.047-5.274c1.421-0.090 2.761-0.7 3.763-1.719l160.081-162.355c25.357-25.715 25.357-67.409 0-93.124-25.353-25.715-66.462-25.715-91.819 0l-160.077 162.355c-1.007 1.020-1.609 2.377-1.698 3.819zM645.798 251.216c0 91.614-73.229 165.882-163.558 165.882-90.332 0-163.56-74.268-163.56-165.882s73.228-165.883 163.56-165.883c90.33 0 163.558 74.268 163.558 165.883z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "user-edit"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1975,
        "id": 45,
        "name": "user-edit",
        "prevSize": 32,
        "code": 60251
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 604
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface UserEditIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const UserEditIcon = ({ size = 16, ...props }: UserEditIconProps) => (
  <IcoMoon iconSet={iconSet} icon="user-edit" size={size} {...props} />
);

UserEditIcon.displayName = 'UserEditIcon';

export default UserEditIcon;
