// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M300.544 388.039c-4.537-0.32-9.115-0.483-13.729-0.483-111.275 0-201.481 94.719-201.481 211.557s90.206 211.554 201.481 211.554h509.631c78.545 0 142.221-66.859 142.221-149.333 0-65.067-39.629-120.41-94.925-140.877M300.544 388.039c44.844-103.077 143.885-174.705 258.863-174.705 157.094 0 284.446 133.718 284.446 298.667 0 2.829-0.038 5.645-0.111 8.457M300.544 388.039c15.177-0.161 50.271 4.495 69.234 24.406M843.742 520.457c0.034 13.773-4.629 46.298-23.595 66.21l-23.701 24.887M796.446 661.333c0-13.073-2.453-26.018-7.219-38.097s-11.75-23.053-20.553-32.299c-8.806-9.246-19.255-16.58-30.758-21.581-11.507-5.005-23.834-7.578-36.288-7.578"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "cloud"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1574,
        "id": 446,
        "name": "cloud",
        "prevSize": 32,
        "code": 59850
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 203
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CloudIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CloudIcon = ({ size = 16, ...props }: CloudIconProps) => (
  <IcoMoon iconSet={iconSet} icon="cloud" size={size} {...props} />
);

CloudIcon.displayName = 'CloudIcon';

export default CloudIcon;
