// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M509.111 203.852c-203.905 0-369.202 164.494-369.202 367.408s165.297 367.407 369.202 367.407c203.904 0 369.199-164.493 369.199-367.407s-165.295-367.408-369.199-367.408zM509.111 203.852v-118.519M366.193 85.333h285.834M128 280.715l77.238-76.863M896 280.715l-77.239-76.864M509.111 369.778v201.482"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "stopwatch"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1919,
        "id": 101,
        "name": "stopwatch",
        "prevSize": 32,
        "code": 60195
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 548
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface StopwatchIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const StopwatchIcon = ({ size = 16, ...props }: StopwatchIconProps) => (
  <IcoMoon iconSet={iconSet} icon="stopwatch" size={size} {...props} />
);

StopwatchIcon.displayName = 'StopwatchIcon';

export default StopwatchIcon;
