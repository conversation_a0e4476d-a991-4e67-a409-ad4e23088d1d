// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M106.667 301.037l208.692-214.123c1.282-1.054 2.962-1.581 4.642-1.581M533.333 301.037l-208.692-214.123c-1.282-1.054-2.962-1.581-4.642-1.581M320 85.333v597.333M490.667 722.961l208.691 214.123c1.284 1.054 2.961 1.583 4.642 1.583M917.333 722.961l-208.691 214.123c-1.284 1.054-2.961 1.583-4.642 1.583M704 938.667v-597.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "arrows-up-down"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1441,
        "id": 579,
        "name": "arrows-up-down",
        "prevSize": 32,
        "code": 59717
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 70
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ArrowsUpDownIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ArrowsUpDownIcon = ({ size = 16, ...props }: ArrowsUpDownIconProps) => (
  <IcoMoon iconSet={iconSet} icon="arrows-up-down" size={size} {...props} />
);

ArrowsUpDownIcon.displayName = 'ArrowsUpDownIcon';

export default ArrowsUpDownIcon;
