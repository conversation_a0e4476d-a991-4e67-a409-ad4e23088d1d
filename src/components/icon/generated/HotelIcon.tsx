// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M128 938.667h48M176 938.667v-734.815c0-65.456 53.726-118.519 120-118.519h432c66.274 0 120 53.062 120 118.519v734.815M176 938.667h672M848 938.667h48M416 938.667v-189.628c0-39.275 32.235-71.113 72-71.113h48c39.765 0 72 31.838 72 71.113v189.628M320 322.371h120M320 488.294h120M584 322.371h120M584 488.294h120"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "hotel"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1715,
        "id": 305,
        "name": "hotel",
        "prevSize": 32,
        "code": 59991
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 344
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface HotelIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const HotelIcon = ({ size = 16, ...props }: HotelIconProps) => (
  <IcoMoon iconSet={iconSet} icon="hotel" size={size} {...props} />
);

HotelIcon.displayName = 'HotelIcon';

export default HotelIcon;
