// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M300.544 392.683l-2.231 31.922 1.282 0.090 1.285-0.014-0.336-31.998zM843.742 523.644l-31.991-0.853-0.013 0.469 0.004 0.474 32-0.090zM346.728 439.019c12.259 12.727 32.516 13.111 45.247 0.853s13.112-32.518 0.854-45.248l-46.1 44.395zM773.393 591.548c-12.258 12.732-11.874 32.986 0.853 45.248 12.732 12.258 32.99 11.874 45.248-0.858l-46.101-44.39zM764.446 662.976c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM701.628 532.514c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM132.741 342.974h32c0-78.639 61.088-140.308 133.926-140.308v-64c-110.439 0-197.926 92.621-197.926 204.308h32zM159.227 436.403l27.165-16.911c-13.657-21.935-21.652-48.169-21.652-76.518h-64c0 40.525 11.462 78.446 31.322 110.342l27.165-16.913zM298.667 170.667v32c44.95 0 85.136 23.235 109.621 59.689l53.126-35.686c-35.481-52.825-94.928-88.003-162.748-88.003v32zM286.815 810.667v-32c-92.474 0-169.481-78.199-169.481-177.229h-64c0 132.079 103.406 241.229 233.481 241.229v-32zM85.333 601.438h32c0-99.034 77.007-177.233 169.481-177.233v-64c-130.076 0-233.481 109.154-233.481 241.233h32zM286.815 392.205v32c3.869 0 7.703 0.135 11.498 0.4l2.231-31.922 2.231-31.922c-5.279-0.369-10.601-0.556-15.96-0.556v32zM300.544 392.683l29.291 12.885c40.139-91.249 128.264-153.67 229.572-153.67v-64c-128.644 0-238.607 79.261-288.154 191.901l29.292 12.884zM559.407 219.898v32c138.295 0 252.446 116.772 252.446 263.383h64c0-179.66-140.548-327.383-316.446-327.383v32zM843.853 515.281h-32c0 2.513-0.034 5.013-0.102 7.509l63.979 1.711c0.081-3.063 0.124-6.135 0.124-9.22h-32zM843.742 523.644l-11.004 30.050c42.505 15.561 73.929 58.167 73.929 109.282h64c0-77.589-47.838-144.457-115.925-169.382l-10.999 30.050zM938.667 662.976h-32c0 65.041-50.475 115.691-110.221 115.691v64c97.348 0 174.221-81.6 174.221-179.691h-32zM796.446 810.667v-32h-509.631v64h509.631v-32zM300.544 392.683l0.336 31.998c4.8-0.050 14.382 0.727 24.362 3.479 10.333 2.846 17.626 6.848 21.486 10.859l46.1-44.395c-15.103-15.684-34.839-23.828-50.588-28.167-16.102-4.436-31.656-5.881-42.032-5.772l0.336 31.998zM843.742 523.644l-32 0.090c0.009 3.827-0.738 12.25-3.413 21.483-2.709 9.365-6.639 16.947-11.23 21.717l46.101 44.39c14.37-14.925 22.255-33.271 26.607-48.32 4.395-15.177 5.961-29.653 5.935-39.445l-32 0.085zM820.147 589.129l-23.049-22.195-23.706 24.614 46.101 44.39 23.706-24.614-23.053-22.195zM796.446 662.976h32c0-16.969-3.217-33.801-9.498-49.536l-59.443 23.71c3.251 8.154 4.941 16.934 4.941 25.826h32zM789.227 625.293l29.722-11.853c-6.276-15.74-15.509-30.118-27.226-42.287l-46.097 44.395c5.892 6.118 10.628 13.453 13.879 21.602l29.722-11.857zM768.674 593.353l23.049-22.199c-11.716-12.169-25.694-21.888-41.165-28.54l-25.289 58.79c7.535 3.243 14.464 8.026 20.356 14.144l23.049-22.195zM737.916 572.006l12.642-29.393c-15.475-6.656-32.102-10.099-48.93-10.099v64c8.081 0 16.107 1.651 23.642 4.89l12.646-29.397z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "cloud-sunny"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1572,
        "id": 448,
        "name": "cloud-sunny",
        "prevSize": 32,
        "code": 59848
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 201
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CloudSunnyIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CloudSunnyIcon = ({ size = 16, ...props }: CloudSunnyIconProps) => (
  <IcoMoon iconSet={iconSet} icon="cloud-sunny" size={size} {...props} />
);

CloudSunnyIcon.displayName = 'CloudSunnyIcon';

export default CloudSunnyIcon;
