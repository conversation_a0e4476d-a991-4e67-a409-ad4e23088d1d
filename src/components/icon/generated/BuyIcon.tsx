// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M524.343 85.333v853.333M697.711 257.63c-76.147-112.132-364.433-123.849-364.433 72.678s415.019 102.519 391.057 333.956c-19.981 192.973-332.267 195.631-425.668 41.591"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "round",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "buy"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "round",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1501,
        "id": 519,
        "name": "buy",
        "prevSize": 32,
        "code": 59777
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 130
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BuyIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BuyIcon = ({ size = 16, ...props }: BuyIconProps) => (
  <IcoMoon iconSet={iconSet} icon="buy" size={size} {...props} />
);

BuyIcon.displayName = 'BuyIcon';

export default BuyIcon;
