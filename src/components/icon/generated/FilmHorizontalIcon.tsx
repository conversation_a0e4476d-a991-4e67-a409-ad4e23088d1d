// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M341.333 85.333h-8.533c-71.687 0-107.53 0-134.911 13.951-24.085 12.272-43.666 31.853-55.938 55.938-13.951 27.38-13.951 63.224-13.951 134.911v8.533M341.333 85.333h341.333M341.333 85.333v213.333M341.333 938.667h-8.533c-71.687 0-107.53 0-134.911-13.952-24.085-12.271-43.666-31.851-55.938-55.936-13.951-27.383-13.951-63.223-13.951-134.912v-8.533M341.333 938.667h341.333M341.333 938.667v-213.333M682.667 938.667h8.533c71.689 0 107.529 0 134.912-13.952 24.085-12.271 43.665-31.851 55.936-55.936 13.952-27.383 13.952-63.223 13.952-134.912v-8.533M682.667 938.667v-213.333M682.667 85.333h8.533c71.689 0 107.529 0 134.912 13.951 24.085 12.272 43.665 31.853 55.936 55.938 13.952 27.38 13.952 63.224 13.952 134.911v8.533M682.667 85.333v213.333M128 512v-213.333M128 512v213.333M128 512h213.333M896 512v-213.333M896 512v213.333M896 512h-213.333M128 298.667h213.333M896 298.667h-213.333M128 725.333h213.333M896 725.333h-213.333M341.333 298.667v213.333M682.667 298.667v213.333M341.333 512h341.333M341.333 512v213.333M682.667 512v213.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "film-horizontal"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1649,
        "id": 371,
        "name": "film-horizontal",
        "prevSize": 32,
        "code": 59925
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 278
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FilmHorizontalIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FilmHorizontalIcon = ({ size = 16, ...props }: FilmHorizontalIconProps) => (
  <IcoMoon iconSet={iconSet} icon="film-horizontal" size={size} {...props} />
);

FilmHorizontalIcon.displayName = 'FilmHorizontalIcon';

export default FilmHorizontalIcon;
