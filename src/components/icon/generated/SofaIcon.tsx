// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M210.824 426.667l-19.337-262.979c-3.113-42.34 31.407-78.354 75.103-78.354h490.821c43.695 0 78.217 36.014 75.102 78.354l-19.337 262.979M311.216 841.143v97.523M712.785 841.143v97.523M325.264 576.781l6.9 73.728c2.351 25.118 24.029 44.348 49.99 44.348h259.694c25.963 0 47.637-19.23 49.988-44.348l6.899-73.728M325.264 576.781l-4.287-45.807c-5.528-59.076-56.516-104.307-117.58-104.307-63.673 0-118.064 47.821-118.064 111.172 0 37.167 19.123 71.881 50.96 92.497l20.511 13.282c2.428 1.574 4.031 4.092 4.393 6.908l10.788 83.831c7.85 61.005 61.233 106.786 124.521 106.786h430.986c63.287 0 116.672-45.781 124.523-106.786l10.786-83.831c0.363-2.816 1.967-5.333 4.395-6.908l20.51-13.282c31.838-20.617 50.961-55.33 50.961-92.497 0-63.351-54.391-111.172-118.063-111.172-61.065 0-112.051 45.231-117.581 104.307l-4.288 45.807M325.264 576.781l11.050-3.827c138.541-42.5 212.499-42.756 351.371 0l11.051 3.827"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "sofa"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1908,
        "id": 112,
        "name": "sofa",
        "prevSize": 32,
        "code": 60184
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 537
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SofaIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SofaIcon = ({ size = 16, ...props }: SofaIconProps) => (
  <IcoMoon iconSet={iconSet} icon="sofa" size={size} {...props} />
);

SofaIcon.displayName = 'SofaIcon';

export default SofaIcon;
