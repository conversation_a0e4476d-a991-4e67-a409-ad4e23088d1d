// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M341.333 533.333h341.333M85.333 437.333v192M938.667 437.333v192M270.222 725.333c-39.273 0-71.111-32.235-71.111-72v-240c0-39.764 31.837-72 71.111-72s71.111 32.236 71.111 72v240c0 39.765-31.837 72-71.111 72zM753.779 725.333c-39.275 0-71.113-32.235-71.113-72v-240c0-39.764 31.838-72 71.113-72 39.27 0 71.108 32.236 71.108 72v240c0 39.765-31.838 72-71.108 72z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "dumbbell"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1613,
        "id": 407,
        "name": "dumbbell",
        "prevSize": 32,
        "code": 59889
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 242
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface DumbbellIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const DumbbellIcon = ({ size = 16, ...props }: DumbbellIconProps) => (
  <IcoMoon iconSet={iconSet} icon="dumbbell" size={size} {...props} />
);

DumbbellIcon.displayName = 'DumbbellIcon';

export default DumbbellIcon;
