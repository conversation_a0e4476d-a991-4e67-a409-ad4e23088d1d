// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 328.885c0 0 38.743-51.078 73.094-68.556 16.672-8.482 39.348-12.632 59.396-14.624 28.94-2.877 56.745-14.816 77.379-35.312 8.132-8.077 16.819-16.121 24.713-22.227 6.11-4.727 13.518-9.663 21.001-14.313 23.816-14.799 42.112-37.555 48.376-64.885l5.417-23.635M195.825 514.705l36.82 41.6c10.096 11.405 24.598 17.937 39.83 17.937h115.741c51.973 0 97.477-34.876 110.985-85.060 3.055-11.341 19.145-11.341 22.199 0 13.508 50.185 59.012 85.060 110.985 85.060h129.434c16.252 0 31.731-6.972 42.505-19.145l35.75-40.393M287.618 704l-72.258-31.45c-3.37-1.468-7.048 1.314-6.549 4.954 12.716 92.843 32.62 101.841 78.807 181.781v79.381M393.481 748.066l73.177 16.725c14.319 3.273 28.958 4.924 43.644 4.924 13.559 0 27.081-1.451 40.333-4.326l79.885-17.323M510.302 891.26v47.407M938.667 332.493c0 0-38.741-51.078-73.092-68.555-16.674-8.483-39.351-12.632-59.396-14.624-28.941-2.877-56.747-14.817-77.38-35.312-8.132-8.077-16.819-16.121-24.713-22.227-6.11-4.726-13.517-9.662-21.001-14.313-23.817-14.798-42.112-37.555-48.375-64.885l-5.419-23.635M727.885 704l72.721-31.654c3.264-1.421 6.865 1.165 6.579 4.715-7.663 94.635-27.588 103.633-79.3 182.225v79.381"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "full-body"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1679,
        "id": 341,
        "name": "full-body",
        "prevSize": 32,
        "code": 59955
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 308
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FullBodyIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FullBodyIcon = ({ size = 16, ...props }: FullBodyIconProps) => (
  <IcoMoon iconSet={iconSet} icon="full-body" size={size} {...props} />
);

FullBodyIcon.displayName = 'FullBodyIcon';

export default FullBodyIcon;
