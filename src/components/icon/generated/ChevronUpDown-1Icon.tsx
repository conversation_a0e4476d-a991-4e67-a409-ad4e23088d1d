// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M361.501 378.51c-12.567 12.425-12.683 32.687-0.257 45.254s32.687 12.682 45.254 0.257l-44.997-45.511zM507.473 279.187v0zM516.527 279.187v0zM617.502 424.021c12.565 12.425 32.828 12.309 45.252-0.257 12.429-12.567 12.314-32.829-0.256-45.254l-44.996 45.511zM406.499 599.979c-12.568-12.425-32.829-12.309-45.254 0.256-12.425 12.57-12.31 32.828 0.257 45.257l44.997-45.513zM507.473 744.815v0zM516.527 744.815v0zM662.498 645.491c12.57-12.429 12.685-32.687 0.256-45.257-12.425-12.565-32.687-12.681-45.252-0.256l44.996 45.513zM155.223 924.715v0zM99.284 868.779v0zM924.715 868.779v0zM868.779 924.715v0zM868.779 99.284v0zM924.715 155.223v0zM155.223 99.284v0zM99.284 155.223v0zM384 401.266l22.499 22.755 123.473-122.079-44.996-45.511-123.473 122.079 22.499 22.756zM516.527 279.187l-22.498 22.755 123.473 122.079 44.996-45.511-123.473-122.079-22.498 22.756zM507.473 279.187l22.498 22.755c-9.967 9.855-25.975 9.855-35.942 0l44.996-45.511c-14.967-14.797-39.083-14.797-54.050 0l22.498 22.756zM384 622.733l-22.499 22.758 123.473 122.078 44.996-45.513-123.473-122.078-22.499 22.754zM516.527 744.815l22.498 22.754 123.473-122.078-44.996-45.513-123.473 122.078 22.498 22.758zM507.473 744.815l-22.498 22.754c14.967 14.797 39.083 14.797 54.050 0l-44.996-45.513c9.967-9.852 25.975-9.852 35.942 0l-22.498 22.758zM290.133 85.333v32h443.733v-64h-443.733v32zM938.667 290.133h-32v443.733h64v-443.733h-32zM733.867 938.667v-32h-443.733v64h443.733v-32zM85.333 733.867h32v-443.733h-64v443.733h32zM290.133 938.667v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM85.333 733.867h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM155.223 924.715l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM938.667 733.867h-32c0 36.373-0.026 61.726-1.638 81.463-1.583 19.366-4.531 30.494-8.823 38.921l57.024 29.056c9.655-18.957 13.683-39.441 15.586-62.763 1.877-22.95 1.852-51.362 1.852-86.677h-32zM733.867 938.667v32c35.315 0 63.727 0.026 86.677-1.852 23.322-1.903 43.806-5.931 62.763-15.586l-29.056-57.024c-8.427 4.292-19.554 7.241-38.921 8.823-19.738 1.613-45.090 1.638-81.463 1.638v32zM924.715 868.779l-28.51-14.528c-9.207 18.061-23.893 32.747-41.954 41.954l29.056 57.024c30.106-15.343 54.579-39.817 69.922-69.922l-28.514-14.528zM733.867 85.333v32c36.373 0 61.726 0.025 81.463 1.638 19.366 1.582 30.494 4.532 38.921 8.826l29.056-57.024c-18.957-9.658-39.441-13.683-62.763-15.589-22.95-1.875-51.362-1.85-86.677-1.85v32zM938.667 290.133h32c0-35.315 0.026-63.727-1.852-86.676-1.903-23.323-5.931-43.809-15.586-62.762l-57.024 29.055c4.292 8.428 7.241 19.553 8.823 38.919 1.613 19.739 1.638 45.093 1.638 81.464h32zM868.779 99.284l-14.528 28.512c18.061 9.204 32.747 23.89 41.954 41.953l57.024-29.055c-15.343-30.106-39.817-54.583-69.922-69.923l-14.528 28.512zM290.133 85.333v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM85.333 290.133h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM155.223 99.284l-14.528-28.512c-30.106 15.34-54.583 39.817-69.923 69.923l57.024 29.055c9.204-18.063 23.89-32.75 41.953-41.953l-14.528-28.512z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "chevron-up-down-1"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1554,
        "id": 466,
        "name": "chevron-up-down-1",
        "prevSize": 32,
        "code": 59830
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 183
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ChevronUpDown-1IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ChevronUpDown-1Icon = ({ size = 16, ...props }: ChevronUpDown-1IconProps) => (
  <IcoMoon iconSet={iconSet} icon="chevron-up-down-1" size={size} {...props} />
);

ChevronUpDown-1Icon.displayName = 'ChevronUpDown-1Icon';

export default ChevronUpDown-1Icon;
