// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M286.815 704c-111.275 0-201.481-91.332-201.481-203.998 0-112.668 90.206-204.002 201.481-204.002 4.614 0 9.192 0.157 13.729 0.466M300.544 296.466c44.844-99.396 143.885-168.466 258.863-168.466 157.094 0 284.446 128.942 284.446 288 0 2.727-0.038 5.445-0.111 8.154M300.544 296.466c15.177-0.156 50.271 4.334 69.234 23.534M843.742 424.154c55.296 19.737 94.925 73.105 94.925 135.846 0 79.531-63.676 144-142.221 144M843.742 424.154c0.034 13.282-4.629 44.646-23.595 63.846M554.185 513.988l-132.997 195.793c-9.28 13.662-1.588 32.508 14.481 35.477l67.051 12.39c12.305 2.274 20.42 14.319 18.057 26.803l-20.070 106.095c-0.905 4.779 5.193 7.492 8.034 3.575l141.069-194.27c9.882-13.606 2.3-33.037-14.071-36.062l-67.452-12.467c-11.661-2.155-19.674-13.137-18.317-25.105l12.386-109.107c0.538-4.732-5.508-7.040-8.171-3.123z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "cloud-lightning"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1567,
        "id": 453,
        "name": "cloud-lightning",
        "prevSize": 32,
        "code": 59843
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 196
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CloudLightningIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CloudLightningIcon = ({ size = 16, ...props }: CloudLightningIconProps) => (
  <IcoMoon iconSet={iconSet} icon="cloud-lightning" size={size} {...props} />
);

CloudLightningIcon.displayName = 'CloudLightningIcon';

export default CloudLightningIcon;
