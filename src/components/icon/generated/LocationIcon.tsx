// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M853.333 426.667c0 188.514-341.333 512-341.333 512s-341.333-323.486-341.333-512c0-188.513 152.82-341.333 341.333-341.333s341.333 152.82 341.333 341.333z",
          "M640 426.667c0 70.694-57.306 128-128 128s-128-57.306-128-128c0-70.692 57.306-128 128-128s128 57.308 128 128z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "location"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1744,
        "id": 276,
        "name": "location",
        "prevSize": 32,
        "code": 60020
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 373
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface LocationIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const LocationIcon = ({ size = 16, ...props }: LocationIconProps) => (
  <IcoMoon iconSet={iconSet} icon="location" size={size} {...props} />
);

LocationIcon.displayName = 'LocationIcon';

export default LocationIcon;
