import { Collapsible, HoverCard } from 'radix-ui';
import React, { type ReactNode, useEffect, useState } from 'react';

import { MenuProvider, type MenuProviderProps, useMenu } from '@components/sidebar/menu-provider';
import { SidebarGroup, SidebarGroupLabel } from '@components/sidebar/sidebar-group';
import { SidebarContainer, SidebarContent, SidebarHeader } from '@components/sidebar/sidebar-layout';
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarRail,
} from '@components/sidebar/sidebar-menu';
import { SIDEBAR_COOKIE_NAME, SidebarProvider, useSidebar } from '@components/sidebar/sidebar-provider';
import { sidebarMenuItemPropDefs, sidebarPropDefs } from '@components/sidebar/sidebar.props';
import type { MenuItem } from '@components/sidebar/type';
import { extractProps } from '@helpers';
import { getCookie } from '@helpers/cookie';
import type { GetPropDefTypes } from '@props';

interface BaseSidebarProps extends MenuProviderProps {
  data: MenuItem[];
}

type SidebarProps = GetPropDefTypes<typeof sidebarMenuItemPropDefs> &
  GetPropDefTypes<typeof sidebarPropDefs> & {
    header?: ReactNode;
  } & BaseSidebarProps;

const Sidebar: React.FC<SidebarProps> = ({ header, data, defaultOpenKeys, onMenuSelected, ...props }) => {
  const sidebarState = getCookie(SIDEBAR_COOKIE_NAME);
  const defaultOpen = sidebarState ? sidebarState === 'true' : true;

  return (
    <SidebarProvider defaultOpen={defaultOpen}>
      <SidebarContainer {...props}>
        <SidebarHeader>{header}</SidebarHeader>
        <SidebarContent>
          <MenuProvider defaultOpenKeys={defaultOpenKeys} onMenuSelected={onMenuSelected}>
            <SidebarMenus data={data} {...props} />
          </MenuProvider>
        </SidebarContent>
        <SidebarRail {...props} />
      </SidebarContainer>
    </SidebarProvider>
  );
};

type SidebarMenusProps = GetPropDefTypes<typeof sidebarMenuItemPropDefs> & BaseSidebarProps;
const SidebarMenus: React.FC<SidebarMenusProps> = ({ data, ...props }) => {
  const menuItemProps = extractProps(props, sidebarMenuItemPropDefs);

  const [container, setContainer] = useState<HTMLElement | null>(null);
  const { openKeys, mergeKeys, onOpenKeyChange, onMenuSelected } = useMenu();
  const { state: sidebarState } = useSidebar();

  useEffect(() => {
    if (sidebarState === 'expanded') return;
    const observer = new MutationObserver(() => {
      const herondContainer = document.querySelector('.herond-themes');
      if (herondContainer) {
        observer.disconnect();
        setContainer(herondContainer as HTMLElement);
      }
    });

    const herondContainer = document.querySelector('.herond-themes');
    if (herondContainer) {
      setContainer(herondContainer as HTMLElement);
    } else {
      const observer = new MutationObserver(() => {
        const herondContainer = document.querySelector('.herond-themes');
        if (herondContainer) {
          observer.disconnect();
          setContainer(herondContainer as HTMLElement);
        }
      });

      observer.observe(document, { subtree: true, childList: true });
    }

    return () => observer.disconnect();
  }, [sidebarState]);

  const renderGroup = (group: MenuItem, children: MenuItem[]) => {
    return (
      <SidebarGroup key={group.key}>
        <SidebarGroupLabel>{group.title}</SidebarGroupLabel>
        <SidebarMenu>{renderTree(children, [group.key])}</SidebarMenu>
      </SidebarGroup>
    );
  };

  const renderMenus = (menus: MenuItem[]) => {
    const sidebarMenus: ReactNode[] = [];
    let sidebarMenusWithoutGroup: ReactNode[] = [];

    menus.forEach((menu, index) => {
      if (menu.type === 'group') {
        if (sidebarMenusWithoutGroup.length > 0) {
          sidebarMenus.push(<SidebarMenu key={index}>{sidebarMenusWithoutGroup}</SidebarMenu>);
        }
        sidebarMenusWithoutGroup = [];
        sidebarMenus.push(renderGroup(menu, menu.children ?? []));
      } else {
        sidebarMenusWithoutGroup.push(renderTree([menu], ['root']));
      }
    });

    if (sidebarMenusWithoutGroup.length > 0)
      sidebarMenus.push(
        <SidebarGroup key={'root'}>
          <SidebarGroupLabel />
          <SidebarMenu>{sidebarMenusWithoutGroup}</SidebarMenu>
        </SidebarGroup>,
      );

    return sidebarMenus;
  };

  const renderTree = (menus: MenuItem[], rootPath: string[]) => {
    return menus.map((item) => {
      const path = [...rootPath, item.key];
      const key = mergeKeys(path);
      const open = openKeys?.startsWith(key);
      const state = openKeys?.startsWith(key) ? 'active' : 'inactive';
      if (item.children) {
        return sidebarState === 'expanded' ? (
          <Collapsible.Root asChild defaultOpen={open} key={key}>
            <SidebarMenuItem {...menuItemProps}>
              <Collapsible.Trigger asChild>
                <SidebarMenuButton
                  key={key}
                  data-active={state}
                  collapsible={true}
                  tooltip={item.title}
                  className={menuItemProps.className}
                  icon={item.icon}
                  title={item.title}
                />
              </Collapsible.Trigger>
              <Collapsible.Content>
                <SidebarMenuSub>{renderTree(item.children, path)}</SidebarMenuSub>
              </Collapsible.Content>
            </SidebarMenuItem>
          </Collapsible.Root>
        ) : (
          <HoverCard.Root key={key} openDelay={0}>
            <SidebarMenuItem {...menuItemProps}>
              <HoverCard.Trigger asChild>
                <SidebarMenuButton
                  key={key}
                  data-active={state}
                  collapsible={true}
                  tooltip={item.title}
                  className={menuItemProps.className}
                  icon={item.icon}
                  title={item.title}
                />
              </HoverCard.Trigger>
              <HoverCard.Portal container={container}>
                <HoverCard.Content className="rt-sidebar-hover-content" sideOffset={12} align="start" side="left">
                  <SidebarGroupLabel>{item.title}</SidebarGroupLabel>
                  <SidebarMenuSub>{renderTree(item.children, path)}</SidebarMenuSub>
                </HoverCard.Content>
              </HoverCard.Portal>
            </SidebarMenuItem>
          </HoverCard.Root>
        );
      }

      const subItem = (
        <SidebarMenuButton
          key={key}
          data-active={state}
          onClick={() => {
            if (key !== openKeys) {
              onOpenKeyChange(key);
              onMenuSelected?.(key, item);
            }
          }}
          tooltip={item.title}
          className={menuItemProps.className}
          icon={item.icon}
          title={item.title}
        />
      );
      return <SidebarMenuItem key={item.title}>{subItem}</SidebarMenuItem>;
    });
  };

  return renderMenus(data);
};

export { Sidebar };

export type { SidebarProps };
