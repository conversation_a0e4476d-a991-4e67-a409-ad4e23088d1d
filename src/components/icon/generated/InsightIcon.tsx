// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 170.667v637.154c0 25.135 21.225 45.513 47.407 45.513h805.926M891.26 329.955l-210.82 216.43c-26.871 30.49-75.149 32.418-104.567 4.177l-81.984-78.703c-28.809-27.657-75.885-26.47-103.139 2.603l-131.074 139.81"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "insight"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1723,
        "id": 297,
        "name": "insight",
        "prevSize": 32,
        "code": 59999
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 352
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface InsightIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const InsightIcon = ({ size = 16, ...props }: InsightIconProps) => (
  <IcoMoon iconSet={iconSet} icon="insight" size={size} {...props} />
);

InsightIcon.displayName = 'InsightIcon';

export default InsightIcon;
