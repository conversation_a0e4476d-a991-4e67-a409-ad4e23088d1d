// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M341.333 362.667l66.985 58.612c9.713 8.499 9.713 23.611 0 32.111l-66.985 58.611M682.667 362.667l-66.987 58.612c-9.711 8.499-9.711 23.611 0 32.111l66.987 58.611M333.401 722.773c26.463-23.966 90.579-72.107 178.595-72.107 88.021 0 152.137 48.141 178.598 72.107M938.667 512c0 235.639-191.027 426.667-426.667 426.667-235.642 0-426.667-191.027-426.667-426.667 0-235.642 191.025-426.667 426.667-426.667 235.639 0 426.667 191.025 426.667 426.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "squint"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1911,
        "id": 109,
        "name": "squint",
        "prevSize": 32,
        "code": 60187
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 540
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SquintIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SquintIcon = ({ size = 16, ...props }: SquintIconProps) => (
  <IcoMoon iconSet={iconSet} icon="squint" size={size} {...props} />
);

SquintIcon.displayName = 'SquintIcon';

export default SquintIcon;
