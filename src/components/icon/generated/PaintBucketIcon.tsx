// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M267.148 336.303l-147.251 149.086c-46.085 46.66-46.084 122.308 0 168.964l225.181 227.985c46.084 46.66 120.804 46.66 166.888 0l367.147-371.721c9.22-9.331 9.22-24.461 0-33.796l-358.69-363.156c-9.216-9.332-24.158-9.332-33.374 0l-219.901 222.638zM267.148 336.303l260.088 62.697M267.148 336.303l-178.129-42.94M527.236 399c-1.041 4.442-1.591 9.076-1.591 13.84 0 32.993 26.415 59.736 59.004 59.736 32.585 0 59.004-26.743 59.004-59.736s-26.419-59.738-59.004-59.738c-27.883 0-51.247 19.579-57.412 45.899zM938.667 841.199c0 42.048-31.838 76.134-71.113 76.134-39.27 0-71.108-34.086-71.108-76.134 0-34.556 48.026-93.116 65.139-112.887 2.65-3.059 7.42-1.818 9.109 1.937 15.919 35.392 67.972 72.094 67.972 110.95z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "paint-bucket"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1811,
        "id": 209,
        "name": "paint-bucket",
        "prevSize": 32,
        "code": 60087
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 440
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface PaintBucketIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const PaintBucketIcon = ({ size = 16, ...props }: PaintBucketIconProps) => (
  <IcoMoon iconSet={iconSet} icon="paint-bucket" size={size} {...props} />
);

PaintBucketIcon.displayName = 'PaintBucketIcon';

export default PaintBucketIcon;
