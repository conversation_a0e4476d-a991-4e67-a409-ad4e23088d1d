import type { Meta, StoryObj } from '@storybook/react-vite';

import { Card } from '@components/card';
import { cardPropDefs } from '@components/card/card.props';

const meta = {
  title: 'DesignSystem/Card',
  component: Card,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    size: { control: 'inline-radio', options: cardPropDefs.size.values },
    variant: { control: 'inline-radio', options: cardPropDefs.variant.values },
    mask: { control: 'boolean' },
  },
} satisfies Meta<typeof Card>;

export default meta;
type Story = StoryObj<typeof meta>;

export const _card: Story = {
  args: {
    size: cardPropDefs.size.default,
    variant: cardPropDefs.variant.default,
    mask: cardPropDefs.mask.default,
    children: 'This is a card',
  },
};
