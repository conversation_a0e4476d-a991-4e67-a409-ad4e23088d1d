// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M341.333 362.667l66.985 58.612c9.713 8.499 9.713 23.611 0 32.111l-66.985 58.611M682.667 362.667l-66.987 58.612c-9.711 8.499-9.711 23.611 0 32.111l66.987 58.611M320 704l34.896-23.266c16.922-11.281 39.456-9.050 53.837 5.333l9.097 9.097c16.662 16.661 43.678 16.661 60.34 0l3.661-3.661c16.661-16.661 43.678-16.661 60.339 0l3.661 3.661c16.661 16.661 43.678 16.661 60.339 0l9.097-9.097c14.383-14.383 36.915-16.614 53.837-5.333l34.897 23.266M938.667 512c0 235.639-191.027 426.667-426.667 426.667-235.642 0-426.667-191.027-426.667-426.667 0-235.642 191.025-426.667 426.667-426.667 235.639 0 426.667 191.025 426.667 426.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "silent-squint"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1893,
        "id": 127,
        "name": "silent-squint",
        "prevSize": 32,
        "code": 60169
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 522
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SilentSquintIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SilentSquintIcon = ({ size = 16, ...props }: SilentSquintIconProps) => (
  <IcoMoon iconSet={iconSet} icon="silent-squint" size={size} {...props} />
);

SilentSquintIcon.displayName = 'SilentSquintIcon';

export default SilentSquintIcon;
