// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M938.667 341.333h-405.333M85.333 341.333h106.667M192 341.333c0 58.91 47.756 106.667 106.667 106.667s106.667-47.756 106.667-106.667c0-58.91-47.756-106.667-106.667-106.667s-106.667 47.756-106.667 106.667zM85.333 682.667h405.333M938.667 682.667h-106.667M832 682.667c0 58.91-47.757 106.667-106.667 106.667s-106.667-47.757-106.667-106.667c0-58.91 47.757-106.667 106.667-106.667s106.667 47.757 106.667 106.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "adjustment-horizontal"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1376,
        "id": 644,
        "name": "adjustment-horizontal",
        "prevSize": 32,
        "code": 59652
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 5
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface AdjustmentHorizontalIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const AdjustmentHorizontalIcon = ({ size = 16, ...props }: AdjustmentHorizontalIconProps) => (
  <IcoMoon iconSet={iconSet} icon="adjustment-horizontal" size={size} {...props} />
);

AdjustmentHorizontalIcon.displayName = 'AdjustmentHorizontalIcon';

export default AdjustmentHorizontalIcon;
