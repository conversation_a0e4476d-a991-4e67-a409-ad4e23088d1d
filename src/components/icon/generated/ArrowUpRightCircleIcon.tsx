// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M638.673 569.259l1.327-181.257c-0.102-1.002-0.602-1.951-1.327-2.675M454.741 385.327l181.257-1.327c1.003 0.103 1.95 0.602 2.675 1.327M638.673 385.327l-254.673 254.673M938.667 512c0 235.639-191.027 426.667-426.667 426.667-235.642 0-426.667-191.027-426.667-426.667 0-235.642 191.025-426.667 426.667-426.667 235.639 0 426.667 191.025 426.667 426.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "arrow-up-right-circle"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1430,
        "id": 590,
        "name": "arrow-up-right-circle",
        "prevSize": 32,
        "code": 59706
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 59
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ArrowUpRightCircleIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ArrowUpRightCircleIcon = ({ size = 16, ...props }: ArrowUpRightCircleIconProps) => (
  <IcoMoon iconSet={iconSet} icon="arrow-up-right-circle" size={size} {...props} />
);

ArrowUpRightCircleIcon.displayName = 'ArrowUpRightCircleIcon';

export default ArrowUpRightCircleIcon;
