// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M300.544 411.333c-4.537-0.303-9.115-0.457-13.729-0.457-111.275 0-201.481 89.497-201.481 199.897 0 110.396 90.206 199.893 201.481 199.893h509.631c78.545 0 142.221-63.172 142.221-141.103 0-8.013-0.674-15.868-1.967-23.518-3.362-19.887-10.918-38.366-21.739-54.507M300.544 411.333c15.177-0.152 50.271 4.247 69.234 23.060M300.544 411.333c31.877-69.233 91.142-123.452 164.049-149.018 29.658-10.4 61.568-16.058 94.814-16.058M938.667 354.436c0 77.927-63.676 141.103-142.221 141.103-78.549 0-142.225-63.177-142.225-141.103 0-77.929 63.676-141.103 142.225-141.103 78.545 0 142.221 63.174 142.221 141.103z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "cloud-notif"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1569,
        "id": 451,
        "name": "cloud-notif",
        "prevSize": 32,
        "code": 59845
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 198
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CloudNotifIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CloudNotifIcon = ({ size = 16, ...props }: CloudNotifIconProps) => (
  <IcoMoon iconSet={iconSet} icon="cloud-notif" size={size} {...props} />
);

CloudNotifIcon.displayName = 'CloudNotifIcon';

export default CloudNotifIcon;
