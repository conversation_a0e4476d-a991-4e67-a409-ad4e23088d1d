// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M431.902 449.557l46.217-76.181c15.206-25.068 35.187-46.979 58.833-64.519l281.276-208.634c29.969-22.229 71.902-19.281 98.389 6.916 26.492 26.197 29.47 67.671 6.993 97.309l-210.953 278.176c-17.737 23.39-39.893 43.149-65.237 58.193l-77.030 45.705M547.311 335.417c69.786 38.026 101.026 66.961 138.487 136.967M173.74 522.253c91.587-90.577 238.171-92.467 327.405-4.211 89.233 88.252 87.326 233.22-4.258 323.802-75.268 74.436-311.943 92.668-393.901 96.802-12.675 0.64-20.818-12.762-16.474-24.653 40.918-112 2.59-308.032 87.228-391.74z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "brush"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1492,
        "id": 528,
        "name": "brush",
        "prevSize": 32,
        "code": 59768
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 121
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BrushIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BrushIcon = ({ size = 16, ...props }: BrushIconProps) => (
  <IcoMoon iconSet={iconSet} icon="brush" size={size} {...props} />
);

BrushIcon.displayName = 'BrushIcon';

export default BrushIcon;
