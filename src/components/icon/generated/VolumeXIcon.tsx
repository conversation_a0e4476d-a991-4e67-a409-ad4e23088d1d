// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M725.333 426.667l85.333 85.333M810.667 512l85.333 85.333M810.667 512l85.333-85.333M810.667 512l-85.333 85.333M232.656 300.354h24.457c24.753 0 48.705-8.876 67.602-25.050l123.656-105.846c50.999-43.654 129.421-12.205 135.356 55.11 18.193 206.251 18.138 366.174-0.141 574.75-5.905 67.379-84.395 98.914-135.441 55.223l-123.43-105.655c-18.897-16.175-42.848-25.050-67.602-25.050h-24.457c-57.8 0-104.656-47.398-104.656-105.869v-211.743c0-58.471 46.856-105.871 104.656-105.871z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "volume-x"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1997,
        "id": 23,
        "name": "volume-x",
        "prevSize": 32,
        "code": 60273
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 626
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface VolumeXIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const VolumeXIcon = ({ size = 16, ...props }: VolumeXIconProps) => (
  <IcoMoon iconSet={iconSet} icon="volume-x" size={size} {...props} />
);

VolumeXIcon.displayName = 'VolumeXIcon';

export default VolumeXIcon;
