import clsx from 'clsx';
import { RadioGroup as RadioGroupPrimitive } from 'radix-ui';
import * as React from 'react';

import { Grid } from '@components/grid';
import { radioCardsRootPropDefs } from '@components/radio-cards/radio-cards.props';
import { type ComponentPropsWithout, type RemovedProps, extractProps } from '@helpers';
import { type GetPropDefTypes, type MarginProps, type Options, marginPropDefs } from '@props';

type RadioCardsRootElement = React.ComponentRef<typeof RadioGroupPrimitive.Root>;
type RadioCardsRootOwnProps = GetPropDefTypes<typeof radioCardsRootPropDefs>;
interface RadioCardsRootProps
  extends ComponentPropsWithout<
      typeof RadioGroupPrimitive.Root,
      'asChild' | 'color' | 'defaultChecked' | 'orientation'
    >,
    MarginProps,
    RadioCardsRootOwnProps {
  options?: Options<string>[];
  orientation?: 'horizontal' | 'vertical';
}
const RadioCardsRoot = React.forwardRef<RadioCardsRootElement, RadioCardsRootProps>((props, forwardedRef) => {
  const { className, color, ...rootProps } = extractProps(props, radioCardsRootPropDefs, marginPropDefs);
  return (
    <Grid asChild>
      <RadioGroupPrimitive.Root
        data-accent-color={color}
        {...rootProps}
        ref={forwardedRef}
        className={clsx('rt-RadioCardsRoot', className)}
      />
    </Grid>
  );
});
RadioCardsRoot.displayName = 'RadioCards.Root';

const RadioCardsGroup: React.FC<RadioCardsRootProps> = (props) => {
  const { options } = props;
  return (
    <RadioCardsRoot {...props} className={clsx(`rt-${props.orientation}`, props.className)}>
      {options?.map((option) => (
        <RadioCardsItem {...option} value={option.value} disabled={option.disabled}>
          {option.label}
        </RadioCardsItem>
      ))}
    </RadioCardsRoot>
  );
};

RadioCardsGroup.displayName = 'RadioCardsGroup';

type RadioCardsItemElement = React.ComponentRef<typeof RadioGroupPrimitive.Item>;
interface RadioCardsItemProps
  extends ComponentPropsWithout<typeof RadioGroupPrimitive.Item, RemovedProps>,
    MarginProps {}
const RadioCardsItem = React.forwardRef<RadioCardsItemElement, RadioCardsItemProps>(
  ({ className, ...props }, forwardedRef) => (
    <RadioGroupPrimitive.Item
      {...props}
      asChild={false}
      ref={forwardedRef}
      className={clsx('rt-reset', 'rt-BaseCard', 'rt-RadioCardsItem', className)}
    />
  ),
);
RadioCardsItem.displayName = 'RadioCards.Item';

export { RadioCardsItem as Item, RadioCardsGroup, RadioCardsRoot as Root };
export type { RadioCardsItemProps as ItemProps, RadioCardsRootProps as RootProps };
