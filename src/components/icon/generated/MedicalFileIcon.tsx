// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M469.333 779.499c-17.673 0-32 14.323-32 32 0 17.673 14.327 32 32 32v-64zM553.463 808.751v0zM605.995 685.41v0zM615.040 685.986v0zM678.758 935.347v0zM687.898 935.676v0zM751.168 754.743v0zM759.33 753.293v0zM811.614 809.954v0zM896 843.499c17.673 0 32-14.327 32-32 0-17.677-14.327-32-32-32v64zM613.491 122.824v0zM815.842 325.176v0zM240.556 924.715v0zM184.618 868.779v0zM184.618 155.223v0zM240.556 99.284v0zM341.333 544c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM682.667 608c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM341.333 693.333c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM405.333 757.333c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM841.835 362.667v0zM512 970.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM821.333 640c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM469.333 811.499v32h79.706v-64h-79.706v32zM553.463 808.751l29.444 12.54 52.527-123.341-58.88-25.075-52.531 123.341 29.44 12.535zM615.040 685.986l-31.006 7.923 63.723 249.361 62.007-15.846-63.723-249.361-31.002 7.923zM687.898 935.676l30.208 10.56 63.266-180.928-60.412-21.124-63.266 180.928 30.204 10.564zM759.33 753.293l-23.518 21.7 52.288 56.666 47.031-43.405-52.284-56.661-23.518 21.7zM815.258 811.499v32h80.742v-64h-80.742v32zM811.614 809.954l-23.514 21.705c7.351 7.966 17.412 11.84 27.157 11.84v-64c6.929 0 14.323 2.743 19.874 8.755l-23.518 21.7zM751.168 754.743l30.204 10.564c-3.942 11.281-13.444 16.687-20.996 18.031-7.569 1.344-17.34-0.516-24.563-8.346l47.036-43.401c-9.626-10.432-22.912-13.18-33.681-11.264-10.752 1.911-23.134 9.344-28.207 23.855l30.208 10.56zM678.758 935.347l-31.002 7.923c4.919 19.255 21.956 26.931 34.291 27.371 12.335 0.444 29.602-5.939 36.058-24.405l-60.412-21.124c5.001-14.306 18.24-18.726 26.641-18.428s21.594 5.734 25.429 20.74l-31.006 7.923zM605.995 685.41l29.44 12.54c-5.662 13.299-18.381 17.19-26.709 16.666-8.333-0.529-20.966-6.14-24.691-20.706l62.007-15.846c-4.774-18.684-21.094-26.547-33.263-27.319s-28.881 4.89-36.224 22.131l29.44 12.535zM549.039 811.499v32c13.278 0 27.571-7.424 33.869-22.208l-58.884-25.075c4.89-11.482 15.663-16.717 25.015-16.717v32zM170.667 733.867h32v-443.733h-64v443.733h32zM613.491 122.824l-22.63 22.627 202.355 202.353 45.252-45.255-202.351-202.353-22.626 22.627zM375.467 85.333v32h147.516v-64h-147.516v32zM375.467 938.667v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM170.667 733.867h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM240.556 924.715l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM170.667 290.133h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM375.467 85.333v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM184.618 155.223l28.512 14.528c9.204-18.063 23.89-32.75 41.953-41.953l-29.055-57.024c-30.106 15.34-54.583 39.817-69.923 69.923l28.512 14.528zM341.333 576v32h341.333v-64h-341.333v32zM341.333 725.333v32h64v-64h-64v32zM613.491 122.824l22.626-22.627c-13.679-13.681-29.585-24.629-46.861-32.492l-26.513 58.252c10.368 4.718 19.908 11.285 28.117 19.495l22.63-22.627zM576 96.83l13.257-29.126c-20.617-9.381-43.191-14.371-66.274-14.371v64c13.85 0 27.392 2.993 39.761 8.623l13.257-29.126zM576 96.83h-32v137.836h64v-137.836h-32zM853.333 415.686h32c0-23.087-4.992-45.66-14.37-66.274l-58.253 26.509c5.632 12.372 8.623 25.913 8.623 39.765h32zM841.835 362.667l29.129-13.254c-7.863-17.277-18.812-33.183-32.495-46.863l-45.252 45.255c8.209 8.209 14.775 17.75 19.494 28.117l29.124-13.254zM704 362.667v32h137.835v-64h-137.835v32zM576 234.667h-32c0 88.366 71.633 160 160 160v-64c-53.018 0-96-42.981-96-96h-32zM512 938.667v-32h-136.533v64h136.533v-32zM853.333 415.686h-32v224.314h64v-224.314h-32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "medical-file"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1760,
        "id": 260,
        "name": "medical-file",
        "prevSize": 32,
        "code": 60036
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 389
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MedicalFileIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MedicalFileIcon = ({ size = 16, ...props }: MedicalFileIconProps) => (
  <IcoMoon iconSet={iconSet} icon="medical-file" size={size} {...props} />
);

MedicalFileIcon.displayName = 'MedicalFileIcon';

export default MedicalFileIcon;
