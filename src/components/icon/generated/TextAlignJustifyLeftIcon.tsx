// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 213.333h426.667M85.333 412.445h426.667M85.333 611.554h853.333M85.333 810.667h853.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "text-align-justify-left"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1941,
        "id": 79,
        "name": "text-align-justify-left",
        "prevSize": 32,
        "code": 60217
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 570
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface TextAlignJustifyLeftIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const TextAlignJustifyLeftIcon = ({ size = 16, ...props }: TextAlignJustifyLeftIconProps) => (
  <IcoMoon iconSet={iconSet} icon="text-align-justify-left" size={size} {...props} />
);

TextAlignJustifyLeftIcon.displayName = 'TextAlignJustifyLeftIcon';

export default TextAlignJustifyLeftIcon;
