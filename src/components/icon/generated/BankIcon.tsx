// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M180.148 369.778h-67.587c-26.89 0-37.449-33.21-15.075-47.414l339.135-215.297c45.645-28.977 105.114-28.977 150.758 0l339.136 215.297c22.374 14.204 11.814 47.414-15.078 47.414h-67.584M180.148 369.778h118.519M180.148 369.778v450.369M298.667 369.778h142.221M298.667 369.778v450.369M440.887 369.778h142.225M440.887 369.778v450.369M583.113 369.778h142.221M583.113 369.778v450.369M725.333 369.778h118.519M725.333 369.778v450.369M843.853 369.778v450.369M85.333 938.667h47.407M132.741 938.667h758.519M132.741 938.667v-47.407c0-39.275 31.837-71.113 71.111-71.113h94.815M891.26 938.667h47.407M891.26 938.667v-47.407c0-39.275-31.838-71.113-71.113-71.113h-94.814M298.667 820.147h142.221M440.887 820.147h142.225M583.113 820.147h142.221"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bank"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1451,
        "id": 569,
        "name": "bank",
        "prevSize": 32,
        "code": 59727
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 80
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": ********,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BankIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BankIcon = ({ size = 16, ...props }: BankIconProps) => (
  <IcoMoon iconSet={iconSet} icon="bank" size={size} {...props} />
);

BankIcon.displayName = 'BankIcon';

export default BankIcon;
