// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M401.867 725.333l-28.987 157.756c-5.33 29.009 18.109 55.578 49.033 55.578h180.175c30.925 0 54.366-26.569 49.033-55.578l-28.988-157.756M263.111 440.887v71.113c0 130.91 111.431 237.039 248.889 237.039 137.459 0 248.887-106.129 248.887-237.039v-71.113M263.111 440.887h-49.778M263.111 440.887h55.751M760.887 440.887h49.779M760.887 440.887h-56.247M288 203.852l28 213.333 2.862 23.702M288 203.852h448M288 203.852c-13.746 0-24.889-10.612-24.889-23.704v-23.703c0-39.274 33.429-71.111 74.667-71.111h348.443c41.237 0 74.667 31.837 74.667 71.111v23.703c0 13.091-11.14 23.704-24.887 23.704M736 203.852l-28.002 213.333-3.358 23.702M318.862 440.887h385.778"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "toilet"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1957,
        "id": 63,
        "name": "toilet",
        "prevSize": 32,
        "code": 60233
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 586
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ToiletIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ToiletIcon = ({ size = 16, ...props }: ToiletIconProps) => (
  <IcoMoon iconSet={iconSet} icon="toilet" size={size} {...props} />
);

ToiletIcon.displayName = 'ToiletIcon';

export default ToiletIcon;
