// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M266.87 455.433l-94.281 94.281c-83.312 83.311-83.312 218.385 0 301.696 83.312 83.315 218.387 83.315 301.698 0l94.281-94.281M455.433 266.869l94.281-94.281c83.311-83.312 218.385-83.312 301.7 0 83.311 83.312 83.311 218.387 0 301.699l-94.285 94.281M218.667 218.667l-80-80M805.333 805.333l80 80M352 192v-106.667M672 832v106.667M192 352h-106.667M832 672h106.667"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "link-broken"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1734,
        "id": 286,
        "name": "link-broken",
        "prevSize": 32,
        "code": 60010
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 363
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface LinkBrokenIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const LinkBrokenIcon = ({ size = 16, ...props }: LinkBrokenIconProps) => (
  <IcoMoon iconSet={iconSet} icon="link-broken" size={size} {...props} />
);

LinkBrokenIcon.displayName = 'LinkBrokenIcon';

export default LinkBrokenIcon;
