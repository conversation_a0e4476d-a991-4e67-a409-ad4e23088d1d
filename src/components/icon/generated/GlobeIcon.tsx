// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M932.766 583.113c3.883-23.13 5.901-46.882 5.901-71.113 0-65.438-14.733-127.436-41.058-182.857M932.766 583.113c-33.852 201.792-209.353 355.554-420.766 355.554M932.766 583.113c-25.382 60.523-130.406 110.246-270.498 131.337M512 938.667c-211.413 0-386.913-153.762-420.768-355.554M512 938.667c64.951 0 121.429-90.709 150.268-224.218M512 938.667c-64.951 0-121.429-90.709-150.269-224.218M91.232 583.113c-3.88-23.125-5.899-46.882-5.899-71.113 0-65.438 14.732-127.436 41.059-182.857M91.232 583.113c25.385 60.523 130.41 110.246 270.499 131.337M662.268 714.449c-46.737 7.036-97.378 10.884-150.268 10.884s-103.531-3.849-150.269-10.884M662.268 714.449c13.013-60.237 20.399-129.186 20.399-202.449 0-33.988-1.591-67.051-4.595-98.744M361.731 714.449c-13.012-60.237-20.398-129.186-20.398-202.449 0-33.988 1.59-67.051 4.593-98.744M345.927 413.256c17.821-188.031 85.412-327.922 166.073-327.922M345.927 413.256c51.045 8.636 107.163 13.411 166.073 13.411s115.029-4.775 166.071-13.411M345.927 413.256c-96.437-16.315-174.757-46.409-219.534-84.113M512 85.333c80.661 0 148.254 139.892 166.071 327.922M512 85.333c-170.203 0-317.129 99.66-385.607 243.81M512 85.333c170.202 0 317.129 99.66 385.609 243.81M678.071 413.256c96.439-16.315 174.758-46.409 219.537-84.113",
          "M662.268 714.449c13.013-60.237 20.399-129.186 20.399-202.449 0-33.988-1.591-67.051-4.595-98.744-17.818-188.031-85.41-327.922-166.071-327.922s-148.252 139.892-166.073 327.922c-3.004 31.694-4.593 64.756-4.593 98.744 0 73.263 7.386 142.212 20.398 202.449 28.84 133.508 85.318 224.218 150.269 224.218s121.429-90.709 150.268-224.218z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "globe"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1693,
        "id": 327,
        "name": "globe",
        "prevSize": 32,
        "code": 59969
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 322
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface GlobeIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const GlobeIcon = ({ size = 16, ...props }: GlobeIconProps) => (
  <IcoMoon iconSet={iconSet} icon="globe" size={size} {...props} />
);

GlobeIcon.displayName = 'GlobeIcon';

export default GlobeIcon;
