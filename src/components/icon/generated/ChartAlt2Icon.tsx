// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M264.533 512h-102.4c-11.948 0-17.922 0-22.485 2.325-4.014 2.044-7.278 5.308-9.323 9.323-2.325 4.565-2.325 10.539-2.325 22.485v358.4c0 11.947 0 17.92 2.325 22.485 2.045 4.015 5.309 7.279 9.323 9.323 4.564 2.325 10.537 2.325 22.485 2.325h102.4c11.948 0 17.922 0 22.485-2.325 4.014-2.044 7.278-5.308 9.323-9.323 2.325-4.565 2.325-10.539 2.325-22.485v-358.4c0-11.947 0-17.92-2.325-22.485-2.045-4.015-5.309-7.279-9.323-9.323-4.564-2.325-10.537-2.325-22.485-2.325z",
          "M563.2 85.333h-102.4c-11.947 0-17.92 0-22.485 2.325-4.015 2.045-7.279 5.309-9.323 9.323-2.325 4.564-2.325 10.537-2.325 22.485v785.067c0 11.947 0 17.92 2.325 22.485 2.044 4.015 5.308 7.279 9.323 9.323 4.565 2.325 10.539 2.325 22.485 2.325h102.4c11.947 0 17.92 0 22.485-2.325 4.015-2.044 7.279-5.308 9.323-9.323 2.325-4.565 2.325-10.539 2.325-22.485v-785.067c0-11.948 0-17.922-2.325-22.485-2.044-4.014-5.308-7.278-9.323-9.323-4.565-2.325-10.539-2.325-22.485-2.325z",
          "M861.867 298.667h-102.4c-11.947 0-17.92 0-22.485 2.325-4.015 2.045-7.279 5.309-9.323 9.323-2.325 4.564-2.325 10.537-2.325 22.485v571.733c0 11.947 0 17.92 2.325 22.485 2.044 4.015 5.308 7.279 9.323 9.323 4.565 2.325 10.539 2.325 22.485 2.325h102.4c11.947 0 17.92 0 22.485-2.325 4.015-2.044 7.279-5.308 9.323-9.323 2.325-4.565 2.325-10.539 2.325-22.485v-571.733c0-11.948 0-17.922-2.325-22.485-2.044-4.014-5.308-7.278-9.323-9.323-4.565-2.325-10.539-2.325-22.485-2.325z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "chart-alt2"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1535,
        "id": 485,
        "name": "chart-alt2",
        "prevSize": 32,
        "code": 59811
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 164
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ChartAlt2IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ChartAlt2Icon = ({ size = 16, ...props }: ChartAlt2IconProps) => (
  <IcoMoon iconSet={iconSet} icon="chart-alt2" size={size} {...props} />
);

ChartAlt2Icon.displayName = 'ChartAlt2Icon';

export default ChartAlt2Icon;
