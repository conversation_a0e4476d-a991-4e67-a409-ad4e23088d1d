// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M798.118 701.628c-65.843 0-119.215 53.065-119.215 118.519s53.372 118.519 119.215 118.519c65.839 0 119.215-53.065 119.215-118.519s-53.376-118.519-119.215-118.519zM798.118 701.628v-282.758c0-31.433-12.561-61.579-34.918-83.805l-224.563-223.25M726.588 109.037h-181.205c-5.269 0-9.54 4.245-9.54 9.481v156.444M225.882 322.371c65.841 0 119.216-53.063 119.216-118.519s-53.375-118.519-119.216-118.519c-65.841 0-119.216 53.062-119.216 118.519s53.375 118.519 119.216 118.519zM225.882 322.371v379.257M225.882 701.628c-65.841 0-119.216 53.065-119.216 118.519s53.375 118.519 119.216 118.519c65.841 0 119.216-53.065 119.216-118.519s-53.375-118.519-119.216-118.519z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "git-pull-request"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1692,
        "id": 328,
        "name": "git-pull-request",
        "prevSize": 32,
        "code": 59968
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 321
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface GitPullRequestIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const GitPullRequestIcon = ({ size = 16, ...props }: GitPullRequestIconProps) => (
  <IcoMoon iconSet={iconSet} icon="git-pull-request" size={size} {...props} />
);

GitPullRequestIcon.displayName = 'GitPullRequestIcon';

export default GitPullRequestIcon;
