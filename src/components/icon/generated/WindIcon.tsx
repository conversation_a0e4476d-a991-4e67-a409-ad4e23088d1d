// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M483.251 320.884c0 0 77.935 37.953 133.658 34.442 82.441-5.195 133.658-34.442 167.782-98.026 13.939-25.977 15.748-62.363 0-87.428-20.851-33.182-62.562-47.689-96.687-39.74-39.040 9.093-64.998 50.649-48.346 84.779 9.502 19.474 26.129 25.187 40.917 25.838M398.001 703.117c0 0-77.935-37.952-133.656-34.445-82.441 5.197-133.656 34.445-167.781 98.027-13.942 25.98-15.749 62.366 0 87.428 20.849 33.182 62.563 47.689 96.687 39.74 39.040-9.092 64.996-50.645 48.344-84.779-7.002-14.349-17.873-21.227-29.027-24.115M938.667 580.949c0 0-116.006 113.937-237.615 104.559-168.593-13.005-249.404-115.52-362.303-143.424-22.454-5.547-43.797-7.706-62.268-8.115M85.333 609.655c16.464-19.631 28.51-30.217 59.159-46.579M86.045 474.884c0 0 44.032-60.9 129.538-65.81 163.869-9.41 286.058 97.533 401.894 90.881 110.336-6.336 149.47-64.171 149.47-64.171"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "wind"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 2012,
        "id": 8,
        "name": "wind",
        "prevSize": 32,
        "code": 60288
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 641
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface WindIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const WindIcon = ({ size = 16, ...props }: WindIconProps) => (
  <IcoMoon iconSet={iconSet} icon="wind" size={size} {...props} />
);

WindIcon.displayName = 'WindIcon';

export default WindIcon;
