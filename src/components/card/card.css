/* stylelint-disable selector-max-type */
/* Disable selector-max-type rule to target `button` and `a` tags. */
/* Make sure these tags are wrapped in `:where()` for 0 specificity. */

.rt-Card {
  --base-card-padding-top: var(--card-padding);
  --base-card-padding-right: var(--card-padding);
  --base-card-padding-bottom: var(--card-padding);
  --base-card-padding-left: var(--card-padding);
  --base-card-border-radius: var(--card-border-radius);
  --base-card-border-width: var(--card-border-width);

  &:where(:focus-visible) {
    outline: 2px solid var(--brand-accent-40);
    outline-offset: -1px;

    &::after {
      outline: inherit;
    }
  }

  &:where(.rt-mask) {
    position: relative;
    overflow: hidden;

    &::before {
      background-color: var(--background-grouped-primary-elevated) !important;
    }

    &::after {
      content: '';
      position: absolute;
      inset: 0;
      backdrop-filter: blur(6px);
      z-index: 2;
      pointer-events: none;
      border-radius: inherit;
    }
  }
}

/* * * * * * * * * * * * * * * * * * * */
/*                                     */
/*                Sizes                */
/*                                     */
/* * * * * * * * * * * * * * * * * * * */

@breakpoints {
  .rt-Card {
    &:where(.rt-r-size-1) {
      --card-padding: var(--spatial-3);
      --card-border-radius: var(--radius-4);
    }
    &:where(.rt-r-size-2) {
      --card-padding: var(--spatial-4);
      --card-border-radius: var(--radius-5);
    }
    &:where(.rt-r-size-3) {
      --card-padding: var(--spatial-5);
      --card-border-radius: var(--radius-6);
    }
    &:where(.rt-r-size-4) {
      --card-padding: var(--spatial-6);
      --card-border-radius: var(--radius-7);
    }
    &:where(.rt-r-size-5) {
      --card-padding: var(--spatial-7);
      --card-border-radius: var(--radius-8);
    }
  }
}

/* * * * * * * * * * * * * * * * * * * */
/*                                     */
/*         Variants / Surface          */
/*                                     */
/* * * * * * * * * * * * * * * * * * * */

.rt-Card:where(.rt-variant-surface) {
  --card-border-width: 1px;

  &::before {
    background-color: var(--background-primary-base);
  }
  &::after {
    box-shadow: var(--base-card-surface-box-shadow);
  }
  &:where(:any-link, button, label) {
    @media (hover: hover) {
      &:where(:hover) {
        &::after {
          box-shadow: var(--base-card-surface-hover-box-shadow);
        }
      }
    }
    &:where([data-state='open']) {
      &::after {
        box-shadow: var(--base-card-surface-hover-box-shadow);
      }
    }
    &:where(:active:not([data-state='open'])) {
      &::after {
        box-shadow: var(--base-card-surface-active-box-shadow);
      }
    }
  }
}

/* * * * * * * * * * * * * * * * * * * */
/*                                     */
/*         Variants / Soft             */
/*                                     */
/* * * * * * * * * * * * * * * * * * * */

.rt-Card:where(.rt-variant-soft) {
  --card-border-width: 0px;

  &::before {
    background-color: var(--background-primary-base);
  }
  &::after {
    box-shadow: var(--base-card-surface-box-shadow);
  }
  &:where(:any-link, button, label) {
    @media (hover: hover) {
      &:where(:hover) {
        &::after {
          box-shadow: var(--base-card-surface-hover-box-shadow);
        }
      }
    }
    &:where([data-state='open']) {
      &::after {
        box-shadow: var(--base-card-surface-hover-box-shadow);
      }
    }
    &:where(:active:not([data-state='open'])) {
      &::after {
        box-shadow: var(--base-card-surface-active-box-shadow);
      }
    }
  }
}

/* * * * * * * * * * * * * * * * * * * */
/*                                     */
/*         Variants / Classic          */
/*                                     */
/* * * * * * * * * * * * * * * * * * * */

.rt-Card:where(.rt-variant-classic) {
  --card-border-width: 1px;

  transition: box-shadow 120ms;
  box-shadow: var(--base-card-classic-box-shadow-outer);

  &::before {
    background-color: var(--background-primary-base);
  }
  &::after {
    box-shadow: var(--base-card-classic-box-shadow-inner);
  }
  &:where(:any-link, button, label) {
    @media (hover: hover) {
      &:where(:hover) {
        transition-duration: 40ms;
        box-shadow: var(--base-card-classic-hover-box-shadow-outer);
        &::after {
          box-shadow: var(--base-card-classic-hover-box-shadow-inner);
        }
      }
    }
    &:where([data-state='open']) {
      transition-duration: 40ms;
      box-shadow: var(--base-card-classic-hover-box-shadow-outer);
      &::after {
        box-shadow: var(--base-card-classic-hover-box-shadow-inner);
      }
    }
    &:where(:active:not([data-state='open'])) {
      transition-duration: 40ms;
      box-shadow: var(--base-card-classic-active-box-shadow-outer);
      &::after {
        box-shadow: var(--base-card-classic-active-box-shadow-inner);
      }
    }
  }
}

/* * * * * * * * * * * * * * * * * * * */
/*                                     */
/*          Variants / Ghost           */
/*                                     */
/* * * * * * * * * * * * * * * * * * * */

.rt-Card:where(.rt-variant-ghost) {
  --card-border-width: 0px;

  /* We reset the defined margin variables to avoid inheriting them from a higher component */
  /* If a margin IS defined on the component itself, the utility class will win and reset it */
  --margin-top: 0px;
  --margin-right: 0px;
  --margin-bottom: 0px;
  --margin-left: 0px;

  /* Define the overrides to incorporate the negative margins */
  --margin-top-override: calc(var(--margin-top) - var(--card-padding));
  --margin-right-override: calc(var(--margin-right) - var(--card-padding));
  --margin-bottom-override: calc(var(--margin-bottom) - var(--card-padding));
  --margin-left-override: calc(var(--margin-left) - var(--card-padding));

  margin-top: var(--margin-top-override);
  margin-right: var(--margin-right-override);
  margin-bottom: var(--margin-bottom-override);
  margin-left: var(--margin-left-override);

  /* Reset the overrides on direct children */
  :where(&) > * {
    --margin-top-override: initial;
    --margin-right-override: initial;
    --margin-bottom-override: initial;
    --margin-left-override: initial;
  }
  &:where(:any-link, button, label) {
    @media (hover: hover) {
      &:where(:hover) {
        background-color: var(--brand-gray300);
        &:where(:focus-visible) {
          background-color: var(--brand-accent-40);
        }
      }
    }
    &:where([data-state='open']) {
      background-color: var(--brand-gray300);
      &:where(:focus-visible) {
        background-color: var(--brand-accent-40);
      }
    }
    &:where(:active:not([data-state='open'])) {
      background-color: var(--brand-gray400);
      &:where(:focus-visible) {
        background-color: var(--brand-accent-40);
      }
    }
  }
}

/* Better -webkit-tap-highlight-color */
.rt-Card:where(:any-link, button, label) {
  @media (pointer: coarse) {
    &:where(:active:not(:focus-visible, [data-state='open'])) {
      &::before {
        background-image: linear-gradient(var(--brand-gray400), var(--brand-gray400));
      }
    }
  }
}
