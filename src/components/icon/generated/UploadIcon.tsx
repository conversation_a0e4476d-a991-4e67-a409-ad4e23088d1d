// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M284.445 303.843l219.508-215.242c2.223-2.179 5.137-3.268 8.047-3.268M739.554 303.843l-219.507-215.242c-2.223-2.178-5.137-3.267-8.047-3.267M512 85.333v592.593M85.333 595.597v175.723c0 92.425 76.41 167.347 170.667 167.347h512c94.255 0 170.667-74.923 170.667-167.347v-175.723"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "upload"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1970,
        "id": 50,
        "name": "upload",
        "prevSize": 32,
        "code": 60246
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 599
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface UploadIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const UploadIcon = ({ size = 16, ...props }: UploadIconProps) => (
  <IcoMoon iconSet={iconSet} icon="upload" size={size} {...props} />
);

UploadIcon.displayName = 'UploadIcon';

export default UploadIcon;
