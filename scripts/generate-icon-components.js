#!/usr/bin/env node
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the full icon set
const iconSetPath = path.join(__dirname, '../src/components/icon/icons.json');
const iconSet = JSON.parse(fs.readFileSync(iconSetPath, 'utf8'));

// Create individual icon components directory
const iconComponentsDir = path.join(__dirname, '../src/components/icon/generated');
if (!fs.existsSync(iconComponentsDir)) {
  fs.mkdirSync(iconComponentsDir, { recursive: true });
}

// Helper function to convert kebab-case to PascalCase
const toPascalCase = (str) => {
  return str
    .split('-')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join('');
};

// Generate individual icon components
iconSet.icons.forEach((icon) => {
  const iconName = icon.properties.name;
  const componentName = toPascalCase(iconName) + 'Icon';

  const individualIconSet = {
    ...iconSet,
    icons: [icon],
  };

  const componentContent = `// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = ${JSON.stringify(individualIconSet, null, 2)};

export interface ${componentName}Props extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ${componentName} = ({ size = 16, ...props }: ${componentName}Props) => (
  <IcoMoon iconSet={iconSet} icon="${iconName}" size={size} {...props} />
);

${componentName}.displayName = '${componentName}';

export default ${componentName};
`;

  const componentFilePath = path.join(iconComponentsDir, `${componentName}.tsx`);
  fs.writeFileSync(componentFilePath, componentContent);
});

// Generate index file that exports all icon components
const iconComponents = iconSet.icons.map((icon) => {
  const iconName = icon.properties.name;
  const componentName = toPascalCase(iconName) + 'Icon';
  return { iconName, componentName };
});

const indexContent = `// Auto-generated icon exports - do not edit manually
${iconComponents
  .map(({ componentName }) => `export { ${componentName}, type ${componentName}Props } from './${componentName}';`)
  .join('\n')}

// Icon name to component mapping for dynamic usage
export const iconComponentMap = {
${iconComponents.map(({ iconName, componentName }) => `  '${iconName}': ${componentName},`).join('\n')}
} as const;

export type IconComponentName = keyof typeof iconComponentMap;
`;

fs.writeFileSync(path.join(iconComponentsDir, 'index.ts'), indexContent);

console.log(`Generated ${iconSet.icons.length} individual icon components`);
console.log(`Each component is tree-shakeable and only includes its specific icon data`);
console.log(`Total size reduction: Individual components are ~1-2KB each vs 1.3MB for all icons`);
