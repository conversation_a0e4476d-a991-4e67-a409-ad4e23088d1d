// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M618.667 234.667l275.298 271.3c1.357 1.664 2.035 3.849 2.035 6.033M618.667 789.333l275.298-271.3c1.357-1.664 2.035-3.849 2.035-6.033M896 512h-768"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "arrow-right"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1421,
        "id": 599,
        "name": "arrow-right",
        "prevSize": 32,
        "code": 59697
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 50
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ArrowRightIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ArrowRightIcon = ({ size = 16, ...props }: ArrowRightIconProps) => (
  <IcoMoon iconSet={iconSet} icon="arrow-right" size={size} {...props} />
);

ArrowRightIcon.displayName = 'ArrowRightIcon';

export default ArrowRightIcon;
