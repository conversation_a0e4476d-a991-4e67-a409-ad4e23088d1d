// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M231.417 377.146c-18.889 0-36.125-4.604-51.709-13.813-15.583-9.444-28.097-21.958-37.542-37.542-9.208-15.583-13.812-32.819-13.812-51.708 0-19.125 4.604-36.479 13.812-52.063 9.445-15.583 21.958-27.979 37.542-37.187 15.584-9.444 32.82-14.167 51.709-14.167 19.125 0 36.479 4.722 52.062 14.167 15.584 9.208 27.979 21.604 37.187 37.187 9.445 15.584 14.167 32.938 14.167 52.063 0 18.889-4.722 36.125-14.167 51.708-9.208 15.584-21.604 28.097-37.187 37.542-15.583 9.209-32.937 13.813-52.062 13.813zM492.083 377.146c-18.889 0-36.241-4.604-52.062-13.813-15.584-9.444-27.98-21.958-37.188-37.542s-13.812-32.819-13.812-51.708c0-19.125 4.604-36.479 13.812-52.063s21.604-27.979 37.188-37.187c15.821-9.444 33.173-14.167 52.062-14.167s36.126 4.722 51.708 14.167c15.821 9.208 28.335 21.604 37.542 37.187s13.811 32.938 13.811 52.063c0 18.889-4.604 36.125-13.811 51.708s-21.722 28.097-37.542 37.542c-15.582 9.209-32.819 13.813-51.708 13.813zM752.751 377.146c-19.127 0-36.48-4.604-52.062-13.813-15.586-9.444-27.981-21.958-37.188-37.542s-13.811-32.819-13.811-51.708c0-19.125 4.604-36.479 13.811-52.063s21.602-27.979 37.188-37.187c15.582-9.444 32.934-14.167 52.062-14.167 18.889 0 36.126 4.722 51.708 14.167 15.582 9.208 27.981 21.604 37.188 37.187 9.442 15.584 14.165 32.938 14.165 52.063 0 18.889-4.723 36.125-14.165 51.708-9.207 15.584-21.606 28.097-37.188 37.542-15.582 9.209-32.819 13.813-51.708 13.813zM231.063 621.167c-18.889 0-36.125-4.604-51.709-13.811-15.583-9.212-28.097-21.606-37.542-37.188-9.208-15.821-13.812-33.173-13.812-52.062 0-19.127 4.604-36.48 13.812-52.062 9.444-15.586 21.958-27.981 37.542-37.188 15.584-9.21 32.82-13.814 51.709-13.814 19.125 0 36.479 4.604 52.062 13.814 15.584 9.207 27.979 21.602 37.187 37.188 9.445 15.582 14.167 32.934 14.167 52.062 0 18.889-4.722 36.241-14.167 52.062-9.208 15.582-21.604 27.977-37.187 37.188-15.583 9.207-32.937 13.811-52.062 13.811zM491.729 621.167c-18.889 0-36.241-4.604-52.062-13.811-15.584-9.212-27.98-21.606-37.188-37.188-9.208-15.821-13.812-33.173-13.812-52.062 0-19.127 4.604-36.48 13.812-52.062 9.208-15.586 21.604-27.981 37.188-37.188 15.821-9.21 33.173-13.814 52.062-13.814s36.126 4.604 51.708 13.814c15.821 9.207 28.335 21.602 37.542 37.188 9.207 15.582 13.811 32.934 13.811 52.062 0 18.889-4.604 36.241-13.811 52.062-9.207 15.582-21.722 27.977-37.542 37.188-15.582 9.207-32.819 13.811-51.708 13.811zM752.397 621.167c-19.127 0-36.48-4.604-52.062-13.811-15.586-9.212-27.981-21.606-37.188-37.188-9.207-15.821-13.811-33.173-13.811-52.062 0-19.127 4.604-36.48 13.811-52.062 9.207-15.586 21.602-27.981 37.188-37.188 15.582-9.21 32.934-13.814 52.062-13.814 18.889 0 36.126 4.604 51.708 13.814 15.582 9.207 27.981 21.602 37.188 37.188 9.442 15.582 14.165 32.934 14.165 52.062 0 18.889-4.723 36.241-14.165 52.062-9.207 15.582-21.606 27.977-37.188 37.188-15.582 9.207-32.819 13.811-51.708 13.811zM231.063 865.186c-18.889 0-36.125-4.604-51.709-13.811s-28.097-21.602-37.542-37.188c-9.208-15.582-13.812-32.939-13.812-52.062 0-18.889 4.604-36.126 13.812-51.708 9.444-15.821 21.958-28.335 37.542-37.542s32.82-13.811 51.709-13.811c19.125 0 36.479 4.604 52.062 13.811s27.979 21.722 37.187 37.542c9.445 15.582 14.167 32.819 14.167 51.708 0 19.123-4.722 36.48-14.167 52.062-9.208 15.586-21.604 27.981-37.187 37.188s-32.937 13.811-52.062 13.811zM491.729 865.186c-18.889 0-36.241-4.604-52.062-13.811-15.584-9.207-27.98-21.602-37.188-37.188-9.208-15.582-13.812-32.939-13.812-52.062 0-18.889 4.604-36.126 13.812-51.708 9.208-15.821 21.604-28.335 37.188-37.542 15.821-9.207 33.173-13.811 52.062-13.811s36.126 4.604 51.708 13.811c15.821 9.207 28.335 21.722 37.542 37.542 9.207 15.582 13.811 32.819 13.811 51.708 0 19.123-4.604 36.48-13.811 52.062-9.207 15.586-21.722 27.981-37.542 37.188-15.582 9.207-32.819 13.811-51.708 13.811zM752.397 865.186c-19.127 0-36.48-4.604-52.062-13.811-15.586-9.207-27.981-21.602-37.188-37.188-9.207-15.582-13.811-32.939-13.811-52.062 0-18.889 4.604-36.126 13.811-51.708 9.207-15.821 21.602-28.335 37.188-37.542 15.582-9.207 32.934-13.811 52.062-13.811 18.889 0 36.126 4.604 51.708 13.811s27.981 21.722 37.188 37.542c9.442 15.582 14.165 32.819 14.165 51.708 0 19.123-4.723 36.48-14.165 52.062-9.207 15.586-21.606 27.981-37.188 37.188s-32.819 13.811-51.708 13.811z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "app"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1403,
        "id": 617,
        "name": "app",
        "prevSize": 32,
        "code": 59679
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 32
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface AppIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const AppIcon = ({ size = 16, ...props }: AppIconProps) => (
  <IcoMoon iconSet={iconSet} icon="app" size={size} {...props} />
);

AppIcon.displayName = 'AppIcon';

export default AppIcon;
