// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M336 774.558c17.673 0 32-14.323 32-32 0-17.673-14.327-32-32-32v64zM192 710.558c-17.673 0-32 14.327-32 32 0 17.677 14.327 32 32 32v-64zM232 934.558c0 17.677 14.327 32 32 32s32-14.323 32-32h-64zM467.132 892.911c-14.165-10.569-34.214-7.65-44.783 6.515-10.566 14.17-7.648 34.219 6.519 44.783l38.263-51.298zM507.998 938.56l-0.081 32h0.081v-32zM507.998 738.56l0.085-32h-0.085v32zM548.868 784.209c14.165 10.569 34.214 7.65 44.783-6.515 10.564-14.17 7.646-34.219-6.519-44.783l-38.263 51.298zM613.491 122.824v0zM815.842 325.176v0zM839.381 868.779v0zM783.445 924.715v0zM184.618 155.223v0zM240.556 99.284v0zM841.835 362.667v0zM138.667 618.667c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM732.16 936.96v0zM693.333 906.483c-17.673 0.247-31.799 14.771-31.552 32.444 0.243 17.673 14.767 31.795 32.439 31.552l-0.887-63.996zM336 742.558v-32h-72v64h72v-32zM264 742.558v-32h-72v64h72v-32zM264 934.558h32v-192h-64v192h32zM448 918.558l-19.132 25.651c22.839 17.037 50.551 26.274 79.049 26.351l0.081-32 0.085-32c-14.758-0.038-29.12-4.826-40.951-13.649l-19.132 25.647zM507.998 938.56v32c20.582 0 44.77-3.26 64.751-16.047 22.302-14.272 35.251-37.786 35.251-67.955h-64c0 5.67-1.109 8.636-1.865 10.065-0.717 1.357-1.796 2.645-3.887 3.985-5.018 3.213-14.831 5.952-30.251 5.952v32zM576 886.558h32c0-29.803-16.316-49.289-35.349-60.885-16.704-10.18-37.525-15.578-52.437-19.541-17.472-4.642-28.813-7.77-36.416-12.087-3.247-1.843-3.972-2.953-3.908-2.859 0.341 0.499 0.111 0.755 0.111-0.627h-64c0 13.623 3.768 26.125 11.115 36.83 7.061 10.295 16.333 17.344 25.088 22.315 16.397 9.306 37.056 14.426 51.584 18.287 17.088 4.535 28.267 7.889 35.563 12.335 4.966 3.025 4.651 4.041 4.651 6.234h32zM448 790.558h32c0-7.403 2.466-11.251 5.325-13.7 3.388-2.901 10.381-6.298 22.673-6.298v-64c-23.706 0-46.707 6.605-64.324 21.705-18.138 15.548-27.674 37.7-27.674 62.293h32zM507.998 738.56l-0.081 32c14.758 0.038 29.12 4.826 40.951 13.649l38.263-51.298c-22.839-17.037-50.551-26.274-79.049-26.351l-0.085 32zM853.333 415.686h-32v318.181h64v-318.181h-32zM613.491 122.824l-22.63 22.627 202.355 202.353 45.252-45.255-202.351-202.353-22.626 22.627zM375.467 85.333v32h147.516v-64h-147.516v32zM853.333 733.867h-32c0 36.373-0.026 61.726-1.638 81.463-1.583 19.366-4.531 30.494-8.823 38.921l57.024 29.056c9.655-18.957 13.683-39.441 15.586-62.763 1.877-22.95 1.852-51.362 1.852-86.677h-32zM783.445 924.715l14.528 28.514c30.106-15.343 54.579-39.817 69.922-69.922l-57.024-29.056c-9.203 18.061-23.893 32.747-41.954 41.954l14.528 28.51zM170.667 290.133h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM375.467 85.333v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM184.618 155.223l28.512 14.528c9.204-18.063 23.89-32.75 41.953-41.953l-29.055-57.024c-30.106 15.34-54.583 39.817-69.923 69.923l28.512 14.528zM613.491 122.824l22.626-22.627c-13.679-13.681-29.585-24.629-46.861-32.492l-26.513 58.252c10.368 4.718 19.908 11.285 28.117 19.495l22.63-22.627zM576 96.83l13.257-29.126c-20.617-9.381-43.191-14.371-66.274-14.371v64c13.85 0 27.392 2.993 39.761 8.623l13.257-29.126zM576 96.83h-32v137.836h64v-137.836h-32zM853.333 415.686h32c0-23.087-4.992-45.66-14.37-66.274l-58.253 26.509c5.632 12.372 8.623 25.913 8.623 39.765h32zM841.835 362.667l29.129-13.254c-7.863-17.277-18.812-33.183-32.495-46.863l-45.252 45.255c8.209 8.209 14.775 17.75 19.494 28.117l29.124-13.254zM704 362.667v32h137.835v-64h-137.835v32zM576 234.667h-32c0 88.366 71.633 160 160 160v-64c-53.018 0-96-42.981-96-96h-32zM170.667 618.667h32v-328.533h-64v328.533h32zM732.16 936.96l2.56 31.898c23.531-1.89 44.169-5.905 63.253-15.629l-29.056-57.024c-8.486 4.322-19.708 7.283-39.317 8.858l2.56 31.898zM693.777 938.483l0.444 31.996c14.895-0.209 28.339-0.649 40.499-1.621l-5.12-63.795c-10.103 0.811-21.931 1.225-36.267 1.421l0.444 32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "file-ts"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1642,
        "id": 378,
        "name": "file-ts",
        "prevSize": 32,
        "code": 59918
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 271
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FileTsIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FileTsIcon = ({ size = 16, ...props }: FileTsIconProps) => (
  <IcoMoon iconSet={iconSet} icon="file-ts" size={size} {...props} />
);

FileTsIcon.displayName = 'FileTsIcon';

export default FileTsIcon;
