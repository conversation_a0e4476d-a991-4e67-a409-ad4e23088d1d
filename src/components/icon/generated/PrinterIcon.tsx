// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M298.667 576h42.667M341.333 576v298.667c0 23.565 19.103 42.667 42.667 42.667h256c23.565 0 42.667-19.102 42.667-42.667v-298.667M341.333 576h341.333M682.667 576h42.667M618.667 405.333h128M299.895 234.667c1.533-25.102 4.978-42.91 12.723-58.111 12.272-24.085 31.853-43.666 55.938-55.938 27.38-13.951 63.222-13.951 134.911-13.951h17.067c71.689 0 107.529 0 134.912 13.951 24.085 12.272 43.665 31.853 55.936 55.938 7.744 15.2 11.191 33.009 12.723 58.111M299.895 234.667h-9.761c-71.687 0-107.53 0-134.911 13.951-24.085 12.272-43.666 31.853-55.938 55.938-13.951 27.38-13.951 63.224-13.951 134.911v145.067c0 71.689 0 107.529 13.951 134.912 12.272 24.085 31.853 43.665 55.938 55.936 19.186 9.775 42.527 12.702 79.444 13.577M299.895 234.667h424.21M724.105 234.667h9.762c71.689 0 107.529 0 134.912 13.951 24.085 12.272 43.665 31.853 55.936 55.938 13.952 27.38 13.952 63.224 13.952 134.911v145.067c0 71.689 0 107.529-13.952 134.912-12.271 24.085-31.851 43.665-55.936 55.936-19.187 9.775-42.53 12.702-79.445 13.577"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "printer"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1835,
        "id": 185,
        "name": "printer",
        "prevSize": 32,
        "code": 60111
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 464
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface PrinterIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const PrinterIcon = ({ size = 16, ...props }: PrinterIconProps) => (
  <IcoMoon iconSet={iconSet} icon="printer" size={size} {...props} />
);

PrinterIcon.displayName = 'PrinterIcon';

export default PrinterIcon;
