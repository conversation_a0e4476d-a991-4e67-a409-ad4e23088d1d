export interface MenuState {
  openKeys: string;
}

export type ActionType = 'SET_OPEN_KEYS';

export interface MenuAction {
  type: ActionType;
  payload: Partial<MenuState>;
}

export const menuReducer = (state: MenuState, action: MenuAction): MenuState => {
  switch (action.type) {
    case 'SET_OPEN_KEYS':
      return {
        ...state,
        openKeys: action.payload.openKeys as string,
      };
    default:
      return state;
  }
};
