// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 938.667h853.333M223.069 440.887l-103.842 103.842c-45.192 45.193-45.192 118.464 0 163.657l196.387 196.386c45.192 45.193 118.464 45.193 163.657 0l103.842-103.842M223.069 440.887l321.66-321.66c45.193-45.192 118.464-45.192 163.657 0l36.689 36.691M223.069 440.887l118.519 118.519M583.113 800.93l321.66-321.66c45.193-45.193 45.193-118.464 0-163.657l-38.933-38.934M583.113 800.93l-120.764-120.759M341.588 559.407l403.487-403.488M341.588 559.407l120.761 120.764M745.075 155.919l120.764 120.761M462.349 680.171l403.49-403.491"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "eraser"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1620,
        "id": 400,
        "name": "eraser",
        "prevSize": 32,
        "code": 59896
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 249
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface EraserIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const EraserIcon = ({ size = 16, ...props }: EraserIconProps) => (
  <IcoMoon iconSet={iconSet} icon="eraser" size={size} {...props} />
);

EraserIcon.displayName = 'EraserIcon';

export default EraserIcon;
