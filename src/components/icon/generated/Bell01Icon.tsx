// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M389.433 874.667c26.986 38.686 71.819 64 122.567 64s95.582-25.314 122.569-64M428.796 213.333h166.409c52.74 0 100.083 32.348 119.249 81.481l82.739 302.519c5.35 13.717 13.879 25.975 24.887 35.759l5.888 5.235c16.132 14.34 25.365 34.897 25.365 56.482 0 41.737-33.835 75.575-75.575 75.575h-531.517c-41.738 0-75.574-33.839-75.574-75.575 0-21.585 9.231-42.142 25.365-56.482l5.89-5.235c11.006-9.783 19.535-22.042 24.887-35.759l82.738-302.519c19.167-49.134 66.508-81.481 119.248-81.481zM576 149.333c0 35.346-28.655 64-64 64s-64-28.654-64-64c0-35.346 28.655-64 64-64s64 28.654 64 64z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bell-01"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1462,
        "id": 558,
        "name": "bell-01",
        "prevSize": 32,
        "code": 59738
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 91
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface Bell01IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const Bell01Icon = ({ size = 16, ...props }: Bell01IconProps) => (
  <IcoMoon iconSet={iconSet} icon="bell-01" size={size} {...props} />
);

Bell01Icon.displayName = 'Bell01Icon';

export default Bell01Icon;
