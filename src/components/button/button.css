.rt-Button {
  &:where(:not(.rt-variant-ghost)) {
    /* stylelint-disable-next-line selector-max-type */
    & :where(svg) {
      opacity: 0.9;
    }
  }
}

/***************************************************************************************************
 *                                                                                                 *
 * SIZES                                                                                           *
 *                                                                                                 *
 ***************************************************************************************************/

@breakpoints {
  .rt-Button {
    &:where(.rt-r-size-1) {
      --base-button-classic-active-padding-top: 1px;
      --base-button-height: var(--spatial-7);
      --base-button-svg-height: 14px;
      gap: var(--spatial-2);
      font-size: var(--font-size-2);
      font-weight: var(--font-weight-semibold);
      line-height: var(--line-height-2);
      letter-spacing: var(--letter-spacing-2);
      border-radius: var(--radius-5);
      padding: calc(var(--spatial-3) / 2) var(--spatial-3);
    }
    &:where(.rt-r-size-2) {
      --base-button-classic-active-padding-top: 2px;
      --base-button-height: var(--spatial-8);
      --base-button-svg-height: var(--spatial-5);
      gap: var(--spatial-2);
      font-size: var(--font-size-3);
      font-weight: var(--font-weight-semibold);
      line-height: var(--line-height-3);
      letter-spacing: var(--letter-spacing-3);
      border-radius: var(--radius-5);
      padding: calc(var(--spatial-4) / 2) var(--spatial-4);
    }
    &:where(.rt-r-size-3) {
      --base-button-classic-active-padding-top: 2px;
      --base-button-height: var(--spatial-9);
      --base-button-svg-height: 18px;
      gap: var(--spatial-3);
      font-size: var(--font-size-5);
      font-weight: var(--font-weight-semibold);
      line-height: var(--line-height-5);
      letter-spacing: var(--letter-spacing-5);
      border-radius: var(--radius-5);
      padding: calc(var(--spatial-5) / 2) var(--spatial-5);
    }
    &:where(.rt-r-size-4) {
      --base-button-classic-active-padding-top: 2px;
      --base-button-height: var(--spatial-10);
      --base-button-svg-height: var(--spatial-6);
      gap: var(--spatial-3);
      font-size: var(--font-size-6);
      font-weight: var(--font-weight-bold);
      line-height: var(--line-height-6);
      letter-spacing: var(--letter-spacing-6);
      border-radius: var(--radius-5);
      padding: calc(var(--spatial-6) / 2) var(--spatial-6);
    }
    &:where(.rt-r-size-5) {
      --base-button-classic-active-padding-top: 2px;
      --base-button-height: var(--spatial-11);
      --base-button-svg-height: var(--spatial-7);
      gap: var(--spatial-4);
      font-size: var(--font-size-6);
      font-weight: var(--font-weight-bold);
      line-height: var(--line-height-6);
      letter-spacing: var(--letter-spacing-6);
      border-radius: var(--radius-5);
      padding: calc(var(--spatial-7) / 2) var(--spatial-7);
    }
  }
}
