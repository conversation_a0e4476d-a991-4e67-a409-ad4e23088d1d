// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M627.554 156.445l57.779-71.111M396.445 156.445l-57.778-71.111M259.628 509.53c-1.215-6.886-1.85-13.986-1.85-21.235v-94.813c0-144.003 113.819-260.741 254.222-260.741s254.221 116.738 254.221 260.741v94.813c0 7.249-0.631 14.349-1.847 21.235M259.628 509.53c21.167-27.375 53.881-44.937 90.595-44.937h323.557c36.71 0 69.427 17.562 90.594 44.937M259.628 509.53c-15.628 20.215-24.961 45.781-24.961 73.583v71.108c0 157.094 124.166 284.446 277.333 284.446 153.169 0 277.333-127.351 277.333-284.446v-71.108c0-27.802-9.331-53.367-24.96-73.583M419.555 322.371h0.463M603.981 322.371h0.465"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bug-droid"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1494,
        "id": 526,
        "name": "bug-droid",
        "prevSize": 32,
        "code": 59770
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 123
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BugDroidIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BugDroidIcon = ({ size = 16, ...props }: BugDroidIconProps) => (
  <IcoMoon iconSet={iconSet} icon="bug-droid" size={size} {...props} />
);

BugDroidIcon.displayName = 'BugDroidIcon';

export default BugDroidIcon;
