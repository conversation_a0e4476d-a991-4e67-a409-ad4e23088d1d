import { asChildPropDef } from '@props';
import { type PropDef, colorPropDef, highContrastPropDef } from '@props';

const sizes = ['1', '2', '3', '4', '5'] as const;
const wrapValues = ['nowrap', 'wrap', 'wrap-reverse'] as const;
const justifyValues = ['start', 'center', 'end'] as const;
const orientations = ['horizontal', 'vertical'] as const;

const tabsListPropDefs = {
  size: { type: 'enum', className: 'rt-r-size', values: sizes, default: '2', responsive: true },
  wrap: {
    type: 'enum',
    className: 'rt-r-fw',
    values: wrapValues,
    responsive: true,
  },
  justify: {
    type: 'enum',
    className: 'rt-r-jc',
    values: justifyValues,
    responsive: true,
  },
  ...colorPropDef,
  ...highContrastPropDef,
  orientation: { type: 'enum', className: 'rt-r-orientation', values: orientations, default: 'horizontal' },
} satisfies {
  size: PropDef<(typeof sizes)[number]>;
  wrap: PropDef<(typeof wrapValues)[number]>;
  justify: PropDef<(typeof justifyValues)[number]>;
  orientation: PropDef<(typeof orientations)[number]>;
};

const tabsRootPropDefs = {
  ...asChildPropDef,
};

const tabsContentPropDefs = {
  ...asChildPropDef,
};

const tabsLinkPropDefs = {
  ...asChildPropDef,
};

const tabNavLinkPropDefs = {
  ...asChildPropDef,
  active: { type: 'boolean', default: false },
} satisfies {
  active: PropDef<boolean>;
};

export { tabsRootPropDefs, tabsContentPropDefs, tabsListPropDefs, tabsLinkPropDefs, tabNavLinkPropDefs };
