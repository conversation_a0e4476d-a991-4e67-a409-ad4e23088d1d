// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M372.741 426.667c-23.438-26.187-57.498-42.667-95.407-42.667-70.692 0-128 57.306-128 128s57.308 128 128 128c37.909 0 71.97-16.482 95.407-42.667M372.741 426.667c20.268 22.647 32.593 52.548 32.593 85.333s-12.324 62.686-32.593 85.333M372.741 426.667l253.209-170.667M372.741 597.333l253.209 170.667M625.95 768c-4.715 13.346-7.283 27.708-7.283 42.667 0 70.694 57.306 128 128 128s128-57.306 128-128c0-70.694-57.306-128-128-128-55.731 0-103.147 35.618-120.717 85.333zM625.95 256c-4.715-13.345-7.283-27.706-7.283-42.667 0-70.692 57.306-128 128-128s128 57.308 128 128c0 70.692-57.306 128-128 128-55.731 0-103.147-35.619-120.717-85.333z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "share"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1881,
        "id": 139,
        "name": "share",
        "prevSize": 32,
        "code": 60157
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 510
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ShareIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ShareIcon = ({ size = 16, ...props }: ShareIconProps) => (
  <IcoMoon iconSet={iconSet} icon="share" size={size} {...props} />
);

ShareIcon.displayName = 'ShareIcon';

export default ShareIcon;
