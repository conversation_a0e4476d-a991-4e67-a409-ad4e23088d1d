import type { Meta, StoryObj } from '@storybook/react-vite';

import { Dots } from '@components/dots';
import { dotsPropDefs } from '@components/dots/dots.props';

const meta: Meta<typeof Dots> = {
  title: 'DesignSystem/Dots',
  component: Dots,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    total: {
      control: { type: 'number', min: 1, max: 10 },
      description: 'Total number of dots',
    },
    current: {
      control: { type: 'number', min: 0, max: 10 },
      description: 'Number of active dots (0-based count)',
    },
    size: {
      control: 'inline-radio',
      options: dotsPropDefs.size.values,
      description: 'Size of the dots',
    },
  },
};

export default meta;
type Story = StoryObj<typeof Dots>;

export const _dots: Story = {
  args: {
    total: dotsPropDefs.total.default,
    current: dotsPropDefs.current.default,
    size: dotsPropDefs.size.default,
  },
};
