// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 298.667v128M213.333 640c0 164.949 133.718 298.667 298.667 298.667s298.667-133.717 298.667-298.667v-256c0-164.949-133.717-298.667-298.667-298.667s-298.667 133.718-298.667 298.667v256z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "mouse-alt-02"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1790,
        "id": 230,
        "name": "mouse-alt-02",
        "prevSize": 32,
        "code": 60066
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 419
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MouseAlt02IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MouseAlt02Icon = ({ size = 16, ...props }: MouseAlt02IconProps) => (
  <IcoMoon iconSet={iconSet} icon="mouse-alt-02" size={size} {...props} />
);

MouseAlt02Icon.displayName = 'MouseAlt02Icon';

export default MouseAlt02Icon;
