// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 277.303c0 0-42.219-159.424-249.117-148.794-34.319 1.763-59.031 32.196-59.031 66.761v517.412c0 43.989 40.137 77.973 82.708 88.047 119.084 28.186 225.44 137.937 225.44 137.937M512 277.303c0 0 42.219-159.424 249.118-148.794 34.317 1.763 59.029 32.196 59.029 66.761v515.509c0 44.634-41.515 80.132-84.245 92.164-111.078 31.283-223.902 135.723-223.902 135.723M512 277.303v661.364M512 938.667c0 0 186.965-50.492 345.007-51.618 43.234-0.311 81.66-34.048 81.66-77.542v-492.928c0-35.632-26.099-66.102-61.372-69.363-18.372-1.698-35.895-2.874-52.608-3.588M512 938.667c0 0-176.268-52.361-346.4-51.631-42.74 0.183-80.266-33.105-80.266-76.096v-490.773c0-35.632 26.098-66.102 61.373-69.363 18.369-1.698 35.895-2.874 52.608-3.588"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "book-open"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1478,
        "id": 542,
        "name": "book-open",
        "prevSize": 32,
        "code": 59754
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 107
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BookOpenIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BookOpenIcon = ({ size = 16, ...props }: BookOpenIconProps) => (
  <IcoMoon iconSet={iconSet} icon="book-open" size={size} {...props} />
);

BookOpenIcon.displayName = 'BookOpenIcon';

export default BookOpenIcon;
