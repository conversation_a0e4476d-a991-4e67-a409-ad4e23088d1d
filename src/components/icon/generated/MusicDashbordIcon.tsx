// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M889.762 567.454v0zM740.429 554.859v0zM155.223 882.048v0zM99.284 826.112v0zM826.112 99.284v0zM882.048 155.223v0zM155.223 99.284v0zM99.284 155.223v0zM864 426.667c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM426.667 928c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM682.667 873.886h-32c0 18.47-14.69 32.781-32 32.781v64c53.385 0 96-43.695 96-96.781h-32zM618.667 938.667v-32c-17.31 0-32-14.31-32-32.781h-64c0 53.086 42.615 96.781 96 96.781v-32zM554.667 873.886h32c0-18.47 14.69-32.781 32-32.781v-64c-53.385 0-96 43.699-96 96.781h32zM618.667 809.105v32c17.31 0 32 14.31 32 32.781h64c0-53.082-42.615-96.781-96-96.781v32zM938.667 873.886h-32c0 18.47-14.69 32.781-32 32.781v64c53.385 0 96-43.695 96-96.781h-32zM874.667 938.667v-32c-17.31 0-32-14.31-32-32.781h-64c0 53.086 42.615 96.781 96 96.781v-32zM810.667 873.886h32c0-18.47 14.69-32.781 32-32.781v-64c-53.385 0-96 43.699-96 96.781h32zM874.667 809.105v32c17.31 0 32 14.31 32 32.781h64c0-53.082-42.615-96.781-96-96.781v32zM740.429 554.859l-2.688 31.885 149.333 12.595 5.376-63.774-149.333-12.595-2.688 31.889zM682.667 873.886h32v-223.219h-64v223.219h32zM682.667 650.667h32v-42.014h-64v42.014h32zM938.667 621.252h-32v50.748h64v-50.748h-32zM938.667 672h-32v201.886h64v-201.886h-32zM682.667 650.667l-2.658 31.889 256 21.333 5.316-63.778-256-21.333-2.658 31.889zM889.762 567.454l-2.688 31.885c10.752 0.909 19.593 10.146 19.593 21.914h64c0-44.386-33.681-81.929-78.217-85.687l-2.688 31.889zM740.429 554.859l2.688-31.889c-50.172-4.233-92.45 35.78-92.45 85.683h64c0-13.261 11.055-22.925 23.074-21.909l2.688-31.885zM290.133 85.333v32h401.067v-64h-401.067v32zM85.333 691.2h32v-401.067h-64v401.067h32zM290.133 896v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM85.333 691.2h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM155.223 882.048l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM691.2 85.333v32c36.373 0 61.726 0.025 81.463 1.638 19.366 1.582 30.494 4.532 38.921 8.826l29.056-57.024c-18.957-9.658-39.441-13.683-62.763-15.589-22.95-1.875-51.362-1.85-86.677-1.85v32zM896 290.133h32c0-35.315 0.026-63.727-1.852-86.676-1.903-23.323-5.931-43.809-15.586-62.762l-57.024 29.055c4.292 8.428 7.241 19.553 8.823 38.919 1.613 19.739 1.638 45.093 1.638 81.464h32zM826.112 99.284l-14.528 28.512c18.061 9.204 32.747 23.89 41.954 41.953l57.024-29.055c-15.343-30.106-39.817-54.583-69.922-69.923l-14.528 28.512zM290.133 85.333v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM85.333 290.133h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM155.223 99.284l-14.528-28.512c-30.106 15.34-54.583 39.817-69.923 69.923l57.024 29.055c9.204-18.063 23.89-32.75 41.953-41.953l-14.528-28.512zM896 290.133h-32v136.533h64v-136.533h-32zM426.667 896v-32h-136.533v64h136.533v-32zM290.133 85.333h-32v810.667h64v-810.667h-32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "music-dashbord"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1793,
        "id": 227,
        "name": "music-dashbord",
        "prevSize": 32,
        "code": 60069
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 422
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MusicDashbordIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MusicDashbordIcon = ({ size = 16, ...props }: MusicDashbordIconProps) => (
  <IcoMoon iconSet={iconSet} icon="music-dashbord" size={size} {...props} />
);

MusicDashbordIcon.displayName = 'MusicDashbordIcon';

export default MusicDashbordIcon;
