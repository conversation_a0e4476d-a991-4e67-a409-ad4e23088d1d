// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M341.333 938.667v-405.333M341.333 85.333v106.667M341.333 192c58.91 0 106.667 47.756 106.667 106.667s-47.756 106.667-106.667 106.667c-58.91 0-106.667-47.756-106.667-106.667s47.756-106.667 106.667-106.667z",
          "M682.667 85.333v405.333M682.667 938.667v-106.667M682.667 832c58.91 0 106.667-47.757 106.667-106.667s-47.757-106.667-106.667-106.667c-58.91 0-106.667 47.757-106.667 106.667s47.757 106.667 106.667 106.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "adjustment-vertical"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1377,
        "id": 643,
        "name": "adjustment-vertical",
        "prevSize": 32,
        "code": 59653
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 6
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface AdjustmentVerticalIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const AdjustmentVerticalIcon = ({ size = 16, ...props }: AdjustmentVerticalIconProps) => (
  <IcoMoon iconSet={iconSet} icon="adjustment-vertical" size={size} {...props} />
);

AdjustmentVerticalIcon.displayName = 'AdjustmentVerticalIcon';

export default AdjustmentVerticalIcon;
