// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M772.74 203.852h47.407c65.455 0 118.519 53.062 118.519 118.519v36.143c0 60.391-45.41 111.123-105.429 117.792l-215.808 23.979c-60.019 6.669-105.429 57.404-105.429 117.794v36.143M512 654.221l-35.191 20.109c-29.538 16.883-47.77 48.299-47.77 82.325v99.051c0 45.815 37.141 82.961 82.961 82.961s82.961-37.146 82.961-82.961v-99.051c0-34.027-18.231-65.442-47.77-82.325l-35.191-20.109zM274.963 87.021c-30.447 0.643-62.087 1.465-95.633 2.456-43.985 1.3-79.017 13.741-82.744 29.44-15.008 63.206-14.973 107.264-0.073 169.664 3.758 15.736 38.977 28.178 83.074 29.463 33.404 0.974 64.956 1.804 95.376 2.468M274.963 87.021c-14.339 91.184-13.958 142.307 0 233.491M274.963 87.021c99.274-2.097 185.863-2.296 284.444-0.281M274.963 320.512c98.53 2.151 185.184 2.569 284.444 0.542M559.407 86.74c30.75 0.628 62.665 1.472 96.499 2.541 43.499 1.374 77.931 13.732 81.698 29.263 15.283 63.021 15.236 107.131-0.094 170.662-3.729 15.455-37.82 27.796-81.092 29.226-34.048 1.125-66.138 1.992-97.011 2.622M559.407 86.74c15.070 93.212 15.121 144.504 0 234.314"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "paint-roller"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1812,
        "id": 208,
        "name": "paint-roller",
        "prevSize": 32,
        "code": 60088
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 441
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface PaintRollerIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const PaintRollerIcon = ({ size = 16, ...props }: PaintRollerIconProps) => (
  <IcoMoon iconSet={iconSet} icon="paint-roller" size={size} {...props} />
);

PaintRollerIcon.displayName = 'PaintRollerIcon';

export default PaintRollerIcon;
