// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M597.333 725.333h-170.667M938.667 554.667c-104.832-52.352-257.152-85.333-426.667-85.333s-321.835 32.981-426.667 85.333M810.667 490.667l-45.141-289.62c-9.173-59.051-73.344-91.051-125.141-62.421l-26.24 14.507c-31.258 17.318-66.406 26.404-102.144 26.404s-70.886-9.086-102.144-26.404l-26.24-14.507c-51.797-28.587-115.968 3.413-125.141 62.421l-45.141 289.62M298.667 640c-33.948 0-66.505 13.487-90.51 37.491s-37.49 56.563-37.49 90.509c0 33.95 13.486 66.509 37.49 90.513s56.562 37.487 90.51 37.487c33.948 0 66.505-13.483 90.51-37.487s37.49-56.563 37.49-90.513c0-33.946-13.486-66.505-37.49-90.509s-56.562-37.491-90.51-37.491zM725.333 640c-33.946 0-66.505 13.487-90.509 37.491s-37.491 56.563-37.491 90.509c0 33.95 13.487 66.509 37.491 90.513s56.563 37.487 90.509 37.487c33.946 0 66.505-13.483 90.509-37.487s37.491-56.563 37.491-90.513c0-33.946-13.487-66.505-37.491-90.509s-56.563-37.491-90.509-37.491z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "round",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "anonymous"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "round",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1401,
        "id": 619,
        "name": "anonymous",
        "prevSize": 32,
        "code": 59677
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 30
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface AnonymousIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const AnonymousIcon = ({ size = 16, ...props }: AnonymousIconProps) => (
  <IcoMoon iconSet={iconSet} icon="anonymous" size={size} {...props} />
);

AnonymousIcon.displayName = 'AnonymousIcon';

export default AnonymousIcon;
