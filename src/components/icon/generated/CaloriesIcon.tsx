// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 938.667c-137.846 0-298.667-101.854-298.667-305.566 0-225.428 88.917-179.836 244.885-541.815 3.238-7.517 12.937-8.086 16.457-0.724 71.927 150.521 57.468 207.363 39.091 396.722-0.777 7.996 6.703 13.692 12.911 9.532 65.229-43.708 94.455-117.882 116.911-197.372 2.419-8.56 12.983-9.871 17.088-2.141 86.43 162.764 149.99 226.742 149.99 335.798 0 203.712-160.819 305.566-298.667 305.566z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "calories"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1513,
        "id": 507,
        "name": "calories",
        "prevSize": 32,
        "code": 59789
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 142
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CaloriesIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CaloriesIcon = ({ size = 16, ...props }: CaloriesIconProps) => (
  <IcoMoon iconSet={iconSet} icon="calories" size={size} {...props} />
);

CaloriesIcon.displayName = 'CaloriesIcon';

export default CaloriesIcon;
