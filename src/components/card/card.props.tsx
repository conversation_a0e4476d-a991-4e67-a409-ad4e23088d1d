import { type PropDef, asChildPropDef, heightPropDefs, radiusPropDef, widthPropDefs } from '@props';

const sizes = ['1', '2', '3', '4', '5'] as const;
const variants = ['surface', 'soft', 'classic', 'ghost'] as const;

const cardPropDefs = {
  size: { type: 'enum', className: 'rt-r-size', values: sizes, default: '1', responsive: true },
  variant: { type: 'enum', className: 'rt-variant', values: variants, default: 'surface' },
  mask: { type: 'boolean', className: 'rt-mask', default: false },
  ...asChildPropDef,
  ...heightPropDefs,
  ...widthPropDefs,
  ...radiusPropDef,
} satisfies {
  size: PropDef<(typeof sizes)[number]>;
  variant: PropDef<(typeof variants)[number]>;
  mask: PropDef<boolean>;
};

export { cardPropDefs };
