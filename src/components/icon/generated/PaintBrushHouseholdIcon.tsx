// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M704.593 699.657l-224.691 224.691c-19.093 19.093-50.048 19.093-69.137 0l-86.42-86.421M704.593 699.657l69.137-69.137c47.727-47.727 47.727-125.111 0-172.838l-14.729-14.729c-20.062-20.061-18.889-52.931 2.551-71.511l145.698-126.272c39.927-34.602 42.108-95.812 4.749-133.171s-98.569-35.176-133.171 4.75l-126.272 145.699c-18.577 21.44-51.447 22.612-71.509 2.551l-14.729-14.727c-47.727-47.729-125.111-47.729-172.838 0l-69.136 69.136M704.593 699.657l-380.249-380.25M324.344 319.407l-224.692 224.691c-19.091 19.093-19.091 50.048 0 69.137l86.42 86.421M324.344 837.926l-138.272-138.27M324.344 837.926l163.95-163.951M186.072 699.657l163.952-163.951"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "paint-brush-household"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1809,
        "id": 211,
        "name": "paint-brush-household",
        "prevSize": 32,
        "code": 60085
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 438
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface PaintBrushHouseholdIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const PaintBrushHouseholdIcon = ({ size = 16, ...props }: PaintBrushHouseholdIconProps) => (
  <IcoMoon iconSet={iconSet} icon="paint-brush-household" size={size} {...props} />
);

PaintBrushHouseholdIcon.displayName = 'PaintBrushHouseholdIcon';

export default PaintBrushHouseholdIcon;
