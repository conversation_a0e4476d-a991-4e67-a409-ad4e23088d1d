// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M571.733 85.333h-119.467c-23.898 0-35.843 0-44.97 4.65-8.028 4.091-14.555 10.618-18.646 18.646-4.65 9.127-4.65 21.075-4.65 44.97v162.133c0 23.895 0 35.843-4.65 44.97-4.091 8.028-10.618 14.555-18.646 18.646-9.127 4.65-21.075 4.65-44.97 4.65h-162.133c-23.895 0-35.843 0-44.97 4.65-8.028 4.091-14.555 10.618-18.646 18.646-4.65 9.127-4.65 21.073-4.65 44.97v119.467c0 23.898 0 35.844 4.65 44.971 4.091 8.030 10.618 14.554 18.646 18.645 9.127 4.651 21.075 4.651 44.97 4.651h162.133c23.895 0 35.843 0 44.97 4.651 8.028 4.092 14.555 10.615 18.646 18.645 4.65 9.126 4.65 21.073 4.65 44.971v162.133c0 23.898 0 35.844 4.65 44.971 4.091 8.030 10.618 14.554 18.646 18.645 9.127 4.651 21.073 4.651 44.97 4.651h119.467c23.898 0 35.844 0 44.971-4.651 8.030-4.092 14.554-10.615 18.645-18.645 4.651-9.126 4.651-21.073 4.651-44.971v-162.133c0-23.898 0-35.844 4.651-44.971 4.092-8.030 10.615-14.554 18.645-18.645 9.126-4.651 21.073-4.651 44.971-4.651h162.133c23.898 0 35.844 0 44.971-4.651 8.030-4.092 14.554-10.615 18.645-18.645 4.651-9.126 4.651-21.073 4.651-44.971v-119.467c0-23.898 0-35.843-4.651-44.97-4.092-8.028-10.615-14.555-18.645-18.646-9.126-4.65-21.073-4.65-44.971-4.65h-162.133c-23.898 0-35.844 0-44.971-4.65-8.030-4.091-14.554-10.618-18.645-18.646-4.651-9.127-4.651-21.075-4.651-44.97v-162.133c0-23.895 0-35.843-4.651-44.97-4.092-8.028-10.615-14.555-18.645-18.646-9.126-4.65-21.073-4.65-44.971-4.65z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "first-aid"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1655,
        "id": 365,
        "name": "first-aid",
        "prevSize": 32,
        "code": 59931
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 284
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FirstAidIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FirstAidIcon = ({ size = 16, ...props }: FirstAidIconProps) => (
  <IcoMoon iconSet={iconSet} icon="first-aid" size={size} {...props} />
);

FirstAidIcon.displayName = 'FirstAidIcon';

export default FirstAidIcon;
