import clsx from 'clsx';
import * as React from 'react';

import { BaseButton } from '@base-components/base-button';
import type { baseButtonPropDefs } from '@base-components/base-button.props';

type IconButtonElement = React.ComponentRef<typeof BaseButton>;
interface IconButtonProps extends Omit<React.ComponentPropsWithoutRef<typeof BaseButton>, 'variant'> {
  variant?: (typeof baseButtonPropDefs.variant.values)[number];
}
const IconButton = React.forwardRef<IconButtonElement, IconButtonProps>(({ className, ...props }, forwardedRef) => (
  <BaseButton {...props} ref={forwardedRef} className={clsx('rt-IconButton', className)} />
));
IconButton.displayName = 'IconButton';

export { IconButton };
export type { IconButtonProps };
