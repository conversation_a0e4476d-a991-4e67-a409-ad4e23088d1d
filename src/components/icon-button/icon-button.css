/***************************************************************************************************
 *                                                                                                 *
 * SIZES                                                                                           *
 *                                                                                                 *
 ***************************************************************************************************/

.rt-IconButton {
  &:where(:not(.rt-variant-ghost)) {
    height: var(--base-button-height);
    width: var(--base-button-height);
  }
  &:where(.rt-variant-ghost) {
    padding: var(--icon-button-ghost-padding);

    /* We reset the defined margin variables to avoid inheriting them from a higher component */
    /* If a margin IS defined on the component itself, the utility class will win and reset it */
    --margin-top: 0px;
    --margin-right: 0px;
    --margin-bottom: 0px;
    --margin-left: 0px;

    /* Define the overrides to incorporate the negative margins */
    --margin-top-override: calc(var(--margin-top) - var(--icon-button-ghost-padding));
    --margin-right-override: calc(var(--margin-right) - var(--icon-button-ghost-padding));
    --margin-bottom-override: calc(var(--margin-bottom) - var(--icon-button-ghost-padding));
    --margin-left-override: calc(var(--margin-left) - var(--icon-button-ghost-padding));

    /* Reset the overrides on direct children */
    :where(&) > * {
      --margin-top-override: initial;
      --margin-right-override: initial;
      --margin-bottom-override: initial;
      --margin-left-override: initial;
    }

    margin: var(--margin-top-override) var(--margin-right-override) var(--margin-bottom-override)
      var(--margin-left-override);

    &:where(:focus, :focus-visible) {
      outline: none;
    }
  }
}

@breakpoints {
  .rt-IconButton {
    &:where(.rt-r-size-1) {
      --base-button-height: var(--spatial-7);
      --base-button-svg-height: 14px;
      border-radius: var(--radius-2);
    }
    &:where(.rt-r-size-2) {
      --base-button-height: var(--spatial-8);
      --base-button-svg-height: var(--spatial-5);
      border-radius: var(--radius-3);
    }
    &:where(.rt-r-size-3) {
      --base-button-height: var(--spatial-9);
      --base-button-svg-height: 18px;
      border-radius: var(--radius-4);
    }
    &:where(.rt-r-size-4) {
      --base-button-height: var(--spatial-10);
      --base-button-svg-height: var(--spatial-6);
      border-radius: var(--radius-4);
    }
    &:where(.rt-r-size-5) {
      --base-button-height: var(--spatial-11);
      --base-button-svg-height: var(--spatial-7);
      border-radius: var(--radius-5);
    }

    &:where(.rt-variant-ghost) {
      &:where(.rt-r-size-1) {
        --icon-button-ghost-padding: var(--spatial-1);
      }
      &:where(.rt-r-size-2) {
        --icon-button-ghost-padding: calc(var(--spatial-1) * 1.5);
      }
      &:where(.rt-r-size-3) {
        --icon-button-ghost-padding: var(--spatial-2);
      }
      &:where(.rt-r-size-4) {
        --icon-button-ghost-padding: var(--spatial-3);
      }
      &:where(.rt-r-size-5) {
        --icon-button-ghost-padding: var(--spatial-4);
      }
    }
  }
}

/***************************************************************************************************
 *                                                                                                 *
 * VARIANTS                                                                                        *
 *                                                                                                 *
 ***************************************************************************************************/

.rt-IconButton:where(.rt-variant-secondary, .rt-variant-tertiary) {
  &:where(:focus, :focus-visible) {
    outline: var(--border-width-thicker) solid var(--separators-non-opaque);
  }
}

/* quaternary */
.rt-BaseButton:where(.rt-variant-quaternary) {
  color: var(--labels-primary);

  &:where(.rt-high-contrast) {
    color: var(--grays-gray);
  }

  /* Better -webkit-tap-highlight-color */
  @media (pointer: coarse) {
    &:where(:active:not([data-state='open'])) {
      outline: 0.5em solid var(--brand-accent-40);
      outline-offset: 0;
    }
  }
  &:where(:focus, :focus-visible) {
    background-color: var(--background-primary-base);
    outline: var(--border-width-thicker) solid var(--separators-non-opaque);
  }
  @media (hover: hover) {
    &:where(:hover) {
      background-color: var(--fills-tertiary);

      &:where(.rt-high-contrast) {
        filter: var(--base-button-secondary-high-contrast-hover-filter);
      }
    }
  }

  &:where(:active:not([data-state='open'])) {
    filter: var(--base-button-secondary-active-filter);
  }
  &:where([data-disabled]) {
    color: var(--labels-tertiary);
    outline: none;
    filter: none;
  }
}

/* material */
.rt-BaseButton:where(.rt-variant-material) {
  color: var(--grays-white);
  background-color: var(--materials-inverted);
  backdrop-filter: blur(25px);
  border: var(--border-width-thinner) solid var(--stroke-glass-specular);

  &:where(.rt-high-contrast) {
    color: var(--grays-gray);
  }

  /* Better -webkit-tap-highlight-color */
  @media (pointer: coarse) {
    &:where(:active:not([data-state='open'])) {
      outline: 0.5em solid var(--brand-accent-40);
      outline-offset: 0;
    }
  }
  &:where(:focus, :focus-visible) {
    outline: var(--border-width-thicker) solid var(--brand-accent);
  }
  @media (hover: hover) {
    &:where(:hover) {
      background:
        linear-gradient(0deg, rgba(0, 0, 0, 0.04) 0%, rgba(0, 0, 0, 0.04) 100%), var(--materials-inverted-elevated);

      &:where(.rt-high-contrast) {
        filter: var(--base-button-secondary-high-contrast-hover-filter);
      }
    }
  }

  &:where([data-disabled]) {
    color: var(--labels-tertiary);
    background: var(--materials-inverted);
    outline: none;
    filter: none;
  }
}
