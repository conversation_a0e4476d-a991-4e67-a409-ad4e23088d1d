// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M261.889 839.381v0zM205.951 783.445v0zM818.048 783.445v0zM762.112 839.381v0zM762.112 99.284v0zM818.048 155.223v0zM261.889 99.284v0zM205.951 155.223v0zM341.333 181.333c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM426.667 245.333c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM555.093 245.333c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM554.667 181.333c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM661.333 245.333c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM660.907 181.333c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM192 309.333c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM832 373.333c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM446.217 601.097v0zM597.009 639.556v0zM298.667 850.065v0zM416 853.333c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM672 853.333c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM396.8 85.333v32h230.4v-64h-230.4v32zM832 290.133h-32v358.4h64v-358.4h-32zM627.2 853.333v-32h-230.4v64h230.4v-32zM192 648.533h32v-358.4h-64v358.4h32zM192 648.533h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM261.889 839.381l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM832 648.533h-32c0 36.373-0.026 61.726-1.638 81.463-1.583 19.366-4.531 30.494-8.823 38.921l57.024 29.056c9.655-18.957 13.683-39.441 15.586-62.763 1.877-22.95 1.852-51.362 1.852-86.677h-32zM818.048 783.445l-28.51-14.528c-9.207 18.061-23.893 32.747-41.954 41.954l29.056 57.024c30.106-15.343 54.579-39.817 69.922-69.922l-28.514-14.528zM627.2 85.333v32c36.373 0 61.726 0.025 81.463 1.638 19.366 1.582 30.494 4.532 38.921 8.826l29.056-57.024c-18.957-9.658-39.441-13.683-62.763-15.589-22.95-1.875-51.362-1.85-86.677-1.85v32zM832 290.133h32c0-35.315 0.026-63.727-1.852-86.676-1.903-23.323-5.931-43.809-15.586-62.762l-57.024 29.055c4.292 8.428 7.241 19.553 8.823 38.919 1.613 19.739 1.638 45.093 1.638 81.464h32zM762.112 99.284l-14.528 28.512c18.061 9.204 32.747 23.89 41.954 41.953l57.024-29.055c-15.343-30.106-39.817-54.583-69.922-69.923l-14.528 28.512zM396.8 85.333v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM192 290.133h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM261.889 99.284l-14.528-28.512c-30.106 15.34-54.583 39.817-69.923 69.923l57.024 29.055c9.204-18.063 23.89-32.75 41.953-41.953l-14.528-28.512zM341.333 213.333v32h85.333v-64h-85.333v32zM555.093 213.333v-32h-0.427v64h0.427v-32zM661.333 213.333v-32h-0.427v64h0.427v-32zM341.333 938.667v-32c-5.891 0-10.667-4.774-10.667-10.667h-64c0 41.237 33.429 74.667 74.667 74.667v-32zM341.333 938.667v32c41.237 0 74.667-33.429 74.667-74.667h-64c0 5.892-4.776 10.667-10.667 10.667v32zM682.667 938.667v-32c-5.892 0-10.667-4.774-10.667-10.667h-64c0 41.237 33.429 74.667 74.667 74.667v-32zM682.667 938.667v32c41.237 0 74.667-33.429 74.667-74.667h-64c0 5.892-4.774 10.667-10.667 10.667v32zM192 341.333v32h640v-64h-640v32zM384 597.333h32c0-53.018 42.982-96 96-96v-64c-88.366 0-160 71.633-160 160h32zM512 469.333v32c53.018 0 96 42.982 96 96h64c0-88.367-71.633-160-160-160v32zM512 725.333v-32c-47.023 0-86.208-33.839-94.415-78.502l-62.946 11.567c13.687 74.492 78.897 130.935 157.361 130.935v-32zM386.112 620.612l31.473-5.781c-1.039-5.649-1.585-11.494-1.585-17.498h-64c0 9.899 0.903 19.614 2.639 29.065l31.473-5.786zM386.112 620.612l9.882 30.438 60.104-19.516-19.763-60.873-60.105 19.516 9.882 30.434zM640 597.333h-32c0 8.337-1.054 16.38-3.021 24.026l61.978 15.949c3.298-12.809 5.043-26.21 5.043-39.974h-32zM635.968 629.333l-30.989-7.974c-10.662 41.412-48.29 71.974-92.979 71.974v64c74.598 0 137.199-51.021 154.957-120.026l-30.989-7.974zM597.009 639.556l8.124 30.95 38.955-10.223-8.119-30.95-8.124-30.95-38.955 10.219 8.119 30.955zM523.712 619.712l-22.626 22.626c27.213 27.213 66.82 37.939 104.047 28.169l-8.124-30.95-8.119-30.955c-15.223 3.998-31.424-0.388-42.551-11.516l-22.626 22.626zM446.217 601.097l9.882 30.438c15.834-5.141 33.216-0.969 44.988 10.803l45.252-45.252c-28.783-28.787-71.283-38.993-110.003-26.423l9.882 30.434zM298.667 896h32v-45.935h-64v45.935h32zM396.8 853.333v-32c-45.197 0-73.079-0.077-93.699-2.961l-8.869 63.381c26.11 3.657 59.372 3.58 102.568 3.58v-32zM298.667 850.065l4.434-31.693c-12.147-1.698-20.227-4.211-26.684-7.501l-29.055 57.024c14.543 7.411 29.952 11.494 46.871 13.858l4.434-31.689zM384 853.333h-32v42.667h64v-42.667h-32zM640 896h32v-42.667h-64v42.667h32zM725.333 850.065h-32v45.935h64v-45.935h-32zM627.2 853.333v32c43.196 0 76.459 0.077 102.566-3.58l-8.866-63.381c-20.621 2.884-48.503 2.961-93.7 2.961v32zM725.333 850.065l4.433 31.689c16.922-2.364 32.329-6.447 46.874-13.858l-29.056-57.024c-6.455 3.29-14.537 5.803-26.684 7.501l4.433 31.693z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "washing-mechine"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 2006,
        "id": 14,
        "name": "washing-mechine",
        "prevSize": 32,
        "code": 60282
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 635
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface WashingMechineIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const WashingMechineIcon = ({ size = 16, ...props }: WashingMechineIconProps) => (
  <IcoMoon iconSet={iconSet} icon="washing-mechine" size={size} {...props} />
);

WashingMechineIcon.displayName = 'WashingMechineIcon';

export default WashingMechineIcon;
