// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 938.667h47.407M132.741 938.667v-687.653c0-61.523 46.844-112.101 106.799-117.989 7.767-0.763 15.575-1.344 23.228-2.875l214.020-42.819c7.654-1.531 15.462-2.493 23.228-1.73 59.955 5.888 106.799 56.465 106.799 117.989v165.985M132.741 938.667h142.222M274.963 938.667h189.63M274.963 938.667v-189.696c0-39.287 31.838-71.138 71.111-71.138h47.407c39.274 0 71.112 31.851 71.112 71.138v189.696M464.593 938.667h142.221M606.814 938.667v-569.092M606.814 938.667h284.446M263.111 322.15h47.407M263.111 488.137h47.407M429.039 322.15h47.407M429.039 488.137h47.407M606.814 369.574l185.412 30.913c57.148 9.528 99.034 58.99 99.034 116.948v421.231M891.26 938.667h47.407M749.039 606.694v94.852"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "building-alt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1496,
        "id": 524,
        "name": "building-alt",
        "prevSize": 32,
        "code": 59772
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 125
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BuildingAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BuildingAltIcon = ({ size = 16, ...props }: BuildingAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="building-alt" size={size} {...props} />
);

BuildingAltIcon.displayName = 'BuildingAltIcon';

export default BuildingAltIcon;
