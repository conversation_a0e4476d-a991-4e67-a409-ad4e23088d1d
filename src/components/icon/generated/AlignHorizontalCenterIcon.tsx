// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 576h-285.867c-11.948 0-17.922 0-22.485 2.325-4.014 2.044-7.278 5.308-9.323 9.323-2.325 4.565-2.325 10.539-2.325 22.485v145.067c0 11.947 0 17.92 2.325 22.485 2.045 4.015 5.309 7.279 9.323 9.323 4.564 2.325 10.537 2.325 22.485 2.325h285.867M512 576h285.867c11.947 0 17.92 0 22.485 2.325 4.015 2.044 7.279 5.308 9.323 9.323 2.325 4.565 2.325 10.539 2.325 22.485v145.067c0 11.947 0 17.92-2.325 22.485-2.044 4.015-5.308 7.279-9.323 9.323-4.565 2.325-10.539 2.325-22.485 2.325h-285.867M512 576v-128M512 789.333v149.333M512 448h179.2c11.947 0 17.92 0 22.485-2.325 4.015-2.044 7.279-5.308 9.323-9.323 2.325-4.565 2.325-10.537 2.325-22.485v-145.067c0-11.948 0-17.922-2.325-22.485-2.044-4.014-5.308-7.278-9.323-9.323-4.565-2.325-10.539-2.325-22.485-2.325h-179.2M512 448h-179.2c-11.948 0-17.922 0-22.485-2.325-4.014-2.044-7.278-5.308-9.323-9.323-2.325-4.565-2.325-10.537-2.325-22.485v-145.067c0-11.948 0-17.922 2.325-22.485 2.045-4.014 5.309-7.278 9.323-9.323 4.564-2.325 10.537-2.325 22.485-2.325h179.2M512 234.667v-149.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "align-horizontal-center"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1391,
        "id": 629,
        "name": "align-horizontal-center",
        "prevSize": 32,
        "code": 59667
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 20
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface AlignHorizontalCenterIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const AlignHorizontalCenterIcon = ({ size = 16, ...props }: AlignHorizontalCenterIconProps) => (
  <IcoMoon iconSet={iconSet} icon="align-horizontal-center" size={size} {...props} />
);

AlignHorizontalCenterIcon.displayName = 'AlignHorizontalCenterIcon';

export default AlignHorizontalCenterIcon;
