// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M514.035 796.446c-36.484 0-72.239-9.95-103.197-28.715-30.959-18.769-55.884-45.602-71.942-77.449M853.333 598.067c0 188.109-152.819 340.599-341.333 340.599s-341.333-152.491-341.333-340.599c0-154.59 230.525-416.572 312.683-505.030 12.706-13.681 35.618-8.128 43.729 8.678 76.399 158.319 326.255 322.514 326.255 496.352z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "drop"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1612,
        "id": 408,
        "name": "drop",
        "prevSize": 32,
        "code": 59888
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 241
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface DropIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const DropIcon = ({ size = 16, ...props }: DropIconProps) => (
  <IcoMoon iconSet={iconSet} icon="drop" size={size} {...props} />
);

DropIcon.displayName = 'DropIcon';

export default DropIcon;
