// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 772.74c-157.094 0-284.445-127.351-284.445-284.446v-320.287c0-28.873 20.457-53.826 48.879-58.912 176.928-31.66 294.215-31.704 471.145-0.009 28.416 5.090 48.866 30.040 48.866 58.908v320.305c0 157.090-127.351 284.442-284.446 284.442zM512 772.74v165.926M512 938.667h-142.222M512 938.667h142.221M226.624 213.426l-87.069 8.707c-30.781 3.078-54.221 28.98-54.221 59.914v64.027c0 101.141 63.347 187.49 152.53 221.529M788.309 566.763c88.038-34.603 150.357-120.367 150.357-220.689v-64.027c0-30.934-23.441-56.836-54.221-59.914l-85.837-8.583"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "cup"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1592,
        "id": 428,
        "name": "cup",
        "prevSize": 32,
        "code": 59868
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 221
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CupIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CupIcon = ({ size = 16, ...props }: CupIconProps) => (
  <IcoMoon iconSet={iconSet} icon="cup" size={size} {...props} />
);

CupIcon.displayName = 'CupIcon';

export default CupIcon;
