// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M417.144 759.172c0 52.002-42.453 94.161-94.822 94.161s-94.822-42.159-94.822-94.161M417.144 759.172c0-52.002-42.453-94.161-94.822-94.161s-94.822 42.159-94.822 94.161M417.144 759.172h189.644M227.5 759.172h-14.78c-59.395 0-109.981-43.631-116.172-102.289-14.686-139.157-15.124-245.753-0.274-384.5 6.253-58.422 56.745-101.716 115.909-101.716h386.703c43.639 0 79.019 35.131 79.019 78.467 0 8.667 7.095 15.667 15.74 16.869 31.787 4.42 71.467 20.842 90.577 41.399l61.504 66.167M796.433 759.172c0 52.002-42.453 94.161-94.822 94.161s-94.822-42.159-94.822-94.161M796.433 759.172c0-52.002-42.453-94.161-94.822-94.161s-94.822 42.159-94.822 94.161M796.433 759.172h23.706c65.459 0 118.528-52.698 118.528-117.7v-70.622M845.726 373.569l61.508 66.166c20.211 21.743 31.433 50.244 31.433 79.834v51.281M845.726 373.569h-49.293c-26.185 0-47.411 21.079-47.411 47.080v56.040c0 52.002 42.453 94.161 94.822 94.161h94.822M298.614 429.611h118.528M417.141 429.611h118.526M417.141 429.611v-117.702M417.141 429.611v117.7"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "ambulance"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1395,
        "id": 625,
        "name": "ambulance",
        "prevSize": 32,
        "code": 59671
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 24
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface AmbulanceIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const AmbulanceIcon = ({ size = 16, ...props }: AmbulanceIconProps) => (
  <IcoMoon iconSet={iconSet} icon="ambulance" size={size} {...props} />
);

AmbulanceIcon.displayName = 'AmbulanceIcon';

export default AmbulanceIcon;
