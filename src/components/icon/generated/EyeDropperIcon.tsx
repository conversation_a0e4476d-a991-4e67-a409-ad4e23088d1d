// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M435.447 385.518l-276.477 276.477c-26.53 26.53-43.894 60.548-49.071 96.132l-24.433 167.932c-1.106 7.599 4.878 13.581 12.477 12.476l167.93-24.435c35.584-5.175 69.6-22.541 96.13-49.071l276.478-276.476M435.447 385.518l203.034 203.035M435.447 385.518l-67.802-67.804c-6.937-6.937-5.050-19.229 3.909-25.467l80.951-56.368c20.608-14.348 46.925-13.192 62.878 2.763 18.487 18.485 50.214 16.73 70.865-3.92l98.697-98.697c62.63-62.633 158.857-67.956 214.921-11.89 56.068 56.066 50.743 152.29-11.891 214.923l-98.697 98.695c-20.651 20.651-22.404 52.378-3.921 70.865 15.957 15.953 17.114 42.27 2.765 62.878l-56.367 80.951c-6.238 8.96-18.53 10.846-25.468 3.908l-67.806-67.802"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "eye-dropper"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1624,
        "id": 396,
        "name": "eye-dropper",
        "prevSize": 32,
        "code": 59900
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 253
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface EyeDropperIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const EyeDropperIcon = ({ size = 16, ...props }: EyeDropperIconProps) => (
  <IcoMoon iconSet={iconSet} icon="eye-dropper" size={size} {...props} />
);

EyeDropperIcon.displayName = 'EyeDropperIcon';

export default EyeDropperIcon;
