// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M533.333 341.333l164.634 164.634c1.664 1.664 2.5 3.849 2.5 6.033M533.333 682.667l164.634-164.634c1.664-1.664 2.5-3.849 2.5-6.033M700.467 512h-551.134M320 256v-42.667c0-70.692 57.308-128 128-128h298.667c70.694 0 128 57.308 128 128v597.333c0 70.694-57.306 128-128 128h-298.667c-70.692 0-128-57.306-128-128v-42.667"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "login"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1748,
        "id": 272,
        "name": "login",
        "prevSize": 32,
        "code": 60024
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 377
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface LoginIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const LoginIcon = ({ size = 16, ...props }: LoginIconProps) => (
  <IcoMoon iconSet={iconSet} icon="login" size={size} {...props} />
);

LoginIcon.displayName = 'LoginIcon';

export default LoginIcon;
