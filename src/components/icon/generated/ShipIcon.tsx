// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M128 804.988l96.806-23.902c62.257-15.369 127.599-13.5 188.85 5.402 64.193 19.81 132.814 20.894 197.616 3.119l11.221-3.081c68.992-18.927 141.943-18.662 210.795 0.768l62.711 17.694M224 938.667l72.461-19.085c46.668-12.288 95.959-10.799 141.777 4.284 48.013 15.808 99.785 16.666 148.309 2.47l8.32-2.436c51.614-15.104 106.586-14.895 158.080 0.606l47.053 14.161M320 315.059l-65.86 30.019c-31.407 14.316-48.068 48.665-39.678 81.807l21.538 85.082M320 315.059l161.826-73.762c19.145-8.726 41.203-8.726 60.348 0l161.826 73.762M320 315.059v-158.62c0-39.27 32.236-71.105 72-71.105h240c39.765 0 72 31.835 72 71.105v158.62M704 315.059l65.86 30.019c31.407 14.316 48.068 48.665 39.676 81.807l-21.534 85.082M272 654.178l-36-142.212M236 511.966l247.294-106.182c18.308-7.863 39.104-7.863 57.417 0l247.292 106.182M788.002 511.966l-36.002 142.212"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "ship"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1885,
        "id": 135,
        "name": "ship",
        "prevSize": 32,
        "code": 60161
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 514
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ShipIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ShipIcon = ({ size = 16, ...props }: ShipIconProps) => (
  <IcoMoon iconSet={iconSet} icon="ship" size={size} {...props} />
);

ShipIcon.displayName = 'ShipIcon';

export default ShipIcon;
