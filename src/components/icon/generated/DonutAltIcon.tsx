// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M285.113 637.995v0zM293.827 611.853v0zM312.726 591.287v0zM819.951 539.849v0zM824.222 552.661v0zM905.579 584.196v0zM907.925 628.309c13.427 11.486 33.63 9.911 45.116-3.516 11.49-13.431 9.916-33.63-3.516-45.12l-41.6 48.636zM120.975 701.577c-13.12 11.84-14.158 32.077-2.318 45.197s32.075 14.157 45.196 2.317l-42.877-47.514zM203.59 670.127v0zM892.617 576.478v0zM898.261 578.564v0zM831.010 568.354v0zM839.569 574.519v0zM791.94 521.69v0zM817.058 532.092v0zM297.974 601.557v0zM302.818 596.288v0zM759.053 524.169v0zM779.383 527.467v0zM278.322 653.687v0zM269.765 659.853v0zM721.476 491.255v0zM727.642 493.811v0zM216.876 661.85v0zM211.049 664.090v0zM169.373 468.041c-12.497 12.497-12.497 32.755 0 45.252s32.758 12.497 45.255 0l-45.255-45.252zM257.294 470.626c12.497-12.497 12.497-32.755 0-45.254-12.497-12.497-32.758-12.497-45.255 0l45.255 45.254zM310.976 288.547c-5.589 16.766 3.472 34.889 20.239 40.477s34.889-3.472 40.477-20.239l-60.716-20.239zM393.024 244.786c5.589-16.766-3.472-34.889-20.239-40.477s-34.889 3.472-40.477 20.239l60.716 20.239zM619.959 190.706c-12.497-12.497-32.755-12.497-45.252 0s-12.497 32.758 0 45.255l45.252-45.255zM617.374 278.627c12.497 12.497 32.755 12.497 45.252 0s12.497-32.758 0-45.255l-45.252 45.255zM827.721 362.667c0-17.673-14.327-32-32-32-17.677 0-32 14.327-32 32h64zM763.721 405.333c0 17.673 14.323 32 32 32 17.673 0 32-14.327 32-32h-64zM682.667 512h-32c0 76.582-62.084 138.667-138.667 138.667v64c111.932 0 202.667-90.735 202.667-202.667h-32zM341.333 512h32c0-76.582 62.084-138.667 138.667-138.667v-64c-111.93 0-202.667 90.737-202.667 202.667h32zM285.113 637.995l30.358 10.121 8.714-26.146-60.716-20.237-8.714 26.142 30.358 10.121zM512 682.667v-32c-58.765 0-109.066-36.565-129.245-88.299l-59.625 23.258c29.44 75.477 102.86 129.041 188.87 129.041v-32zM352.943 573.995l29.812-11.627c-6.073-15.569-9.422-32.542-9.422-50.368h-64c0 25.911 4.879 50.761 13.797 73.626l29.812-11.631zM312.726 591.287l12.64 29.397 40.217-17.293-25.28-58.795-40.217 17.293 12.64 29.397zM512 341.333v32c70.69 0 129.067 52.923 137.591 121.293l63.509-7.919c-12.471-100.006-97.728-177.374-201.101-177.374v32zM681.348 490.667l-31.757 3.959c0.708 5.679 1.075 11.477 1.075 17.374h64c0-8.55-0.533-16.994-1.566-25.293l-31.753 3.959zM681.348 490.667v32h29.845v-64h-29.845v32zM735.33 500.663l-22.626 22.626 19.695 19.695 45.252-45.252-19.695-19.695-22.626 22.626zM819.951 539.849l-30.362 10.116 4.271 12.817 60.719-20.241-4.271-12.813-30.357 10.121zM856.602 576v32h26.79v-64h-26.79v32zM905.579 584.196l-20.8 24.316 23.147 19.797 41.6-48.636-23.142-19.797-20.804 24.32zM226.458 661.333v32h26.274v-64h-26.274v32zM142.414 725.333l21.439 23.757 61.176-55.206-42.878-47.514-61.176 55.206 21.439 23.757zM883.392 576v32c1.203 0 2.082 0 2.837 0.004 0.742 0.009 1.148 0.017 1.365 0.030 0.363 0.013-0.448 0-1.724-0.273l6.746-31.283 6.741-31.279c-6.327-1.365-12.651-1.199-15.966-1.199v32zM905.579 584.196l20.804-24.32c-2.522-2.155-7.219-6.391-12.911-9.468l-30.421 56.311c-1.148-0.623-1.771-1.139-1.485-0.917 0.171 0.137 0.486 0.393 1.054 0.87 0.576 0.486 1.246 1.058 2.159 1.839l20.8-24.316zM892.617 576.478l-6.746 31.283c-0.981-0.213-1.933-0.563-2.82-1.041l30.421-56.311c-4.437-2.394-9.186-4.151-14.114-5.21l-6.741 31.279zM824.222 552.661l-30.362 10.116c1.937 5.807 5.175 17.22 12.604 26.103l49.097-41.058c1.587 1.899 2.074 3.345 1.719 2.483-0.188-0.461-0.486-1.242-0.964-2.615-0.478-1.378-1.020-2.991-1.737-5.15l-30.357 10.121zM856.602 576v-32c-2.274 0-3.977 0-5.431-0.021-1.455-0.017-2.291-0.055-2.79-0.090-0.93-0.064 0.597-0.055 2.901 0.849l-23.424 59.563c10.778 4.237 22.63 3.699 28.745 3.699v-32zM831.010 568.354l-24.546 20.527c5.726 6.848 13.082 12.151 21.393 15.42l23.424-59.563c1.66 0.657 3.132 1.715 4.279 3.085l-24.55 20.531zM784.414 525.129l14.31 28.621c0.994-0.499 1.762-0.883 2.423-1.207 0.661-0.329 1.079-0.529 1.361-0.657 0.623-0.29 0.017 0.030-1.169 0.393l-18.795-61.175c-4.885 1.498-9.766 4.066-12.446 5.406l14.315 28.621zM819.951 539.849l30.357-10.121c-0.947-2.842-2.586-8.107-4.979-12.621l-56.546 29.969c-0.585-1.097-0.785-1.749-0.55-1.105 0.107 0.29 0.26 0.725 0.499 1.425 0.235 0.7 0.508 1.515 0.858 2.569l30.362-10.116zM791.94 521.69l9.399 30.588c-4.894 1.502-10.163-0.678-12.557-5.201l56.55-29.969c-11.985-22.609-38.332-33.523-62.788-26.005l9.395 30.588zM293.827 611.853l30.358 10.116c0.459-1.378 0.796-2.385 1.093-3.247 0.295-0.858 0.466-1.323 0.566-1.579 0.169-0.435-0.154 0.486-1.009 1.809l-53.721-34.786c-4.201 6.485-6.385 13.79-7.644 17.566l30.358 10.121zM312.726 591.287l-12.64-29.397c-3.656 1.574-10.75 4.365-16.861 9.097l39.187 50.598c-1.244 0.964-2.137 1.365-1.717 1.161 0.247-0.119 0.697-0.333 1.525-0.695 0.834-0.371 1.812-0.789 3.147-1.365l-12.64-29.397zM297.974 601.557l26.86 17.395c-0.653 1.007-1.472 1.899-2.423 2.633l-39.187-50.598c-4.751 3.678-8.846 8.137-12.112 13.18l26.861 17.391zM755.025 520.358l-22.626 22.626c1.408 1.408 4.011 4.096 6.916 6.37l39.475-50.377c0.636 0.499 0.969 0.832 0.738 0.614-0.115-0.111-0.311-0.303-0.64-0.631-0.333-0.329-0.721-0.713-1.237-1.229l-22.626 22.626zM784.414 525.129l-14.315-28.621c-0.653 0.324-1.139 0.567-1.557 0.777-0.418 0.205-0.661 0.324-0.811 0.393-0.286 0.137 0.137-0.077 0.896-0.346l21.517 60.271c3.477-1.237 6.797-2.961 8.58-3.853l-14.31-28.621zM759.053 524.169l-19.733 25.186c14.404 11.29 33.587 14.404 50.825 8.252l-21.517-60.275c3.447-1.233 7.283-0.61 10.163 1.647l-19.738 25.19zM285.113 637.995l-30.358-10.121c-0.719 2.159-1.258 3.772-1.737 5.15-0.478 1.374-0.777 2.155-0.966 2.615-0.355 0.862 0.134-0.585 1.722-2.483l49.096 41.058c7.429-8.883 10.667-20.301 12.601-26.099l-30.358-10.121zM252.731 661.333v32c6.117 0 17.969 0.538 28.746-3.699l-23.424-59.563c2.304-0.905 3.829-0.913 2.899-0.849-0.497 0.034-1.334 0.073-2.787 0.090-1.458 0.021-3.16 0.021-5.434 0.021v32zM278.322 653.687l-24.548-20.531c1.146-1.37 2.617-2.428 4.279-3.085l23.424 59.563c8.309-3.268 15.665-8.572 21.392-15.42l-24.547-20.527zM711.194 490.667v32c1.348 0 2.33 0 3.174 0.009 0.836 0.009 1.293 0.021 1.54 0.030 0.422 0.021-0.486 0.009-1.903-0.333l14.942-62.234c-6.993-1.677-14.059-1.472-17.754-1.472v32zM735.33 500.663l22.626-22.626c-2.611-2.611-7.458-7.753-13.594-11.511l-33.438 54.571c-1.246-0.764-1.894-1.395-1.583-1.109 0.183 0.166 0.516 0.482 1.114 1.067 0.602 0.589 1.297 1.284 2.249 2.236l22.626-22.626zM721.476 491.255l-7.471 31.117c-1.088-0.26-2.129-0.691-3.081-1.276l33.438-54.571c-4.77-2.923-9.975-5.077-15.415-6.387l-7.471 31.117zM226.458 661.333v-32c-3.444 0-10.018-0.179-16.573 1.289l13.982 62.455c-1.322 0.294-2.167 0.307-1.785 0.29 0.23-0.009 0.651-0.021 1.427-0.026 0.782-0.009 1.696-0.009 2.949-0.009v-32zM203.59 670.127l21.439 23.757c0.93-0.841 1.609-1.451 2.193-1.971 0.581-0.516 0.902-0.789 1.079-0.934 0.294-0.243-0.341 0.311-1.521 0.977l-31.461-55.731c-5.85 3.302-10.611 7.838-13.168 10.146l21.439 23.757zM216.876 661.85l-6.991-31.228c-5.104 1.143-10.012 3.029-14.567 5.602l31.462 55.731c-0.911 0.516-1.893 0.892-2.914 1.122l-6.991-31.228zM192 490.667l22.627 22.626 42.667-42.667-45.255-45.254-42.667 42.668 22.627 22.626zM341.333 298.667l30.358 10.119 21.333-64-60.716-20.239-21.333 64 30.358 10.119zM597.333 213.333l-22.626 22.627 42.667 42.667 45.252-45.255-42.667-42.667-22.626 22.627zM938.667 512h-32c0 217.967-176.7 394.667-394.667 394.667v64c253.316 0 458.667-205.35 458.667-458.667h-32zM512 938.667v-32c-217.968 0-394.667-176.7-394.667-394.667h-64c0 253.316 205.352 458.667 458.667 458.667v-32zM85.333 512h32c0-217.968 176.698-394.667 394.667-394.667v-64c-253.315 0-458.667 205.352-458.667 458.667h32zM512 85.333v32c217.967 0 394.667 176.698 394.667 394.667h64c0-253.315-205.35-458.667-458.667-458.667v32zM795.721 362.667h-32v42.667h64v-42.667h-32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "donut-alt"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1603,
        "id": 417,
        "name": "donut-alt",
        "prevSize": 32,
        "code": 59879
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 232
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface DonutAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const DonutAltIcon = ({ size = 16, ...props }: DonutAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="donut-alt" size={size} {...props} />
);

DonutAltIcon.displayName = 'DonutAltIcon';

export default DonutAltIcon;
