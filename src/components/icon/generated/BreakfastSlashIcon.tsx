// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M597.103 409.725v0zM573.973 401.671v0zM493.312 264.719c7.437 16.034 26.458 23.005 42.492 15.571s23.006-26.458 15.573-42.491l-58.065 26.92zM517.478 240.762v0zM521.591 169.516v0zM523.098 167.077v0zM527.211 95.83v0zM551.377 71.873c-7.437-16.034-26.458-23.005-42.492-15.572s-23.006 26.458-15.573 42.492l58.065-26.92zM361.434 288.423c7.434 16.033 26.458 23.005 42.491 15.571s23.005-26.458 15.572-42.492l-58.063 26.921zM385.599 264.466v0zM389.712 193.219v0zM391.22 190.781v0zM424.364 106.074c-7.434-16.034-26.458-23.005-42.491-15.571s-23.005 26.458-15.572 42.491l58.063-26.92zM625.19 288.423c7.437 16.034 26.458 23.005 42.492 15.572s23.006-26.458 15.573-42.491l-58.065 26.92zM649.357 264.466v0zM653.47 193.219v0zM654.976 190.781v0zM659.089 119.534v0zM683.255 95.577c-7.433-16.034-26.458-23.005-42.492-15.572s-23.006 26.458-15.573 42.491l58.065-26.92zM194.131 110.983c-12.017-12.959-32.264-13.723-45.222-1.707s-13.723 32.264-1.707 45.223l46.929-43.516zM872.533 936.721c12.019 12.958 32.265 13.722 45.227 1.707 12.958-12.015 13.722-32.265 1.707-45.222l-46.933 43.516zM376.858 553.481c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM496.414 686.221c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM724.497 900.907c13.21-11.742 14.4-31.966 2.658-45.175s-31.97-14.4-45.175-2.658l42.517 47.834zM368.643 506.334c6.196-16.555-2.199-34.995-18.75-41.19s-34.992 2.197-41.188 18.752l59.938 22.438zM541.184 938.667v-32h-37.679v64h37.679v-32zM503.505 938.667v-32c-117.396 0-212.919-95.846-212.919-214.519h-64c0 153.626 123.784 278.519 276.919 278.519v-32zM691.904 521.481v32c34.155 0 62.195 27.925 62.195 62.814h64c0-69.841-56.303-126.814-126.195-126.814v32zM352.785 521.481v-32c-69.894 0-126.199 56.973-126.199 126.814h64c0-34.889 28.044-62.814 62.199-62.814v-32zM616.542 407.704v32.002c34.155 0 62.199 27.925 62.199 62.814h64c0-69.841-56.303-126.816-126.199-126.816v32zM597.103 409.725l6.613 31.308c4.113-0.866 8.397-1.327 12.826-1.327v-64.002c-8.9 0-17.617 0.931-26.048 2.711l6.609 31.31zM503.505 369.778v32c18.432 0 35.004 8.061 46.468 21.060l47.996-42.334c-23.044-26.13-56.823-42.726-94.464-42.726v32zM710.741 502.519h-32c0 4.356-0.435 8.58-1.254 12.638l62.737 12.651c1.655-8.201 2.517-16.661 2.517-25.289h-32zM597.103 409.725l-6.609-31.31c3.682-0.777 6.31 0.764 7.475 2.089l-47.996 42.334c12.975 14.708 33.25 22.525 53.743 18.194l-6.613-31.308zM258.586 692.147h32v-37.926h-64v37.926h32zM258.586 654.221h32v-37.926h-64v37.926h32zM786.099 616.294h-32v37.926h64v-37.926h-32zM786.099 654.221h-32v37.926h64v-37.926h-32zM522.347 251.259l29.030-13.46-4.868-10.497-58.061 26.92 4.864 10.497 29.035-13.46zM521.591 169.516l27.217 16.826 1.51-2.438-54.438-33.652-1.51 2.438 27.221 16.826zM527.211 95.83l29.030-13.46-4.864-10.497-58.065 26.92 4.868 10.497 29.030-13.46zM523.098 167.077l27.221 16.826c18.833-30.469 20.988-69.043 5.922-101.533l-58.061 26.92c6.101 13.159 5.116 28.964-2.3 40.961l27.217 16.826zM517.478 240.762l29.030-13.46c-6.101-13.159-5.116-28.964 2.3-40.961l-54.438-33.652c-18.833 30.469-20.988 69.043-5.922 101.533l29.030-13.46zM390.466 274.963l29.032-13.46-4.867-10.496-58.063 26.92 4.867 10.497 29.031-13.46zM389.712 193.219l27.219 16.826 1.507-2.438-54.438-33.652-1.507 2.438 27.219 16.826zM391.22 190.781l27.219 16.826c18.835-30.469 20.99-69.044 5.926-101.534l-58.063 26.92c6.101 13.159 5.116 28.964-2.301 40.961l27.219 16.826zM385.599 264.466l29.032-13.46c-6.101-13.159-5.116-28.964 2.301-40.961l-54.438-33.653c-18.835 30.469-20.989 69.044-5.926 101.534l29.031-13.46zM654.225 274.963l29.030-13.46-4.868-10.497-58.061 26.92 4.864 10.496 29.035-13.46zM653.47 193.219l27.217 16.826 1.51-2.438-54.438-33.652-1.506 2.438 27.217 16.826zM659.089 119.534l29.030-13.46-4.864-10.496-58.065 26.92 4.868 10.497 29.030-13.46zM654.976 190.781l27.221 16.826c18.833-30.469 20.988-69.044 5.922-101.534l-58.061 26.92c6.101 13.159 5.116 28.964-2.3 40.961l27.217 16.826zM649.357 264.466l29.030-13.46c-6.101-13.159-5.116-28.964 2.3-40.961l-54.434-33.653c-18.837 30.469-20.992 69.044-5.926 101.534l29.030-13.46zM170.667 132.741l-23.465 21.758 256.367 276.473 46.927-43.515-256.365-276.474-23.465 21.758zM427.034 409.214l26.018 18.63c11.383-15.9 29.773-26.066 50.453-26.066v-64c-42.3 0-79.666 20.933-102.49 52.806l26.018 18.63zM427.034 409.214l-23.465 21.757 104.101 112.269 46.929-43.516-104.102-112.268-23.462 21.758zM531.136 521.481v32h160.768v-64h-160.768v32zM531.136 521.481l-23.467 21.76 123.089 132.74 46.929-43.516-123.089-132.74-23.462 21.756zM654.221 654.221v32h131.878v-64h-131.878v32zM654.221 654.221l-23.462 21.76 116.087 125.193 46.929-43.516-116.087-125.193-23.467 21.756zM770.313 779.418l-23.467 21.756 125.687 135.548 46.933-43.516-125.692-135.548-23.462 21.76zM786.099 692.147h-32c0 26.825-4.868 52.425-13.739 76.002l59.904 22.532c11.537-30.677 17.835-63.902 17.835-98.534h-32zM352.785 521.481v32h24.073v-64h-24.073v32zM258.586 654.221v32h237.828v-64h-237.828v32zM703.236 876.992l-21.257-23.919c-37.572 33.395-86.818 53.594-140.796 53.594v64c70.298 0 134.519-26.385 183.313-69.76l-21.261-23.915zM333.945 521.481h32c0-5.513 0.984-10.573 2.698-15.147l-59.938-22.438c-4.365 11.657-6.76 24.311-6.76 37.585h32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "breakfast-slash"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1489,
        "id": 531,
        "name": "breakfast-slash",
        "prevSize": 32,
        "code": 59765
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 118
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BreakfastSlashIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BreakfastSlashIcon = ({ size = 16, ...props }: BreakfastSlashIconProps) => (
  <IcoMoon iconSet={iconSet} icon="breakfast-slash" size={size} {...props} />
);

BreakfastSlashIcon.displayName = 'BreakfastSlashIcon';

export default BreakfastSlashIcon;
