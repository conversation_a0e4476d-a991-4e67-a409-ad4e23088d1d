// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M901.116 554.667c0-71.689 0-169.356-13.393-196.736-11.78-24.085-30.579-43.666-53.7-55.938-26.287-13.951-60.693-13.951-129.515-13.951h-81.395c-25.647 0-38.473 0-50.116-3.683-10.304-3.261-19.891-8.606-28.211-15.731-9.399-8.048-16.512-19.165-30.737-41.398l-5.129-8.012c-6.805-10.639-12.527-19.581-17.621-27.218M491.298 192c-10.871-16.303-18.884-26.658-28.484-34.879-12.48-10.687-26.859-18.705-42.319-23.596-17.462-5.524-36.699-5.524-75.175-5.524h-66.796c-68.819 0-103.229 0-129.514 13.951-23.121 12.272-41.92 31.853-53.701 55.938-13.393 27.38-13.393 63.224-13.393 134.911v358.4c0 71.689 0 107.529 13.393 134.912 11.781 24.085 30.579 43.665 53.701 55.936 26.285 13.952 60.695 13.952 129.514 13.952h233.471M491.298 192h286.938c67.866 0 122.88 57.308 122.88 128v128M741.828 896c-70.379 0-127.433-57.306-127.433-128s57.054-128 127.433-128c50.91 0 94.848 29.99 115.26 73.34 47.458 3.119 84.988 42.773 84.988 91.23 0 50.496-40.751 91.43-91.021 91.43h-109.227z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "folder-cloud"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1663,
        "id": 357,
        "name": "folder-cloud",
        "prevSize": 32,
        "code": 59939
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 292
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FolderCloudIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FolderCloudIcon = ({ size = 16, ...props }: FolderCloudIconProps) => (
  <IcoMoon iconSet={iconSet} icon="folder-cloud" size={size} {...props} />
);

FolderCloudIcon.displayName = 'FolderCloudIcon';

export default FolderCloudIcon;
