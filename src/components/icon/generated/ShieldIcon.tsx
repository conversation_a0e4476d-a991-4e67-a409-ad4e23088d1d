// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M853.333 532.215v-285.444c0-20.235 0-30.352-3.273-38.526-2.931-7.308-7.471-13.373-13.487-18.010-6.729-5.186-16.077-7.384-34.769-11.782-35.793-8.418-83.298-20.236-119.138-31.495-47.731-14.996-112.781-40.945-147.153-55.011-8.892-3.639-13.338-5.459-17.69-6.145-3.977-0.626-7.671-0.626-11.648 0-4.352 0.686-8.798 2.505-17.69 6.145-34.372 14.066-99.421 40.015-147.153 55.011-35.84 11.26-83.347 23.077-119.136 31.495-18.696 4.398-28.044 6.596-34.772 11.782-6.016 4.637-10.557 10.701-13.484 18.010-3.274 8.174-3.274 18.292-3.274 38.526v285.444c0 71.991 0 107.989 9.676 140.271 8.562 28.565 22.572 54.895 41.135 77.312 20.977 25.335 49.839 43.584 107.561 80.085l150.385 95.1c11.857 7.497 17.783 11.243 24.098 12.71 5.589 1.297 11.366 1.297 16.956 0 6.315-1.468 12.241-5.214 24.098-12.71l150.387-95.1c57.719-36.501 86.583-54.75 107.558-80.085 18.564-22.417 32.572-48.747 41.135-77.312 9.677-32.282 9.677-68.279 9.677-140.271z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "shield"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1884,
        "id": 136,
        "name": "shield",
        "prevSize": 32,
        "code": 60160
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 513
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ShieldIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ShieldIcon = ({ size = 16, ...props }: ShieldIconProps) => (
  <IcoMoon iconSet={iconSet} icon="shield" size={size} {...props} />
);

ShieldIcon.displayName = 'ShieldIcon';

export default ShieldIcon;
