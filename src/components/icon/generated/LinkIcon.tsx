// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M393.481 320h-118.519c-104.729 0-189.629 85.961-189.629 192s84.9 192 189.629 192h118.519M630.519 320h118.519c104.73 0 189.628 85.961 189.628 192s-84.898 192-189.628 192h-118.519M322.371 512h379.257"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "link"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1736,
        "id": 284,
        "name": "link",
        "prevSize": 32,
        "code": 60012
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 365
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface LinkIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const LinkIcon = ({ size = 16, ...props }: LinkIconProps) => (
  <IcoMoon iconSet={iconSet} icon="link" size={size} {...props} />
);

LinkIcon.displayName = 'LinkIcon';

export default LinkIcon;
