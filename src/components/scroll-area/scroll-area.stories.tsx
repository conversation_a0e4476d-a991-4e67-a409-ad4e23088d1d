import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import { fn } from 'storybook/test';

import { Box } from '@components/box';
import { Flex } from '@components/flex';
import { Heading } from '@components/heading';
import { ScrollArea, type ScrollAreaProps } from '@components/scroll-area';
import { Text } from '@components/text';

import { scrollAreaPropDefs } from './scroll-area.props';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: 'DesignSystem/ScrollArea',
  component: ScrollArea,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  // tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    scrollbars: { control: 'inline-radio', options: scrollAreaPropDefs.scrollbars.values },
    size: { control: 'inline-radio', options: scrollAreaPropDefs.size.values },
  },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: { onClick: fn() },
} satisfies Meta<typeof ScrollArea>;

export default meta;
type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const _scrollArea: Story = {
  args: {
    scrollbars: scrollAreaPropDefs.scrollbars.default,
    size: scrollAreaPropDefs.size.default,
    type: 'always',
    style: { height: '500px', width: '500px' },
  },

  render: (args) => (
    <ScrollArea {...args}>
      <Box p="2" pr="8" minWidth={'600px'}>
        <Heading size="9" mb="2" trim="start">
          Principles of the typographic craft
        </Heading>
        <Flex direction="column" gap="4">
          <Text as="p" size="8">
            Three fundamental aspects of typography are legibility, readability, and aesthetics. Although in a
            non-technical sense “legible” and “readable” are often used synonymously, typographically they are separate
            but related concepts.
          </Text>

          <Text as="p" size="8">
            Legibility describes how easily individual characters can be distinguished from one another. It is described
            by Walter Tracy as “the quality of being decipherable and recognisable”. For instance, if a “b” and an “h”,
            or a “3” and an “8”, are difficult to distinguish at small sizes, this is a problem of legibility.
          </Text>

          <Text as="p" size="8">
            Typographers are concerned with legibility insofar as it is their job to select the correct font to use.
            Brush Script is an example of a font containing many characters that might be difficult to distinguish. The
            selection of cases influences the legibility of typography because using only uppercase letters (all-caps)
            reduces legibility.
          </Text>
          <Text as="p" size="8">
            Three fundamental aspects of typography are legibility, readability, and aesthetics. Although in a
            non-technical sense “legible” and “readable” are often used synonymously, typographically they are separate
            but related concepts.
          </Text>

          <Text as="p" size="8">
            Legibility describes how easily individual characters can be distinguished from one another. It is described
            by Walter Tracy as “the quality of being decipherable and recognisable”. For instance, if a “b” and an “h”,
            or a “3” and an “8”, are difficult to distinguish at small sizes, this is a problem of legibility.
          </Text>

          <Text as="p" size="8">
            Typographers are concerned with legibility insofar as it is their job to select the correct font to use.
            Brush Script is an example of a font containing many characters that might be difficult to distinguish. The
            selection of cases influences the legibility of typography because using only uppercase letters (all-caps)
            reduces legibility.
          </Text>
          <Text as="p" size="8">
            Three fundamental aspects of typography are legibility, readability, and aesthetics. Although in a
            non-technical sense “legible” and “readable” are often used synonymously, typographically they are separate
            but related concepts.
          </Text>

          <Text as="p" size="8">
            Legibility describes how easily individual characters can be distinguished from one another. It is described
            by Walter Tracy as “the quality of being decipherable and recognisable”. For instance, if a “b” and an “h”,
            or a “3” and an “8”, are difficult to distinguish at small sizes, this is a problem of legibility.
          </Text>

          <Text as="p" size="8">
            Typographers are concerned with legibility insofar as it is their job to select the correct font to use.
            Brush Script is an example of a font containing many characters that might be difficult to distinguish. The
            selection of cases influences the legibility of typography because using only uppercase letters (all-caps)
            reduces legibility.
          </Text>
          <Text as="p" size="8">
            Three fundamental aspects of typography are legibility, readability, and aesthetics. Although in a
            non-technical sense “legible” and “readable” are often used synonymously, typographically they are separate
            but related concepts.
          </Text>

          <Text as="p" size="8">
            Legibility describes how easily individual characters can be distinguished from one another. It is described
            by Walter Tracy as “the quality of being decipherable and recognisable”. For instance, if a “b” and an “h”,
            or a “3” and an “8”, are difficult to distinguish at small sizes, this is a problem of legibility.
          </Text>

          <Text as="p" size="8">
            Typographers are concerned with legibility insofar as it is their job to select the correct font to use.
            Brush Script is an example of a font containing many characters that might be difficult to distinguish. The
            selection of cases influences the legibility of typography because using only uppercase letters (all-caps)
            reduces legibility.
          </Text>
          <Text as="p" size="8">
            Three fundamental aspects of typography are legibility, readability, and aesthetics. Although in a
            non-technical sense “legible” and “readable” are often used synonymously, typographically they are separate
            but related concepts.
          </Text>

          <Text as="p" size="8">
            Legibility describes how easily individual characters can be distinguished from one another. It is described
            by Walter Tracy as “the quality of being decipherable and recognisable”. For instance, if a “b” and an “h”,
            or a “3” and an “8”, are difficult to distinguish at small sizes, this is a problem of legibility.
          </Text>

          <Text as="p" size="8">
            Typographers are concerned with legibility insofar as it is their job to select the correct font to use.
            Brush Script is an example of a font containing many characters that might be difficult to distinguish. The
            selection of cases influences the legibility of typography because using only uppercase letters (all-caps)
            reduces legibility.
          </Text>

          <Text as="p" size="8">
            Legibility describes how easily individual characters can be distinguished from one another. It is described
            by Walter Tracy as “the quality of being decipherable and recognisable”. For instance, if a “b” and an “h”,
            or a “3” and an “8”, are difficult to distinguish at small sizes, this is a problem of legibility.
          </Text>

          <Text as="p" size="8">
            Typographers are concerned with legibility insofar as it is their job to select the correct font to use.
            Brush Script is an example of a font containing many characters that might be difficult to distinguish. The
            selection of cases influences the legibility of typography because using only uppercase letters (all-caps)
            reduces legibility.
          </Text>
          <Text as="p" size="8">
            Three fundamental aspects of typography are legibility, readability, and aesthetics. Although in a
            non-technical sense “legible” and “readable” are often used synonymously, typographically they are separate
            but related concepts.
          </Text>

          <Text as="p" size="8">
            Legibility describes how easily individual characters can be distinguished from one another. It is described
            by Walter Tracy as “the quality of being decipherable and recognisable”. For instance, if a “b” and an “h”,
            or a “3” and an “8”, are difficult to distinguish at small sizes, this is a problem of legibility.
          </Text>

          <Text as="p" size="8">
            Typographers are concerned with legibility insofar as it is their job to select the correct font to use.
            Brush Script is an example of a font containing many characters that might be difficult to distinguish. The
            selection of cases influences the legibility of typography because using only uppercase letters (all-caps)
            reduces legibility.
          </Text>
          <Text as="p" size="8">
            Three fundamental aspects of typography are legibility, readability, and aesthetics. Although in a
            non-technical sense “legible” and “readable” are often used synonymously, typographically they are separate
            but related concepts.
          </Text>

          <Text as="p" size="8">
            Legibility describes how easily individual characters can be distinguished from one another. It is described
            by Walter Tracy as “the quality of being decipherable and recognisable”. For instance, if a “b” and an “h”,
            or a “3” and an “8”, are difficult to distinguish at small sizes, this is a problem of legibility.
          </Text>

          <Text as="p" size="8">
            Typographers are concerned with legibility insofar as it is their job to select the correct font to use.
            Brush Script is an example of a font containing many characters that might be difficult to distinguish. The
            selection of cases influences the legibility of typography because using only uppercase letters (all-caps)
            reduces legibility.
          </Text>
          <Text as="p" size="8">
            Legibility describes how easily individual characters can be distinguished from one another. It is described
            by Walter Tracy as “the quality of being decipherable and recognisable”. For instance, if a “b” and an “h”,
            or a “3” and an “8”, are difficult to distinguish at small sizes, this is a problem of legibility.
          </Text>

          <Text as="p" size="8">
            Typographers are concerned with legibility insofar as it is their job to select the correct font to use.
            Brush Script is an example of a font containing many characters that might be difficult to distinguish. The
            selection of cases influences the legibility of typography because using only uppercase letters (all-caps)
            reduces legibility.
          </Text>
          <Text as="p" size="8">
            Three fundamental aspects of typography are legibility, readability, and aesthetics. Although in a
            non-technical sense “legible” and “readable” are often used synonymously, typographically they are separate
            but related concepts.
          </Text>

          <Text as="p" size="8">
            Legibility describes how easily individual characters can be distinguished from one another. It is described
            by Walter Tracy as “the quality of being decipherable and recognisable”. For instance, if a “b” and an “h”,
            or a “3” and an “8”, are difficult to distinguish at small sizes, this is a problem of legibility.
          </Text>

          <Text as="p" size="8">
            Typographers are concerned with legibility insofar as it is their job to select the correct font to use.
            Brush Script is an example of a font containing many characters that might be difficult to distinguish. The
            selection of cases influences the legibility of typography because using only uppercase letters (all-caps)
            reduces legibility.
          </Text>
          <Text as="p" size="8">
            Three fundamental aspects of typography are legibility, readability, and aesthetics. Although in a
            non-technical sense “legible” and “readable” are often used synonymously, typographically they are separate
            but related concepts.
          </Text>

          <Text as="p" size="8">
            Legibility describes how easily individual characters can be distinguished from one another. It is described
            by Walter Tracy as “the quality of being decipherable and recognisable”. For instance, if a “b” and an “h”,
            or a “3” and an “8”, are difficult to distinguish at small sizes, this is a problem of legibility.
          </Text>

          <Text as="p" size="8">
            Typographers are concerned with legibility insofar as it is their job to select the correct font to use.
            Brush Script is an example of a font containing many characters that might be difficult to distinguish. The
            selection of cases influences the legibility of typography because using only uppercase letters (all-caps)
            reduces legibility.
          </Text>
        </Flex>
      </Box>
    </ScrollArea>
  ),
};
