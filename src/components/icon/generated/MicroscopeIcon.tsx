// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M376.586 524.279c16.999 13.086 35.354 24.503 58.038 36.527 11.294 5.99 31.317-4.126 47.33-23.859 33.404-41.182 61.082-76.382 87.94-112.443M376.586 524.279c-17.085-13.15-32.8-27.985-50.165-46.818-8.737-9.472-4.514-31.898 9.885-52.957 57.227-83.686 99.393-141.809 164.323-221.059 15.846-19.34 35.482-29.066 46.622-23.198 45.798 24.119 73.937 45.775 108.847 83.824 8.602 9.376 4.595 31.399-9.54 52.223-27.477 40.474-51.716 74.715-76.663 108.211M376.586 524.279l-49.085 65.877M614.421 85.333l113.353 87.365M85.333 938.667h510.349M938.667 938.667h-342.985M180.148 818.125h189.63M620.019 360.070c0 0 226.825 77.472 271.241 203.488 52.087 147.797-142.221 375.108-142.221 375.108M569.894 424.504c0 0 138.982 59.408 179.145 139.054 71.181 141.167-153.357 375.108-153.357 375.108"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "microscope"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1773,
        "id": 247,
        "name": "microscope",
        "prevSize": 32,
        "code": 60049
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 402
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MicroscopeIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MicroscopeIcon = ({ size = 16, ...props }: MicroscopeIconProps) => (
  <IcoMoon iconSet={iconSet} icon="microscope" size={size} {...props} />
);

MicroscopeIcon.displayName = 'MicroscopeIcon';

export default MicroscopeIcon;
