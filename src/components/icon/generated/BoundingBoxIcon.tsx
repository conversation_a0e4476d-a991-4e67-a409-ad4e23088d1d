// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M274.963 180.148c0 52.365-42.45 94.815-94.815 94.815M274.963 180.148c0-52.365-42.45-94.815-94.815-94.815s-94.815 42.45-94.815 94.815c0 52.365 42.45 94.815 94.815 94.815M274.963 180.148h474.076M180.148 274.963v474.076M274.963 843.853c0 52.365-42.45 94.814-94.815 94.814s-94.815-42.449-94.815-94.814c0-52.365 42.45-94.814 94.815-94.814M274.963 843.853c0-52.365-42.45-94.814-94.815-94.814M274.963 843.853h474.076M843.853 274.963c52.365 0 94.814-42.45 94.814-94.815s-42.449-94.815-94.814-94.815c-52.365 0-94.814 42.45-94.814 94.815M843.853 274.963c-52.365 0-94.814-42.45-94.814-94.815M843.853 274.963v474.076M749.039 843.853c0 52.365 42.449 94.814 94.814 94.814s94.814-42.449 94.814-94.814c0-52.365-42.449-94.814-94.814-94.814M749.039 843.853c0-52.365 42.449-94.814 94.814-94.814"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bounding-box"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1482,
        "id": 538,
        "name": "bounding-box",
        "prevSize": 32,
        "code": 59758
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 111
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BoundingBoxIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BoundingBoxIcon = ({ size = 16, ...props }: BoundingBoxIconProps) => (
  <IcoMoon iconSet={iconSet} icon="bounding-box" size={size} {...props} />
);

BoundingBoxIcon.displayName = 'BoundingBoxIcon';

export default BoundingBoxIcon;
