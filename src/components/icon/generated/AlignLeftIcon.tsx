// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M896 755.2v0zM896 610.133v0zM861.867 576v0zM290.133 576v0zM256 610.133v0zM256 755.2v0zM290.133 789.333v0zM861.867 789.333v0zM258.325 777.685v0zM267.648 787.008v0zM267.648 578.325v0zM258.325 587.648v0zM893.675 587.648v0zM884.352 578.325v0zM893.675 777.685v0zM884.352 787.008v0zM725.333 413.867v0zM725.333 268.8v0zM691.2 234.667v0zM290.133 234.667v0zM256 268.8v0zM256 413.867v0zM290.133 448v0zM691.2 448v0zM258.325 436.352v0zM267.648 445.675v0zM267.648 236.992v0zM258.325 246.315v0zM723.008 246.315v0zM713.685 236.992v0zM723.008 436.352v0zM713.685 445.675v0zM96 938.667c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM160 85.333c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM896 755.2h32v-145.067h-64v145.067h32zM861.867 576v-32h-571.733v64h571.733v-32zM256 610.133h-32v145.067h64v-145.067h-32zM290.133 789.333v32h571.733v-64h-571.733v32zM256 755.2h-32c0 5.444-0.025 11.456 0.397 16.619 0.452 5.534 1.572 12.851 5.416 20.395l57.024-29.056c1.519 2.982 1.476 5.026 1.347 3.447-0.063-0.768-0.12-1.984-0.152-3.994s-0.032-4.352-0.032-7.411h-32zM290.133 789.333v-32c-3.060 0-5.401 0-7.409-0.034-2.010-0.030-3.227-0.085-3.997-0.149-1.579-0.128 0.467-0.175 3.448 1.344l-29.056 57.028c7.545 3.844 14.859 4.962 20.396 5.414 5.163 0.422 11.171 0.397 16.617 0.397v-32zM258.325 777.685l-28.512 14.528c5.113 10.035 13.272 18.193 23.308 23.309l29.056-57.028c2.007 1.024 3.639 2.658 4.661 4.663l-28.512 14.528zM290.133 576v-32c-5.446 0-11.455-0.026-16.617 0.397-5.536 0.452-12.851 1.57-20.396 5.414l29.056 57.028c-2.982 1.519-5.027 1.472-3.448 1.344 0.77-0.064 1.987-0.119 3.997-0.149 2.009-0.034 4.349-0.034 7.409-0.034v-32zM256 610.133h32c0-3.059 0.001-5.402 0.032-7.411 0.032-2.005 0.089-3.226 0.152-3.994 0.129-1.579 0.172 0.465-1.347 3.447l-57.024-29.056c-3.844 7.543-4.963 14.861-5.416 20.395-0.422 5.163-0.397 11.174-0.397 16.619h32zM267.648 578.325l-14.528-28.514c-10.035 5.116-18.194 13.274-23.308 23.309l57.024 29.056c-1.023 2.005-2.654 3.639-4.661 4.663l-14.528-28.514zM896 610.133h32c0-5.444 0.026-11.456-0.397-16.619-0.452-5.534-1.57-12.851-5.414-20.395l-57.028 29.056c-1.519-2.982-1.472-5.026-1.344-3.447 0.064 0.768 0.119 1.988 0.149 3.994 0.034 2.010 0.034 4.352 0.034 7.411h32zM861.867 576v32c3.059 0 5.402 0 7.411 0.034 2.010 0.030 3.226 0.085 3.994 0.149 1.579 0.128-0.465 0.175-3.447-1.344l29.056-57.028c-7.543-3.844-14.861-4.962-20.395-5.414-5.163-0.422-11.174-0.397-16.619-0.397v32zM893.675 587.648l28.514-14.528c-5.116-10.035-13.274-18.193-23.309-23.309l-29.056 57.028c-2.005-1.024-3.639-2.658-4.663-4.663l28.514-14.528zM896 755.2h-32c0 3.059 0 5.402-0.034 7.411-0.030 2.010-0.085 3.226-0.149 3.994-0.128 1.579-0.175-0.465 1.344-3.447l57.028 29.056c3.844-7.543 4.962-14.861 5.414-20.395 0.422-5.163 0.397-11.174 0.397-16.619h-32zM861.867 789.333v32c5.444 0 11.456 0.026 16.619-0.397 5.534-0.452 12.851-1.57 20.395-5.414l-29.056-57.028c2.982-1.519 5.026-1.472 3.447-1.344-0.768 0.064-1.988 0.119-3.994 0.149-2.010 0.034-4.352 0.034-7.411 0.034v32zM893.675 777.685l-28.514-14.528c1.024-2.005 2.658-3.639 4.663-4.663l29.056 57.028c10.035-5.116 18.193-13.274 23.309-23.309l-28.514-14.528zM725.333 413.867h32v-145.067h-64v145.067h32zM691.2 234.667v-32h-401.067v64h401.067v-32zM256 268.8h-32v145.067h64v-145.067h-32zM290.133 448v32h401.067v-64h-401.067v32zM256 413.867h-32c0 5.446-0.025 11.455 0.397 16.619 0.452 5.534 1.572 12.851 5.416 20.395l57.024-29.056c1.519 2.982 1.476 5.026 1.347 3.449-0.063-0.77-0.12-1.987-0.152-3.997s-0.032-4.349-0.032-7.409h-32zM290.133 448v-32c-3.060 0-5.4-0.001-7.409-0.032s-3.227-0.089-3.997-0.152c-1.579-0.129 0.467-0.172 3.448 1.347l-29.056 57.026c7.545 3.844 14.859 4.962 20.396 5.414 5.163 0.422 11.171 0.397 16.617 0.397v-32zM258.325 436.352l-28.512 14.528c5.113 10.035 13.272 18.193 23.308 23.309l29.055-57.026c2.007 1.023 3.639 2.655 4.662 4.661l-28.512 14.528zM290.133 234.667v-32c-5.446 0-11.455-0.025-16.617 0.397-5.536 0.452-12.851 1.572-20.396 5.416l29.056 57.024c-2.981 1.519-5.027 1.476-3.448 1.347 0.77-0.063 1.987-0.12 3.997-0.152s4.349-0.032 7.409-0.032v-32zM256 268.8h32c0-3.060 0.001-5.401 0.032-7.409 0.032-2.010 0.089-3.227 0.152-3.997 0.129-1.578 0.172 0.468-1.347 3.449l-57.024-29.056c-3.844 7.545-4.963 14.859-5.416 20.396-0.422 5.163-0.397 11.171-0.397 16.617h32zM267.648 236.992l-14.528-28.512c-10.036 5.114-18.195 13.273-23.308 23.307l57.024 29.056c-1.022 2.007-2.654 3.639-4.662 4.662l-14.527-28.512zM725.333 268.8h32c0-5.446 0.026-11.455-0.397-16.617-0.452-5.536-1.57-12.851-5.414-20.396l-57.024 29.056c-1.523-2.981-1.476-5.027-1.348-3.449 0.064 0.77 0.119 1.987 0.149 3.997 0.034 2.008 0.034 4.349 0.034 7.409h32zM691.2 234.667v32c3.059 0 5.402 0.001 7.411 0.032s3.226 0.089 3.994 0.152c1.579 0.129-0.465 0.172-3.447-1.347l29.056-57.024c-7.543-3.844-14.861-4.963-20.395-5.416-5.163-0.422-11.174-0.397-16.619-0.397v32zM723.008 246.315l28.514-14.528c-5.116-10.035-13.274-18.194-23.309-23.308l-29.056 57.025c-2.005-1.023-3.639-2.655-4.659-4.662l28.51-14.528zM725.333 413.867h-32c0 3.060 0 5.401-0.034 7.409-0.030 2.010-0.085 3.227-0.149 3.997-0.128 1.577-0.175-0.467 1.348-3.449l57.024 29.056c3.844-7.543 4.962-14.861 5.414-20.395 0.422-5.164 0.397-11.173 0.397-16.619h-32zM691.2 448v32c5.444 0 11.456 0.026 16.619-0.397 5.534-0.452 12.851-1.57 20.395-5.414l-29.056-57.026c2.982-1.519 5.026-1.476 3.447-1.347-0.768 0.063-1.984 0.12-3.994 0.152s-4.352 0.032-7.411 0.032v32zM723.008 436.352l-28.51-14.528c1.020-2.007 2.654-3.639 4.659-4.661l29.056 57.026c10.035-5.116 18.193-13.274 23.309-23.309l-28.514-14.528zM128 938.667h32v-853.333h-64v853.333h32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "align-left"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1392,
        "id": 628,
        "name": "align-left",
        "prevSize": 32,
        "code": 59668
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 21
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface AlignLeftIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const AlignLeftIcon = ({ size = 16, ...props }: AlignLeftIconProps) => (
  <IcoMoon iconSet={iconSet} icon="align-left" size={size} {...props} />
);

AlignLeftIcon.displayName = 'AlignLeftIcon';

export default AlignLeftIcon;
