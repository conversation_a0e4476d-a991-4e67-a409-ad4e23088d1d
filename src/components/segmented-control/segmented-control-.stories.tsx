import type { Meta, StoryObj } from '@storybook/react-vite';
import { useArgs } from 'storybook/internal/preview-api';

import * as BaseSegmentedControl from '@components/segmented-control';
import { segmentedControlRootPropDefs } from '@components/segmented-control/segmented-control.props';

interface SegmentedControlProps {
  variant?: (typeof segmentedControlRootPropDefs.variant.values)[number];
  size?: (typeof segmentedControlRootPropDefs.size.values)[number];
  radius?: (typeof segmentedControlRootPropDefs.radius.values)[number];
  disabled?: boolean;
  defaultValue?: string;
  value?: string;
  onValueChange?: (value: string) => void;
}

const SegmentedControl = ({
  variant,
  size,
  radius,
  disabled,
  defaultValue,
  value,
  onValueChange,
}: SegmentedControlProps) => {
  return (
    <BaseSegmentedControl.Root
      variant={variant}
      size={size}
      radius={radius}
      disabled={disabled}
      defaultValue={defaultValue}
      value={value}
      onValueChange={onValueChange}
    >
      <BaseSegmentedControl.Item value="inbox">Inbox</BaseSegmentedControl.Item>
      <BaseSegmentedControl.Item value="drafts">Drafts</BaseSegmentedControl.Item>
      <BaseSegmentedControl.Item value="sent">Sent</BaseSegmentedControl.Item>
    </BaseSegmentedControl.Root>
  );
};

const meta = {
  title: 'DesignSystem/SegmentedControl',
  component: SegmentedControl,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    variant: { control: 'inline-radio', type: 'string', options: segmentedControlRootPropDefs.variant.values },
    size: { control: 'inline-radio', type: 'string', options: segmentedControlRootPropDefs.size.values },
    disabled: { control: 'boolean', type: 'boolean' },
    defaultValue: { control: 'text', type: 'string' },
    value: { control: 'text', type: 'string' },
    onValueChange: { control: 'object', type: 'function' },
  },
} satisfies Meta<typeof SegmentedControl>;

export default meta;
type Story = StoryObj<typeof meta>;

export const _segmentedControl: Story = {
  args: {
    variant: segmentedControlRootPropDefs.variant.default,
    size: segmentedControlRootPropDefs.size.default,
    disabled: segmentedControlRootPropDefs.disabled.default,
    defaultValue: undefined,
    value: 'inbox',
  },
  render: (args) => {
    const [, updateArgs] = useArgs();

    return (
      <SegmentedControl
        {...args}
        onValueChange={(value) => {
          updateArgs({ value });
        }}
      />
    );
  },
};
