// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M213.333 981.333l17.836-32.107M231.17 949.227h561.662M231.17 949.227l65.126-117.227M810.667 981.333l-17.835-32.107M792.832 949.227l-65.126-117.227M296.296 832l41.482-74.667M296.296 832h431.409M727.706 832l-41.485-74.667M362.667 583.113h0.498M661.333 583.113h0.499M298.482 721.545c157.249 14.528 268.276 14.562 427.555-0.073 39.561-3.631 70.912-37.79 74.185-80.674 13.184-172.617 14.473-293.481 0.495-464.662-3.465-42.412-34.603-75.984-73.741-79.656-158.814-14.899-269.969-14.856-430.066 0.092-38.947 3.636-70.047 36.874-73.65 79.063-14.764 172.876-11.899 294.044 0.975 464.911 3.24 42.995 34.592 77.333 74.248 80.998zM387.448 454.473c91.729 5.589 156.492 5.602 249.408-0.026 23.074-1.399 41.365-14.537 43.277-31.029 7.689-66.39 8.439-112.878 0.286-178.716-2.018-16.312-20.186-29.225-43.017-30.637-92.638-5.731-157.483-5.714-250.871 0.035-22.719 1.399-40.861 14.182-42.962 30.409-8.612 66.491-6.941 113.094 0.569 178.812 1.89 16.538 20.178 29.743 43.311 31.151z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "train"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1959,
        "id": 61,
        "name": "train",
        "prevSize": 32,
        "code": 60235
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 588
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface TrainIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const TrainIcon = ({ size = 16, ...props }: TrainIconProps) => (
  <IcoMoon iconSet={iconSet} icon="train" size={size} {...props} />
);

TrainIcon.displayName = 'TrainIcon';

export default TrainIcon;
