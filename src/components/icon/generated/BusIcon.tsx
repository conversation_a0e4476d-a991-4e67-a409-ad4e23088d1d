// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M274.963 796.446v71.108c0 39.275 31.838 71.113 71.111 71.113s71.111-31.838 71.111-71.113v-71.108M606.814 796.446v71.108c0 39.275 31.838 71.113 71.113 71.113s71.113-31.838 71.113-71.113v-71.108M843.853 214.285v511.048c0 39.275-31.838 71.113-71.113 71.113h-521.481c-39.274 0-71.111-31.838-71.111-71.113v-511.194M843.853 214.285c0-59.727-44.476-110.422-103.902-116.407-165.269-16.645-291.051-16.782-456.061-0.081-59.361 6.008-103.742 56.678-103.742 116.342M843.853 214.285l67.648 26.381c16.38 6.388 27.166 22.169 27.166 39.751v65.657M180.148 214.139l-67.703 26.505c-16.352 6.402-27.112 22.17-27.112 39.73v65.699M322.371 642.372h0.474M701.154 642.372h0.474M440.887 642.372h142.225M369.778 488.294h284.443c39.275 0 71.113-31.834 71.113-71.109v-137.807c0-37.118-28.578-68.1-65.621-70.435-107.2-6.755-188.459-6.811-295.482-0.035-37.020 2.344-65.563 33.318-65.563 70.412v137.865c0 39.276 31.837 71.109 71.111 71.109z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "round",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bus"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "round",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1500,
        "id": 520,
        "name": "bus",
        "prevSize": 32,
        "code": 59776
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 129
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BusIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BusIcon = ({ size = 16, ...props }: BusIconProps) => (
  <IcoMoon iconSet={iconSet} icon="bus" size={size} {...props} />
);

BusIcon.displayName = 'BusIcon';

export default BusIcon;
