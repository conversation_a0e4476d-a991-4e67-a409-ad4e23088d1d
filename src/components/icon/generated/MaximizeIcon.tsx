// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M425.052 309.333c17.675 0 32.002-14.327 32.002-32s-14.327-32-32.002-32v64zM245.333 431.68c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM425.082 778.667c17.675 0 32.002-14.327 32.002-32s-14.327-32-32.002-32v64zM309.364 592.35c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM598.946 245.333c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM714.667 431.68c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM598.916 714.667c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM778.637 592.35c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM588.89 586.189l22.822-22.438-0.294-0.29-22.528 22.729zM435.11 586.185l-22.53-22.729-0.287 0.294 22.817 22.434zM279.819 744.124v0zM744.188 744.119v0zM279.848 279.817v0zM155.223 924.715v0zM99.284 868.779v0zM924.715 868.779v0zM868.779 924.715v0zM868.779 99.284v0zM924.715 155.223v0zM155.223 99.284v0zM99.284 155.223v0zM425.052 277.333v-32h-139.185v64h139.185v-32zM277.333 285.867h-32v145.813h64v-145.813h-32zM425.082 746.667v-32h-139.185v64h139.185v-32zM277.364 738.133h32v-145.783h-64v145.783h32zM598.946 277.333v32h139.187v-64h-139.187v32zM746.667 285.867h-32v145.813h64v-145.813h-32zM598.916 746.667v32h139.187v-64h-139.187v32zM746.637 738.133h32v-145.783h-64v145.783h32zM512 509.965l-22.528 22.724 76.89 76.224 45.056-45.453-76.89-76.224-22.528 22.729zM512 509.965l-22.528-22.729-76.89 76.224 45.056 45.449 76.89-76.22-22.528-22.724zM285.897 746.667v-32c6.535 0 12.495 2.714 16.713 6.993l-45.583 44.924c7.314 7.424 17.577 12.083 28.87 12.083v-32zM279.819 744.124l22.792-22.464c4.143 4.203 6.753 10.065 6.753 16.474h-64c0 11.076 4.485 21.167 11.664 28.45l22.792-22.46zM279.819 744.124l22.818 22.434 155.292-157.939-45.635-44.868-155.292 157.935 22.818 22.438zM738.103 746.667v32c11.302 0 21.572-4.668 28.89-12.1l-45.615-44.894c4.22-4.288 10.185-7.006 16.725-7.006v32zM744.188 744.119l22.805 22.443c7.168-7.279 11.644-17.365 11.644-28.429h-64c0-6.4 2.607-12.258 6.741-16.461l22.81 22.447zM744.188 744.119l22.814-22.438-155.294-157.931-45.632 44.873 155.294 157.931 22.818-22.434zM738.133 277.333v32c-6.417 0-12.288-2.618-16.491-6.772l44.975-45.533c-7.287-7.198-17.391-11.696-28.484-11.696v32zM744.128 279.795l-22.485 22.767c-4.271-4.218-6.976-10.17-6.976-16.695h64c0-11.276-4.646-21.525-12.049-28.838l-22.49 22.766zM744.128 279.795l-22.528-22.723-232.132 230.168 45.065 45.444 232.128-230.166-22.532-22.723zM285.867 277.333v-32c-11.146 0-21.293 4.541-28.588 11.799l45.138 45.37c-4.209 4.187-10.102 6.831-16.551 6.831v-32zM279.848 279.817l-22.569-22.685c-7.343 7.305-11.946 17.513-11.946 28.735h64c0 6.494-2.68 12.422-6.916 16.636l-22.569-22.685zM279.848 279.817l-22.529 22.726 232.153 230.147 45.056-45.453-232.151-230.144-22.529 22.725zM290.133 85.333v32h443.733v-64h-443.733v32zM938.667 290.133h-32v443.733h64v-443.733h-32zM733.867 938.667v-32h-443.733v64h443.733v-32zM85.333 733.867h32v-443.733h-64v443.733h32zM290.133 938.667v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM85.333 733.867h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM155.223 924.715l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM938.667 733.867h-32c0 36.373-0.026 61.726-1.638 81.463-1.583 19.366-4.531 30.494-8.823 38.921l57.024 29.056c9.655-18.957 13.683-39.441 15.586-62.763 1.877-22.95 1.852-51.362 1.852-86.677h-32zM733.867 938.667v32c35.315 0 63.727 0.026 86.677-1.852 23.322-1.903 43.806-5.931 62.763-15.586l-29.056-57.024c-8.427 4.292-19.554 7.241-38.921 8.823-19.738 1.613-45.090 1.638-81.463 1.638v32zM924.715 868.779l-28.51-14.528c-9.207 18.061-23.893 32.747-41.954 41.954l29.056 57.024c30.106-15.343 54.579-39.817 69.922-69.922l-28.514-14.528zM733.867 85.333v32c36.373 0 61.726 0.025 81.463 1.638 19.366 1.582 30.494 4.532 38.921 8.826l29.056-57.024c-18.957-9.658-39.441-13.683-62.763-15.589-22.95-1.875-51.362-1.85-86.677-1.85v32zM938.667 290.133h32c0-35.315 0.026-63.727-1.852-86.676-1.903-23.323-5.931-43.809-15.586-62.762l-57.024 29.055c4.292 8.428 7.241 19.553 8.823 38.919 1.613 19.739 1.638 45.093 1.638 81.464h32zM868.779 99.284l-14.528 28.512c18.061 9.204 32.747 23.89 41.954 41.953l57.024-29.055c-15.343-30.106-39.817-54.583-69.922-69.923l-14.528 28.512zM290.133 85.333v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM85.333 290.133h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM155.223 99.284l-14.528-28.512c-30.106 15.34-54.583 39.817-69.923 69.923l57.024 29.055c9.204-18.063 23.89-32.75 41.953-41.953l-14.528-28.512z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "maximize"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1757,
        "id": 263,
        "name": "maximize",
        "prevSize": 32,
        "code": 60033
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 386
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MaximizeIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MaximizeIcon = ({ size = 16, ...props }: MaximizeIconProps) => (
  <IcoMoon iconSet={iconSet} icon="maximize" size={size} {...props} />
);

MaximizeIcon.displayName = 'MaximizeIcon';

export default MaximizeIcon;
