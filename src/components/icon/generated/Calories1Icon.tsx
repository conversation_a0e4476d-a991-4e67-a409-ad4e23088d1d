// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M318.211 235.743c0.407-10.305 0.839-20.745 1.296-31.331 2.402-55.633 23.136-99.464 49.1-104.259 106.73-19.711 180.838-19.769 286.711-0.122 26.091 4.842 46.852 49.113 49.161 105.041 0.427 10.329 0.832 20.519 1.216 30.583M318.211 235.743c-7.007 0.538-14.105 1.102-21.301 1.693-38.947 3.197-70.047 32.417-73.65 69.506-14.764 151.981-11.899 258.498 0.975 408.715 3.24 37.798 34.592 67.985 74.248 71.206 6.953 0.563 13.814 1.105 20.594 1.617M318.211 235.743c141.826-10.884 246.743-10.921 387.483-0.086M705.694 235.657c7.002 0.539 14.093 1.105 21.282 1.698 39.138 3.228 70.276 32.742 73.741 70.028 13.978 150.487 12.689 256.744-0.495 408.496-3.273 37.7-34.624 67.725-74.185 70.921-7.019 0.567-13.943 1.109-20.783 1.626M705.254 788.425c-0.354 9.677-0.721 19.469-1.105 29.389-2.185 56.555-23.087 101.594-49.459 106.385-106.185 19.298-180.203 19.251-285.035 0.094-26.438-4.83-47.339-50.112-49.498-106.807-0.373-9.788-0.733-19.452-1.080-29.005M705.254 788.425c-141.508 10.692-246.37 10.675-386.178 0.055M362.667 512.614h55.795c1.373 0 2.604-0.806 3.098-2.031l36.771-91.365c1.173-2.919 5.594-2.622 6.332 0.425l44.604 184.713c0.759 3.149 5.38 3.324 6.396 0.243l44.288-134.025c0.789-2.389 4.036-2.999 5.713-1.075l36.604 41.975c0.627 0.721 1.562 1.139 2.547 1.139h56.521"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "calories-1"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1512,
        "id": 508,
        "name": "calories-1",
        "prevSize": 32,
        "code": 59788
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 141
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface Calories1IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const Calories1Icon = ({ size = 16, ...props }: Calories1IconProps) => (
  <IcoMoon iconSet={iconSet} icon="calories-1" size={size} {...props} />
);

Calories1Icon.displayName = 'Calories1Icon';

export default Calories1Icon;
