// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 725.333v-213.333c0-235.642 191.025-426.667 426.667-426.667 235.639 0 426.667 191.025 426.667 426.667v213.333M192 938.667c-58.91 0-106.667-47.757-106.667-106.667v-118.519c0-58.91 47.756-106.667 106.667-106.667s106.667 47.757 106.667 106.667v118.519c0 58.91-47.756 106.667-106.667 106.667zM832 938.667c-58.91 0-106.667-47.757-106.667-106.667v-118.519c0-58.91 47.757-106.667 106.667-106.667s106.667 47.757 106.667 106.667v118.519c0 58.91-47.757 106.667-106.667 106.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "headphone"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1703,
        "id": 317,
        "name": "headphone",
        "prevSize": 32,
        "code": 59979
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 332
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface HeadphoneIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const HeadphoneIcon = ({ size = 16, ...props }: HeadphoneIconProps) => (
  <IcoMoon iconSet={iconSet} icon="headphone" size={size} {...props} />
);

HeadphoneIcon.displayName = 'HeadphoneIcon';

export default HeadphoneIcon;
