// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M321.24 938.667c-2.184 0-4.367-0.678-6.034-2.035l-187.206-189.965M321.24 938.667c2.184 0 4.368-0.678 6.034-2.035l187.205-189.965M321.24 938.667v-565.953c0-158.716 128.663-287.38 287.379-287.38s287.381 128.664 287.381 287.38v373.953"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "arrow-turn-down"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1422,
        "id": 598,
        "name": "arrow-turn-down",
        "prevSize": 32,
        "code": 59698
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 51
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ArrowTurnDownIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ArrowTurnDownIcon = ({ size = 16, ...props }: ArrowTurnDownIconProps) => (
  <IcoMoon iconSet={iconSet} icon="arrow-turn-down" size={size} {...props} />
);

ArrowTurnDownIcon.displayName = 'ArrowTurnDownIcon';

export default ArrowTurnDownIcon;
