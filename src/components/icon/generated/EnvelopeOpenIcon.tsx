// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 377.812l387.52-274.289c14.101-9.984 21.154-14.976 28.826-16.909 6.775-1.708 13.867-1.708 20.642 0 7.671 1.934 14.724 6.925 28.826 16.909l387.52 274.289M85.333 377.812l256 168.257M85.333 377.812v427.112M938.667 377.812l-256 168.257M938.667 377.812v427.112M341.333 546.069l133.461 87.718c13.466 8.849 20.203 13.274 27.456 14.997 6.413 1.523 13.086 1.523 19.499 0 7.253-1.724 13.99-6.148 27.456-14.997l133.461-87.718M341.333 546.069l-256 258.854M85.333 804.924c0 4.006 0 6.007 0.044 7.701 1.782 68.826 56.54 124.194 124.605 125.999 1.675 0.043 3.656 0.043 7.617 0.043h588.8c3.959 0 5.943 0 7.616-0.043 68.066-1.805 122.825-57.173 124.608-125.999 0.043-1.694 0.043-3.695 0.043-7.701M682.667 546.069l256 258.854"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "envelope-open"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1618,
        "id": 402,
        "name": "envelope-open",
        "prevSize": 32,
        "code": 59894
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 247
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface EnvelopeOpenIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const EnvelopeOpenIcon = ({ size = 16, ...props }: EnvelopeOpenIconProps) => (
  <IcoMoon iconSet={iconSet} icon="envelope-open" size={size} {...props} />
);

EnvelopeOpenIcon.displayName = 'EnvelopeOpenIcon';

export default EnvelopeOpenIcon;
