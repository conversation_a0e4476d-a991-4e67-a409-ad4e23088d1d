// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M384 378.916l121.967 130.413c1.664 1.779 3.849 2.671 6.033 2.671M640 378.916l-121.967 130.413c-1.664 1.779-3.849 2.671-6.033 2.671M512 512v-362.667M248.765 810.53c4.687-13.308 7.235-27.622 7.235-42.53 0-70.694-57.308-128-128-128-14.909 0-29.223 2.547-42.53 7.236M248.765 810.53c12.327 0.137 26.027 0.137 41.369 0.137h443.733c15.343 0 29.043 0 41.37-0.137M248.765 810.53c-45.272-0.503-72.021-2.85-93.542-13.815-24.085-12.271-43.666-31.851-55.938-55.936-10.965-21.521-13.312-48.273-13.814-93.542M85.47 647.236c-0.137-12.326-0.137-26.027-0.137-41.37v-187.733c0-15.342 0-29.042 0.137-41.369M85.47 376.765c13.306 4.687 27.62 7.235 42.53 7.235 70.692 0 128-57.308 128-128 0-14.909-2.549-29.223-7.235-42.53M85.47 376.765c0.502-45.272 2.849-72.021 13.814-93.542 12.272-24.085 31.853-43.666 55.938-55.938 21.521-10.965 48.27-13.312 93.542-13.814M248.765 213.47c12.326-0.137 26.027-0.137 41.368-0.137h72.533M775.236 213.47c-4.689 13.306-7.236 27.62-7.236 42.53 0 70.692 57.306 128 128 128 14.908 0 29.222-2.549 42.53-7.235M775.236 213.47c-12.326-0.137-26.027-0.137-41.37-0.137h-72.533M775.236 213.47c45.269 0.502 72.021 2.849 93.542 13.814 24.085 12.272 43.665 31.853 55.936 55.938 10.965 21.521 13.312 48.27 13.815 93.542M938.53 376.765c0.137 12.327 0.137 26.027 0.137 41.369v187.733c0 15.343 0 29.043-0.137 41.37M938.53 647.236c-13.308-4.689-27.622-7.236-42.53-7.236-70.694 0-128 57.306-128 128 0 14.908 2.547 29.222 7.236 42.53M938.53 647.236c-0.503 45.269-2.85 72.021-13.815 93.542-12.271 24.085-31.851 43.665-55.936 55.936-21.521 10.965-48.273 13.312-93.542 13.815M256.427 512h-0.427M768.427 512h-0.427M391.284 554.667c17.571 49.715 64.985 85.333 120.716 85.333s103.147-35.618 120.717-85.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "money-receive-alt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1781,
        "id": 239,
        "name": "money-receive-alt",
        "prevSize": 32,
        "code": 60057
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 410
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MoneyReceiveAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MoneyReceiveAltIcon = ({ size = 16, ...props }: MoneyReceiveAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="money-receive-alt" size={size} {...props} />
);

MoneyReceiveAltIcon.displayName = 'MoneyReceiveAltIcon';

export default MoneyReceiveAltIcon;
