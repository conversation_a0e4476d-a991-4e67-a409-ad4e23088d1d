// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M630.622 569.997c-17.289-5.636-35.511-9.276-54.455-10.637-65.506-4.706-126.558-4.698-192.19 0.026-114.874 8.265-203.129 100.275-219.582 212.873l-3.148 21.542c-9.494 64.964 36.281 125.564 102.363 132.527 50.833 5.355 121.071 9.626 172.696 11.648l67.712 0.691M624.013 772.74h239.987M648.009 251.259c0 91.638-75.213 165.926-167.991 165.926s-167.991-74.288-167.991-165.926c0-91.639 75.213-165.926 167.991-165.926s167.991 74.287 167.991 165.926z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "user-minus"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1980,
        "id": 40,
        "name": "user-minus",
        "prevSize": 32,
        "code": 60256
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 609
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface UserMinusIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const UserMinusIcon = ({ size = 16, ...props }: UserMinusIconProps) => (
  <IcoMoon iconSet={iconSet} icon="user-minus" size={size} {...props} />
);

UserMinusIcon.displayName = 'UserMinusIcon';

export default UserMinusIcon;
