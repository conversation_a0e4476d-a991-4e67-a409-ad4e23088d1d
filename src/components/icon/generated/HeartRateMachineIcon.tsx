// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M298.667 480.832c-17.673 0-32 14.323-32 32 0 17.673 14.327 32 32 32v-64zM382.799 510.084v0zM435.328 386.745v0zM444.373 387.319v0zM508.092 636.681v0zM517.231 637.009v0zM580.501 456.077v0zM588.663 454.626v0zM640.947 511.287v0zM725.333 544.832c17.673 0 32-14.327 32-32 0-17.677-14.327-32-32-32v64zM155.223 839.381v0zM99.284 783.445v0zM924.715 783.445v0zM868.779 839.381v0zM868.779 184.618v0zM924.715 240.556v0zM155.223 184.618v0zM99.284 240.556v0zM298.667 512.832v32h79.707v-64h-79.707v32zM382.799 510.084l29.441 12.54 52.529-123.34-58.881-25.077-52.529 123.343 29.441 12.535zM444.373 387.319l-31.005 7.923 63.722 249.362 62.007-15.846-63.723-249.361-31.002 7.922zM517.231 637.009l30.208 10.56 63.266-180.928-60.412-21.124-63.266 180.928 30.204 10.564zM588.663 454.626l-23.518 21.7 52.288 56.666 47.031-43.405-52.284-56.661-23.518 21.7zM644.591 512.832v32h80.742v-64h-80.742v32zM640.947 511.287l-23.514 21.705c7.347 7.966 17.412 11.84 27.157 11.84v-64c6.929 0 14.327 2.743 19.874 8.755l-23.518 21.7zM580.501 456.077l30.204 10.564c-3.942 11.281-13.444 16.687-20.996 18.031-7.569 1.344-17.34-0.516-24.563-8.346l47.036-43.401c-9.626-10.431-22.912-13.179-33.681-11.265-10.752 1.912-23.134 9.345-28.207 23.856l30.208 10.56zM508.092 636.681l-31.002 7.923c4.919 19.255 21.956 26.931 34.291 27.371 12.335 0.444 29.602-5.939 36.058-24.405l-60.412-21.124c5.001-14.306 18.24-18.726 26.641-18.428s21.594 5.73 25.429 20.74l-31.006 7.923zM435.328 386.745l29.44 12.539c-5.662 13.299-18.381 17.192-26.709 16.663-8.333-0.529-20.968-6.137-24.69-20.705l62.007-15.845c-4.774-18.683-21.094-26.549-33.263-27.321s-28.883 4.89-36.225 22.131l29.441 12.538zM378.374 512.832v32c13.278 0 27.569-7.424 33.866-22.208l-58.882-25.075c4.889-11.482 15.662-16.717 25.016-16.717v32zM290.133 170.667v32h443.733v-64h-443.733v32zM938.667 375.467h-32v273.067h64v-273.067h-32zM733.867 853.333v-32h-443.733v64h443.733v-32zM85.333 648.533h32v-273.067h-64v273.067h32zM290.133 853.333v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM85.333 648.533h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM155.223 839.381l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM938.667 648.533h-32c0 36.373-0.026 61.726-1.638 81.463-1.583 19.366-4.531 30.494-8.823 38.921l57.024 29.056c9.655-18.957 13.683-39.441 15.586-62.763 1.877-22.95 1.852-51.362 1.852-86.677h-32zM733.867 853.333v32c35.315 0 63.727 0.026 86.677-1.852 23.322-1.903 43.806-5.931 62.763-15.586l-29.056-57.024c-8.427 4.292-19.554 7.241-38.921 8.823-19.738 1.613-45.090 1.638-81.463 1.638v32zM924.715 783.445l-28.51-14.528c-9.207 18.061-23.893 32.747-41.954 41.954l29.056 57.024c30.106-15.343 54.579-39.817 69.922-69.922l-28.514-14.528zM733.867 170.667v32c36.373 0 61.726 0.025 81.463 1.638 19.366 1.582 30.494 4.532 38.921 8.826l29.056-57.024c-18.957-9.658-39.441-13.683-62.763-15.589-22.95-1.875-51.362-1.85-86.677-1.85v32zM938.667 375.467h32c0-35.315 0.026-63.727-1.852-86.676-1.903-23.323-5.931-43.809-15.586-62.762l-57.024 29.055c4.292 8.428 7.241 19.553 8.823 38.919 1.613 19.739 1.638 45.093 1.638 81.464h32zM868.779 184.618l-14.528 28.512c18.061 9.204 32.747 23.89 41.954 41.953l57.024-29.055c-15.343-30.106-39.817-54.583-69.922-69.923l-14.528 28.512zM290.133 170.667v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM85.333 375.467h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM155.223 184.618l-14.528-28.512c-30.106 15.34-54.583 39.817-69.923 69.923l57.024 29.055c9.204-18.063 23.89-32.75 41.953-41.953l-14.528-28.512z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "heart-rate-machine"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1705,
        "id": 315,
        "name": "heart-rate-machine",
        "prevSize": 32,
        "code": 59981
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 334
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface HeartRateMachineIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const HeartRateMachineIcon = ({ size = 16, ...props }: HeartRateMachineIconProps) => (
  <IcoMoon iconSet={iconSet} icon="heart-rate-machine" size={size} {...props} />
);

HeartRateMachineIcon.displayName = 'HeartRateMachineIcon';

export default HeartRateMachineIcon;
