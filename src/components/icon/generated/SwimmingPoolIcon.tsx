// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M128 804.988l96.806-23.902c62.257-15.373 127.599-13.504 188.85 5.402 64.193 19.81 132.814 20.894 197.616 3.115l11.221-3.076c68.992-18.927 141.943-18.662 210.795 0.764l62.711 17.698M224 938.667l72.461-19.085c46.668-12.288 95.959-10.799 141.777 4.284 48.013 15.808 99.785 16.666 148.309 2.466l8.32-2.432c51.614-15.104 106.586-14.895 158.080 0.606l47.053 14.161M224 668.949l72.461-19.085c46.668-12.288 95.959-10.799 141.777 4.284 48.013 15.804 99.785 16.666 148.309 2.466l8.32-2.432c51.614-15.104 106.586-14.895 158.080 0.606l47.053 14.161M344 298.651h288M344 298.651v-142.212c0-39.27 32.236-71.106 72-71.106s72 31.835 72 71.106M344 298.651v142.211M632 298.651v-142.212c0-39.27 32.235-71.106 72-71.106s72 31.835 72 71.106M632 298.651v142.211M344 440.862h288M344 440.862v71.108M632 440.862v71.108"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "swimming-pool"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1926,
        "id": 94,
        "name": "swimming-pool",
        "prevSize": 32,
        "code": 60202
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 555
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SwimmingPoolIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SwimmingPoolIcon = ({ size = 16, ...props }: SwimmingPoolIconProps) => (
  <IcoMoon iconSet={iconSet} icon="swimming-pool" size={size} {...props} />
);

SwimmingPoolIcon.displayName = 'SwimmingPoolIcon';

export default SwimmingPoolIcon;
