// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M582.349 99.608v0zM441.408 99.556v0zM619.87 130.141v0zM611.55 235.608v0zM412.45 235.608v0zM404.248 129.968v0zM270.782 172.431c-17.299 3.618-28.389 20.574-24.771 37.873s20.575 28.39 37.873 24.771l-13.102-62.644zM633.378 180.046v0zM740.113 235.074c17.297 3.62 34.257-7.468 37.875-24.767s-7.467-34.256-24.768-37.876l-13.107 62.643zM441.6 479.945c-17.673 0-32 14.323-32 32 0 17.673 14.327 32 32 32v-64zM582.4 543.945c17.673 0 32-14.327 32-32 0-17.677-14.327-32-32-32v64zM605.867 749.013h-32v94.827h64v-94.827h-32zM418.133 843.84h32v-94.827h-64v94.827h32zM512 938.667v-32c-33.86 0-61.867-27.823-61.867-62.827h-64c0 69.734 56.047 126.827 125.867 126.827v-32zM605.867 843.84h-32c0 35.004-28.006 62.827-61.867 62.827v64c69.82 0 125.867-57.092 125.867-126.827h-32zM512 654.182v32c33.86 0 61.867 27.823 61.867 62.831h64c0-69.739-56.047-126.831-125.867-126.831v32zM512 654.182v-32c-69.82 0-125.867 57.092-125.867 126.831h64c0-35.008 28.006-62.831 61.867-62.831v-32zM490.185 274.874v32h43.631v-64h-43.631v32zM582.349 99.608l10.914-30.082c-28.493-10.335-54.618-16.18-81.664-16.192-27.068-0.012-53.043 5.822-81.233 16.189l22.089 60.067c23.437-8.618 41.728-12.264 59.119-12.256 17.417 0.008 35.947 3.681 59.866 12.357l10.91-30.082zM619.87 130.141l26.705-17.626c-12.348-18.715-30.443-34.694-53.312-42.99l-21.824 60.164c8.115 2.944 15.808 9.117 21.722 18.076l26.709-17.625zM533.815 274.874v32c40.789 0 79.091-19.404 103.428-52.191l-51.383-38.15c-12.365 16.65-31.663 26.34-52.045 26.34v32zM412.45 235.608l-25.693 19.075c24.341 32.786 62.639 52.191 103.428 52.191v-64c-20.378 0-39.68-9.69-52.041-26.34l-25.694 19.075zM404.248 129.968l26.707 17.625c5.892-8.925 13.5-15.062 21.5-18.004l-22.089-60.067c-22.703 8.348-40.593 24.283-52.826 42.821l26.708 17.625zM404.248 129.968l-26.708-17.625c-14.541 22.034-20.28 46.088-18.793 69.713l63.874-4.021c-0.638-10.134 1.666-20.334 8.334-30.442l-26.707-17.625zM390.684 180.046l-31.937 2.010c1.693 26.9 12.608 51.881 28.010 72.626l51.387-38.15c-9.617-12.952-14.759-26.354-15.523-38.498l-31.937 2.011zM390.684 180.046l-6.551-31.322-113.35 23.707 13.102 62.644 113.35-23.707-6.551-31.322zM611.55 235.608l25.694 19.075c15.381-20.714 26.359-45.68 28.070-72.607l-63.872-4.060c-0.768 12.112-5.931 25.514-15.582 38.517l25.69 19.075zM633.378 180.046l31.936 2.030c1.498-23.565-4.215-47.557-18.739-69.56l-53.414 35.251c6.647 10.072 8.922 20.197 8.282 30.25l31.936 2.030zM633.378 180.046l-6.554 31.321 113.289 23.707 13.107-62.643-113.289-23.707-6.554 31.322zM465.067 322.287v32h93.867v-64h-93.867v32zM746.667 511.945h-32v165.948h64v-165.948h-32zM277.333 677.892h32v-165.948h-64v165.948h32zM394.667 796.425v-32c-46.822 0-85.333-38.434-85.333-86.532h-64c0 82.829 66.552 150.532 149.333 150.532v-32zM746.667 677.892h-32c0 48.098-38.511 86.532-85.333 86.532v64c82.782 0 149.333-67.703 149.333-150.532h-32zM558.933 322.287v32c85.705 0 155.733 70.277 155.733 157.657h64c0-122.111-98.069-221.657-219.733-221.657v32zM465.067 322.287v-32c-121.662 0-219.733 99.546-219.733 221.657h64c0-87.38 70.031-157.657 155.733-157.657v-32zM418.133 796.425v-32h-23.467v64h23.467v-32zM629.333 796.425v-32h-23.467v64h23.467v-32zM441.6 511.945v32h140.8v-64h-140.8v32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "motorcycle"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1789,
        "id": 231,
        "name": "motorcycle",
        "prevSize": 32,
        "code": 60065
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 418
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MotorcycleIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MotorcycleIcon = ({ size = 16, ...props }: MotorcycleIconProps) => (
  <IcoMoon iconSet={iconSet} icon="motorcycle" size={size} {...props} />
);

MotorcycleIcon.displayName = 'MotorcycleIcon';

export default MotorcycleIcon;
