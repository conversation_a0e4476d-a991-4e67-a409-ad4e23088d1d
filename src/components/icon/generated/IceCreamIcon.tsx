// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M324.267 440.657c-26.329-2.492-46.933-24.879-46.933-52.126 0-26.625 17.206-40.102 37-50.867 10.027-5.454 15.066-16.992 13.014-28.307-2.023-11.154-3.080-22.649-3.080-34.394 0-104.729 84.051-189.629 187.733-189.629 103.684 0 187.733 84.9 187.733 189.629 0 11.758-1.058 23.266-3.089 34.432-2.052 11.306 2.974 22.833 13.086 28.108 19.396 10.118 36.937 22.676 36.937 48.268 0 25.687-19.887 46.115-44.173 47.715M324.267 440.657l4.293 12.079 79.802 220.305M324.267 440.657c36.69 3.477 75.069 0.23 111.919 0.23 28.058 0 50.010 24.422 47.309 52.629-3.11 32.499 26.108 58.59 57.655 51.482l2.573-0.58c22.601-5.094 38.677-25.353 38.677-48.742v-8.576c0-36.898 32.354-65.245 68.523-60.028l41.724 6.020c3.337 0.482 6.63 0.602 9.847 0.393M702.494 433.485l-7.049 19.251-58.381 161.165M408.361 673.041l93.783 258.901c3.157 8.964 16.555 8.964 19.712 0l115.209-318.042M408.361 673.041c49.772 3.255 165.194-4.015 228.703-59.14"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "ice-cream"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1718,
        "id": 302,
        "name": "ice-cream",
        "prevSize": 32,
        "code": 59994
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 347
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface IceCreamIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const IceCreamIcon = ({ size = 16, ...props }: IceCreamIconProps) => (
  <IcoMoon iconSet={iconSet} icon="ice-cream" size={size} {...props} />
);

IceCreamIcon.displayName = 'IceCreamIcon';

export default IceCreamIcon;
