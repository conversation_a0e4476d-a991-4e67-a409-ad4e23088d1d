// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M333.401 650.667c26.463 23.966 90.579 72.107 178.595 72.107 88.021 0 152.137-48.141 178.598-72.107M938.667 512c0 235.639-191.027 426.667-426.667 426.667-235.642 0-426.667-191.027-426.667-426.667 0-235.642 191.025-426.667 426.667-426.667 235.639 0 426.667 191.025 426.667 426.667zM704 416c0 29.457-23.876 53.333-53.333 53.333s-53.333-23.876-53.333-53.333c0-29.455 23.876-53.333 53.333-53.333s53.333 23.878 53.333 53.333zM426.667 416c0 29.457-23.878 53.333-53.333 53.333s-53.333-23.876-53.333-53.333c0-29.455 23.878-53.333 53.333-53.333s53.333 23.878 53.333 53.333z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "smile"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1903,
        "id": 117,
        "name": "smile",
        "prevSize": 32,
        "code": 60179
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 532
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SmileIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SmileIcon = ({ size = 16, ...props }: SmileIconProps) => (
  <IcoMoon iconSet={iconSet} icon="smile" size={size} {...props} />
);

SmileIcon.displayName = 'SmileIcon';

export default SmileIcon;
