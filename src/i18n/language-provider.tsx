import React, { type JSX, useEffect, useState } from 'react';
import { I18nextProvider } from 'react-i18next';

import { initializeI18n } from './i18n';

type Locales = readonly string[];
type Namespaces = readonly string[];
interface Props<N extends Namespaces = Namespaces, L extends Locales = Locales> extends React.PropsWithChildren {
  resources: Record<L[number], Record<N[number], any>>;
  namespaces: N;
  locales: L;
}

type LanguageProviderFunction = <const L extends Locales, const N extends Namespaces>(
  props: Props<L, N>,
) => JSX.Element;

export const LanguageProvider: LanguageProviderFunction = (props): JSX.Element => {
  const [i18n, setI18n] = useState<Awaited<ReturnType<typeof initializeI18n>> | null>(null);

  useEffect(() => {
    let cancelled = false;

    const initialize = async () => {
      const inst = await initializeI18n({
        resources: props.resources,
        ns: props.namespaces,
        defaultNS: 'translation',
      });
      if (cancelled) return;

      (globalThis as any).T = inst.t.bind(inst);
      (globalThis as any).I18N = inst;
      setI18n(inst);
    };

    initialize();
    return () => {
      cancelled = true;
    };
  }, [props.resources, props.namespaces, props.locales]);

  if (!i18n) return <></>;

  return <I18nextProvider i18n={i18n}>{props.children}</I18nextProvider>;
};
