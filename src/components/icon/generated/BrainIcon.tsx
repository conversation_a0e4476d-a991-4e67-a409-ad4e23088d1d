// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M511.996 862.447c0 5.922-2.103 11.755-6.020 16.188-93.437 105.754-178.747 49.318-215.503 0.913-3.803-5.009-9.094-8.695-15.066-10.641-57.973-18.893-70.833-67.115-69.346-101.978 0.54-12.638-5.896-25.126-16.937-31.258-66.219-36.783-58.583-96.905-40.322-134.959 5.021-10.462 4.144-23.078-2.263-32.747-37.642-56.832-10.397-113.387 15.045-142.688 5.21-6.001 8.119-13.8 7.748-21.747-3.43-73.458 35.817-107.449 68.375-119.839 12.025-4.576 21.631-15.282 23.481-28.041 7.652-52.767 41.273-91.462 74.112-117.795 93.059-74.622 150.377-54.678 171.966-31.286 3.383 3.667 4.727 8.65 4.727 13.645M511.996 862.447c0 5.922 2.112 11.755 6.029 16.188 93.436 105.754 178.748 49.318 215.501 0.913 3.806-5.009 9.097-8.695 15.066-10.641 57.975-18.893 70.835-67.115 69.346-101.978-0.538-12.638 5.897-25.126 16.939-31.258 66.219-36.783 58.581-96.905 40.32-134.959-5.018-10.462-4.143-23.078 2.266-32.747 37.641-56.832 10.398-113.387-15.049-142.688-5.21-6.001-8.115-13.8-7.744-21.747 3.43-73.458-35.819-107.449-68.378-119.839-12.023-4.576-21.632-15.282-23.479-28.041-7.654-52.767-41.276-91.462-74.112-117.795-93.060-74.622-150.379-54.678-171.968-31.286-3.383 3.667-4.736 8.65-4.736 13.645M511.996 862.447v-742.231M317.859 416.177c5.815-16.868 27.543-55.664 67.93-75.906M713.937 416.177c-5.815-16.868-27.541-55.664-67.93-75.906M284.82 643.891c9.751 4.89 28.544 10.91 50.484 11.17M335.305 655.061c15.921 0.188 33.501-2.658 50.484-11.17M335.305 655.061c-8.414 21.577-15.145 74.859 25.242 115.341M739.179 643.891c-9.749 4.89-28.544 10.91-50.483 11.17M688.695 655.061c-15.923 0.188-33.502-2.658-50.483-11.17M688.695 655.061c8.414 21.577 15.147 74.859-25.242 115.341"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "brain"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1487,
        "id": 533,
        "name": "brain",
        "prevSize": 32,
        "code": 59763
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 116
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BrainIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BrainIcon = ({ size = 16, ...props }: BrainIconProps) => (
  <IcoMoon iconSet={iconSet} icon="brain" size={size} {...props} />
);

BrainIcon.displayName = 'BrainIcon';

export default BrainIcon;
