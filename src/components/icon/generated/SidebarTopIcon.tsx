// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M733.867 938.667v0zM290.133 938.667v0zM85.333 733.867v0zM85.333 725.333v0zM85.333 290.133v0zM938.667 290.133v0zM938.667 733.867v0zM868.779 924.715v0zM924.715 868.779v0zM868.779 99.284v0zM924.715 155.223v0zM155.223 99.284v0zM99.284 155.223v0zM155.223 924.715v0zM99.284 868.779v0zM361.216 446.865c-12.41 12.582-12.269 32.845 0.314 45.252 12.583 12.412 32.844 12.271 45.254-0.316l-45.568-44.937zM508.983 342.601v0zM515.017 342.601v0zM617.216 491.802c12.407 12.587 32.67 12.727 45.252 0.316 12.587-12.407 12.727-32.67 0.316-45.252l-45.568 44.937zM290.133 85.333v32h443.733v-64h-443.733v32zM733.867 938.667v-32h-443.733v64h443.733v-32zM85.333 733.867h32v-8.533h-64v8.533h32zM85.333 725.333h32v-435.2h-64v435.2h32zM938.667 290.133h-32v435.2h64v-435.2h-32zM938.667 725.333h-32v8.533h64v-8.533h-32zM85.333 725.333v32h853.333v-64h-853.333v32zM733.867 938.667v32c35.315 0 63.727 0.026 86.677-1.852 23.322-1.903 43.806-5.931 62.763-15.586l-29.056-57.024c-8.427 4.292-19.554 7.241-38.921 8.823-19.738 1.613-45.090 1.638-81.463 1.638v32zM938.667 733.867h-32c0 36.373-0.026 61.726-1.638 81.463-1.583 19.366-4.531 30.494-8.823 38.921l57.024 29.056c9.655-18.957 13.683-39.441 15.586-62.763 1.877-22.95 1.852-51.362 1.852-86.677h-32zM868.779 924.715l14.528 28.514c30.106-15.343 54.579-39.817 69.922-69.922l-57.024-29.056c-9.207 18.061-23.893 32.747-41.954 41.954l14.528 28.51zM733.867 85.333v32c36.373 0 61.726 0.025 81.463 1.638 19.366 1.582 30.494 4.532 38.921 8.826l29.056-57.024c-18.957-9.658-39.441-13.683-62.763-15.589-22.95-1.875-51.362-1.85-86.677-1.85v32zM938.667 290.133h32c0-35.315 0.026-63.727-1.852-86.676-1.903-23.323-5.931-43.809-15.586-62.762l-57.024 29.055c4.292 8.428 7.241 19.553 8.823 38.919 1.613 19.739 1.638 45.093 1.638 81.464h32zM868.779 99.284l-14.528 28.512c18.061 9.204 32.747 23.89 41.954 41.953l57.024-29.055c-15.343-30.106-39.817-54.583-69.922-69.923l-14.528 28.512zM290.133 85.333v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM85.333 290.133h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.365 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM155.223 99.284l-14.528-28.512c-30.106 15.34-54.583 39.817-69.922 69.923l57.024 29.055c9.204-18.063 23.89-32.75 41.953-41.953l-14.528-28.512zM290.133 938.667v-32c-36.371 0-61.725-0.026-81.464-1.638-19.365-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM85.333 733.867h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM155.223 924.715l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.583 69.923 69.922l14.528-28.514zM384 469.333l22.784 22.468 124.983-126.732-45.568-44.939-124.983 126.734 22.784 22.468zM515.017 342.601l-22.784 22.47 124.983 126.732 45.568-44.937-124.983-126.734-22.784 22.47zM508.983 342.601l22.784 22.47c-10.867 11.018-28.668 11.018-39.535 0l45.568-44.939c-14.199-14.397-37.402-14.397-51.601 0l22.784 22.47z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "sidebar-top"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1891,
        "id": 129,
        "name": "sidebar-top",
        "prevSize": 32,
        "code": 60167
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 520
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SidebarTopIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SidebarTopIcon = ({ size = 16, ...props }: SidebarTopIconProps) => (
  <IcoMoon iconSet={iconSet} icon="sidebar-top" size={size} {...props} />
);

SidebarTopIcon.displayName = 'SidebarTopIcon';

export default SidebarTopIcon;
