import clsx from 'clsx';
import { Tooltip } from 'radix-ui';
import { createContext, useCallback, useContext, useEffect, useMemo, useReducer } from 'react';

import { Flex } from '@components/flex';
import { sidebarReducer } from '@components/sidebar/sidebar-reducer';

export const SIDEBAR_COOKIE_NAME = 'sidebar_state';
const SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7; // 7 days
const SIDEBAR_KEYBOARD_SHORTCUT = 'b';

type SidebarContextProps = {
  state: 'expanded' | 'collapsed';
  open: boolean;
  toggleSidebar: () => void;
};

const SidebarContext = createContext<SidebarContextProps | null>(null);

export function useSidebar() {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error('useSidebar must be used within a SidebarProvider.');
  }

  return context;
}

type Props = React.ComponentProps<'div'> & {
  defaultOpen?: boolean;
};

export const SidebarProvider: React.FC<Props> = ({ defaultOpen = true, className, style, children, ...props }) => {
  const [state, dispatch] = useReducer(sidebarReducer, {
    open: defaultOpen,
  });

  const handleOpen = useCallback(
    (value: boolean | ((value: boolean) => boolean)) => {
      const openState = typeof value === 'function' ? value(state.open) : value;
      dispatch({ type: 'SET_OPEN', payload: { open: openState } });

      // This sets the cookie to keep the sidebar state.
      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;
    },
    [state.open],
  );

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === SIDEBAR_KEYBOARD_SHORTCUT && (event.metaKey || event.ctrlKey)) {
        event.preventDefault();
        handleOpen((open) => !open);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleOpen]);

  // We add a state so that we can do data-state="expanded" or "collapsed".
  const openState = state.open ? 'expanded' : 'collapsed';

  const contextValue = useMemo<SidebarContextProps>(
    () => ({
      state: openState,
      open: state.open,
      toggleSidebar: () => handleOpen((open) => !open),
    }),
    [openState, state.open, handleOpen],
  );

  return (
    <SidebarContext.Provider value={contextValue}>
      <Tooltip.Provider delayDuration={0}>
        <Flex
          data-slot="sidebar-wrapper"
          minHeight="100svh"
          width={'100%'}
          className={clsx('rt-sidebar-wrapper', className)}
          {...props}
        >
          {children}
        </Flex>
      </Tooltip.Provider>
    </SidebarContext.Provider>
  );
};
