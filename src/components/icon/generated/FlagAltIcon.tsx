// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M271.388 85.333v94.815M271.388 938.667h-100.721M271.388 938.667h100.721M271.388 938.667v-320M271.388 180.148l209.504-40.34c23.825-4.588 48.41-0.868 70.071 10.601l22.797 12.072c21.658 11.468 46.242 15.188 70.067 10.601l156.732-30.179c27.482-5.291 52.774 17.065 52.774 46.643v330.297c0 22.912-15.471 42.543-36.757 46.643l-172.749 33.263c-23.825 4.587-48.41 0.866-70.067-10.603l-17.92-9.489c-24.627-13.039-52.932-16.009-79.497-8.333l-204.955 47.343M271.388 180.148v438.519"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "flag-alt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1657,
        "id": 363,
        "name": "flag-alt",
        "prevSize": 32,
        "code": 59933
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 286
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FlagAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FlagAltIcon = ({ size = 16, ...props }: FlagAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="flag-alt" size={size} {...props} />
);

FlagAltIcon.displayName = 'FlagAltIcon';

export default FlagAltIcon;
