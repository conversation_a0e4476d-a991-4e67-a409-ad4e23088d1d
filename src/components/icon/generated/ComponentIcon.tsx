// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M371.347 283.439c-12.308-12.308-18.463-18.462-20.768-25.559-2.028-6.242-2.028-12.966 0-19.209 2.306-7.096 8.46-13.251 20.768-25.559l105.491-105.489c12.305-12.308 18.462-18.463 25.557-20.768 6.242-2.028 12.966-2.028 19.209 0 7.095 2.306 13.252 8.46 25.557 20.768l105.493 105.489c12.305 12.308 18.462 18.463 20.766 25.559 2.027 6.242 2.027 12.966 0 19.209-2.304 7.097-8.461 13.251-20.766 25.559l-105.493 105.49c-12.305 12.308-18.462 18.462-25.557 20.768-6.242 2.028-12.966 2.028-19.209 0-7.095-2.306-13.252-8.46-25.557-20.768l-105.491-105.49z",
          "M635.072 547.162c-12.309-12.305-18.462-18.462-20.77-25.557-2.027-6.242-2.027-12.966 0-19.209 2.308-7.095 8.461-13.252 20.77-25.557l105.489-105.491c12.309-12.308 18.462-18.463 25.557-20.768 6.242-2.028 12.966-2.028 19.209 0 7.1 2.306 13.252 8.46 25.562 20.768l105.489 105.491c12.309 12.305 18.462 18.462 20.766 25.557 2.031 6.242 2.031 12.966 0 19.209-2.304 7.095-8.457 13.252-20.766 25.557l-105.489 105.493c-12.309 12.305-18.462 18.462-25.562 20.766-6.242 2.027-12.966 2.027-19.209 0-7.095-2.304-13.248-8.461-25.557-20.766l-105.489-105.493z",
          "M371.347 810.889c-12.308-12.309-18.463-18.462-20.768-25.562-2.028-6.242-2.028-12.966 0-19.209 2.306-7.095 8.46-13.248 20.768-25.557l105.491-105.489c12.305-12.309 18.462-18.462 25.557-20.77 6.242-2.027 12.966-2.027 19.209 0 7.095 2.308 13.252 8.461 25.557 20.77l105.493 105.489c12.305 12.309 18.462 18.462 20.766 25.557 2.027 6.242 2.027 12.966 0 19.209-2.304 7.1-8.461 13.252-20.766 25.562l-105.493 105.489c-12.305 12.309-18.462 18.462-25.557 20.766-6.242 2.031-12.966 2.031-19.209 0-7.095-2.304-13.252-8.457-25.557-20.766l-105.491-105.489z",
          "M107.623 547.162c-12.308-12.305-18.463-18.462-20.768-25.557-2.028-6.242-2.028-12.966 0-19.209 2.306-7.095 8.46-13.252 20.768-25.557l105.489-105.491c12.308-12.308 18.463-18.463 25.559-20.768 6.242-2.028 12.966-2.028 19.209 0 7.097 2.306 13.251 8.46 25.559 20.768l105.49 105.491c12.308 12.305 18.462 18.462 20.768 25.557 2.028 6.242 2.028 12.966 0 19.209-2.306 7.095-8.46 13.252-20.768 25.557l-105.49 105.493c-12.308 12.305-18.462 18.462-25.559 20.766-6.242 2.027-12.966 2.027-19.209 0-7.096-2.304-13.251-8.461-25.559-20.766l-105.489-105.493z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "component"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1581,
        "id": 439,
        "name": "component",
        "prevSize": 32,
        "code": 59857
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 210
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ComponentIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ComponentIcon = ({ size = 16, ...props }: ComponentIconProps) => (
  <IcoMoon iconSet={iconSet} icon="component" size={size} {...props} />
);

ComponentIcon.displayName = 'ComponentIcon';

export default ComponentIcon;
