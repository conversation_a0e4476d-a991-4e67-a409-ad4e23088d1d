// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M269.653 853.333c-64.518 0-96.777 0-121.42-12.557-21.676-11.042-39.3-28.668-50.345-50.342-12.556-24.644-12.556-56.905-12.556-121.421M669.013 85.333c64.516 0 96.777 0 121.421 12.556 21.675 11.045 39.3 28.668 50.342 50.345 12.557 24.643 12.557 56.902 12.557 121.42M269.653 85.333c-64.518 0-96.777 0-121.42 12.556-21.676 11.045-39.3 28.668-50.345 50.345-12.556 24.643-12.556 56.902-12.556 121.42M392.533 85.333h153.6M85.333 546.133v-153.6M853.333 392.533v153.6M546.133 853.333h-153.6M725.333 832h106.667M832 832h106.667M832 832v-106.667M832 832v106.667"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "selection-plus"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1872,
        "id": 148,
        "name": "selection-plus",
        "prevSize": 32,
        "code": 60148
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 501
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SelectionPlusIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SelectionPlusIcon = ({ size = 16, ...props }: SelectionPlusIconProps) => (
  <IcoMoon iconSet={iconSet} icon="selection-plus" size={size} {...props} />
);

SelectionPlusIcon.displayName = 'SelectionPlusIcon';

export default SelectionPlusIcon;
