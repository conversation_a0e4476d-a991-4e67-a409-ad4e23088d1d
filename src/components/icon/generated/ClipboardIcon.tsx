// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M341.335 149.817c-38.994 1.079-63.755 4.567-83.878 15.837-20.596 11.535-37.602 28.54-49.136 49.136-16.32 29.142-16.32 68.009-16.32 145.743v366.933c0 77.734 0 116.599 16.32 145.745 11.535 20.595 28.54 37.602 49.136 49.135 29.142 16.32 68.009 16.32 145.743 16.32h217.6c77.734 0 116.599 0 145.745-16.32 20.595-11.533 37.598-28.54 49.135-49.135 16.32-29.146 16.32-68.011 16.32-145.745v-366.933c0-77.734 0-116.601-16.32-145.743-11.537-20.596-28.54-37.602-49.135-49.136-20.126-11.27-44.885-14.758-83.878-15.837M341.335 149.817c0.26 35.124 28.814 63.516 63.998 63.516h213.333c35.183 0 63.74-28.393 64-63.516M341.335 149.817c-0.001-0.161-0.002-0.323-0.002-0.484 0-35.346 28.654-64 64-64h213.333c35.345 0 64 28.654 64 64 0 0.161 0 0.323 0 0.484"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "clipboard"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1559,
        "id": 461,
        "name": "clipboard",
        "prevSize": 32,
        "code": 59835
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 188
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ClipboardIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ClipboardIcon = ({ size = 16, ...props }: ClipboardIconProps) => (
  <IcoMoon iconSet={iconSet} icon="clipboard" size={size} {...props} />
);

ClipboardIcon.displayName = 'ClipboardIcon';

export default ClipboardIcon;
