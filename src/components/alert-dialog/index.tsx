import clsx from 'clsx';
import { AlertDialog as AlertDialogPrimitive } from 'radix-ui';
import * as React from 'react';

import {
  type AlertDialogContentOwnProps,
  alertDialogContentPropDefs,
} from '@components/alert-dialog/alert-dialog.props';
import { Heading } from '@components/heading';
import { Text } from '@components/text';
import { Theme } from '@components/theme';
import {
  type ComponentPropsAs,
  type ComponentPropsWithout,
  type RemovedProps,
  extractProps,
  requireReactElement,
} from '@helpers';

interface AlertDialogRootProps extends React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Root> {}
const AlertDialogRoot: React.FC<AlertDialogRootProps> = (props) => <AlertDialogPrimitive.Root {...props} />;
AlertDialogRoot.displayName = 'AlertDialog.Root';

type AlertDialogTriggerElement = React.ComponentRef<typeof AlertDialogPrimitive.Trigger>;
interface AlertDialogTriggerProps extends ComponentPropsWithout<typeof AlertDialogPrimitive.Trigger, RemovedProps> {}
const AlertDialogTrigger = React.forwardRef<AlertDialogTriggerElement, AlertDialogTriggerProps>(
  ({ children, ...props }, forwardedRef) => (
    <AlertDialogPrimitive.Trigger {...props} ref={forwardedRef} asChild>
      {requireReactElement(children)}
    </AlertDialogPrimitive.Trigger>
  ),
);
AlertDialogTrigger.displayName = 'AlertDialog.Trigger';

type AlertDialogContentElement = React.ComponentRef<typeof AlertDialogPrimitive.Content>;
interface AlertDialogContentProps
  extends ComponentPropsWithout<typeof AlertDialogPrimitive.Content, RemovedProps>,
    AlertDialogContentOwnProps {
  container?: React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Portal>['container'];
}
const AlertDialogContent = React.forwardRef<AlertDialogContentElement, AlertDialogContentProps>(
  ({ align, ...props }, forwardedRef) => {
    const { align: alignPropDef, ...propDefs } = alertDialogContentPropDefs;
    const { className: alignClassName } = extractProps({ align }, { align: alignPropDef });
    const { className, forceMount, container, ...contentProps } = extractProps(props, propDefs);
    return (
      <AlertDialogPrimitive.Portal container={container} forceMount={forceMount}>
        <Theme asChild>
          <AlertDialogPrimitive.Overlay className="rt-BaseDialogOverlay rt-AlertDialogOverlay">
            <div className="rt-BaseDialogScroll rt-AlertDialogScroll">
              <div className={`rt-BaseDialogScrollPadding rt-AlertDialogScrollPadding ${alignClassName}`}>
                <AlertDialogPrimitive.Content
                  {...contentProps}
                  ref={forwardedRef}
                  className={clsx('rt-BaseDialogContent', 'rt-AlertDialogContent', className)}
                />
              </div>
            </div>
          </AlertDialogPrimitive.Overlay>
        </Theme>
      </AlertDialogPrimitive.Portal>
    );
  },
);
AlertDialogContent.displayName = 'AlertDialog.Content';

type AlertDialogTitleElement = React.ComponentRef<typeof Heading>;
type AlertDialogTitleProps = ComponentPropsWithout<typeof Heading, 'asChild'>;
const AlertDialogTitle = React.forwardRef<AlertDialogTitleElement, AlertDialogTitleProps>((props, forwardedRef) => (
  <AlertDialogPrimitive.Title asChild>
    <Heading size="6" mb="3" trim="start" {...props} asChild={false} ref={forwardedRef} />
  </AlertDialogPrimitive.Title>
));
AlertDialogTitle.displayName = 'AlertDialog.Title';

type AlertDialogDescriptionElement = HTMLParagraphElement;
type AlertDialogDescriptionProps = ComponentPropsAs<typeof Text, 'p'>;
const AlertDialogDescription = React.forwardRef<AlertDialogDescriptionElement, AlertDialogDescriptionProps>(
  (props, forwardedRef) => (
    <AlertDialogPrimitive.Description asChild>
      <Text as="p" size="4" {...props} asChild={false} ref={forwardedRef} />
    </AlertDialogPrimitive.Description>
  ),
);
AlertDialogDescription.displayName = 'AlertDialog.Description';

type AlertDialogActionElement = React.ComponentRef<typeof AlertDialogPrimitive.Action>;
interface AlertDialogActionProps extends ComponentPropsWithout<typeof AlertDialogPrimitive.Action, RemovedProps> {}
const AlertDialogAction = React.forwardRef<AlertDialogActionElement, AlertDialogActionProps>(
  ({ children, ...props }, forwardedRef) => (
    <AlertDialogPrimitive.Action {...props} ref={forwardedRef} asChild>
      {requireReactElement(children)}
    </AlertDialogPrimitive.Action>
  ),
);
AlertDialogAction.displayName = 'AlertDialog.Action';

type AlertDialogCancelElement = React.ComponentRef<typeof AlertDialogPrimitive.Cancel>;
interface AlertDialogCancelProps extends ComponentPropsWithout<typeof AlertDialogPrimitive.Cancel, RemovedProps> {}
const AlertDialogCancel = React.forwardRef<AlertDialogCancelElement, AlertDialogCancelProps>(
  ({ children, ...props }, forwardedRef) => (
    <AlertDialogPrimitive.Cancel {...props} ref={forwardedRef} asChild>
      {requireReactElement(children)}
    </AlertDialogPrimitive.Cancel>
  ),
);
AlertDialogCancel.displayName = 'AlertDialog.Cancel';

export {
  AlertDialogAction as Action,
  AlertDialogCancel as Cancel,
  AlertDialogContent as Content,
  AlertDialogDescription as Description,
  AlertDialogRoot as Root,
  AlertDialogTitle as Title,
  AlertDialogTrigger as Trigger,
};

export type {
  AlertDialogActionProps as ActionProps,
  AlertDialogCancelProps as CancelProps,
  AlertDialogContentProps as ContentProps,
  AlertDialogDescriptionProps as DescriptionProps,
  AlertDialogRootProps as RootProps,
  AlertDialogTitleProps as TitleProps,
  AlertDialogTriggerProps as TriggerProps,
};
