// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M511.774 192c5.303 7.637 11.264 16.579 18.355 27.218l5.342 8.012c14.818 22.234 22.225 33.35 32.017 41.398 8.666 7.124 18.654 12.47 29.389 15.731 12.126 3.683 25.485 3.683 52.203 3.683h84.787c71.689 0 107.529 0 134.912 13.951 24.085 12.272 43.665 31.853 55.936 55.938 13.952 27.38 13.952 63.224 13.952 134.912v198.357c0 71.689 0 107.529-13.952 134.912-12.271 24.085-31.851 43.665-55.936 55.936-27.383 13.952-63.223 13.952-134.912 13.952h-443.733c-71.687 0-107.53 0-134.911-13.952-24.085-12.271-43.666-31.851-55.938-55.936-13.951-27.383-13.951-63.223-13.951-134.912v-358.4c0-71.687 0-107.53 13.951-134.911 12.272-24.085 31.853-43.666 55.938-55.938 27.38-13.951 63.224-13.951 134.911-13.951h69.579c40.079 0 60.118 0 78.308 5.524 16.102 4.891 31.083 12.91 44.083 23.596 10.001 8.221 18.342 18.576 29.67 34.879zM511.774 192h298.893c70.694 0 128 57.308 128 128v128M341.333 725.333h341.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "folder"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1672,
        "id": 348,
        "name": "folder",
        "prevSize": 32,
        "code": 59948
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 301
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FolderIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FolderIcon = ({ size = 16, ...props }: FolderIconProps) => (
  <IcoMoon iconSet={iconSet} icon="folder" size={size} {...props} />
);

FolderIcon.displayName = 'FolderIcon';

export default FolderIcon;
