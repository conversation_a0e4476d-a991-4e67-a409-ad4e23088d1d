import { defineConfig } from 'vite';

export default defineConfig({
  build: {
    outDir: 'dist/i18n',
    emptyOutDir: false,
    lib: {
      entry: 'src/i18n/i18next-parser.ts',
      formats: ['cjs'], // CLI as CJS or ES is fine; pick one
      fileName: () => 'i18next-parser.cjs',
    },
    rollupOptions: {
      // keep Node core & runtime deps external
      external: [
        /^node:/,
        'fs',
        'path',
        'os',
        'child_process', // if you used bare names
        'commander',
        'i18next-parser',
      ],
    },
    target: 'node18', // Node target
  },
});
