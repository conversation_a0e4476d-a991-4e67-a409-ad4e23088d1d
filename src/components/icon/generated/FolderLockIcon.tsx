// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M530.129 219.218v0zM535.471 227.229v0zM567.488 268.628v0zM596.877 284.358v0zM438.020 133.524v0zM482.103 157.121v0zM155.223 882.048v0zM99.284 826.112v0zM906.667 492.843c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM924.715 357.931v0zM868.779 301.993v0zM99.284 197.889v0zM155.223 141.951v0zM511.774 192v0zM906.667 448c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM533.333 928c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM667.179 890.419v0zM645.427 868.045v0zM933.239 868.045v0zM911.488 890.419v0zM859.021 608c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM911.488 645.581v0zM933.239 667.955v0zM719.646 672c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM667.179 645.581v0zM645.427 667.955v0zM855.706 640v32h32v-32h-32zM722.961 640h-32v32h32v-32zM85.333 691.2h32v-358.4h-64v358.4h32zM290.133 128v32h69.579v-64h-69.579v32zM530.129 219.218l-26.628 17.747 5.342 8.012 53.252-35.494-5.338-8.012-26.628 17.748zM649.079 288.041v32h84.787v-64h-84.787v32zM535.471 227.229l-26.628 17.747c13.713 20.575 23.996 36.592 38.323 48.37l40.64-49.439c-5.252-4.317-9.783-10.534-25.711-34.426l-26.624 17.747zM649.079 288.041v-32c-28.71 0-36.399-0.326-42.906-2.302l-18.598 61.238c17.749 5.39 36.779 5.064 61.504 5.064v-32zM567.488 268.628l-20.322 24.719c11.917 9.797 25.651 17.147 40.41 21.63l18.598-61.238c-6.707-2.038-12.949-5.379-18.368-9.832l-20.318 24.72zM359.712 128v32c42.071 0 56.439 0.326 69.007 4.143l18.598-61.238c-23.808-7.232-49.519-6.906-87.605-6.906v32zM438.020 133.524l-9.301 30.619c12.079 3.668 23.313 9.682 33.062 17.697l40.644-49.44c-16.252-13.359-34.978-23.382-55.108-29.495l-9.297 30.619zM290.133 896v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM85.333 691.2h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM155.223 882.048l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM938.667 492.843h32c0-35.315 0.026-63.727-1.852-86.677-1.903-23.323-5.931-43.809-15.586-62.762l-57.024 29.055c4.292 8.428 7.241 19.553 8.823 38.919 1.613 19.74 1.638 45.092 1.638 81.466h32zM733.867 288.041v32c36.373 0 61.726 0.025 81.463 1.638 19.366 1.582 30.494 4.532 38.921 8.826l29.056-57.024c-18.957-9.658-39.441-13.683-62.763-15.589-22.95-1.875-51.362-1.85-86.677-1.85v32zM924.715 357.931l28.514-14.528c-15.343-30.106-39.817-54.583-69.922-69.923l-29.056 57.024c18.061 9.204 32.747 23.89 41.954 41.953l28.51-14.528zM85.333 332.8h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM290.133 128v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM99.284 197.889l28.512 14.528c9.204-18.063 23.89-32.75 41.953-41.953l-29.055-57.024c-30.106 15.34-54.583 39.817-69.923 69.923l28.512 14.528zM810.667 192v32c53.018 0 96 42.981 96 96h64c0-88.366-71.633-160-160-160v32zM530.129 219.218l26.628-17.747c-7.036-10.558-13.184-19.784-18.705-27.728l-52.561 36.515c5.094 7.33 10.867 15.987 18.010 26.708l26.628-17.747zM511.774 192l26.278-18.257c-11.682-16.82-22.076-30.2-35.627-41.341l-40.644 49.44c6.447 5.3 12.745 12.632 23.71 28.417l26.283-18.257zM938.667 320h-32v128h64v-128h-32zM533.333 896v-32h-243.2v64h243.2v-32zM938.667 721.92h-32v92.16h64v-92.16h-32zM859.021 896v-32h-139.375v64h139.375v-32zM640 814.080h32v-92.16h-64v92.16h32zM719.646 896v-32c-14.485 0-23.364-0.026-30.016-0.585-3.098-0.26-4.988-0.589-6.148-0.87-1.054-0.256-1.412-0.448-1.446-0.469l-29.709 56.687c10.662 5.589 21.594 7.556 31.945 8.427 9.95 0.836 21.978 0.811 35.375 0.811v-32zM640 814.080h-32c0 13.824-0.026 26.065 0.777 36.16 0.832 10.449 2.697 21.35 7.974 32.009l57.348-28.407c-0.145-0.294-0.994-2.039-1.523-8.666-0.55-6.976-0.576-16.243-0.576-31.095h-32zM667.179 890.419l14.857-28.343c-3.277-1.715-6.11-4.548-7.936-8.235l-57.348 28.407c7.718 15.578 20.117 28.416 35.575 36.514l14.852-28.343zM938.667 814.080h-32c0 14.852-0.026 24.119-0.576 31.095-0.529 6.626-1.378 8.371-1.523 8.666l57.348 28.407c5.278-10.658 7.142-21.559 7.974-32.009 0.802-10.095 0.777-22.336 0.777-36.16h-32zM859.021 896v32c13.397 0 25.425 0.026 35.375-0.811 10.351-0.87 21.282-2.837 31.945-8.427l-29.709-56.687c-0.034 0.021-0.393 0.213-1.446 0.469-1.161 0.282-3.051 0.61-6.148 0.87-6.652 0.559-15.531 0.585-30.016 0.585v32zM933.239 868.045l-28.672-14.204c-1.826 3.686-4.659 6.519-7.936 8.235l29.709 56.687c15.458-8.098 27.857-20.937 35.575-36.514l-28.676-14.204zM859.021 640v32c14.485 0 23.364 0.026 30.016 0.585 3.098 0.26 4.988 0.589 6.148 0.87 1.054 0.256 1.412 0.448 1.446 0.469l29.709-56.687c-10.662-5.589-21.594-7.556-31.945-8.427-9.95-0.836-21.978-0.811-35.375-0.811v32zM938.667 721.92h32c0-13.824 0.026-26.065-0.777-36.16-0.832-10.449-2.697-21.35-7.974-32.009l-57.348 28.407c0.145 0.294 0.994 2.039 1.523 8.666 0.55 6.976 0.576 16.243 0.576 31.095h32zM911.488 645.581l-14.857 28.343c3.277 1.715 6.11 4.548 7.936 8.235l57.348-28.407c-7.718-15.578-20.117-28.416-35.575-36.514l-14.852 28.343zM719.646 640v-32c-13.397 0-25.425-0.026-35.375 0.811-10.351 0.87-21.282 2.837-31.945 8.427l29.709 56.687c0.034-0.021 0.393-0.213 1.446-0.469 1.161-0.282 3.051-0.61 6.148-0.87 6.652-0.559 15.531-0.585 30.016-0.585v-32zM640 721.92h32c0-14.852 0.026-24.119 0.576-31.095 0.529-6.626 1.378-8.371 1.523-8.666l-57.348-28.407c-5.278 10.658-7.142 21.559-7.974 32.009-0.802 10.095-0.777 22.336-0.777 36.16h32zM667.179 645.581l-14.852-28.343c-15.458 8.098-27.857 20.937-35.575 36.514l57.348 28.407c1.826-3.686 4.659-6.519 7.936-8.235l-14.857-28.343zM855.706 622.933h-32v17.067h64v-17.067h-32zM855.706 640v-32h-132.745v64h132.745v-32zM722.961 640h32v-17.067h-64v17.067h32zM789.333 554.667v32c18.138 0 34.372 15.381 34.372 36.267h64c0-54.519-43.2-100.267-98.372-100.267v32zM789.333 554.667v-32c-55.172 0-98.372 45.747-98.372 100.267h64c0-20.885 16.235-36.267 34.372-36.267v-32zM511.774 192v32h298.893v-64h-298.893v32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "folder-lock"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1666,
        "id": 354,
        "name": "folder-lock",
        "prevSize": 32,
        "code": 59942
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 295
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FolderLockIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FolderLockIcon = ({ size = 16, ...props }: FolderLockIconProps) => (
  <IcoMoon iconSet={iconSet} icon="folder-lock" size={size} {...props} />
);

FolderLockIcon.displayName = 'FolderLockIcon';

export default FolderLockIcon;
