.rt-checkbox-label:where(.rt-r-size-1) {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--font-size-4);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-4);
}

.rt-checkbox-caption:where(.rt-r-size-1) {
  font-size: var(--font-size-3, 12px);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-3);
}

.rt-checkbox-label:where(.rt-r-size-2) {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--font-size-5);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-5);
}

.rt-checkbox-caption:where(.rt-r-size-2) {
  font-size: var(--font-size-4);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-4);
}

.rt-checkbox-label:where(.rt-r-size-3) {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--font-size-6);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-6);
}

.rt-checkbox-caption:where(.rt-r-size-3) {
  font-size: var(--font-size-5);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-5);
}

.rt-checkbox-label:where(.rt-r-size-4) {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--font-size-7);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-7);
}

.rt-checkbox-caption:where(.rt-r-size-4) {
  font-size: var(--font-size-6);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-6);
}

.rt-checkbox-label:where(.rt-r-size-5) {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--font-size-8);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-8);
}

.rt-checkbox-caption:where(.rt-r-size-5) {
  font-size: var(--font-size-7);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-7);
}
