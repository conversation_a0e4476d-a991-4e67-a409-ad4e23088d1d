// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M276.766 456.333l-89.784 89.783c-79.338 79.339-78.534 208.777 1.796 289.105 80.33 80.333 209.766 81.135 289.106 1.796l89.783-89.783M456.333 276.766l89.783-89.784c79.339-79.338 208.777-78.534 289.105 1.796 80.333 80.33 81.135 209.766 1.796 289.106l-89.783 89.783M368.346 655.654l287.309-287.309"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "link-alt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1733,
        "id": 287,
        "name": "link-alt",
        "prevSize": 32,
        "code": 60009
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 362
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface LinkAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const LinkAltIcon = ({ size = 16, ...props }: LinkAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="link-alt" size={size} {...props} />
);

LinkAltIcon.displayName = 'LinkAltIcon';

export default LinkAltIcon;
