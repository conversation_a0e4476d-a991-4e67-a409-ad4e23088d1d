import type { PropDef } from '@props';

const levels = ['weak', 'medium', 'strong'] as const;

const strengthMeterPropDefs = {
  level: {
    type: 'enum',
    className: 'rt-level',
    values: levels,
    default: 'weak',
    required: true,
  },
  segments: {
    type: 'number',
    default: 3,
    required: true,
  },
} satisfies {
  level: PropDef<(typeof levels)[number]>;
  segments: PropDef<number>;
};

export { strengthMeterPropDefs };
