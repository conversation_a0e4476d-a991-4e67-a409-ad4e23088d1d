import type { Meta, StoryObj } from '@storybook/react-vite';
import { useArgs } from 'storybook/internal/preview-api';

import { Radio } from '@components/radio';
import { radioPropDefs } from '@components/radio/radio.props';

const meta = {
  title: 'DesignSystem/Radio/Radio',
  component: Radio,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    label: { control: 'text', type: 'string' },
    caption: { control: 'text', type: 'string' },
    labelAlign: { control: 'inline-radio', type: 'string', options: ['left', 'right'] },
    variant: { control: 'inline-radio', type: 'string', options: radioPropDefs.variant.values },
    size: { control: 'inline-radio', type: 'string', options: radioPropDefs.size.values },
    value: { control: 'text', type: 'string' },
    disabled: { control: 'boolean' },
    onValueChange: { control: false },
  },
} satisfies Meta<typeof Radio>;

export default meta;
type Story = StoryObj<typeof meta>;

export const _radio: Story = {
  args: {
    label: 'Label',
    caption: 'caption',
    labelAlign: 'right',
    variant: radioPropDefs.variant.default,
    size: radioPropDefs.size.default,
    value: '',
    disabled: false,
  },
  render: (args) => {
    const [, updateArgs] = useArgs();
    return (
      <Radio
        {...args}
        onValueChange={(value) => {
          updateArgs({ value });
        }}
      />
    );
  },
};
