// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M396.445 85.333h46.222M442.667 85.333v189.629M442.667 85.333h138.667M581.333 85.333h46.221M581.333 85.333v189.629M234.667 464.593h554.667M419.555 606.814v189.632M604.446 606.814v189.632M350.222 938.667h323.557c63.817 0 115.554-53.065 115.554-118.519v-426.666c0-65.456-51.738-118.519-115.554-118.519h-323.557c-63.82 0-115.555 53.063-115.555 118.519v426.666c0 65.455 51.736 118.519 115.555 118.519z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "luggage"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1752,
        "id": 268,
        "name": "luggage",
        "prevSize": 32,
        "code": 60028
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 381
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface LuggageIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const LuggageIcon = ({ size = 16, ...props }: LuggageIconProps) => (
  <IcoMoon iconSet={iconSet} icon="luggage" size={size} {...props} />
);

LuggageIcon.displayName = 'LuggageIcon';

export default LuggageIcon;
