// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M785.067 938.667h-546.133c-37.703 0-68.267-31.838-68.267-71.113M170.667 867.554c0-39.27 30.564-71.108 68.267-71.108h591.646c12.565 0 22.754-10.615 22.754-23.706v-663.703c0-13.091-10.189-23.704-22.754-23.704h-546.135c-62.838 0-113.778 53.063-113.778 118.519v663.702zM716.8 85.334v323.642c0 7.142-9.084 10.865-14.69 6.020l-75.341-65.105c-13.41-11.588-34.082-11.588-47.492 0l-75.345 65.105c-5.606 4.844-14.686 1.122-14.686-6.020v-323.643"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "book-saved"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1479,
        "id": 541,
        "name": "book-saved",
        "prevSize": 32,
        "code": 59755
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 108
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BookSavedIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BookSavedIcon = ({ size = 16, ...props }: BookSavedIconProps) => (
  <IcoMoon iconSet={iconSet} icon="book-saved" size={size} {...props} />
);

BookSavedIcon.displayName = 'BookSavedIcon';

export default BookSavedIcon;
