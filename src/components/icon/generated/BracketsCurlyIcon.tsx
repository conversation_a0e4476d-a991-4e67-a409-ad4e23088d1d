// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M387.573 85.333h-23.703c-65.456 0-118.519 53.062-118.519 118.519v168.204c0 54.384-37.013 101.791-89.774 114.979l-63.062 15.765c-9.576 2.394-9.576 16.004 0 18.398l63.062 15.765c52.761 13.188 89.774 60.595 89.774 114.978v168.205c0 65.455 53.062 118.519 118.519 118.519h23.703M636.425 85.333h23.706c65.455 0 118.519 53.062 118.519 118.519v168.204c0 54.384 37.013 101.791 89.771 114.979l63.066 15.765c9.574 2.394 9.574 16.004 0 18.398l-63.066 15.765c-52.757 13.188-89.771 60.595-89.771 114.978v168.205c0 65.455-53.065 118.519-118.519 118.519h-23.706"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "brackets-curly"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1484,
        "id": 536,
        "name": "brackets-curly",
        "prevSize": 32,
        "code": 59760
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 113
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BracketsCurlyIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BracketsCurlyIcon = ({ size = 16, ...props }: BracketsCurlyIconProps) => (
  <IcoMoon iconSet={iconSet} icon="brackets-curly" size={size} {...props} />
);

BracketsCurlyIcon.displayName = 'BracketsCurlyIcon';

export default BracketsCurlyIcon;
