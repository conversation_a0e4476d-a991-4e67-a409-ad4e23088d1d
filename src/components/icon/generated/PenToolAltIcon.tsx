// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M436.45 246.106l-200.436 57.133c-59.227 16.883-102.174 68.113-108.469 129.392l-41.726 406.174c-2.933 28.557 7.633 54.865 26.072 73.306M436.45 246.106l341.444 341.443M436.45 246.106l86.336-105.553c56.439-69.004 160.192-74.103 223.317-10.976l148.318 148.317c63.13 63.127 58.027 166.88-10.974 223.319l-105.553 86.336M777.894 587.55l-57.135 200.435c-16.883 59.23-68.113 102.174-129.391 108.471l-406.175 41.724c-28.554 2.935-54.864-7.633-73.303-26.069M328.338 695.663c32.602 32.602 85.46 32.602 118.062 0s32.602-85.461 0-118.063c-32.602-32.602-85.46-32.602-118.062 0s-32.602 85.461 0 118.063zM328.338 695.663l-216.448 216.448"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "pen-tool-alt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1817,
        "id": 203,
        "name": "pen-tool-alt",
        "prevSize": 32,
        "code": 60093
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 446
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface PenToolAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const PenToolAltIcon = ({ size = 16, ...props }: PenToolAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="pen-tool-alt" size={size} {...props} />
);

PenToolAltIcon.displayName = 'PenToolAltIcon';

export default PenToolAltIcon;
