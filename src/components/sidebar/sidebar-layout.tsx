import clsx from 'clsx';
import type { JSX } from 'react';

import { Flex } from '@components/flex';
import { useSidebar } from '@components/sidebar/sidebar-provider';
import { sidebarPropDefs } from '@components/sidebar/sidebar.props';
import { extractProps } from '@helpers';
import type { GetPropDefTypes } from '@props';

const SidebarHeader: React.FC<React.ComponentProps<'div'>> = (props) => {
  return <Flex data-slot="sidebar-header" data-sidebar="header" gap={'2'} direction={'column'} p={'2'} {...props} />;
};

const SidebarContent: React.FC<React.ComponentProps<'div'>> = ({ className, ...props }) => {
  return (
    <Flex
      data-slot="sidebar-content"
      data-sidebar="content"
      direction={'column'}
      overflow={'auto'}
      gap={'2'}
      flexBasis={'0'}
      flexGrow={'1'}
      flexShrink={'1'}
      {...props}
      className={clsx('rt-sidebar-content', className)}
    />
  );
};

type SideBarContainerProps = React.ComponentProps<'div'> & GetPropDefTypes<typeof sidebarPropDefs>;

const SidebarContainer: React.FC<SideBarContainerProps> = (p): JSX.Element => {
  const { state } = useSidebar();
  const { className, children, ...props } = extractProps(p, sidebarPropDefs);

  if (p.collapsible === 'none') {
    return (
      <Flex
        data-slot="sidebar"
        direction={'column'}
        width={'var(--sidebar-width)'}
        className={clsx('rt-sidebar', className)}
        {...props}
      >
        {children}
      </Flex>
    );
  }

  return (
    <div
      className="rt-sidebar"
      data-state={state}
      data-collapsible={state === 'collapsed' ? p.collapsible : ''}
      data-type={p.type}
      data-side={p.side}
      data-slot="sidebar"
    >
      {/* This is what handles the sidebar gap on desktop */}
      <div data-slot="sidebar-gap" className={clsx('rt-sidebar-gap', p.type)} />
      <div data-slot="sidebar-container" className={clsx('rt-sidebar-container', p.side, p.type, className)} {...props}>
        <div data-sidebar="sidebar" data-slot="sidebar-inner" className="rt-sidebar-inner">
          {children}
        </div>
      </div>
    </div>
  );
};

export { SidebarContent, SidebarHeader, SidebarContainer };
