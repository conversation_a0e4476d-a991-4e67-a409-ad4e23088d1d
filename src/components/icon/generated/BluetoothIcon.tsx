// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M256 289.229l267.063 222.771M523.063 512l229.291 191.266c20.86 17.399 20.86 45.611 0 63.010l-201.937 168.448c-10.095 8.422-27.354 2.458-27.354-9.451v-413.274zM523.063 512l-267.063 222.771M523.063 512l229.291-191.267c20.86-17.399 20.86-45.61 0-63.009l-201.937-168.449c-10.095-8.42-27.354-2.457-27.354 9.452v413.274z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bluetooth"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1474,
        "id": 546,
        "name": "bluetooth",
        "prevSize": 32,
        "code": 59750
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 103
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BluetoothIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BluetoothIcon = ({ size = 16, ...props }: BluetoothIconProps) => (
  <IcoMoon iconSet={iconSet} icon="bluetooth" size={size} {...props} />
);

BluetoothIcon.displayName = 'BluetoothIcon';

export default BluetoothIcon;
