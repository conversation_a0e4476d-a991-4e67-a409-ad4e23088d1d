// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 512l-377.87-213.333M512 512v426.667M512 512l188.937-106.667M889.869 298.667c3.942 6.633 6.131 14.333 6.131 22.356v381.953c0 15.723-8.401 30.208-21.948 37.845l-341.333 192.397c-6.443 3.631-13.581 5.449-20.719 5.449M889.869 298.667c-3.78-6.365-9.182-11.747-15.817-15.486l-341.333-192.399c-12.885-7.264-28.553-7.264-41.438 0l-170.668 96.2M889.869 298.667l-188.932 106.667M134.13 298.667c3.783-6.365 9.183-11.747 15.816-15.486l170.667-96.2M134.13 298.667c-3.942 6.633-6.13 14.333-6.13 22.356v381.953c0 15.723 8.401 30.208 21.946 37.845l341.335 192.397c6.443 3.631 13.581 5.449 20.719 5.449M320.613 186.981l380.324 218.353M700.937 405.333v170.667"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "package-2"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1801,
        "id": 219,
        "name": "package-2",
        "prevSize": 32,
        "code": 60077
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 430
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface Package2IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const Package2Icon = ({ size = 16, ...props }: Package2IconProps) => (
  <IcoMoon iconSet={iconSet} icon="package-2" size={size} {...props} />
);

Package2Icon.displayName = 'Package2Icon';

export default Package2Icon;
