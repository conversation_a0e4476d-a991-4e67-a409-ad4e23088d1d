// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M693.333 149.333l32 0.119v-0.119h-32zM352 149.333h-32l0.001 0.119 32-0.119zM826.347 873.212v0zM777.212 922.347v0zM826.347 214.79v0zM777.212 165.654v0zM268.123 165.654v0zM218.987 214.79v0zM707.785 905.587c-17.647 0.956-31.181 16.034-30.225 33.681s16.034 31.181 33.681 30.225l-3.456-63.906zM170.746 324.072c-0.126 17.673 14.098 32.101 31.771 32.227s32.102-14.098 32.227-31.771l-63.998-0.456zM458.099 449.161v0zM584.567 570.573v0zM225.014 930.295v0zM190.053 896.734v0zM564.318 930.295v0zM599.279 896.734v0zM190.053 468.599v0zM225.014 435.038v0zM600.815 593.067v0zM416 85.333v32h213.333v-64h-213.333v32zM629.333 213.333v-32h-213.333v64h213.333v-32zM629.333 85.333v32c17.673 0 32 14.327 32 32h64c0-53.019-42.982-96-96-96v32zM416 85.333v-32c-53.019 0-96 42.981-96 96h64c0-17.673 14.327-32 32-32v-32zM842.667 360.533h-32v366.933h64v-366.933h-32zM842.667 727.467h-32c0 39.441-0.030 66.935-1.916 88.277-1.847 20.915-5.291 32.841-10.325 41.83l55.842 31.27c11.285-20.151 16.004-42.231 18.236-67.465 2.193-24.815 2.163-55.62 2.163-93.914h-32zM826.347 873.212l-27.921-15.637c-8.649 15.445-21.406 28.203-36.851 36.851l31.27 55.842c25.745-14.421 47.002-35.678 61.423-61.423l-27.921-15.633zM842.667 360.533h32c0-38.293 0.030-69.099-2.163-93.911-2.231-25.235-6.95-47.315-18.236-67.468l-55.842 31.272c5.035 8.989 8.478 20.914 10.325 41.832 1.886 21.34 1.916 48.835 1.916 88.276h32zM777.212 165.654l-15.637 27.92c15.445 8.651 28.203 21.405 36.851 36.852l55.842-31.272c-14.421-25.745-35.678-47.002-61.423-61.42l-15.633 27.92zM268.123 165.654l-15.636-27.92c-25.745 14.418-47.002 35.675-61.42 61.42l55.84 31.272c8.651-15.447 21.405-28.201 36.852-36.852l-15.636-27.92zM352.002 149.817l-0.885-31.988c-39.314 1.088-71.101 4.489-98.629 19.905l31.272 55.84c12.72-7.123 30.455-10.698 69.128-11.769l-0.885-31.988zM416 213.333v-32c-17.591 0-31.869-14.197-31.999-31.753l-63.998 0.474c0.39 52.691 43.219 95.279 95.997 95.279v-32zM352.002 149.817l32-0.119-0.002-0.484-63.999 0.237 0.002 0.483 32-0.118zM693.333 149.817l-0.887 31.988c38.673 1.071 56.41 4.646 69.129 11.769l31.27-55.84c-27.529-15.416-59.315-18.816-98.628-19.905l-0.883 31.988zM693.333 149.333l-32-0.119v0.484l64 0.237v-0.483l-32-0.119zM693.333 149.817l-32-0.237c-0.132 17.556-14.409 31.753-32 31.753v64c52.779 0 95.607-42.589 95.996-95.279l-31.996-0.237zM709.513 937.54l1.728 31.953c31.373-1.698 57.916-5.961 81.604-19.226l-31.27-55.842c-10.688 5.986-25.289 9.621-53.79 11.162l1.728 31.953zM202.746 324.3l31.999 0.228c0.399-55.965 3.297-78.272 12.162-94.102l-55.84-31.272c-18.257 32.601-19.945 72.154-20.321 124.918l31.999 0.228zM480 938.667v-32h-170.667v64h170.667v-32zM181.333 815.787h32v-266.24h-64v266.24h32zM608 624.879h-32v190.908h64v-190.908h-32zM458.099 449.161l-22.161 23.083 126.468 121.412 44.322-46.165-126.468-121.414-22.161 23.084zM309.333 426.667v32h92.196v-64h-92.196v32zM309.333 938.667v-32c-22.909 0-38.243-0.021-50.042-0.947-11.462-0.9-16.806-2.5-20.217-4.169l-28.121 57.489c13.702 6.703 28.115 9.289 43.334 10.483 14.882 1.165 33.151 1.143 55.046 1.143v-32zM181.333 815.787h-32c0 20.958-0.027 38.677 1.205 53.154 1.267 14.895 4.035 29.205 11.249 42.795l56.531-30.003c-1.507-2.837-3.098-7.497-4.011-18.219-0.948-11.132-0.975-25.673-0.975-47.727h-32zM225.014 930.295l14.060-28.745c-9.144-4.471-16.334-11.486-20.756-19.819l-56.531 30.003c10.918 20.57 28.204 37.052 49.166 47.305l14.061-28.745zM480 938.667v32c21.897 0 40.162 0.021 55.044-1.143 15.219-1.195 29.632-3.78 43.337-10.483l-28.122-57.489c-3.413 1.668-8.755 3.268-20.215 4.169-11.802 0.926-27.136 0.947-50.044 0.947v32zM608 815.787h-32c0 22.054-0.026 36.595-0.973 47.727-0.913 10.722-2.505 15.381-4.011 18.219l56.529 30.003c7.215-13.589 9.98-27.9 11.251-42.795 1.229-14.477 1.203-32.196 1.203-53.154h-32zM564.318 930.295l14.063 28.745c20.962-10.253 38.246-26.735 49.165-47.305l-56.529-30.003c-4.425 8.333-11.614 15.347-20.757 19.819l14.059 28.745zM181.333 549.547h32c0-22.054 0.027-36.595 0.975-47.727 0.913-10.722 2.504-15.381 4.011-18.219l-56.531-30.003c-7.213 13.589-9.981 27.9-11.249 42.795-1.232 14.477-1.205 32.196-1.205 53.154h32zM309.333 426.667v-32c-21.895 0-40.164-0.023-55.046 1.144-15.219 1.194-29.632 3.779-43.334 10.481l28.121 57.49c3.411-1.668 8.756-3.268 20.217-4.169 11.798-0.926 27.133-0.947 50.042-0.947v-32zM190.053 468.599l28.266 15.002c4.422-8.333 11.612-15.347 20.756-19.819l-28.121-57.49c-20.962 10.253-38.249 26.736-49.166 47.306l28.265 15.002zM458.099 449.161l22.161-23.084c-9.63-9.242-20.766-16.585-32.781-21.836l-25.623 58.65c5.259 2.295 10.029 5.461 14.082 9.353l22.161-23.083zM434.667 433.566l12.813-29.325c-14.34-6.263-29.985-9.574-45.95-9.574v64c7.122 0 14.048 1.481 20.326 4.224l12.811-29.325zM434.667 433.566h-32v82.701h64v-82.701h-32zM608 624.879h32c0-15.906-3.584-31.415-10.27-45.521l-57.83 27.418c2.692 5.683 4.1 11.844 4.1 18.103h32zM600.815 593.067l28.915-13.709c-5.602-11.819-13.372-22.626-23.002-31.868l-44.322 46.165c4.053 3.891 7.232 8.35 9.493 13.12l28.915-13.709zM514.667 593.067v32h86.148v-64h-86.148v32zM434.667 516.267h-32c0 61.308 51.388 108.8 112 108.8v-64c-27.755 0-48-21.278-48-44.8h-32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "clipboard-document"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1558,
        "id": 462,
        "name": "clipboard-document",
        "prevSize": 32,
        "code": 59834
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 187
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ClipboardDocumentIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ClipboardDocumentIcon = ({ size = 16, ...props }: ClipboardDocumentIconProps) => (
  <IcoMoon iconSet={iconSet} icon="clipboard-document" size={size} {...props} />
);

ClipboardDocumentIcon.displayName = 'ClipboardDocumentIcon';

export default ClipboardDocumentIcon;
