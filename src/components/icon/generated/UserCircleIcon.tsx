// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M247.698 853.525c22.884-93.594 99.22-167.381 196.56-173.449 45.909-2.859 89.549-2.867 135.373-0.030 97.941 6.063 174.613 80.704 197.052 175.091M654.221 417.185c0 78.546-63.676 142.222-142.221 142.222s-142.222-63.676-142.222-142.222c0-78.547 63.677-142.223 142.222-142.223s142.221 63.675 142.221 142.223zM938.667 512c0 235.639-191.027 426.667-426.667 426.667-235.642 0-426.667-191.027-426.667-426.667 0-235.642 191.025-426.667 426.667-426.667 235.639 0 426.667 191.025 426.667 426.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "user-circle"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1974,
        "id": 46,
        "name": "user-circle",
        "prevSize": 32,
        "code": 60250
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 603
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface UserCircleIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const UserCircleIcon = ({ size = 16, ...props }: UserCircleIconProps) => (
  <IcoMoon iconSet={iconSet} icon="user-circle" size={size} {...props} />
);

UserCircleIcon.displayName = 'UserCircleIcon';

export default UserCircleIcon;
