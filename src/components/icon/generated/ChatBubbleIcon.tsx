// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M884.215 720.725v0zM932.561 915.84v0zM882.957 730.458v0zM720.725 884.215v0zM915.84 932.561v0zM730.458 882.957v0zM934.571 929.664v0zM929.664 934.571v0zM341.333 405.333c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM682.667 469.333c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM341.333 554.667c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM682.667 618.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM512 938.667v-32c-217.968 0-394.667-176.7-394.667-394.667h-64c0 253.316 205.352 458.667 458.667 458.667v-32zM85.333 512h32c0-217.968 176.698-394.667 394.667-394.667v-64c-253.315 0-458.667 205.352-458.667 458.667h32zM512 85.333v32c217.967 0 394.667 176.698 394.667 394.667h64c0-253.315-205.35-458.667-458.667-458.667v32zM938.667 512h-32c0 70.187-18.295 136.004-50.351 193.050l55.795 31.351c37.295-66.368 58.556-142.95 58.556-224.401h-32zM932.561 915.84l30.912-8.269-49.604-185.382-61.824 16.542 49.6 185.382 30.916-8.273zM720.725 884.215l-15.676-27.9c-57.045 32.055-122.863 50.351-193.050 50.351v64c81.451 0 158.033-21.261 224.401-58.556l-15.676-27.895zM915.84 932.561l8.273-30.916-185.382-49.6-16.542 61.824 185.382 49.604 8.269-30.912zM932.561 915.84l-30.916 8.273c0.525 1.95 0.883 3.302 1.165 4.425 0.286 1.143 0.35 1.515 0.341 1.468 0-0.013-0.222-1.207-0.188-3.093 0.034-2.039 0.367-5.154 1.737-8.717l59.746 22.942c3.657-9.519 2.441-18.133 1.783-21.956-0.683-3.989-1.92-8.474-2.756-11.61l-30.912 8.269zM915.84 932.561l-8.269 30.912c3.136 0.836 7.62 2.074 11.61 2.756 3.823 0.657 12.437 1.873 21.956-1.783l-22.942-59.746c3.563-1.37 6.677-1.702 8.717-1.737 1.886-0.034 3.081 0.188 3.093 0.188 0.047 0.009-0.324-0.055-1.468-0.341-1.122-0.282-2.475-0.64-4.425-1.165l-8.273 30.916zM934.571 929.664l-29.871-11.469c2.385-6.208 7.287-11.11 13.495-13.495l22.942 59.746c10.722-4.117 19.191-12.587 23.309-23.309l-29.875-11.473zM341.333 437.333v32h341.333v-64h-341.333v32zM341.333 586.667v32h341.333v-64h-341.333v32zM720.725 884.215l15.676 27.895c-4.139 2.325-9.225 3.093-14.212 1.758l16.542-61.824c-11.554-3.089-23.625-1.382-33.685 4.271l15.68 27.9zM884.215 720.725l-27.9-15.68c-5.653 10.061-7.36 22.131-4.271 33.685l61.824-16.542c1.335 4.988 0.567 10.074-1.758 14.212l-27.895-15.676z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "chat-bubble"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1539,
        "id": 481,
        "name": "chat-bubble",
        "prevSize": 32,
        "code": 59815
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 168
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ChatBubbleIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ChatBubbleIcon = ({ size = 16, ...props }: ChatBubbleIconProps) => (
  <IcoMoon iconSet={iconSet} icon="chat-bubble" size={size} {...props} />
);

ChatBubbleIcon.displayName = 'ChatBubbleIcon';

export default ChatBubbleIcon;
