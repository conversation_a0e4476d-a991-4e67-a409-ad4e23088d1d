// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M396.851 300.354h-26.681c-63.055 0-114.17 47.4-114.17 105.871v211.743c0 58.47 51.116 105.869 114.17 105.869h26.681c27.004 0 53.133 8.875 73.745 25.050l134.652 105.655c55.689 43.691 141.312 12.156 147.755-55.223 19.938-208.576 20.002-368.499 0.154-574.75-6.477-67.315-92.023-98.764-147.661-55.11l-134.899 105.846c-20.612 16.175-46.742 25.050-73.745 25.050z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "volume"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1998,
        "id": 22,
        "name": "volume",
        "prevSize": 32,
        "code": 60274
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 627
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface VolumeIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const VolumeIcon = ({ size = 16, ...props }: VolumeIconProps) => (
  <IcoMoon iconSet={iconSet} icon="volume" size={size} {...props} />
);

VolumeIcon.displayName = 'VolumeIcon';

export default VolumeIcon;
