// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M704 512c0 106.039-85.961 192-192 192s-192-85.961-192-192M704 512c0-106.039-85.961-192-192-192s-192 85.961-192 192M704 512h234.667M320 512h-234.667"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "git-commit"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1688,
        "id": 332,
        "name": "git-commit",
        "prevSize": 32,
        "code": 59964
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 317
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface GitCommitIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const GitCommitIcon = ({ size = 16, ...props }: GitCommitIconProps) => (
  <IcoMoon iconSet={iconSet} icon="git-commit" size={size} {...props} />
);

GitCommitIcon.displayName = 'GitCommitIcon';

export default GitCommitIcon;
