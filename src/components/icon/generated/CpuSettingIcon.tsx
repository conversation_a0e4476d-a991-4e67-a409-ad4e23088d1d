// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M369.778 184.561c-30.769 1.813-62.776 4.184-96.768 7.114-43.274 3.73-77.83 37.82-81.833 81.091-3.15 34.048-5.578 66.139-7.343 97.012M369.778 184.561v-99.228M369.778 184.561c49.573-2.921 95.937-4.392 142.222-4.413M512 180.148v-94.815M512 180.148c46.929-0.021 93.777 1.449 143.808 4.413M655.808 184.561c30.276 1.793 61.722 4.133 95.053 7.020 43.486 3.766 78.089 38.199 81.937 81.699 2.991 33.833 5.355 65.749 7.117 96.499M655.808 184.561v-99.228M839.915 369.778h98.752M839.915 369.778c0.917 16.086 1.673 31.853 2.27 47.407M369.778 839.569c-30.292-1.758-61.724-4.058-95.020-6.895-44.063-3.759-78.898-38.98-82.497-83.076-2.727-33.404-5.049-64.956-6.909-95.377M369.778 839.569v99.098M369.778 839.569c24.312 1.412 47.89 2.475 71.11 3.191M185.352 654.221h-100.019M185.352 654.221c-3.018-49.361-4.817-95.74-5.149-142.221M180.204 512h-94.87M180.204 512c-0.329-46.298 0.798-92.69 3.631-142.222M183.834 369.778h-98.501M440.887 699.742c-20.431-1.058-41.679-2.56-64.454-4.501-25.179-2.146-45.085-22.272-47.142-47.471-8.174-100.143-9.993-171.157-0.619-272.475 2.287-24.726 22.034-44.207 46.762-46.338 101.647-8.76 172.226-8.786 273.056-0.054 24.849 2.152 44.625 21.828 46.822 46.685 1.003 11.35 1.882 22.322 2.641 33.004M559.407 649.626v151.415c0 22.123 11.593 42.564 30.413 53.623l128.806 75.708c18.816 11.059 42.005 11.059 60.821 0l128.806-75.708c18.82-11.059 30.413-31.501 30.413-53.623v-151.415c0-22.123-11.593-42.564-30.413-53.623l-128.806-75.708c-18.816-11.059-42.005-11.059-60.821 0l-128.806 75.708c-18.82 11.059-30.413 31.501-30.413 53.623zM820.147 725.333c0 39.275-31.838 71.113-71.108 71.113-39.275 0-71.113-31.838-71.113-71.113s31.838-71.113 71.113-71.113c39.27 0 71.108 31.838 71.108 71.113z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "cpu-setting"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1588,
        "id": 432,
        "name": "cpu-setting",
        "prevSize": 32,
        "code": 59864
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 217
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CpuSettingIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CpuSettingIcon = ({ size = 16, ...props }: CpuSettingIconProps) => (
  <IcoMoon iconSet={iconSet} icon="cpu-setting" size={size} {...props} />
);

CpuSettingIcon.displayName = 'CpuSettingIcon';

export default CpuSettingIcon;
