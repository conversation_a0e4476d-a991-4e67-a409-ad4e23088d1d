// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M731.614 85.333l103.526 103.527M835.14 188.86l103.526 103.526M835.14 188.86l-109.807 109.807M552.789 264.158l-34.509-34.509M552.789 264.158l34.509 34.509M552.789 264.158l-135.604 135.604M180.148 843.853l-12.229-134.515c-3.184-35.029 9.355-69.666 34.227-94.537l37.847-37.85M180.148 843.853l134.516 12.228c35.030 3.183 69.664-9.357 94.536-34.227M180.148 843.853l-94.815 94.814M587.298 298.667l35.494-35.496c18.513-18.514 48.533-18.514 67.046 0l35.494 35.496M587.298 298.667l90.202 90.2M725.333 298.667l35.494 35.496c14.801 14.801 17.771 36.954 8.905 54.704M239.993 576.951l58.673 58.675M239.993 576.951l88.657-88.657M328.65 488.294l64.831 64.832M328.65 488.294l88.535-88.533M417.185 399.761l64.833 64.832M870.080 716.45c0 20.83-4.143 40.695-11.657 58.807M870.080 716.45c0-20.83-4.143-40.691-11.657-58.807M870.080 716.45h68.587M858.423 775.258c-7.727 18.633-19.012 35.413-33.007 49.493M858.423 775.258c15.543-37.568 15.543-80.047 0-117.615M858.423 775.258c-7.441 17.984-18.445 34.842-33.007 49.493M825.417 824.751c-0.111 0.111-0.222 0.222-0.333 0.333M825.084 825.084c-0.111 0.111-0.222 0.222-0.333 0.333-29.956 29.777-69.129 44.663-108.301 44.663M825.084 825.084l48.499 48.499M657.643 858.423c-18.556-7.693-35.277-18.918-49.323-32.841M657.643 858.423c-17.916-7.411-34.709-18.359-49.323-32.841M657.643 858.423c18.782 7.774 38.797 11.657 58.807 11.657M608.32 825.583c0 0-0.337-0.333-0.503-0.499M607.817 825.084c-0.166-0.166-0.333-0.333-0.499-0.503M607.817 825.084l-48.495 48.499M607.317 824.58c-27.507-27.759-44.497-65.963-44.497-108.13M607.317 824.58c-29.666-29.935-44.497-69.030-44.497-108.13M562.82 716.45c0-20.83 4.147-40.691 11.657-58.807M562.82 716.45h-68.587M562.82 716.45c0-20.011 3.887-40.026 11.657-58.807M574.477 657.643c7.757-18.705 19.102-35.546 33.178-49.664M574.477 657.643c7.471-18.052 18.53-34.97 33.178-49.664M607.654 607.979l0.162-0.162M607.817 607.817l0.162-0.162M607.817 607.817l-48.495-48.495M607.979 607.654c27.635-27.55 65.702-44.642 107.763-44.834M607.979 607.654c29.798-29.709 68.753-44.655 107.763-44.834M715.742 562.82c0.235 0 0.474 0 0.708 0M716.45 562.82c0.239 0 0.474 0 0.708 0M716.45 562.82v-68.587M717.158 562.82c20.574 0.094 40.192 4.233 58.099 11.657M717.158 562.82c19.776 0.090 39.539 3.977 58.099 11.657M775.258 574.477c18.778 7.787 35.674 19.191 49.826 33.34M775.258 574.477c18.121 7.497 35.098 18.611 49.826 33.34M825.084 607.817c14.153 14.153 25.553 31.049 33.34 49.826M825.084 607.817c14.729 14.729 25.843 31.706 33.34 49.826M825.084 607.817l48.499-48.495M716.45 494.234h-32.922M716.45 494.234h32.922M938.667 716.45v-32.922M938.667 716.45v32.922M716.45 938.667h-32.922M716.45 938.667h32.922M716.45 938.667v-68.587M494.234 716.45v-32.922M494.234 716.45v32.922M873.583 559.322l-23.279-23.279M873.583 559.322l23.275 23.279M873.583 873.583l23.275-23.279M873.583 873.583l-23.279 23.275M559.322 873.583l-23.279-23.279M559.322 873.583l23.279 23.275M559.322 559.322l23.279-23.279M559.322 559.322l-23.279 23.279"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "virus-lab-research"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1990,
        "id": 30,
        "name": "virus-lab-research",
        "prevSize": 32,
        "code": 60266
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 619
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface VirusLabResearchIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const VirusLabResearchIcon = ({ size = 16, ...props }: VirusLabResearchIconProps) => (
  <IcoMoon iconSet={iconSet} icon="virus-lab-research" size={size} {...props} />
);

VirusLabResearchIcon.displayName = 'VirusLabResearchIcon';

export default VirusLabResearchIcon;
