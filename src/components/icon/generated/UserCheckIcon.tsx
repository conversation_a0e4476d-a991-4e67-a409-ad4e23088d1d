// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M651.145 569.903c-16.87-5.649-34.645-9.297-53.129-10.658-63.91-4.715-123.473-4.706-187.503 0.026-112.072 8.282-198.174 100.489-214.226 213.325l-3.072 21.589c-9.262 65.101 35.396 125.833 99.866 132.809 49.594 5.367 118.117 9.647 168.486 11.674M551.040 795.085l88.119 93.589c2.953 3.136 8.158 3.174 11.162 0.077l181.679-187.281M668.105 251.216c0 91.614-73.378 165.882-163.891 165.882-90.518 0-163.896-74.268-163.896-165.882s73.378-165.883 163.896-165.883c90.513 0 163.891 74.268 163.891 165.883z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "user-check"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1973,
        "id": 47,
        "name": "user-check",
        "prevSize": 32,
        "code": 60249
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 602
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface UserCheckIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const UserCheckIcon = ({ size = 16, ...props }: UserCheckIconProps) => (
  <IcoMoon iconSet={iconSet} icon="user-check" size={size} {...props} />
);

UserCheckIcon.displayName = 'UserCheckIcon';

export default UserCheckIcon;
