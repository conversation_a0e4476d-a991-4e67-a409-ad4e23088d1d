// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M298.432 342.032l-89.634 155.141c-26.486 45.841-21.304 102.566 13.087 143.266l227.958 269.781c16.026 18.961 39.091 28.446 62.157 28.446M298.432 342.032h427.136M298.432 342.032l-12.020-115.578c-7.858-75.558 53.848-141.121 132.817-141.121h185.54c78.972 0 140.676 65.563 132.817 141.121l-12.019 115.578M725.568 342.032l89.634 155.141c26.487 45.841 21.303 102.566-13.086 143.266l-227.959 269.781c-16.026 18.965-39.091 28.446-62.157 28.446M512 677.926c40.785 0 73.847-31.838 73.847-71.113s-33.062-71.108-73.847-71.108c-40.785 0-73.847 31.834-73.847 71.108s33.062 71.113 73.847 71.113zM512 677.926v260.74"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "pen-tool"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1818,
        "id": 202,
        "name": "pen-tool",
        "prevSize": 32,
        "code": 60094
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 447
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface PenToolIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const PenToolIcon = ({ size = 16, ...props }: PenToolIconProps) => (
  <IcoMoon iconSet={iconSet} icon="pen-tool" size={size} {...props} />
);

PenToolIcon.displayName = 'PenToolIcon';

export default PenToolIcon;
