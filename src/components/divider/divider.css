.rt-divider {
  display: block;

  &:where(.rt-color-none-opaque) {
    background: var(--separators-non-opaque);
  }

  &:where(.rt-color-opaque) {
    background: var(--separators-opaque);
  }

  &:where(.rt-color-inverted-primary) {
    background: var(--separators-inverted-primary);
  }

  &:where(.rt-color-inverted-secondary) {
    background: var(--separators-inverted-secondary);
  }

  &:where(.rt-color-card-photo-stroke) {
    background: var(--separators-card-photo-stroke);
  }
}

/***************************************************************************************************
 *                                                                                                 *
 * ORIENTATION                                                                                     *
 *                                                                                                 *
 ***************************************************************************************************/

@breakpoints {
  .rt-divider {
    &:where(.rt-r-orientation-horizontal) {
      width: var(--divider-size);
      height: 1px;
    }

    &:where(.rt-r-orientation-vertical) {
      width: 1px;
      height: var(--divider-size);
    }
  }
}

/***************************************************************************************************
 *                                                                                                 *
 * SIZES                                                                                           *
 *                                                                                                 *
 ***************************************************************************************************/

@breakpoints {
  .rt-divider {
    &:where(.rt-r-size-1) {
      --divider-size: var(--spatial-4);
    }

    &:where(.rt-r-size-2) {
      --divider-size: var(--spatial-6);
    }

    &:where(.rt-r-size-3) {
      --divider-size: var(--spatial-9);
    }

    &:where(.rt-r-size-4) {
      --divider-size: 100%;
    }
  }
}
