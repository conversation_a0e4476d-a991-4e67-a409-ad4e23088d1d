// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M132.741 938.667v-532.493c0-36.327 16.803-70.63 45.553-92.995l260.742-202.83c42.876-33.354 103.053-33.354 145.929 0l260.74 202.83c28.753 22.365 45.555 56.668 45.555 92.995v532.493M132.741 938.667h189.63M132.741 938.667h-47.407M322.371 938.667h379.257M322.371 938.667v-306.829c0-65.173 53.062-118.007 118.517-118.007h142.225c65.455 0 118.515 52.834 118.515 118.007v306.829M701.628 938.667h189.632M891.26 938.667h47.407M440.887 711.428v62.025M417.185 372.217h189.629M654.221 159.798v-47.204c0-13.035 10.615-23.602 23.706-23.602h142.221c13.090 0 23.706 10.567 23.706 23.602v188.816"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "house"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1717,
        "id": 303,
        "name": "house",
        "prevSize": 32,
        "code": 59993
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 346
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface HouseIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const HouseIcon = ({ size = 16, ...props }: HouseIconProps) => (
  <IcoMoon iconSet={iconSet} icon="house" size={size} {...props} />
);

HouseIcon.displayName = 'HouseIcon';

export default HouseIcon;
