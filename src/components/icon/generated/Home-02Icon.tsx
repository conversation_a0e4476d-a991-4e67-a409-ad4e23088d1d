// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M397.81 755.119h228.378M461.329 100.747l-293.643 196.666c-41.237 27.619-64.416 75.456-60.615 125.1l29.789 389.050c5.492 71.723 65.022 127.104 136.624 127.104h477.030c71.603 0 131.132-55.381 136.623-127.104l29.79-389.050c3.802-49.644-19.375-97.481-60.612-125.1l-293.645-196.666c-30.682-20.551-70.66-20.551-101.342 0z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "home-02"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1712,
        "id": 308,
        "name": "home-02",
        "prevSize": 32,
        "code": 59988
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 341
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface Home-02IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const Home-02Icon = ({ size = 16, ...props }: Home-02IconProps) => (
  <IcoMoon iconSet={iconSet} icon="home-02" size={size} {...props} />
);

Home-02Icon.displayName = 'Home-02Icon';

export default Home-02Icon;
