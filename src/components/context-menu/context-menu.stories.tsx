import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react-vite';

import { Button, ContextMenu } from '@components';
import { contextMenuContentPropDefs } from '@components/context-menu/context-menu.props';

interface ContextMenuStoryProps {
  size?: (typeof contextMenuContentPropDefs.size.values)[number];
  variant?: (typeof contextMenuContentPropDefs.variant.values)[number];
  highContrast?: boolean;
}

const StoryContextMenu = ({
  size = contextMenuContentPropDefs.size.default,
  variant = contextMenuContentPropDefs.variant.default,
  highContrast = false,
}: ContextMenuStoryProps) => {
  return (
    <ContextMenu.Root>
      <ContextMenu.Trigger>
        <Button>Right click me</Button>
      </ContextMenu.Trigger>
      <ContextMenu.Content size={size} variant={variant} highContrast={highContrast}>
        <ContextMenu.Item shortcut="⌘ E">Edit</ContextMenu.Item>
        <ContextMenu.Item shortcut="⌘ D">Duplicate</ContextMenu.Item>
        <ContextMenu.Separator />
        <ContextMenu.Item shortcut="⌘ N">Archive</ContextMenu.Item>
        <ContextMenu.Sub>
          <ContextMenu.SubTrigger>More</ContextMenu.SubTrigger>
          <ContextMenu.SubContent>
            <ContextMenu.Item>Move to project…</ContextMenu.Item>
            <ContextMenu.Item>Move to folder…</ContextMenu.Item>
            <ContextMenu.Separator />
            <ContextMenu.Item>Advanced options…</ContextMenu.Item>
          </ContextMenu.SubContent>
        </ContextMenu.Sub>
        <ContextMenu.Separator />
        <ContextMenu.Item>Share</ContextMenu.Item>
        <ContextMenu.Item>Add to favorites</ContextMenu.Item>
        <ContextMenu.Separator />
        <ContextMenu.Item shortcut="⌘ ⌫" color="red">
          Delete
        </ContextMenu.Item>
      </ContextMenu.Content>
    </ContextMenu.Root>
  );
};

const meta = {
  title: 'DesignSystem/ContextMenu',
  component: StoryContextMenu,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    size: { control: 'inline-radio', options: contextMenuContentPropDefs.size.values },
    variant: { control: 'inline-radio', options: contextMenuContentPropDefs.variant.values },
    highContrast: { control: 'boolean' },
  },
} satisfies Meta<typeof StoryContextMenu>;

export default meta;
type Story = StoryObj<typeof meta>;

export const _default: Story = {
  args: {
    size: contextMenuContentPropDefs.size.default,
    variant: contextMenuContentPropDefs.variant.default,
    highContrast: false,
  },
  render: StoryContextMenu,
};
