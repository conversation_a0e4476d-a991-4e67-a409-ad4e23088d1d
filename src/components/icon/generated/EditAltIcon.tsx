// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M747.695 276.31l-87.607 87.607M938.667 938.667h-853.333M112.984 938.496l169.502-19.746c49.894-5.815 96.366-28.301 131.885-63.821l473.492-473.493c67.738-67.738 67.738-177.562 0-245.299s-177.562-67.738-245.299 0l-473.492 473.493c-35.519 35.52-58.007 81.988-63.82 131.883l-19.748 169.502c-1.85 15.881 11.6 29.333 27.48 27.482z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "edit-alt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1614,
        "id": 406,
        "name": "edit-alt",
        "prevSize": 32,
        "code": 59890
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 243
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface EditAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const EditAltIcon = ({ size = 16, ...props }: EditAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="edit-alt" size={size} {...props} />
);

EditAltIcon.displayName = 'EditAltIcon';

export default EditAltIcon;
