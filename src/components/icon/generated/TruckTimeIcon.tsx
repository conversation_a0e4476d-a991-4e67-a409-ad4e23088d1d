// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M907.234 487.104v0zM784.235 356.273v0zM85.333 686.545l-31.744-4.041c-0.171 1.34-0.256 2.688-0.256 4.041h32zM598.519 741.333c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM602.103 195.328c-14.225-10.49-34.257-7.462-44.745 6.761-10.492 14.224-7.462 34.257 6.758 44.747l37.986-51.508zM132.421 570.069c2.232-17.532-10.171-33.553-27.703-35.785s-33.553 10.172-35.785 27.703l63.488 8.081zM328.296 256c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM296.296 325.818l16.242 27.572c9.764-5.752 15.758-16.239 15.758-27.572h-32zM220.795 333.156c-15.227 8.97-20.3 28.586-11.329 43.814s28.586 20.3 43.814 11.329l-32.484-55.143zM417.185 802.91h-32c0 33.186-27.567 61.090-62.815 61.090v64c69.482 0 126.816-55.454 126.816-125.090h-32.001zM322.371 896v-32c-35.248 0-62.815-27.904-62.815-61.090h-64c0 69.636 57.333 125.090 126.815 125.090v-32zM227.555 802.91h32c0-33.19 27.567-61.090 62.815-61.090v-64c-69.482 0-126.815 55.454-126.815 125.090h32zM322.371 709.82v32c35.248 0 62.815 27.9 62.815 61.090h64.001c0-69.636-57.334-125.090-126.816-125.090v32zM796.446 802.91h-32c0 33.186-27.567 61.090-62.818 61.090v64c69.483 0 126.818-55.454 126.818-125.090h-32zM701.628 896v-32c-35.247 0-62.814-27.904-62.814-61.090h-64c0 69.636 57.335 125.090 126.814 125.090v-32zM606.814 802.91h32c0-33.19 27.567-61.090 62.814-61.090v-64c-69.478 0-126.814 55.454-126.814 125.090h32zM701.628 709.82v32c35.251 0 62.818 27.9 62.818 61.090h64c0-69.636-57.335-125.090-126.818-125.090v32zM697.148 318.836v-32h-66.628v64h66.628v-32zM630.519 318.836h32v-4.655h-64v4.655h32zM784.235 356.273l23.313-21.92c-28.553-30.368-68.595-47.517-110.4-47.517v64c24.346 0 47.45 9.996 63.774 27.356l23.313-21.919zM938.667 566.029h32c0-37.474-14.383-73.472-40.115-100.847l-46.63 43.84c14.682 15.616 22.746 35.977 22.746 57.007h32zM938.667 686.545h-32c0 46.042-38.178 84.365-86.519 84.365v64c82.573 0 150.519-65.873 150.519-148.365h-32zM203.852 802.91v-32c-48.339 0-86.519-38.323-86.519-84.365h-64c0 82.492 67.945 148.365 150.519 148.365v-32zM796.446 802.91v32h23.701v-64h-23.701v32zM630.519 318.836h-32v422.497h64v-422.497h-32zM203.852 802.91v32h23.703v-64h-23.703v32zM417.185 802.91v32h189.629v-64h-189.629v32zM907.234 487.104l23.317-21.922-61.504-65.414-46.626 43.837 61.5 65.417 23.313-21.918zM845.734 421.688l23.313-21.92-61.5-65.415-46.626 43.839 61.5 65.413 23.313-21.917zM845.734 421.688v-32h-49.289v64h49.289v-32zM749.039 468.233h-32v55.403h64v-55.403h-32zM938.667 686.545h32v-69.82h-64v69.82h32zM938.667 616.725h32v-50.697h-64v50.697h32zM843.853 616.725v32h94.814v-64h-94.814v32zM749.039 523.635h-32c0 69.636 57.331 125.090 126.814 125.090v-64c-35.247 0-62.814-27.9-62.814-61.090h-32zM796.446 421.688v-32c-43.302 0-79.407 34.615-79.407 78.545h64c0-7.479 6.34-14.545 15.407-14.545v-32zM630.519 314.182h32c0-48.804-23.919-91.939-60.416-118.854l-37.986 51.508c21.077 15.544 34.402 40.009 34.402 67.345h32zM100.677 566.029l-31.744-4.041-15.343 120.516 63.487 8.081 15.344-120.516-31.744-4.041zM474.074 314.182h-32c0 84.601-70.016 154.183-157.629 154.183v64c121.847 0 221.629-97.135 221.629-218.183h-32zM284.445 500.365v-32c-87.613 0-157.63-69.582-157.63-154.183h-64c0 121.048 99.783 218.183 221.63 218.183v-32zM94.815 314.182h32c0-84.602 70.017-154.182 157.63-154.182v-64c-121.847 0-221.63 97.132-221.63 218.182h32zM284.445 128v32c87.613 0 157.629 69.58 157.629 154.182h64c0-121.050-99.782-218.182-221.629-218.182v32zM296.296 256h-32v69.818h64v-69.818h-32zM296.296 325.818l-16.242-27.572-59.259 34.909 32.484 55.143 59.259-34.909-16.242-27.572z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "truck-time"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1966,
        "id": 54,
        "name": "truck-time",
        "prevSize": 32,
        "code": 60242
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 595
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface TruckTimeIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const TruckTimeIcon = ({ size = 16, ...props }: TruckTimeIconProps) => (
  <IcoMoon iconSet={iconSet} icon="truck-time" size={size} {...props} />
);

TruckTimeIcon.displayName = 'TruckTimeIcon';

export default TruckTimeIcon;
