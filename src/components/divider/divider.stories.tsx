import type { Meta, StoryObj } from '@storybook/react-vite';
import { fn } from 'storybook/test';

import { Divider } from '@components/divider';
import { dividerPropDefs } from '@components/divider/divider.props';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: 'DesignSystem/Divider',
  component: Divider,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  // tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    size: { control: 'inline-radio', options: dividerPropDefs.size.values },
    orientation: { control: 'inline-radio', options: dividerPropDefs.orientation.values },
    color: { control: 'inline-radio', options: dividerPropDefs.color.values },
  },
  decorators: [
    (Story) => (
      <div style={{ height: '100vh', minWidth: '800px' }}>
        <Story />
      </div>
    ),
  ],
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: { onClick: fn() },
} satisfies Meta<typeof Divider>;

export default meta;
type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const _divider: Story = {
  args: {
    orientation: dividerPropDefs.orientation.default,
    size: dividerPropDefs.size.default,
    color: dividerPropDefs.color.default,
  },
};
