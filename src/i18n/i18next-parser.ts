#!/usr/bin/env node
import { Option, program } from 'commander';
import { spawn } from 'node:child_process';
import { existsSync, writeFileSync } from 'node:fs';
import { createRequire } from 'node:module';
import { tmpdir } from 'node:os';
import path, { join, resolve } from 'node:path';
import { pathToFileURL } from 'node:url';

const req = createRequire(import.meta.url);

type UserConfig = {
  input?: string[]; // glob patterns
  output?: string; // output template
  locales?: string[];
  namespace?: string; // default namespace
  [k: string]: unknown; // keep other keys (lexers, trans, etc.)
};

function splitList(val?: string | string[]): string[] | undefined {
  if (!val) return undefined;
  const parts = Array.isArray(val) ? val : [val];
  return parts
    .flatMap((p) => p.split(','))
    .map((s) => s.trim())
    .filter(Boolean);
}

const defaultConfig: UserConfig = {
  input: ['src/**/*.{js,jsx,ts,tsx}'],
  output: 'src/locales/$LOCALE/$NAMESPACE.json',
  locales: ['en', 'vi'],
  defaultNamespace: 'translation',
  defaultValue: '',
  keepRemoved: false,
  keySeparator: false,
  namespaceSeparator: false,
  lexers: {
    js: [
      {
        lexer: 'JavascriptLexer',
        functions: ['t', 'i18next.t', 'useTranslation', 'T', 'window.T'],
        functionsNamespace: [{ list: ['T', 'window.T'], namespace: '$NAMESPACE' }],
      },
    ],
    jsx: [
      {
        lexer: 'JsxLexer',
        functions: ['t', 'i18next.t', 'useTranslation', 'T', 'window.T'],
        functionsNamespace: [{ list: ['T', 'window.T'], namespace: '$NAMESPACE' }],
      },
    ],
    ts: [
      {
        lexer: 'JavascriptLexer',
        functions: ['t', 'i18next.t', 'useTranslation', 'T', 'window.T'],
        functionsNamespace: [{ list: ['T', 'window.T'], namespace: '$NAMESPACE' }],
      },
    ],
    tsx: [
      {
        lexer: 'JsxLexer',
        functions: ['t', 'i18next.t', 'useTranslation', 'T', 'window.T'],
        functionsNamespace: [{ list: ['T', 'window.T'], namespace: '$NAMESPACE' }],
      },
    ],
    default: ['JavascriptLexer'],
  },
  trans: {
    component: 'Trans',
    i18nKey: 'i18nKey',
    defaultsKey: 'defaults',
    extensions: ['.js', '.jsx', '.ts', '.tsx'],
    fallbackKey: true,
  },
};

async function loadConfig(configPath?: string): Promise<UserConfig> {
  const candidates = configPath
    ? [configPath]
    : ['i18n.config.js', 'i18n.config.mjs', 'i18next-parser.config.js', 'i18next-parser.config.mjs'];

  for (const c of candidates) {
    const abs = resolve(process.cwd(), c);
    if (existsSync(abs)) {
      const mod = await import(pathToFileURL(abs).href);
      return (mod.default ?? mod) as UserConfig;
    }
  }
  return {};
}

function buildEffectiveConfig(fileCfg: UserConfig, cli: UserConfig): Required<UserConfig> {
  const input = cli.input ?? fileCfg.input ?? defaultConfig.input;
  const output = cli.output ?? fileCfg.output ?? defaultConfig.output;
  const locales = cli.locales ?? fileCfg.locales ?? defaultConfig.locales;
  return { ...defaultConfig, ...fileCfg, ...cli, input, output, locales } as Required<UserConfig>;
}

function writeTempConfig(effective: UserConfig): string {
  const p = join(tmpdir(), `i18n-extract-${Date.now()}.mjs`);
  const contents = `export default ${JSON.stringify(effective, null, 2)};\n`;
  writeFileSync(p, contents, 'utf8');
  return p;
}

function resolveI18nextParserBin(): string {
  try {
    return req.resolve('i18next-parser/bin/cli.js');
  } catch {
    return path.resolve(process.cwd(), 'node_modules', '.bin', 'i18next');
  }
}

async function main() {
  program
    .name('i18n')
    .description('Run i18n extraction with optional overrides.')
    .option('-c, --config <file>', 'path to config file (ESM, export default)')
    .addOption(new Option('-i, --input <globs...>', 'glob patterns (space/comma separated)'))
    .option('-o, --output <pattern>', 'output pattern, e.g. src/locales/$LOCALE/$NAMESPACE.json')
    .option('-n, --namespace <name>', 'default namespace (e.g. "translation")')
    .option('-l, --locales <list>', 'locales (comma/space separated)')
    .option('--print', 'print effective config and exit', false);

  program.parse(process.argv);
  const opts = program.opts<{
    config?: string;
    input?: string[] | string;
    output?: string;
    locales?: string[] | string;
    namespace?: string;
    print?: boolean;
  }>();

  const fileCfg = await loadConfig(opts.config);
  const cliCfg: UserConfig = {
    input: splitList(opts.input),
    output: opts.output,
    locales: splitList(opts.locales),
    defaultNamespace: opts.namespace,
  };

  const effective = buildEffectiveConfig(fileCfg, cliCfg);

  if (opts.print) {
    console.log(JSON.stringify(effective, null, 2));
    process.exit(0);
  }

  const tmpConfig = writeTempConfig(effective);

  // ---- IMPORTANT: i18next-parser expects positional input globs ----
  const inputArgs = Array.isArray(effective.input) ? effective.input : [effective.input];
  const cliArgs = [...inputArgs, '--config', tmpConfig, '--output', effective.output];

  const bin = resolveI18nextParserBin();
  const isJs = /\.(mjs|cjs|js)$/.test(bin);

  const child = spawn(isJs ? process.execPath : bin, isJs ? [bin, ...cliArgs] : cliArgs, {
    stdio: 'inherit',
    cwd: process.cwd(),
    env: process.env,
  });

  child.on('exit', (code) => {
    process.exit(code ?? 1);
  });
}

main().catch((err) => {
  console.error('[i18n-extract] fatal:', err);
  process.exit(1);
});
