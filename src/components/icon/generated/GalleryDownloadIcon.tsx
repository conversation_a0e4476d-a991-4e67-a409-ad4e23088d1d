// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M682.667 814.204l121.967 121.963c1.664 1.668 3.849 2.5 6.033 2.5M938.667 814.204l-121.967 121.963c-1.664 1.668-3.849 2.5-6.033 2.5M810.667 938.667v-386.5",
          "M85.333 746.667l253.412-253.414c24.994-24.994 65.516-24.994 90.511 0l146.743 146.748M576 640l42.667 42.667M576 640l82.743-82.748M618.667 938.667h-328.533c-71.687 0-107.53 0-134.911-13.952-24.085-12.271-43.666-31.851-55.938-55.936-13.951-27.383-13.951-63.223-13.951-134.912v-443.733c0-71.687 0-107.53 13.951-134.911 12.272-24.085 31.853-43.666 55.938-55.938 27.38-13.951 63.224-13.951 134.911-13.951h443.733c71.689 0 107.529 0 134.912 13.951 24.085 12.272 43.665 31.853 55.936 55.938 13.952 27.38 13.952 63.224 13.952 134.911v392.533M661.333 362.665c0 35.346-28.655 64-64 64s-64-28.654-64-64c0-35.346 28.655-64 64-64s64 28.654 64 64z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "gallery-download"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1681,
        "id": 339,
        "name": "gallery-download",
        "prevSize": 32,
        "code": 59957
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 310
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface GalleryDownloadIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const GalleryDownloadIcon = ({ size = 16, ...props }: GalleryDownloadIconProps) => (
  <IcoMoon iconSet={iconSet} icon="gallery-download" size={size} {...props} />
);

GalleryDownloadIcon.displayName = 'GalleryDownloadIcon';

export default GalleryDownloadIcon;
