// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M125.525 560.721v0zM89.914 708.215v0zM126.772 570.458v0zM102.452 720.751v0zM240.208 683.895v0zM88.404 718.583v0zM92.084 722.261v0zM898.475 774.054v0zM934.084 921.549v0zM897.229 783.791v0zM774.054 898.475v0zM921.549 934.084v0zM783.791 897.229v0zM935.595 931.917v0zM931.917 935.595v0zM490.667 530.667c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM746.667 594.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM490.667 642.667c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM682.667 706.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM711.625 312.377v0zM312.377 711.625v0zM249.948 685.141v0zM405.333 85.333v-32c-194.404 0-352 157.596-352 352h64c0-159.058 128.942-288 288-288v-32zM85.333 405.333h-32c0 61.978 16.044 120.299 44.23 170.948l55.923-31.125c-23.025-41.374-36.154-89.015-36.154-139.823h-32zM89.914 708.215l30.912 8.269 36.858-137.754-61.825-16.542-36.858 137.754 30.913 8.273zM102.452 720.751l8.271 30.916 137.755-36.86-16.542-61.824-137.755 36.855 8.271 30.912zM89.914 708.215l-30.913-8.273c-0.589 2.202-1.635 5.986-2.224 9.421-0.562 3.277-1.777 11.494 1.754 20.689l59.746-22.942c1.431 3.725 1.793 7.019 1.832 9.267 0.036 2.095-0.206 3.529-0.254 3.81-0.055 0.324-0.061 0.265 0.134-0.516 0.19-0.764 0.441-1.702 0.838-3.187l-30.912-8.269zM102.452 720.751l-8.271-30.912c-1.483 0.397-2.424 0.649-3.188 0.841-0.781 0.192-0.838 0.188-0.517 0.132 0.284-0.047 1.719-0.29 3.811-0.256 2.249 0.043 5.543 0.401 9.268 1.835l-22.944 59.746c9.196 3.529 17.416 2.317 20.689 1.754 3.438-0.589 7.222-1.634 9.422-2.223l-8.271-30.916zM88.404 718.583l-29.873 11.473c3.9 10.155 11.925 18.18 22.080 22.080l22.946-59.746c6.77 2.598 12.119 7.949 14.72 14.72l-29.873 11.473zM298.667 618.667h32c0-159.057 128.943-288 288-288v-64c-194.404 0-352 157.596-352 352h32zM938.667 618.667h-32c0 50.807-13.129 98.449-36.151 139.823l55.923 31.125c28.186-50.65 44.228-108.971 44.228-170.948h-32zM934.084 921.549l30.916-8.273-36.86-137.754-61.824 16.542 36.855 137.754 30.912-8.269zM774.054 898.475l-15.565-27.959c-41.374 23.023-89.015 36.151-139.823 36.151v64c61.978 0 120.299-16.043 170.948-44.228l-15.561-27.964zM921.549 934.084l8.269-30.912-137.754-36.855-16.542 61.824 137.754 36.86 8.273-30.916zM934.084 921.549l-30.912 8.269c0.397 1.485 0.649 2.423 0.841 3.187 0.192 0.781 0.188 0.841 0.132 0.516-0.047-0.282-0.29-1.715-0.256-3.81 0.043-2.249 0.405-5.542 1.835-9.267l59.746 22.946c3.529-9.199 2.317-17.417 1.754-20.693-0.589-3.435-1.634-7.219-2.223-9.421l-30.916 8.273zM921.549 934.084l-8.273 30.916c2.202 0.589 5.986 1.634 9.421 2.223 3.277 0.563 11.494 1.775 20.693-1.754l-22.946-59.746c3.725-1.429 7.019-1.792 9.267-1.835 2.095-0.034 3.529 0.209 3.81 0.256 0.324 0.055 0.265 0.060-0.516-0.132-0.764-0.192-1.702-0.444-3.187-0.841l-8.269 30.912zM935.595 931.917l-29.871-11.473c2.598-6.771 7.949-12.122 14.72-14.72l22.942 59.746c10.159-3.9 18.185-11.925 22.084-22.084l-29.875-11.469zM490.667 562.667v32h256v-64h-256v32zM490.667 674.667v32h192v-64h-192v32zM618.667 298.667v32c29.15 0 57.233 4.32 83.674 12.335l18.564-61.248c-32.393-9.818-66.731-15.086-102.238-15.086v32zM711.625 312.377l-9.284 30.624c118.281 35.851 204.326 145.75 204.326 275.665h64c0-158.891-105.25-293.114-249.762-336.914l-9.28 30.624zM711.625 312.377l30.622-9.282c-43.802-144.512-178.022-249.762-336.913-249.762v64c129.916 0 239.817 86.044 275.665 204.326l30.626-9.282zM618.667 938.667v-32c-129.916 0-239.815-86.046-275.665-204.326l-61.248 18.564c43.8 144.512 178.023 249.762 336.914 249.762v-32zM312.377 711.625l30.624-9.284c-8.014-26.441-12.335-54.524-12.335-83.674h-64c0 35.507 5.268 69.845 15.086 102.238l30.624-9.28zM249.948 685.141l-15.561 27.964c21.6 12.019 44.605 21.837 68.709 29.141l18.564-61.248c-19.686-5.965-38.485-13.986-56.15-23.817l-15.561 27.959zM774.054 898.475l15.561 27.964c-4.045 2.249-9.079 3.042-14.093 1.702l16.542-61.824c-11.58-3.098-23.59-1.357-33.574 4.194l15.565 27.964zM240.208 683.895l8.271 30.912c-5.014 1.34-10.048 0.546-14.093-1.702l31.122-55.927c-9.981-5.551-21.995-7.292-33.572-4.194l8.271 30.912zM898.475 774.054l-27.964-15.565c-5.551 9.984-7.292 21.995-4.194 33.574l61.824-16.542c1.34 5.013 0.546 10.048-1.702 14.093l-27.964-15.561zM125.525 560.721l-27.962 15.561c-2.251-4.045-3.046-9.079-1.704-14.093l61.825 16.542c3.098-11.58 1.356-23.59-4.198-33.574l-27.962 15.565z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "chat-bubble-alt2"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1538,
        "id": 482,
        "name": "chat-bubble-alt2",
        "prevSize": 32,
        "code": 59814
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 167
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ChatBubbleAlt2IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ChatBubbleAlt2Icon = ({ size = 16, ...props }: ChatBubbleAlt2IconProps) => (
  <IcoMoon iconSet={iconSet} icon="chat-bubble-alt2" size={size} {...props} />
);

ChatBubbleAlt2Icon.displayName = 'ChatBubbleAlt2Icon';

export default ChatBubbleAlt2Icon;
