import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import type { ReactNode } from 'react';
import { useArgs } from 'storybook/internal/preview-api';

import { Select as BaseSelect } from '@components/fields';
import {
  selectContentPropDefs,
  selectRootPropDefs,
  selectTriggerPropDefs,
} from '@components/fields/select/select.props';
import Icon, { type IconType } from '@components/icon';
import { iconName } from '@components/icon/icon-name';
import { accentColors, radii } from '@props';

interface Props {
  prefix?: ReactNode;
  suffix?: ReactNode;
  label?: ReactNode;
  helperText?: ReactNode;
  error?: boolean;
  size: (typeof selectRootPropDefs.size.values)[number];
  triggerVariant: (typeof selectTriggerPropDefs.variant.values)[number];
  contentVariant: (typeof selectContentPropDefs.variant.values)[number];
  color?: (typeof accentColors)[number];
  radius?: (typeof radii)[number];
  placeholder?: string;
  disabled?: boolean;
  defaultValue?: string;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

const Select = ({
  prefix,
  suffix,
  label,
  helperText,
  error,
  size,
  triggerVariant,
  contentVariant,
  color,
  radius,
  placeholder,
  disabled,
  defaultValue,
  open,
  onOpenChange,
}: Props) => (
  <BaseSelect.Root size={size} disabled={disabled} defaultValue={defaultValue} open={open} onOpenChange={onOpenChange}>
    <BaseSelect.Trigger
      prefix={prefix}
      suffix={suffix}
      label={label}
      helperText={helperText}
      error={error}
      color={color}
      radius={radius}
      variant={triggerVariant}
      placeholder={placeholder}
    />
    <BaseSelect.Content variant={contentVariant}>
      <BaseSelect.Group>
        <BaseSelect.Label>Fruits</BaseSelect.Label>
        <BaseSelect.Item value="orange">Orange</BaseSelect.Item>
        <BaseSelect.Item value="apple">Apple</BaseSelect.Item>
        <BaseSelect.Item value="grape" disabled>
          Grape
        </BaseSelect.Item>
      </BaseSelect.Group>
      <BaseSelect.Separator />
      <BaseSelect.Group>
        <BaseSelect.Label>Vegetables</BaseSelect.Label>
        <BaseSelect.Item value="carrot">Carrot</BaseSelect.Item>
        <BaseSelect.Item value="potato">Potato</BaseSelect.Item>
      </BaseSelect.Group>
    </BaseSelect.Content>
  </BaseSelect.Root>
);

const prefixSuffixMapping: Record<IconType, React.ReactNode> = iconName.reduce(
  (map, name) => {
    map[name] = <Icon icon={name} size={16} />;
    return map;
  },
  {} as Record<IconType, React.ReactNode>,
);

const meta: Meta<typeof Select> = {
  title: 'DesignSystem/InputFields/Select',
  component: Select,
  parameters: { layout: 'centered' },
  argTypes: {
    label: { control: 'text', type: 'string' },
    helperText: { control: 'text', type: 'string' },
    error: { control: 'boolean', type: 'boolean' },
    size: { control: 'inline-radio', options: selectRootPropDefs.size.values },
    prefix: {
      control: { type: 'select' },
      options: iconName,
      mapping: prefixSuffixMapping,
    },
    suffix: {
      control: { type: 'select' },
      options: iconName,
      mapping: prefixSuffixMapping,
    },
    triggerVariant: { control: 'inline-radio', name: 'trigger variant', options: selectTriggerPropDefs.variant.values },
    contentVariant: {
      control: 'inline-radio',
      name: 'content variant',
      options: selectContentPropDefs.variant.values,
    },
    color: { control: 'select', options: accentColors },
    radius: { control: 'select', options: radii },
    placeholder: { control: 'text' },
    disabled: { control: 'boolean' },
    defaultValue: { control: 'text' },
    open: { control: 'boolean' },
  },
};

export default meta;
type Story = StoryObj<typeof Select>;

export const _select: Story = {
  args: {
    label: 'Label',
    helperText: 'Hint',
    placeholder: 'Choose...',
    size: selectRootPropDefs.size.default,
    radius: radii[3],
    open: false,
    error: false,
    disabled: false,
    triggerVariant: selectTriggerPropDefs.variant.default,
    contentVariant: selectContentPropDefs.variant.default,
    prefix: 'search',
    suffix: 'check',
    color: 'accent',
    defaultValue: undefined,
  },
  render: (args) => {
    const [, updateArgs] = useArgs();
    return (
      <Select
        {...args}
        open={args.open}
        defaultValue={args.defaultValue}
        onOpenChange={(open) => updateArgs({ open })}
      />
    );
  },
};
