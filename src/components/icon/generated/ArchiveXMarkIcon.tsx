// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M156.163 344l21.512 437.956c3.138 63.881 55.247 114.044 118.467 114.044h431.742c63.236 0 115.354-50.189 118.468-114.086l21.491-440.58M156.163 344l711.681-2.667M156.163 344c-26.21-1.613-49.232-19.011-56.269-44.605-8.525-31.009-13.117-57.731-14.469-85.888-2.342-48.759 40.366-85.507 88.618-85.507h675.974c48.256 0 90.901 36.752 88.555 85.51-1.378 28.685-6.059 55.35-14.451 85.903-6.946 25.277-30.349 41.92-56.277 41.92M426.667 533.333l85.333 85.333M512 618.667l85.333 85.333M512 618.667l-85.333 85.333M512 618.667l85.333-85.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "archive-x-mark"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1404,
        "id": 616,
        "name": "archive-x-mark",
        "prevSize": 32,
        "code": 59680
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 33
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ArchiveXMarkIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ArchiveXMarkIcon = ({ size = 16, ...props }: ArchiveXMarkIconProps) => (
  <IcoMoon iconSet={iconSet} icon="archive-x-mark" size={size} {...props} />
);

ArchiveXMarkIcon.displayName = 'ArchiveXMarkIcon';

export default ArchiveXMarkIcon;
