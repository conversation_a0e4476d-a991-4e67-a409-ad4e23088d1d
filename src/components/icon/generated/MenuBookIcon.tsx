// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M785.067 938.667h-546.133c-37.703 0-68.267-31.838-68.267-71.113M170.667 867.554c0-39.27 30.564-71.108 68.267-71.108h591.646c12.565 0 22.754-10.615 22.754-23.706v-663.703c0-13.091-10.189-23.704-22.754-23.704h-546.135c-62.838 0-113.778 53.062-113.778 118.519v663.702zM490.667 342.505c7.006-0.774 14.123-1.172 21.333-1.172s14.327 0.398 21.333 1.172M490.667 342.505c-95.999 10.612-170.667 92-170.667 190.828v42.667M490.667 342.505v-22.505c0-11.782 9.553-21.333 21.333-21.333s21.333 9.551 21.333 21.333v22.505M533.333 342.505c96 10.612 170.667 92 170.667 190.828v42.667M320 576h-21.333M320 576h384M704 576h21.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "menu-book"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1765,
        "id": 255,
        "name": "menu-book",
        "prevSize": 32,
        "code": 60041
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 394
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MenuBookIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MenuBookIcon = ({ size = 16, ...props }: MenuBookIconProps) => (
  <IcoMoon iconSet={iconSet} icon="menu-book" size={size} {...props} />
);

MenuBookIcon.displayName = 'MenuBookIcon';

export default MenuBookIcon;
