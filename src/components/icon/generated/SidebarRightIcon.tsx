// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M99.284 868.779v0zM155.223 924.715v0zM924.715 868.779v0zM868.779 924.715v0zM924.715 155.223v0zM868.779 99.284v0zM99.284 155.223v0zM155.223 99.284v0zM577.135 361.216c-12.582-12.41-32.845-12.269-45.252 0.314-12.412 12.583-12.271 32.844 0.316 45.254l44.937-45.568zM681.399 508.983v0zM681.399 515.017v0zM532.198 617.216c-12.587 12.407-12.727 32.67-0.316 45.252 12.407 12.587 32.67 12.727 45.252 0.316l-44.937-45.568zM938.667 290.133h-32v443.733h64v-443.733h-32zM85.333 733.867h32v-443.733h-64v443.733h32zM290.133 85.333v32h8.533v-64h-8.533v32zM298.667 85.333v32h435.2v-64h-435.2v32zM733.867 938.667v-32h-435.2v64h435.2v-32zM298.667 938.667v-32h-8.533v64h8.533v-32zM298.667 85.333h-32v853.333h64v-853.333h-32zM85.333 733.867h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM290.133 938.667v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM99.284 868.779l-28.512 14.528c15.34 30.106 39.817 54.579 69.923 69.922l29.055-57.024c-18.063-9.207-32.75-23.893-41.953-41.954l-28.512 14.528zM938.667 733.867h-32c0 36.373-0.026 61.726-1.638 81.463-1.583 19.366-4.531 30.494-8.823 38.921l57.024 29.056c9.655-18.957 13.683-39.441 15.586-62.763 1.877-22.95 1.852-51.362 1.852-86.677h-32zM733.867 938.667v32c35.315 0 63.727 0.026 86.677-1.852 23.322-1.903 43.806-5.931 62.763-15.586l-29.056-57.024c-8.427 4.292-19.554 7.241-38.921 8.823-19.738 1.613-45.090 1.638-81.463 1.638v32zM924.715 868.779l-28.51-14.528c-9.207 18.061-23.893 32.747-41.954 41.954l29.056 57.024c30.106-15.343 54.579-39.817 69.922-69.922l-28.514-14.528zM938.667 290.133h32c0-35.315 0.026-63.727-1.852-86.676-1.903-23.323-5.931-43.809-15.586-62.762l-57.024 29.055c4.292 8.428 7.241 19.553 8.823 38.919 1.613 19.739 1.638 45.093 1.638 81.464h32zM733.867 85.333v32c36.373 0 61.726 0.025 81.463 1.638 19.366 1.582 30.494 4.532 38.921 8.826l29.056-57.024c-18.957-9.658-39.441-13.683-62.763-15.589-22.95-1.875-51.362-1.85-86.677-1.85v32zM924.715 155.223l28.514-14.528c-15.343-30.106-39.817-54.583-69.922-69.923l-29.056 57.024c18.061 9.204 32.747 23.89 41.954 41.953l28.51-14.528zM85.333 290.133h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM290.133 85.333v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM99.284 155.223l28.512 14.528c9.204-18.063 23.89-32.75 41.953-41.953l-29.055-57.024c-30.106 15.34-54.583 39.817-69.923 69.923l28.512 14.528zM554.667 384l-22.468 22.784 126.733 124.983 44.937-45.568-126.733-124.983-22.468 22.784zM681.399 515.017l-22.468-22.784-126.733 124.983 44.937 45.568 126.733-124.983-22.468-22.784zM681.399 508.983l-22.468 22.784c-11.021-10.867-11.021-28.668 0-39.535l44.937 45.568c14.4-14.199 14.4-37.402 0-51.601l-22.468 22.784z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "sidebar-right"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1890,
        "id": 130,
        "name": "sidebar-right",
        "prevSize": 32,
        "code": 60166
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 519
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SidebarRightIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SidebarRightIcon = ({ size = 16, ...props }: SidebarRightIconProps) => (
  <IcoMoon iconSet={iconSet} icon="sidebar-right" size={size} {...props} />
);

SidebarRightIcon.displayName = 'SidebarRightIcon';

export default SidebarRightIcon;
