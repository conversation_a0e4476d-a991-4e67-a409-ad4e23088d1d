import type { <PERSON>a, StoryObj } from '@storybook/react-vite';

import { OTPInput } from '@components/fields/otp';
import { otpInputPropDefs } from '@components/fields/otp/otp.props';
import { radii } from '@props';

const meta: Meta<typeof OTPInput> = {
  title: 'DesignSystem/InputFields/OTPInput',
  component: OTPInput,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    length: { control: 'number', defaultValue: 6 },
    disabled: { control: 'boolean' },
    error: { control: 'boolean' },
    size: { control: 'inline-radio', type: 'string', options: otpInputPropDefs.size.values },
    variant: { control: 'inline-radio', type: 'string', options: otpInputPropDefs.variant.values },
    radius: { control: 'select', options: radii },
    separator: { control: 'text' },
    separatorAlign: {
      control: 'inline-radio',
      options: ['between', 'symmetric'],
      defaultValue: 'between',
      description: '<PERSON><PERSON>ch hiển thị separator g<PERSON><PERSON><PERSON> các input',
    },
  },
};

export default meta;
type Story = StoryObj<typeof OTPInput>;

export const _OTPInput: Story = {
  args: {
    length: 6,
    disabled: false,
    error: false,
    size: otpInputPropDefs.size.default,
    variant: otpInputPropDefs.variant.default,
    radius: 'medium',
    separator: '-',
    separatorAlign: 'symmetric',
    placeholder: '-',
  },
  render: (args) => {
    return (
      <OTPInput
        {...args}
        onChange={(val) => {
          console.log('onChange:', val);
        }}
        onActiveChange={(val, index) => {
          console.log('onActiveChange:', val, index);
        }}
      />
    );
  },
};
