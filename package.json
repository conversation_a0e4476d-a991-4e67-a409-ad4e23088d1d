{"name": "@herondlabs/design-system", "version": "0.0.13", "type": "module", "style": "./styles.css", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "bin": {"i18n": "./dist/i18n/i18next-parser.cjs"}, "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.cjs", "import": "./dist/index.js"}, "./components": {"types": "./dist/components/index.d.ts", "require": "./dist/components/index.cjs", "import": "./dist/components/index.js"}, "./icons": {"types": "./dist/components/icon/generated/index.d.ts", "require": "./dist/components/icon/generated/index.cjs", "import": "./dist/components/icon/generated/index.js"}, "./styles.css": "./dist/styles.css"}, "files": ["dist/**", "text-style.json", "theme.json", "colors.json"], "sideEffects": ["*.css"], "scripts": {"dev": "npm run dev:css && vite", "dev:css": "postcss src/styles/index.css -o styles.css --watch", "build": "npm run build:icons && npm run build:css && vite build --mode production", "build:icons": "node scripts/generate-icon-components.js", "build:dev": "npm run build:css && vite build --mode development", "build:analyze": "npm run build && open dist/bundle-analysis.html", "build:cli": "vite build -c vite.cli.config.ts", "build:diff": "npm run build:js:cjs && npm run build:js:esm", "build:js:cjs": "vite build --config vite.config.cjs.ts", "build:js:esm": "vite build --config vite.config.esm.ts", "build:css": "npm run build:css:index", "build:css:index": "postcss src/styles/index.css -o styles.css", "build:css:components": "postcss src/components/index.css -o components.css", "build:css:tokens": "npm run build:css:tokens:index && npm run build:css:tokens:colors", "build:css:tokens:index": "postcss src/styles/tokens/index.css -o tokens.css", "build:css:tokens:colors": "postcss src/styles/tokens/colors/*.css --dir tokens/colors", "build:css:layout": "npm run build:css:layout:index && npm run build:css:layout:components && npm run build:css:layout:utilities", "build:css:layout:index": "postcss src/styles/layout.css -o layout.css", "build:css:layout:components": "postcss src/components/layout.css -o layout/components.css", "build:css:layout:utilities": "postcss src/styles/utilities/layout.css -o layout/utilities.css", "extract-icons": "node scripts/extract-icons.js", "preview": "vite preview", "lint": "npm run lint:js && npm run lint:ts && npm run lint:css", "lint:js": "eslint \"src/**/*.{ts,tsx}\"", "lint:ts": "tsc --noEmit", "lint:css": "stylelint \"src/**/*.css\"", "pretty": "eslint --fix src && prettier --write src", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "test": "vitest", "test:ci": "vitest run", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "clean": "rm -rf dist *.css tokens layout fonts storybook-static", "prepublishOnly": "npm run lint", "prepare": "husky install", "lint-staged": "lint-staged"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["npm run pretty"], "*.{css,scss,md,json}": ["npm run pretty"]}, "dependencies": {"clsx": "^2.1.1", "i18next": "^25.3.4", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "i18next-icu": "^2.3.0", "i18next-parser": "^9.3.0", "radix-ui": "^1.4.3", "react-i18next": "^15.6.1", "react-icomoon": "^2.6.1", "react-remove-scroll-bar": "^2.3.8"}, "peerDependencies": {"@radix-ui/themes": "*", "@types/react": "*", "@types/react-dom": "*", "react": "16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}, "radix-ui": {"optional": true}, "@radix-ui/themes": {"optional": true}}, "devDependencies": {"@chromatic-com/storybook": "^4.0.1", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.29.0", "@storybook/addon-a11y": "^9.0.15", "@storybook/addon-docs": "^9.0.15", "@storybook/addon-vitest": "^9.0.15", "@storybook/react-vite": "^9.0.15", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/node": "^20.11.30", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-storybook": "^9.0.15", "globals": "^16.2.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "playwright": "^1.53.2", "postcss": "^8.4.33", "postcss-cli": "^11.0.0", "postcss-combine-duplicated-selectors": "^10.0.3", "postcss-custom-media": "^11.0.6", "postcss-discard-empty": "^7.0.1", "postcss-functions": "^4.0.2", "postcss-import": "^16.0.0", "postcss-nesting": "^12.0.2", "postcss-url": "^10.1.3", "prettier": "^3.6.2", "react": "^19.0.0", "react-dom": "^19.0.0", "rollup-plugin-visualizer": "^6.0.3", "storybook": "^9.0.15", "stylelint": "^16.21.1", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.0", "vite-plugin-dts": "^4.5.4", "vite-plugin-static-copy": "^3.1.0", "vitest": "^3.2.4"}, "publishConfig": {"registry": "https://npm.pkg.github.com/"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/herondlabs/design-system.git"}, "bugs": {"url": "https://github.com/herondlabs/design-system/issues"}, "homepage": "https://github.com/herondlabs/design-system"}