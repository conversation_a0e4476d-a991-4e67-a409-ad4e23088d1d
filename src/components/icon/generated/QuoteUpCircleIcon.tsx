// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M768 574.49v11.379c0 53.461-43.605 96.798-97.395 96.798h-22.592c-49.681 0-90.628-38.746-93.065-88.068-2.496-50.522 11.721-100.463 40.482-142.195l74.871-108.644c3.366-4.885 11.063-1.806 10.082 4.035l-19.52 116.41c-2.185 13.018 7.915 24.866 21.193 24.866 47.467 0 85.943 38.242 85.943 85.419z",
          "M469.333 574.49v11.379c0 53.461-43.604 96.798-97.393 96.798h-22.592c-49.684 0-90.63-38.746-93.068-88.068-2.497-50.522 11.721-100.463 40.482-142.195l74.874-108.644c3.367-4.885 11.060-1.806 10.081 4.035l-19.521 116.41c-2.182 13.018 7.917 24.866 21.193 24.866 47.467 0 85.943 38.242 85.943 85.419z",
          "M938.667 512c0 235.639-191.027 426.667-426.667 426.667-235.642 0-426.667-191.027-426.667-426.667 0-235.642 191.025-426.667 426.667-426.667 235.639 0 426.667 191.025 426.667 426.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "quote-up-circle"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1841,
        "id": 179,
        "name": "quote-up-circle",
        "prevSize": 32,
        "code": 60117
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 470
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface QuoteUpCircleIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const QuoteUpCircleIcon = ({ size = 16, ...props }: QuoteUpCircleIconProps) => (
  <IcoMoon iconSet={iconSet} icon="quote-up-circle" size={size} {...props} />
);

QuoteUpCircleIcon.displayName = 'QuoteUpCircleIcon';

export default QuoteUpCircleIcon;
