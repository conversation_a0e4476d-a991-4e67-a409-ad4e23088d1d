// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M349.763 674.236c-136.666-136.666-208.367-238.511-245.786-305.269-32.618-58.192-19.653-125.744 17.351-181.251l52.25-78.375c20.77-31.155 66.193-32.172 88.335-1.977l138.252 188.525c14.082 19.203 9.295 46.273-10.519 59.482-46.253 30.835-65.093 88.421-33.666 134.275 18.945 27.639 45.297 60.74 81.463 96.909M349.763 674.236l87.681-87.68M349.763 674.236l-264.43 264.431M437.444 586.556l501.222-501.222M520 657.852c4.928 3.605 9.719 6.989 14.353 10.167 45.854 31.428 103.441 12.587 134.276-33.668 13.21-19.81 40.277-24.597 59.482-10.517l188.523 138.253c30.195 22.144 29.18 67.567-1.975 88.337l-78.374 52.25c-55.505 37.001-123.059 49.967-181.252 17.348-53.252-29.85-128.841-81.519-226.62-170.581"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "call-slash"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1509,
        "id": 511,
        "name": "call-slash",
        "prevSize": 32,
        "code": 59785
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 138
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CallSlashIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CallSlashIcon = ({ size = 16, ...props }: CallSlashIconProps) => (
  <IcoMoon iconSet={iconSet} icon="call-slash" size={size} {...props} />
);

CallSlashIcon.displayName = 'CallSlashIcon';

export default CallSlashIcon;
