// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M746.667 426.667c0-19.611-3.861-39.029-11.366-57.147s-18.505-34.58-32.371-48.447c-13.867-13.867-30.332-24.867-48.448-32.372s-37.538-11.367-57.148-11.367M896 426.667c0-39.221-7.727-78.059-22.733-114.295-15.010-36.236-37.009-69.161-64.747-96.895-27.733-27.734-60.655-49.733-96.892-64.743s-75.072-22.735-114.295-22.735M400.166 295.89l-138.252-188.525c-22.143-30.195-67.566-29.178-88.335 1.977l-52.25 78.375c-37.004 55.506-49.969 123.059-17.351 181.251 37.419 66.758 109.12 168.603 245.786 305.269s238.512 208.367 305.268 245.786c58.193 32.619 125.747 19.652 181.252-17.348l78.374-52.25c31.155-20.77 32.171-66.193 1.975-88.337l-188.523-138.253c-19.204-14.080-46.272-9.293-59.482 10.517-30.835 46.255-88.422 65.097-134.276 33.668-27.639-18.944-60.74-45.299-96.909-81.463-36.166-36.169-62.518-69.269-81.463-96.909-31.427-45.854-12.587-103.44 33.666-134.275 19.814-13.209 24.6-40.279 10.519-59.482z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "call-calling"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1505,
        "id": 515,
        "name": "call-calling",
        "prevSize": 32,
        "code": 59781
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 134
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CallCallingIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CallCallingIcon = ({ size = 16, ...props }: CallCallingIconProps) => (
  <IcoMoon iconSet={iconSet} icon="call-calling" size={size} {...props} />
);

CallCallingIcon.displayName = 'CallCallingIcon';

export default CallCallingIcon;
