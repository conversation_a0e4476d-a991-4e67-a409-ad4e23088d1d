// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M749.039 128c-39.275 0-71.113 32.236-71.113 72v24M749.039 128c39.27 0 71.108 32.236 71.108 72v24M749.039 128h-474.076c-39.273 0-71.111 32.236-71.111 72v24M677.926 800v53.333c0 23.565-19.102 42.667-42.667 42.667h-388.741c-23.564 0-42.667-19.102-42.667-42.667v-125.333M677.926 800h99.554c23.565 0 42.667-19.102 42.667-42.667v-533.333M677.926 800v-72M85.333 176v48M85.333 224v48M85.333 224h118.519M938.667 224v-48M938.667 224v48M938.667 224h-118.519M203.852 224v384M677.926 224h142.221M677.926 224v384M203.852 608v120M203.852 608h474.074M203.852 728h474.074M677.926 728v-120"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "towel"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1958,
        "id": 62,
        "name": "towel",
        "prevSize": 32,
        "code": 60234
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 587
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface TowelIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const TowelIcon = ({ size = 16, ...props }: TowelIconProps) => (
  <IcoMoon iconSet={iconSet} icon="towel" size={size} {...props} />
);

TowelIcon.displayName = 'TowelIcon';

export default TowelIcon;
