import type { Meta, StoryObj } from '@storybook/react-vite';
import { fn } from 'storybook/test';

import { Switch } from '@components/switch';
import { switchPropDefs } from '@components/switch/switch.props';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: 'DesignSystem/Switch',
  component: Switch,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
  },
  argTypes: {
    variant: { control: 'inline-radio', options: switchPropDefs.variant.values },
    size: { control: 'inline-radio', options: switchPropDefs.size.values },
    highContrast: { control: 'boolean' },
    disabled: { control: 'boolean' },
    checked: { control: 'boolean' },
    label: { control: 'text', type: 'string' },
    caption: { control: 'text', type: 'string' },
  },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: { onClick: fn() },
} satisfies Meta<typeof Switch>;

export default meta;
type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const _switch: Story = {
  args: {
    variant: switchPropDefs.variant.default,
    size: switchPropDefs.size.default,
    highContrast: false,
    disabled: false,
    defaultChecked: true,
    label: 'Label',
    caption: 'abc',
  },
};
