// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M508.147 349.403c1.549-3.239 6.157-3.239 7.706 0l44.305 92.849c0.623 1.306 1.86 2.206 3.294 2.394l101.999 13.444c3.554 0.469 4.979 4.855 2.377 7.326l-74.615 70.831c-1.045 0.994-1.519 2.453-1.254 3.87l18.731 101.163c0.653 3.529-3.076 6.238-6.229 4.527l-90.423-49.075c-1.267-0.691-2.803-0.691-4.070 0l-90.421 49.075c-3.154 1.711-6.884-0.998-6.231-4.527l18.73-101.163c0.265-1.417-0.209-2.876-1.254-3.87l-74.617-70.831c-2.603-2.47-1.178-6.857 2.38-7.326l101.997-13.444c1.434-0.188 2.671-1.088 3.294-2.394l44.305-92.849z",
          "M810.667 213.333h-597.333c-70.692 0-128 57.308-128 128v64c58.91 0 106.667 47.757 106.667 106.667s-47.756 106.667-106.667 106.667v64c0 70.694 57.308 128 128 128h597.333c70.694 0 128-57.306 128-128v-64c-58.91 0-106.667-47.757-106.667-106.667s47.757-106.667 106.667-106.667v-64c0-70.692-57.306-128-128-128z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "ticket-1"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1954,
        "id": 66,
        "name": "ticket-1",
        "prevSize": 32,
        "code": 60230
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 583
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface Ticket1IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const Ticket1Icon = ({ size = 16, ...props }: Ticket1IconProps) => (
  <IcoMoon iconSet={iconSet} icon="ticket-1" size={size} {...props} />
);

Ticket1Icon.displayName = 'Ticket1Icon';

export default Ticket1Icon;
