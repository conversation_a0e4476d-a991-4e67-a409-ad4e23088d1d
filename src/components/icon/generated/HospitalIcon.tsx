// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M274.963 938.667h474.076M274.963 938.667v-687.407M274.963 938.667h-118.518M749.039 938.667v-687.407M749.039 938.667h118.515M274.963 251.259v-47.407c0-65.456 53.063-118.519 118.519-118.519h237.038c65.455 0 118.519 53.062 118.519 118.519v47.407M274.963 251.259l-74.038 59.23c-28.114 22.491-44.48 56.544-44.48 92.547v535.63M85.333 938.667h71.111M749.039 251.259l74.035 59.23c28.117 22.491 44.48 56.544 44.48 92.547v535.63M867.554 938.667h71.113M417.185 298.667h94.815M512 298.667h94.814M512 298.667v-94.815M512 298.667v94.815M405.333 535.706h47.407M571.26 535.706h47.407M440.887 938.667h142.225c13.090 0 23.701-10.611 23.701-23.706v-165.922c0-39.275-31.838-71.113-71.108-71.113h-47.411c-39.27 0-71.109 31.838-71.109 71.113v165.922c0 13.094 10.612 23.706 23.702 23.706z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "hospital"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1714,
        "id": 306,
        "name": "hospital",
        "prevSize": 32,
        "code": 59990
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 343
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface HospitalIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const HospitalIcon = ({ size = 16, ...props }: HospitalIconProps) => (
  <IcoMoon iconSet={iconSet} icon="hospital" size={size} {...props} />
);

HospitalIcon.displayName = 'HospitalIcon';

export default HospitalIcon;
