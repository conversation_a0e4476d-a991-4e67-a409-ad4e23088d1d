// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 480c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM938.667 544c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM356.908 713.707v0zM310.292 667.093v0zM713.707 667.093v0zM667.093 713.707v0zM667.093 310.292v0zM713.707 356.908v0zM356.908 310.292v0zM310.292 356.908v0zM332.8 970.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM155.223 924.715v0zM117.333 691.2c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM99.284 868.779v0zM970.667 691.2c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM924.715 868.779v0zM691.2 906.667c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM868.779 924.715v0zM691.2 53.333c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM868.779 99.284v0zM906.667 332.8c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM924.715 155.223v0zM332.8 117.333c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM155.223 99.284v0zM53.333 332.8c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM99.284 155.223v0zM85.333 512v32h213.333v-64h-213.333v32zM298.667 512v32h426.667v-64h-426.667v32zM725.333 512v32h213.333v-64h-213.333v32zM469.333 298.667v32h85.333v-64h-85.333v32zM725.333 469.333h-32v42.667h64v-42.667h-32zM725.333 512h-32v42.667h64v-42.667h-32zM554.667 725.333v-32h-85.333v64h85.333v-32zM298.667 554.667h32v-42.667h-64v42.667h32zM298.667 512h32v-42.667h-64v42.667h32zM469.333 725.333v-32c-30.396 0-51.271-0.026-67.453-1.348-15.808-1.289-24.3-3.657-30.445-6.788l-29.056 57.024c16.672 8.495 34.523 11.938 54.289 13.551 19.392 1.587 43.322 1.562 72.664 1.562v-32zM298.667 554.667h-32c0 29.342-0.025 53.274 1.559 72.666 1.615 19.763 5.060 37.615 13.554 54.289l57.024-29.056c-3.131-6.148-5.5-14.639-6.791-30.447-1.322-16.179-1.347-37.056-1.347-67.452h-32zM356.908 713.707l14.528-28.51c-14.050-7.159-25.472-18.581-32.631-32.631l-57.024 29.056c13.295 26.091 34.508 47.305 60.599 60.599l14.528-28.514zM725.333 554.667h-32c0 30.396-0.026 51.273-1.348 67.452-1.289 15.808-3.657 24.299-6.788 30.447l57.024 29.056c8.495-16.674 11.938-34.526 13.551-54.289 1.587-19.392 1.562-43.324 1.562-72.666h-32zM554.667 725.333v32c29.342 0 53.274 0.026 72.666-1.562 19.763-1.613 37.615-5.056 54.289-13.551l-29.056-57.024c-6.148 3.132-14.639 5.5-30.447 6.788-16.179 1.323-37.056 1.348-67.452 1.348v32zM713.707 667.093l-28.51-14.528c-7.159 14.050-18.581 25.472-32.631 32.631l29.056 57.024c26.091-13.295 47.305-34.509 60.599-60.599l-28.514-14.528zM554.667 298.667v32c30.396 0 51.273 0.025 67.452 1.347 15.808 1.292 24.299 3.66 30.447 6.791l29.056-57.024c-16.674-8.495-34.526-11.939-54.289-13.554-19.392-1.584-43.324-1.559-72.666-1.559v32zM725.333 469.333h32c0-29.342 0.026-53.272-1.562-72.664-1.613-19.766-5.056-37.617-13.551-54.289l-57.024 29.056c3.132 6.146 5.5 14.637 6.788 30.445 1.323 16.181 1.348 37.057 1.348 67.453h32zM667.093 310.292l-14.528 28.512c14.050 7.159 25.472 18.581 32.631 32.631l57.024-29.056c-13.295-26.092-34.509-47.305-60.599-60.599l-14.528 28.512zM469.333 298.667v-32c-29.342 0-53.272-0.025-72.664 1.559-19.766 1.615-37.617 5.060-54.289 13.554l29.056 57.024c6.146-3.131 14.637-5.5 30.445-6.791 16.181-1.322 37.057-1.347 67.453-1.347v-32zM298.667 469.333h32c0-30.396 0.025-51.271 1.347-67.453 1.292-15.808 3.66-24.3 6.791-30.445l-57.024-29.056c-8.495 16.672-11.939 34.523-13.554 54.289-1.584 19.392-1.559 43.322-1.559 72.664h32zM356.908 310.292l-14.528-28.512c-26.092 13.295-47.305 34.508-60.599 60.599l57.024 29.056c7.159-14.050 18.581-25.472 32.631-32.631l-14.528-28.512zM332.8 938.667v-32c-36.080 0-72.695-0.009-103.663-1.698-15.462-0.841-28.867-2.069-39.67-3.789-11.427-1.818-17.446-3.819-19.717-4.975l-29.055 57.024c11.419 5.815 25.482 9.050 38.716 11.153 13.859 2.206 29.696 3.593 46.245 4.493 33.054 1.801 71.538 1.792 107.144 1.792v-32zM85.333 691.2h-32c0 35.605-0.009 74.091 1.791 107.145 0.902 16.55 2.287 32.384 4.492 46.242 2.106 13.235 5.337 27.298 11.156 38.72l57.024-29.056c-1.157-2.27-3.157-8.29-4.975-19.716-1.719-10.803-2.95-24.209-3.792-39.671-1.687-30.967-1.696-67.584-1.696-103.663h-32zM155.223 924.715l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM938.667 691.2h-32c0 36.079-0.009 72.695-1.698 103.663-0.841 15.462-2.069 28.868-3.789 39.671-1.818 11.426-3.819 17.446-4.975 19.716l57.024 29.056c5.815-11.422 9.050-25.485 11.153-38.72 2.206-13.858 3.593-29.692 4.493-46.242 1.801-33.054 1.792-71.539 1.792-107.145h-32zM691.2 938.667v32c35.605 0 74.091 0.009 107.145-1.792 16.55-0.9 32.384-2.287 46.242-4.493 13.235-2.103 27.298-5.338 38.72-11.153l-29.056-57.024c-2.27 1.156-8.29 3.157-19.716 4.975-10.803 1.719-24.209 2.948-39.671 3.789-30.967 1.69-67.584 1.698-103.663 1.698v32zM924.715 868.779l-28.51-14.528c-9.207 18.061-23.893 32.747-41.954 41.954l29.056 57.024c30.106-15.343 54.579-39.817 69.922-69.922l-28.514-14.528zM691.2 85.333v32c36.079 0 72.695 0.009 103.663 1.696 15.462 0.842 28.868 2.073 39.671 3.792 11.426 1.818 17.446 3.818 19.716 4.975l29.056-57.024c-11.422-5.819-25.485-9.050-38.72-11.156-13.858-2.205-29.692-3.59-46.242-4.492-33.054-1.801-71.539-1.791-107.145-1.791v32zM938.667 332.8h32c0-35.606 0.009-74.090-1.792-107.144-0.9-16.549-2.287-32.386-4.493-46.245-2.103-13.234-5.338-27.297-11.153-38.716l-57.024 29.055c1.156 2.271 3.157 8.29 4.975 19.717 1.719 10.803 2.948 24.208 3.789 39.67 1.69 30.968 1.698 67.583 1.698 103.663h32zM868.779 99.284l-14.528 28.512c18.061 9.204 32.747 23.89 41.954 41.953l57.024-29.055c-15.343-30.106-39.817-54.583-69.922-69.923l-14.528 28.512zM332.8 85.333v-32c-35.606 0-74.090-0.009-107.144 1.791-16.549 0.902-32.386 2.287-46.245 4.492-13.234 2.106-27.297 5.337-38.716 11.156l29.055 57.024c2.271-1.157 8.29-3.157 19.717-4.975 10.803-1.719 24.208-2.95 39.67-3.792 30.968-1.687 67.583-1.696 103.663-1.696v-32zM85.333 332.8h32c0-36.080 0.009-72.695 1.696-103.663 0.842-15.462 2.073-28.867 3.792-39.67 1.818-11.427 3.818-17.446 4.975-19.717l-57.024-29.055c-5.819 11.419-9.050 25.482-11.156 38.716-2.205 13.859-3.59 29.696-4.492 46.245-1.801 33.054-1.791 71.538-1.791 107.144h32zM155.223 99.284l-14.528-28.512c-30.106 15.34-54.583 39.817-69.923 69.923l57.024 29.055c9.204-18.063 23.89-32.75 41.953-41.953l-14.528-28.512z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "scan-qr-code"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1863,
        "id": 157,
        "name": "scan-qr-code",
        "prevSize": 32,
        "code": 60139
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 492
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ScanQrCodeIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ScanQrCodeIcon = ({ size = 16, ...props }: ScanQrCodeIconProps) => (
  <IcoMoon iconSet={iconSet} icon="scan-qr-code" size={size} {...props} />
);

ScanQrCodeIcon.displayName = 'ScanQrCodeIcon';

export default ScanQrCodeIcon;
