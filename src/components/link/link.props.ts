import { type PropDef } from '@props';

const sizes = ['1', '2', '3', '4', '5'] as const;
const colors = ['primary', 'quaternary'] as const;

const linkPropDefs = {
  size: {
    type: 'enum',
    className: 'rt-r-size',
    values: sizes,
    responsive: true,
  },
  color: {
    type: 'enum',
    className: 'rt-color',
    values: colors,
    default: 'primary',
  },
} satisfies {
  size: PropDef<(typeof sizes)[number]>;
  color: PropDef<(typeof colors)[number]>;
};

export { linkPropDefs };
