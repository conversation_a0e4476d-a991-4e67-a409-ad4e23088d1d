// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M278.518 106.667l-107.061 104.346c-0.527 0.641-0.791 1.481-0.791 2.321M278.518 320l-107.061-104.346c-0.527-0.641-0.791-1.481-0.791-2.321M170.666 213.333h563.2c71.684 0 107.529 0 134.912 13.951 24.085 12.272 43.665 31.853 55.936 55.938 13.952 27.38 13.952 63.224 13.952 134.911v72.533M745.481 917.333l107.063-104.346c0.525-0.64 0.789-1.481 0.789-2.321M745.481 704l107.063 104.346c0.525 0.64 0.789 1.481 0.789 2.321M853.333 810.667h-563.2c-71.687 0-107.53 0-134.911-13.952-24.085-12.271-43.666-31.851-55.938-55.936-13.951-27.383-13.951-63.223-13.951-134.912v-72.533"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "repeate"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1850,
        "id": 170,
        "name": "repeate",
        "prevSize": 32,
        "code": 60126
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 479
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface RepeateIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const RepeateIcon = ({ size = 16, ...props }: RepeateIconProps) => (
  <IcoMoon iconSet={iconSet} icon="repeate" size={size} {...props} />
);

RepeateIcon.displayName = 'RepeateIcon';

export default RepeateIcon;
