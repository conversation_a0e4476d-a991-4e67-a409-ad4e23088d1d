// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M612.301 358.722l-293.777 283.104c-32.45 31.27-32.45 81.971 0 113.242s85.062 31.27 117.513 0l411.29-396.346c64.9-62.541 64.9-163.941 0-226.482s-170.125-62.541-235.025 0l-411.289 396.345c-97.35 93.811-97.35 245.909 0 339.725 97.35 93.811 255.184 93.811 352.532 0l293.781-283.106"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "tree"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1964,
        "id": 56,
        "name": "tree",
        "prevSize": 32,
        "code": 60240
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 593
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface TreeIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const TreeIcon = ({ size = 16, ...props }: TreeIconProps) => (
  <IcoMoon iconSet={iconSet} icon="tree" size={size} {...props} />
);

TreeIcon.displayName = 'TreeIcon';

export default TreeIcon;
