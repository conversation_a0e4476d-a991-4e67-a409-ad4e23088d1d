// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 251.259v-5.926c0-88.366 71.634-160 160-160h266.667M938.667 251.259v-5.926c0-88.366-71.633-160-160-160h-266.667M512 85.333v853.333M512 938.667h-142.222M512 938.667h142.221"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "text"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1953,
        "id": 67,
        "name": "text",
        "prevSize": 32,
        "code": 60229
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 582
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface TextIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const TextIcon = ({ size = 16, ...props }: TextIconProps) => (
  <IcoMoon iconSet={iconSet} icon="text" size={size} {...props} />
);

TextIcon.displayName = 'TextIcon';

export default TextIcon;
