import { type PropDef, accentColorPropDef, asChildPropDef, highContrastPropDef, radiusPropDef } from '@props';

const sizes = ['1', '2', '3', '4', '5'] as const;
const variants = ['primary', 'primary-light', 'primary-outline', 'secondary', 'tertiary'] as const;

const tagPropDefs = {
  ...asChildPropDef,
  size: { type: 'enum', className: 'rt-r-size', values: sizes, default: '1', responsive: true },
  variant: { type: 'enum', className: 'rt-variant', values: variants, default: 'primary' },
  ...accentColorPropDef,
  ...highContrastPropDef,
  ...radiusPropDef,
} satisfies {
  size: PropDef<(typeof sizes)[number]>;
  variant: PropDef<(typeof variants)[number]>;
};

export { tagPropDefs };
