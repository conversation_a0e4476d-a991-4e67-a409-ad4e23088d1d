// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M597.333 85.333h-170.667c-11.78 0-21.332 9.551-21.332 21.333v96.314c0 19.006-22.979 28.524-36.418 15.085l-68.099-68.1c-8.331-8.331-21.839-8.331-30.17 0l-120.679 120.68c-8.332 8.331-8.332 21.839 0 30.17l68.101 68.101c13.44 13.44 3.921 36.419-15.085 36.419h-96.312c-11.782 0-21.333 9.551-21.333 21.332v170.667c0 11.785 9.551 21.333 21.333 21.333h96.31c19.006 0 28.524 22.98 15.085 36.42l-68.103 68.1c-8.331 8.333-8.331 21.841 0 30.174l120.68 120.678c8.331 8.329 21.839 8.329 30.17 0l68.104-68.105c13.439-13.44 36.418-3.921 36.418 15.083v96.316c0 11.78 9.551 21.333 21.332 21.333h170.667c11.785 0 21.333-9.553 21.333-21.333v-96.316c0-19.004 22.98-28.523 36.42-15.083l68.109 68.109c8.333 8.329 21.837 8.329 30.17 0l120.678-120.678c8.333-8.333 8.333-21.841 0-30.17l-68.105-68.109c-13.44-13.44-3.921-36.42 15.083-36.42h96.316c11.785 0 21.333-9.549 21.333-21.333v-170.667c0-11.781-9.549-21.332-21.333-21.332h-96.316c-19.008 0-28.527-22.979-15.087-36.419l68.105-68.105c8.333-8.331 8.333-21.839 0-30.17l-120.678-120.68c-8.333-8.331-21.837-8.331-30.17 0l-68.105 68.104c-13.44 13.439-36.42 3.921-36.42-15.085v-96.314c0-11.782-9.549-21.333-21.333-21.333z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "settings-02"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1878,
        "id": 142,
        "name": "settings-02",
        "prevSize": 32,
        "code": 60154
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 507
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface Settings-02IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const Settings-02Icon = ({ size = 16, ...props }: Settings-02IconProps) => (
  <IcoMoon iconSet={iconSet} icon="settings-02" size={size} {...props} />
);

Settings-02Icon.displayName = 'Settings-02Icon';

export default Settings-02Icon;
