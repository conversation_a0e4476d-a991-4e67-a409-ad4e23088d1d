// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M128 512h768M128 768h405.333M128 256h768M682.667 768h106.667M789.333 768h106.667M789.333 768v-106.667M789.333 768v106.667"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "list-plus"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1741,
        "id": 279,
        "name": "list-plus",
        "prevSize": 32,
        "code": 60017
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 370
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ListPlusIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ListPlusIcon = ({ size = 16, ...props }: ListPlusIconProps) => (
  <IcoMoon iconSet={iconSet} icon="list-plus" size={size} {...props} />
);

ListPlusIcon.displayName = 'ListPlusIcon';

export default ListPlusIcon;
