// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M192 106.667c0-11.782 9.551-21.333 21.333-21.333h170.667c11.782 0 21.333 9.551 21.333 21.333v810.667c0 11.78-9.551 21.333-21.333 21.333h-170.667c-11.782 0-21.333-9.553-21.333-21.333v-810.667z",
          "M618.667 106.667c0-11.782 9.553-21.333 21.333-21.333h170.667c11.78 0 21.333 9.551 21.333 21.333v810.667c0 11.78-9.553 21.333-21.333 21.333h-170.667c-11.78 0-21.333-9.553-21.333-21.333v-810.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "volume-low-1"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1994,
        "id": 26,
        "name": "volume-low-1",
        "prevSize": 32,
        "code": 60270
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 623
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface VolumeLow-1IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const VolumeLow-1Icon = ({ size = 16, ...props }: VolumeLow-1IconProps) => (
  <IcoMoon iconSet={iconSet} icon="volume-low-1" size={size} {...props} />
);

VolumeLow-1Icon.displayName = 'VolumeLow-1Icon';

export default VolumeLow-1Icon;
