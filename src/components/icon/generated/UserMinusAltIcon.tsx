// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M456.418 555.836c-31.4 0.009-62.814 1.19-95.363 3.55-114.060 8.265-201.689 100.275-218.026 212.873l-3.126 21.542c-9.426 64.964 36.024 125.564 101.637 132.527 50.473 5.355 120.212 9.626 171.473 11.648l33.616 0.346M611.302 749.039h166.801M623.219 251.259c0 91.638-74.679 165.926-166.801 165.926-92.124 0-166.803-74.288-166.803-165.926s74.679-165.926 166.803-165.926c92.122 0 166.801 74.287 166.801 165.926zM885.333 749.039c0 104.73-85.346 189.628-190.63 189.628-105.28 0-190.63-84.898-190.63-189.628s85.35-189.632 190.63-189.632c105.284 0 190.63 84.902 190.63 189.632z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "user-minus-alt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1979,
        "id": 41,
        "name": "user-minus-alt",
        "prevSize": 32,
        "code": 60255
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 608
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface UserMinusAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const UserMinusAltIcon = ({ size = 16, ...props }: UserMinusAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="user-minus-alt" size={size} {...props} />
);

UserMinusAltIcon.displayName = 'UserMinusAltIcon';

export default UserMinusAltIcon;
