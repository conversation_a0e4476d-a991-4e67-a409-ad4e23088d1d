import { type PropDef, accentColorPropDef, asChildPropDef, highContrastPropDef, radiusPropDef } from '@props';

const sizes = ['1', '2', '3', '4', '5', '6', '7', '8', '9'] as const;
const variants = ['solid', 'soft', 'ghost'] as const;
const shapes = ['circle', 'square'] as const;

const avatarPropDefs = {
  ...asChildPropDef,
  size: { type: 'enum', className: 'rt-r-size', values: sizes, default: '3', responsive: true },
  variant: { type: 'enum', className: 'rt-variant', values: variants, default: 'solid' },
  shape: { type: 'enum', className: 'rt-shape', values: shapes, default: 'circle' },
  fallback: { type: 'ReactNode', required: true },
  ...accentColorPropDef,
  ...highContrastPropDef,
  ...radiusPropDef,
} satisfies {
  size: PropDef<(typeof sizes)[number]>;
  variant: PropDef<(typeof variants)[number]>;
  shape: PropDef<(typeof shapes)[number]>;
  fallback: PropDef<React.ReactNode>;
};

export { avatarPropDefs };
