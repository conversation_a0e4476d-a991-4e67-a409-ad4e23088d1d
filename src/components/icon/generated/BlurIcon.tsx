// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 938.667c-188.513 0-341.333-151.906-341.333-339.285 0-128.619 160.806-348.981 261.67-474.554 21.15-26.329 50.406-39.494 79.663-39.494M512 938.667v-142.221M512 938.667c114.586 0 215.983-56.124 277.888-142.221M512 85.333c29.257 0 58.513 13.164 79.663 39.494 23.893 29.749 51.157 64.819 78.844 102.729M512 85.333v142.222M512 512h323.558M512 512v142.221M512 512v-142.222M835.558 512c11.247 31.834 17.775 61.47 17.775 87.381 0 18.671-1.519 36.988-4.437 54.839M835.558 512c-15.723-44.501-40.674-93.296-69.922-142.222M512 654.221h336.896M512 654.221v142.225M848.896 654.221c-8.567 52.407-29.214 100.787-59.008 142.225M512 369.778h253.636M512 369.778v-142.223M765.636 369.778c-29.077-48.644-62.404-97.417-95.13-142.223M512 796.446h277.888M512 227.555h158.507"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "blur"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1475,
        "id": 545,
        "name": "blur",
        "prevSize": 32,
        "code": 59751
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 104
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BlurIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BlurIcon = ({ size = 16, ...props }: BlurIconProps) => (
  <IcoMoon iconSet={iconSet} icon="blur" size={size} {...props} />
);

BlurIcon.displayName = 'BlurIcon';

export default BlurIcon;
