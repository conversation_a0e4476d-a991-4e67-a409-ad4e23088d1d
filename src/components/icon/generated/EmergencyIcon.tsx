// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M208.593 820.147v-226.5c0-162.914 135.84-294.98 303.407-294.98 167.569 0 303.407 132.066 303.407 294.98v226.5M208.593 820.147h606.814M208.593 820.147c-20.946 0-37.926 16.508-37.926 36.873v44.774c0 20.365 16.98 36.873 37.926 36.873h606.814c20.945 0 37.926-16.508 37.926-36.873v-44.774c0-20.365-16.981-36.873-37.926-36.873M512 85.333v94.815M170.667 180.148l68.96 67.044M853.333 180.148l-68.958 67.044M390.095 571.26h121.905M512 571.26h121.903M512 571.26v-118.519M512 571.26v118.519"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "emergency"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1617,
        "id": 403,
        "name": "emergency",
        "prevSize": 32,
        "code": 59893
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 246
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface EmergencyIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const EmergencyIcon = ({ size = 16, ...props }: EmergencyIconProps) => (
  <IcoMoon iconSet={iconSet} icon="emergency" size={size} {...props} />
);

EmergencyIcon.displayName = 'EmergencyIcon';

export default EmergencyIcon;
