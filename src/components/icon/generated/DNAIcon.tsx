// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M213.333 85.333c0 29.003 2.976 57.64 8.728 85.333M300.811 336.749c56.011 66.679 211.189 175.251 211.189 175.251M300.811 336.749c-38.784-46.172-65.8-103.734-78.749-166.083M300.811 336.749h253.856M512 512c0 0 155.179-108.571 211.187-175.251 56.013-66.679 87.479-157.117 87.479-251.416M512 512c0 0 155.179 108.57 211.187 175.249M213.333 938.667c0-94.298 31.467-184.738 87.477-251.418 24.801-29.525 69.046-67.264 110.652-100.151M723.187 687.249c38.784 46.174 65.801 103.735 78.75 166.084M723.187 687.249h-253.854M810.667 938.667c0-29.005-2.978-57.638-8.73-85.333M222.062 170.667h439.272M801.937 853.333h-439.27"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "DNA"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1602,
        "id": 418,
        "name": "DNA",
        "prevSize": 32,
        "code": 59878
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 231
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface DNAIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const DNAIcon = ({ size = 16, ...props }: DNAIconProps) => (
  <IcoMoon iconSet={iconSet} icon="DNA" size={size} {...props} />
);

DNAIcon.displayName = 'DNAIcon';

export default DNAIcon;
