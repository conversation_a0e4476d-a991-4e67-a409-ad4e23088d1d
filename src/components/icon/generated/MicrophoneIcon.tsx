// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M213.333 346.074v118.519c0 170.185 133.718 308.147 298.667 308.147M810.667 346.074v118.519c0 170.185-133.717 308.147-298.667 308.147M512 772.74v165.926M512 938.667h-137.846M512 938.667h137.847M443.076 369.778c54.332-13.984 84.676-12.746 137.847 0M489.024 488.294c17.907-3.9 28.015-3.635 45.952 0M512 654.221c-101.507 0-183.795-84.898-183.795-189.628v-189.63c0-104.729 82.288-189.629 183.795-189.629 101.508 0 183.795 84.9 183.795 189.629v189.63c0 104.73-82.287 189.628-183.795 189.628z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "microphone"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1772,
        "id": 248,
        "name": "microphone",
        "prevSize": 32,
        "code": 60048
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 401
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MicrophoneIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MicrophoneIcon = ({ size = 16, ...props }: MicrophoneIconProps) => (
  <IcoMoon iconSet={iconSet} icon="microphone" size={size} {...props} />
);

MicrophoneIcon.displayName = 'MicrophoneIcon';

export default MicrophoneIcon;
