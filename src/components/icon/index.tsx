import type { IconProps } from 'react-icomoon';

import type { AvailableIconType } from '@components/icon/icons';

import { type IconComponentName, iconComponentMap } from './generated';

export const iconSizes = [14, 16, 18, 20, 24] as const;

export type IconSize = (typeof iconSizes)[number];

export type IconType = AvailableIconType;

interface IIconName extends Omit<IconProps, 'iconSet' | 'icon'> {
  icon: IconType;
  size?: IconSize | number;
}

const Icon = ({ icon, size = 16, ...props }: IIconName) => {
  // Get the specific icon component
  const IconComponent = iconComponentMap[icon as IconComponentName];

  if (!IconComponent) {
    console.warn(`Icon "${icon}" not found`);
    // Return a fallback div
    return <div style={{ width: size, height: size, display: 'inline-block', backgroundColor: '#ccc' }} />;
  }

  return <IconComponent size={size} {...props} />;
};

Icon.displayName = 'Icon';

export default Icon;

export { availableIcons as iconNames } from './icons/index';
export type { IconProps };
