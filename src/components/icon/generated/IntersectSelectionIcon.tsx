// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M413.213 412.039l198.747 198.747M413.213 412.039c-28.513 28.362-51.505 62.265-67.226 99.961M413.213 412.039c28.119-27.971 61.612-50.548 98.787-66.052M611.959 610.786c55.42-55.714 89.668-132.51 89.668-217.305 0-170.185-137.963-308.148-308.146-308.148-170.185 0-308.148 137.963-308.148 308.148 0 170.184 137.963 308.146 308.148 308.146 42 0 82.034-8.401 118.519-23.616M611.959 610.786c-28.361 28.514-62.263 51.507-99.959 67.226M345.987 512c-15.215 36.484-23.616 76.518-23.616 118.519 0 170.185 137.964 308.147 308.149 308.147s308.147-137.963 308.147-308.147c0-170.185-137.963-308.149-308.147-308.149-42.001 0-82.035 8.402-118.519 23.616M345.987 512l166.013 166.012M512 345.987l165.926 165.928"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "intersect-selection"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1724,
        "id": 296,
        "name": "intersect-selection",
        "prevSize": 32,
        "code": 60000
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 353
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface IntersectSelectionIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const IntersectSelectionIcon = ({ size = 16, ...props }: IntersectSelectionIconProps) => (
  <IcoMoon iconSet={iconSet} icon="intersect-selection" size={size} {...props} />
);

IntersectSelectionIcon.displayName = 'IntersectSelectionIcon';

export default IntersectSelectionIcon;
