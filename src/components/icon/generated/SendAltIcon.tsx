// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M118.021 87.792l808.187 404.052c16.61 8.303 16.61 32.009 0 40.311l-808.187 404.053c-18.536 9.267-38.702-9.289-31.005-28.527l126.632-316.548c2.871-7.177 9.221-12.382 16.822-13.786l328.972-60.915c4.919-0.913 4.919-7.953 0-8.866l-328.972-60.915c-7.601-1.404-13.951-6.609-16.822-13.786l-126.632-316.548c-7.697-19.24 12.469-37.793 31.005-28.526z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "send-alt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1875,
        "id": 145,
        "name": "send-alt",
        "prevSize": 32,
        "code": 60151
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 504
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SendAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SendAltIcon = ({ size = 16, ...props }: SendAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="send-alt" size={size} {...props} />
);

SendAltIcon.displayName = 'SendAltIcon';

export default SendAltIcon;
