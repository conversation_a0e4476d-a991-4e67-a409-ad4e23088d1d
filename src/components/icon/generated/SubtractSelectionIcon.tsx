// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M175.621 175.555c39.719-39.707 90.227-68.624 146.749-81.975M175.621 175.555c-39.742 39.73-68.683 90.263-82.042 146.816M175.621 175.555l435.788 435.786M322.371 93.579c22.828-5.392 46.636-8.246 71.111-8.246 148.108 0 271.811 104.489 301.414 243.772M322.371 93.579l371.014 371.014M693.385 464.593c5.393-22.827 8.243-46.636 8.243-71.112 0-22.078-2.321-43.614-6.733-64.376M693.385 464.593c-13.252 56.107-41.847 106.287-81.105 145.873-0.29 0.294-0.58 0.585-0.87 0.875M93.579 322.371c-5.392 22.828-8.246 46.636-8.246 71.111 0 148.108 104.489 271.811 243.772 301.414M93.579 322.371l371.014 371.014M464.593 693.385c-22.827 5.393-46.636 8.243-71.112 8.243-22.078 0-43.614-2.321-64.376-6.733M464.593 693.385c56.555-13.359 107.085-42.3 146.816-82.044M329.105 694.895c29.603 139.281 153.306 243.772 301.414 243.772 24.474 0 48.282-2.854 71.108-8.247 56.969-13.453 107.827-42.722 147.691-82.914 39.258-39.586 67.849-89.766 81.101-145.877 5.393-22.827 8.247-46.635 8.247-71.108 0-148.109-104.491-271.811-243.772-301.414"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "subtract-selection"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1920,
        "id": 100,
        "name": "subtract-selection",
        "prevSize": 32,
        "code": 60196
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 549
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SubtractSelectionIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SubtractSelectionIcon = ({ size = 16, ...props }: SubtractSelectionIconProps) => (
  <IcoMoon iconSet={iconSet} icon="subtract-selection" size={size} {...props} />
);

SubtractSelectionIcon.displayName = 'SubtractSelectionIcon';

export default SubtractSelectionIcon;
