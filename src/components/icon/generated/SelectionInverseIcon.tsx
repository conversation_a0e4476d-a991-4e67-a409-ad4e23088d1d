// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M924.715 868.779c13.952-27.383 13.952-63.223 13.952-134.912v-443.733c0-71.687 0-107.53-13.952-134.911-12.271-24.085-31.851-43.666-55.936-55.938-27.383-13.951-63.223-13.951-134.912-13.951h-443.733c-71.687 0-107.53 0-134.911 13.951M924.715 868.779c-6.135 12.041-14.097 22.959-23.539 32.397M924.715 868.779c-12.271 24.085-31.851 43.665-55.936 55.936M155.223 99.284c-12.042 6.136-22.959 14.099-32.399 23.539M155.223 99.284c-24.085 12.272-43.666 31.853-55.938 55.938M122.824 122.824c-9.44 9.44-17.403 20.357-23.539 32.399M122.824 122.824l778.352 778.352M99.284 155.223c-13.951 27.38-13.951 63.224-13.951 134.911M901.175 901.175c-9.438 9.442-20.356 17.404-32.397 23.539M868.779 924.715c-27.383 13.952-63.223 13.952-134.912 13.952M290.133 938.667c-71.687 0-107.53 0-134.911-13.952-24.085-12.271-43.666-31.851-55.938-55.936-13.951-27.383-13.951-63.223-13.951-134.912M85.333 597.333v-170.667M597.333 938.667h-170.667"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "selection-inverse"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1871,
        "id": 149,
        "name": "selection-inverse",
        "prevSize": 32,
        "code": 60147
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 500
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SelectionInverseIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SelectionInverseIcon = ({ size = 16, ...props }: SelectionInverseIconProps) => (
  <IcoMoon iconSet={iconSet} icon="selection-inverse" size={size} {...props} />
);

SelectionInverseIcon.displayName = 'SelectionInverseIcon';

export default SelectionInverseIcon;
