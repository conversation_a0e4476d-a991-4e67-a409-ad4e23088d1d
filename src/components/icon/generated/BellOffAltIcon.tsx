// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M149.333 149.333l157.050 157.050M874.667 874.667l-104.282-104.282M389.433 874.667c26.986 38.686 71.819 64 122.567 64s95.582-25.314 122.569-64M306.383 306.383l3.164-11.569c19.167-49.134 66.508-81.481 119.248-81.481h166.409c52.74 0 100.083 32.348 119.249 81.481l82.739 302.519c5.35 13.717 13.879 25.975 24.887 35.759l5.888 5.235c16.132 14.34 25.365 34.897 25.365 56.482 0 41.737-33.835 75.575-75.575 75.575h-7.373M306.383 306.383l464.002 464.002M273.486 426.667l-46.677 170.667c-5.352 13.717-13.881 25.975-24.887 35.759l-5.89 5.235c-16.134 14.34-25.365 34.897-25.365 56.482 0 41.737 33.836 75.575 75.574 75.575h370.963M576 149.333c0 35.346-28.655 64-64 64s-64-28.654-64-64c0-35.346 28.655-64 64-64s64 28.654 64 64z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bell-off-alt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1466,
        "id": 554,
        "name": "bell-off-alt",
        "prevSize": 32,
        "code": 59742
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 95
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BellOffAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BellOffAltIcon = ({ size = 16, ...props }: BellOffAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="bell-off-alt" size={size} {...props} />
);

BellOffAltIcon.displayName = 'BellOffAltIcon';

export default BellOffAltIcon;
