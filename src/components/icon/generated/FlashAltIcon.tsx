// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M128 180.148h286.118M128 843.853h262.275M199.529 369.778h119.216M128 654.221h166.902M438.673 559.407h171.469c17.267 0 29.129 17.626 22.852 33.95l-116.676 331.657c-4.041 10.513 9.532 18.667 16.666 10.014l399.919-485.141c13.363-16.209 2.005-40.877-18.816-40.877h-151.727c-18.564 0-30.4-20.114-21.602-36.702l132.774-250.271c8.802-16.588-3.034-36.702-21.598-36.702h-197.047c-19.307 0-36.821 11.492-44.74 29.361l-193.842 409.597c-7.305 16.478 4.569 35.115 22.369 35.115z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "flash-alt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1659,
        "id": 361,
        "name": "flash-alt",
        "prevSize": 32,
        "code": 59935
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 288
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FlashAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FlashAltIcon = ({ size = 16, ...props }: FlashAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="flash-alt" size={size} {...props} />
);

FlashAltIcon.displayName = 'FlashAltIcon';

export default FlashAltIcon;
