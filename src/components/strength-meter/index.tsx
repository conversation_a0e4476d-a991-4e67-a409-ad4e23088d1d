'use client';

import clsx from 'clsx';
import * as React from 'react';

import { Flex } from '@components/flex';
import { type ComponentPropsWithout, type RemovedProps, extractProps } from '@helpers';
import { type GetPropDefTypes } from '@props';

import { strengthMeterPropDefs } from './strength-meter.props';

interface Thresholds {
  weak: number;
  medium: number;
}

interface StrengthMeterProps
  extends ComponentPropsWithout<'div', RemovedProps>,
    GetPropDefTypes<typeof strengthMeterPropDefs> {
  thresholds: Thresholds;
}

type StrengthMeterElement = React.ComponentRef<'div'>;

const assertValidThresholds = (thresholds: Thresholds, segments: number) => {
  if (
    typeof thresholds.weak !== 'number' ||
    typeof thresholds.medium !== 'number' ||
    thresholds.weak >= thresholds.medium ||
    thresholds.medium >= segments
  ) {
    throw new Error(
      `Invalid "thresholds" prop passed to <StrengthMeter />:\n` +
        `- Must satisfy: weak < medium < segments\n` +
        `- Received: weak=${thresholds.weak}, medium=${thresholds.medium}, segments=${segments}`,
    );
  }
};

const StrengthMeter = React.forwardRef<StrengthMeterElement, StrengthMeterProps>((props, forwardedRef) => {
  const { className, segments = 3, thresholds, ...rest } = extractProps(props, strengthMeterPropDefs);

  assertValidThresholds(thresholds, segments); // runtime validation

  const levelMap: Record<'weak' | 'medium' | 'strong', number> = {
    weak: thresholds.weak ?? Math.max(1, Math.ceil(segments / 3)),
    medium: thresholds.medium ?? Math.max(2, Math.floor((segments * 2) / 3)),
    strong: segments,
  };

  const safeLevel = (props.level ?? 'weak') as keyof typeof levelMap;
  const filledSegments = levelMap[safeLevel];

  return (
    <Flex ref={forwardedRef} className={clsx('rt-StrengthMeter', className)} {...rest}>
      {Array.from({ length: segments }).map((_, idx) => (
        <div key={idx} className={clsx('rt-StrengthMeterBar', { [`rt-level-${safeLevel}`]: idx < filledSegments })} />
      ))}
    </Flex>
  );
});

StrengthMeter.displayName = 'StrengthMeter';

export { StrengthMeter };
export type { StrengthMeterProps };
