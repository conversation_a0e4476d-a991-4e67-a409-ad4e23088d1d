// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 298.667c-59.273 0.213-144.23 17.938-174.091 24.598-6.825 1.522-13.904-0.408-19.575-4.499-17.533-12.647-39.063-20.099-62.334-20.099-53.785 0-92.631 88.794-121.359 177.988-34.565 107.311-49.308 221.269-49.308 334.012 0 70.694 57.308 128 128 128 40.080 0 75.857-18.419 99.328-47.258l22.865-30.451c46.234-61.568 99.477-135.625 176.474-135.625s130.24 74.057 176.474 135.625l22.865 30.451c23.471 28.838 59.247 47.258 99.328 47.258 70.694 0 128-57.306 128-128 0-112.951-11.516-227.23-40.469-336.41-23.595-88.981-58.91-175.59-125.444-175.59-23.275 0-44.813 7.456-62.349 20.11-5.662 4.086-12.715 6.020-19.533 4.511-30.353-6.723-117.901-24.84-178.871-24.621zM512 298.667l-23.949-71.841c-9.207-27.628 11.358-56.159 40.478-56.159h47.471c23.565 0 42.667-19.103 42.667-42.667v-42.667M341.333 448v85.333M341.333 533.333v85.333M341.333 533.333h85.333M341.333 533.333h-85.333M618.24 533.333h0.427M767.573 533.333h0.427M692.907 458.667h0.427M692.907 608h0.427"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "game"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1684,
        "id": 336,
        "name": "game",
        "prevSize": 32,
        "code": 59960
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 313
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface GameIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const GameIcon = ({ size = 16, ...props }: GameIconProps) => (
  <IcoMoon iconSet={iconSet} icon="game" size={size} {...props} />
);

GameIcon.displayName = 'GameIcon';

export default GameIcon;
