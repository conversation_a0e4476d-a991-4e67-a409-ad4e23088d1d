// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M332.8 970.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM155.223 924.715v0zM117.333 691.2c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM99.284 868.779v0zM970.667 691.2c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM924.715 868.779v0zM691.2 906.667c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM868.779 924.715v0zM691.2 53.333c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM868.779 99.284v0zM906.667 332.8c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM924.715 155.223v0zM332.8 117.333c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM155.223 99.284v0zM53.333 332.8c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM99.284 155.223v0zM337.494 738.995v0zM306.417 708.228v0zM717.581 708.228v0zM686.507 738.995v0zM611.554 362.667c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM686.507 402.34v0zM717.581 433.105v0zM412.445 426.667c17.674 0 32.001-14.327 32.001-32s-14.327-32-32.001-32v64zM337.494 402.34v0zM306.417 433.105v0zM606.814 394.667v32h32v-32h-32zM417.185 394.667h-32v32h32v-32zM332.8 938.667v-32c-36.080 0-72.695-0.009-103.663-1.698-15.462-0.841-28.867-2.069-39.67-3.789-11.427-1.818-17.446-3.819-19.717-4.975l-29.055 57.024c11.419 5.815 25.482 9.050 38.716 11.153 13.859 2.206 29.696 3.593 46.245 4.493 33.054 1.801 71.538 1.792 107.144 1.792v-32zM85.333 691.2h-32c0 35.605-0.009 74.091 1.791 107.145 0.902 16.55 2.287 32.384 4.492 46.242 2.106 13.235 5.337 27.298 11.156 38.72l57.024-29.056c-1.157-2.27-3.157-8.29-4.975-19.716-1.719-10.803-2.95-24.209-3.792-39.671-1.687-30.967-1.696-67.584-1.696-103.663h-32zM155.223 924.715l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM938.667 691.2h-32c0 36.079-0.009 72.695-1.698 103.663-0.841 15.462-2.069 28.868-3.789 39.671-1.818 11.426-3.819 17.446-4.975 19.716l57.024 29.056c5.815-11.422 9.050-25.485 11.153-38.72 2.206-13.858 3.593-29.692 4.493-46.242 1.801-33.054 1.792-71.539 1.792-107.145h-32zM691.2 938.667v32c35.605 0 74.091 0.009 107.145-1.792 16.55-0.9 32.384-2.287 46.242-4.493 13.235-2.103 27.298-5.338 38.72-11.153l-29.056-57.024c-2.27 1.156-8.29 3.157-19.716 4.975-10.803 1.719-24.209 2.948-39.671 3.789-30.967 1.69-67.584 1.698-103.663 1.698v32zM924.715 868.779l-28.51-14.528c-9.207 18.061-23.893 32.747-41.954 41.954l29.056 57.024c30.106-15.343 54.579-39.817 69.922-69.922l-28.514-14.528zM691.2 85.333v32c36.079 0 72.695 0.009 103.663 1.696 15.462 0.842 28.868 2.073 39.671 3.792 11.426 1.818 17.446 3.818 19.716 4.975l29.056-57.024c-11.422-5.819-25.485-9.050-38.72-11.156-13.858-2.205-29.692-3.59-46.242-4.492-33.054-1.801-71.539-1.791-107.145-1.791v32zM938.667 332.8h32c0-35.606 0.009-74.090-1.792-107.144-0.9-16.549-2.287-32.386-4.493-46.245-2.103-13.234-5.338-27.297-11.153-38.716l-57.024 29.055c1.156 2.271 3.157 8.29 4.975 19.717 1.719 10.803 2.948 24.208 3.789 39.67 1.69 30.968 1.698 67.583 1.698 103.663h32zM868.779 99.284l-14.528 28.512c18.061 9.204 32.747 23.89 41.954 41.953l57.024-29.055c-15.343-30.106-39.817-54.583-69.922-69.923l-14.528 28.512zM332.8 85.333v-32c-35.606 0-74.090-0.009-107.144 1.791-16.549 0.902-32.386 2.287-46.245 4.492-13.234 2.106-27.297 5.337-38.716 11.156l29.055 57.024c2.271-1.157 8.29-3.157 19.717-4.975 10.803-1.719 24.208-2.95 39.67-3.792 30.968-1.687 67.583-1.696 103.663-1.696v-32zM85.333 332.8h32c0-36.080 0.009-72.695 1.696-103.663 0.842-15.462 2.073-28.867 3.792-39.67 1.818-11.427 3.818-17.446 4.975-19.717l-57.024-29.055c-5.819 11.419-9.050 25.482-11.156 38.716-2.205 13.859-3.59 29.696-4.492 46.245-1.801 33.054-1.791 71.538-1.791 107.144h32zM155.223 99.284l-14.528-28.512c-30.106 15.34-54.583 39.817-69.923 69.923l57.024 29.055c9.204-18.063 23.89-32.75 41.953-41.953l-14.528-28.512zM725.333 507.307h-32v126.72h64v-126.72h-32zM611.554 746.667v-32h-199.11v64h199.11v-32zM298.667 634.027h32v-126.72h-64v126.72h32zM412.445 746.667v-32c-20.436 0-33.857-0.026-44.126-0.853-9.904-0.802-14.042-2.193-16.413-3.392l-28.824 57.143c12.841 6.477 26.266 8.922 40.077 10.039 13.447 1.088 29.895 1.062 49.286 1.062v-32zM298.667 634.027h-32c0 19.179-0.026 35.511 1.077 48.87 1.134 13.743 3.622 27.153 10.22 39.974l56.906-29.286c-1.152-2.24-2.54-6.217-3.343-15.953-0.835-10.116-0.861-23.36-0.861-43.605h-32zM337.494 738.995l14.412-28.574c-7.386-3.725-13.33-9.638-17.036-16.836l-56.906 29.286c9.93 19.294 25.743 34.918 45.117 44.693l14.412-28.57zM725.333 634.027h-32c0 20.245-0.026 33.489-0.862 43.605-0.802 9.737-2.189 13.713-3.341 15.953l56.905 29.286c6.601-12.821 9.088-26.231 10.223-39.974 1.101-13.359 1.075-29.692 1.075-48.87h-32zM611.554 746.667v32c19.392 0 35.84 0.026 49.289-1.062 13.811-1.118 27.234-3.563 40.077-10.039l-28.826-57.143c-2.372 1.199-6.507 2.59-16.414 3.392-10.266 0.828-23.689 0.853-44.126 0.853v32zM717.581 708.228l-28.45-14.643c-3.708 7.198-9.651 13.111-17.037 16.836l28.826 57.143c19.375-9.775 35.187-25.399 45.116-44.693l-28.454-14.643zM611.554 394.667v32c20.437 0 33.86 0.026 44.126 0.853 9.907 0.802 14.042 2.193 16.414 3.392l28.826-57.143c-12.843-6.477-26.266-8.922-40.077-10.039-13.449-1.088-29.897-1.063-49.289-1.063v32zM725.333 507.307h32c0-19.179 0.026-35.511-1.075-48.87-1.135-13.743-3.622-27.153-10.223-39.974l-56.905 29.286c1.152 2.24 2.539 6.217 3.341 15.953 0.836 10.116 0.862 23.36 0.862 43.605h32zM686.507 402.34l-14.413 28.572c7.386 3.725 13.329 9.638 17.037 16.836l56.905-29.286c-9.929-19.293-25.741-34.919-45.116-44.693l-14.413 28.571zM412.445 394.667v-32c-19.39 0-35.838-0.024-49.286 1.063-13.812 1.117-27.237 3.562-40.077 10.039l28.824 57.143c2.371-1.199 6.508-2.59 16.413-3.392 10.269-0.828 23.69-0.853 44.126-0.853v-32zM298.667 507.307h32c0-20.245 0.026-33.489 0.861-43.605 0.803-9.737 2.191-13.713 3.343-15.953l-56.906-29.286c-6.598 12.821-9.086 26.231-10.22 39.974-1.103 13.359-1.077 29.692-1.077 48.87h32zM337.494 402.34l-14.412-28.571c-19.375 9.774-35.188 25.4-45.117 44.693l56.906 29.286c3.706-7.198 9.65-13.111 17.036-16.836l-14.412-28.572zM606.814 371.2h-32v23.467h64v-23.467h-32zM606.814 394.667v-32h-189.629v64h189.629v-32zM417.185 394.667h32.001v-23.467h-64.001v23.467h32zM512 277.333v32c34.995 0 62.814 28.001 62.814 61.867h64c0-69.817-57.079-125.867-126.814-125.867v32zM512 277.333v-32c-69.734 0-126.815 56.050-126.815 125.867h64.001c0-33.865 27.819-61.867 62.814-61.867v-32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "finger-scan-1"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1653,
        "id": 367,
        "name": "finger-scan-1",
        "prevSize": 32,
        "code": 59929
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 282
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FingerScan-1IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FingerScan-1Icon = ({ size = 16, ...props }: FingerScan-1IconProps) => (
  <IcoMoon iconSet={iconSet} icon="finger-scan-1" size={size} {...props} />
);

FingerScan-1Icon.displayName = 'FingerScan-1Icon';

export default FingerScan-1Icon;
