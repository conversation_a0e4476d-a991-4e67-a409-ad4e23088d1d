.rt-TextAreaRoot {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  position: relative;

  font-family: var(--default-font-family);
  font-weight: var(--font-weight-regular);
  font-style: normal;
  text-align: start;
  width: fit-content;
  /* Allows the `resize` property to work on the div */
  /* overflow: hidden; */

  &:hover {
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.04) 0%, rgba(0, 0, 0, 0.04) 100%), var(--fills-secondary);
    transition: width 0.3s;
    transition-timing-function: cubic-bezier(0.44, 0, 0.56, 1);
  }

  &:where(:focus-within) {
    outline: 2px solid var(--brand-accent-40);
    outline-offset: -1px;
  }
}

.rt-TextAreaInput {
  resize: inherit;

  display: block;
  width: 100%;
  height: 100%;
  flex-grow: 1;

  /* scrollbar */
  & {
    /* Arrow mouse cursor over the scrollbar */
    cursor: auto;
  }
  &::-webkit-scrollbar {
    width: var(--spatial-3);
    height: var(--spatial-3);
  }
  &::-webkit-scrollbar-track,
  &::-webkit-scrollbar-thumb {
    background-clip: content-box;
    border: var(--spatial-1) solid transparent;
    border-radius: var(--spatial-3);
  }
  &::-webkit-scrollbar-track {
    background-color: var(--brand-gray300);
  }
  &::-webkit-scrollbar-thumb {
    background-color: var(--brand-gray800);
  }
  @media (hover: hover) {
    :where(&:not(:disabled))::-webkit-scrollbar-thumb:hover {
      background-color: var(--brand-gray900);
    }
  }
  &:where(:autofill, [data-com-onepassword-filled]) {
    /* Reliably removes native autofill colors */
    background-clip: text;
    -webkit-text-fill-color: var(--brand-gray950);
  }
}

/***************************************************************************************************
 *                                                                                                 *
 * SIZES                                                                                           *
 *                                                                                                 *
 ***************************************************************************************************/

@breakpoints {
  .rt-TextAreaRoot {
    &:where(.rt-r-size-1) {
      min-height: var(--spatial-8);
      border-radius: var(--radius-4);
      padding: var(--spatial-3) var(--spatial-4);
      font-size: var(--font-size-3);
      line-height: var(--line-height-3);
      letter-spacing: var(--letter-spacing-3);
    }
    &:where(.rt-r-size-2) {
      min-height: var(--spatial-9);
      border-radius: var(--radius-5);
      padding: var(--spatial-4) var(--spatial-5);
      font-size: var(--font-size-4);
      line-height: var(--line-height-4);
      letter-spacing: var(--letter-spacing-4);
    }
    &:where(.rt-r-size-3) {
      min-height: 80px;
      border-radius: var(--radius-6);
      padding: var(--spatial-5) var(--spatial-6);
      font-size: var(--font-size-5);
      line-height: var(--line-height-5);
      letter-spacing: var(--letter-spacing-5);
    }
  }
}

/***************************************************************************************************
 *                                                                                                 *
 * VARIANTS                                                                                        *
 *                                                                                                 *
 ***************************************************************************************************/

/* surface */
.rt-TextAreaRoot:where(.rt-variant-surface) {
  --text-area-border-width: 1px;

  /* Blend inner shadow with page background */
  background: var(--fills-tertiary);
  box-shadow: inset 0 0 0 var(--text-area-border-width) var(--brand-gray700);
  color: var(--labels-primary);

  & :where(.rt-TextAreaInput) {
    &::placeholder {
      color: var(--labels-secondary);
    }
  }

  /* prettier-ignore */
  &:where(:has(.rt-TextAreaInput:where(:autofill, [data-com-onepassword-filled]):not(:disabled, :read-only))) {
    /* Blend with focus color */
    background-image: linear-gradient(var(--brand-accent-button-bezeled-fill), var(--brand-accent-button-bezeled-fill));
    box-shadow: inset 0 0 0 1px var(--brand-accent-button-bezeled-fill-hover), inset 0 0 0 1px var(--brand-gray500);
  }

  &:where(:has(.rt-TextAreaInput:where(:disabled, :read-only))) {
    /* Blend with grey */
    background: var(--fills-tertiary);
    box-shadow: inset 0 0 0 var(--text-area-border-width) var(--brand-gray600);
  }
}

/* classic */
.rt-TextAreaRoot:where(.rt-variant-classic) {
  --text-area-border-width: 1px;

  /* Blend inner shadow with page background */
  background: var(--fills-tertiary);
  box-shadow:
    inset 0 0 0 1px var(--brand-gray500),
    inset 0 1.5px 2px 0 var(--brand-gray200),
    inset 0 1.5px 2px 0 hexToRgba(var(--grays-black), 0.2);
  color: var(--labels-primary);

  & :where(.rt-TextAreaInput) {
    &::placeholder {
      color: var(--labels-secondary);
    }
  }

  /* prettier-ignore */
  &:where(:has(.rt-TextAreaInput:where(:autofill, [data-com-onepassword-filled]):not(:disabled, :read-only))) {
   /* Blend with focus color */
    background-image: linear-gradient(var(--brand-accent-button-bezeled-fill), var(--brand-accent-button-bezeled-fill));
    box-shadow: inset 0 0 0 1px var(--brand-accent-button-bezeled-fill-hover), inset 0 0 0 1px var(--brand-gray500);
  }

  &:where(:has(.rt-TextAreaInput:where(:disabled, :read-only))) {
    /* Blend with grey */
    background: var(--fills-tertiary);
  }
}

/* soft */
.rt-TextAreaRoot:where(.rt-variant-soft) {
  --text-area-border-width: 0px;

  background: var(--fills-tertiary);
  color: var(--labels-primary);

  & :where(.rt-TextAreaInput) {
    &::selection {
      /* Use gray selection when component color is gray */
      background-color: var(--brand-gray500);
    }
    &::placeholder {
      color: var(--labels-secondary);
      opacity: 0.65;
    }
  }

  &:where(:focus-within) {
    /* Use gray outline when component color is gray */
    outline-color: var(--brand-accent-40);
  }

  /* prettier-ignore */
  &:where(:has(.rt-TextAreaInput:where(:autofill, [data-com-onepassword-filled]):not(:disabled, :read-only))) {
    /* Use gray autofill color when component color is gray */
    box-shadow: inset 0 0 0 1px var(--brand-accent-button-bezeled-fill-hover), inset 0 0 0 1px var(--brand-gray400);
  }

  &:where(:has(.rt-TextAreaInput:where(:disabled, :read-only))) {
    background: var(--fills-tertiary);
  }
}

/* all disabled and read-only text-areas */
.rt-TextAreaInput {
  &:where(:disabled, :read-only) {
    cursor: text;
    color: var(--labels-tertiary);
    /* Safari */
    -webkit-text-fill-color: var(--labels-tertiary);

    &::placeholder {
      opacity: 0.5;
    }
    &:where(:placeholder-shown) {
      cursor: var(--cursor-disabled);
    }
    &::selection {
      background-color: var(--brand-gray500);
    }
    .rt-TextAreaRoot:where(:focus-within:has(&)) {
      outline-color: var(--brand-accent-40);
    }
  }
}
