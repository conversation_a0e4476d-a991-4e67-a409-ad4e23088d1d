// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M592.585 85.333l34.607 34.608M627.191 119.942l276.868 276.867M627.191 119.942l-224.954 224.954M904.060 396.809l34.607 34.607M904.060 396.809l-103.825 103.825M402.237 344.896l397.998 155.738M402.237 344.896l-103.825 103.825M800.235 500.634l-380.693 380.693c-76.455 76.454-200.412 76.454-276.867 0s-76.455-200.414 0-276.868l51.913-51.913M298.412 448.721l69.217 69.218M298.412 448.721l-103.825 103.825M194.587 552.546l69.217 69.218"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "test-tube"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1938,
        "id": 82,
        "name": "test-tube",
        "prevSize": 32,
        "code": 60214
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 567
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface TestTubeIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const TestTubeIcon = ({ size = 16, ...props }: TestTubeIconProps) => (
  <IcoMoon iconSet={iconSet} icon="test-tube" size={size} {...props} />
);

TestTubeIcon.displayName = 'TestTubeIcon';

export default TestTubeIcon;
