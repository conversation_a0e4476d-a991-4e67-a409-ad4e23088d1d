// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M680 322.371c25.037 0 77.197 18.963 85.551 94.815M708.757 190.5c139.853 24.439 186.244 165.332 187.234 309.566 1.22 178.458-113.805 302.793-280.849 346.517-79.279 20.753-187.597-12.39-257.593-40.115-32.602-12.915-70.064 10.987-70.064 46.549v34.287c0 4.096-2.578 7.731-6.401 9.028l-123.497 41.839c-5.502 1.865-11.331-1.754-12.213-7.578l-15.981-105.587c-13.312-87.945 70.964-160.371 158.265-153.779 47.148 3.558 94.442 0.939 130.163-14.955 72.026-32.047 113.311-87.846 127.109-166.524 21.039-119.971-74.829-194.198-118.165-239.549-13.856-14.498-22.259-33.671-22.259-53.883v-101.469c0-5.255 4.192-9.516 9.362-9.516h99.984c4.463 0 8.303 3.202 9.178 7.649l12.305 62.524c5.227 26.579 31.245 43.485 57.579 39.209 34.308-5.572 80.478-10.396 115.844-4.215z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "stomach-care"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1916,
        "id": 104,
        "name": "stomach-care",
        "prevSize": 32,
        "code": 60192
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 545
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface StomachCareIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const StomachCareIcon = ({ size = 16, ...props }: StomachCareIconProps) => (
  <IcoMoon iconSet={iconSet} icon="stomach-care" size={size} {...props} />
);

StomachCareIcon.displayName = 'StomachCareIcon';

export default StomachCareIcon;
