// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M409.171 313.106l96.702-96.703c1.677-2.047 3.904-3.070 6.127-3.070M614.831 313.106l-96.704-96.703c-1.677-2.047-3.904-3.070-6.127-3.070M409.171 710.895l96.702 96.704c1.677 2.044 3.904 3.068 6.127 3.068M614.831 710.895l-96.704 96.704c-1.677 2.044-3.904 3.068-6.127 3.068M512 810.667v-597.333M128 85.333h768M128 938.667h768"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "paragraph-spacing"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1814,
        "id": 206,
        "name": "paragraph-spacing",
        "prevSize": 32,
        "code": 60090
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 443
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ParagraphSpacingIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ParagraphSpacingIcon = ({ size = 16, ...props }: ParagraphSpacingIconProps) => (
  <IcoMoon iconSet={iconSet} icon="paragraph-spacing" size={size} {...props} />
);

ParagraphSpacingIcon.displayName = 'ParagraphSpacingIcon';

export default ParagraphSpacingIcon;
