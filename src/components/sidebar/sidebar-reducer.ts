export interface SidebarState {
  open: boolean;
}

export type ActionType = 'TOGGLE_MENU' | 'SET_OPEN';

export interface SidebarAction {
  type: ActionType;
  payload: Partial<SidebarState>;
}

export const sidebarReducer = (state: SidebarState, action: SidebarAction): SidebarState => {
  switch (action.type) {
    case 'TOGGLE_MENU':
      return {
        ...state,
        open: !state.open,
      };
    case 'SET_OPEN':
      return {
        ...state,
        open: action.payload.open as boolean,
      };
    default:
      return state;
  }
};
