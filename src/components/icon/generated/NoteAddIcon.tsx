// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M640 768h85.333M725.333 768h85.333M725.333 768v-85.333M725.333 768v85.333M298.667 149.333h-42.667c-70.692 0-128 57.308-128 128v533.333c0 70.694 57.308 128 128 128h234.667M298.667 149.333h341.333M298.667 149.333v-64M298.667 149.333v64M640 149.333h42.667c70.694 0 128 57.308 128 128v213.333M640 149.333v-64M640 149.333v64M298.667 384h341.333M298.667 533.333h213.333M298.667 682.667h128M896 768c0 94.255-76.412 170.667-170.667 170.667s-170.667-76.412-170.667-170.667c0-94.255 76.412-170.667 170.667-170.667s170.667 76.412 170.667 170.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "note-add"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1799,
        "id": 221,
        "name": "note-add",
        "prevSize": 32,
        "code": 60075
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 428
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface NoteAddIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const NoteAddIcon = ({ size = 16, ...props }: NoteAddIconProps) => (
  <IcoMoon iconSet={iconSet} icon="note-add" size={size} {...props} />
);

NoteAddIcon.displayName = 'NoteAddIcon';

export default NoteAddIcon;
