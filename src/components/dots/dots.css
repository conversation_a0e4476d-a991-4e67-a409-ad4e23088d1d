.rt-DotWrapper {
  display: flex;
  align-items: center;
  gap: var(--spatial-2);

  &:where(.rt-r-size-1) .rt-Dot {
    width: var(--spatial-2);
    height: var(--spatial-2);
  }

  &:where(.rt-r-size-2) .rt-Dot {
    width: var(--spatial-3);
    height: var(--spatial-3);
  }

  &:where(.rt-r-size-3) .rt-Dot {
    width: var(--spatial-4);
    height: var(--spatial-4);
  }

  &:where(.rt-r-size-4) .rt-Dot {
    width: var(--spatial-5);
    height: var(--spatial-5);
  }

  &:where(.rt-r-size-5) .rt-Dot {
    width: var(--spatial-6);
    height: var(--spatial-6);
  }
}

.rt-Dot {
  border-radius: var(--radius-full);
  background-color: var(--fills-tertiary);
  transition: background-color 0.2s ease;
}

.rt-Dot:where(.rt-active) {
  background-color: var(--labels-success);
}
