// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M610.078 257.271l-485.109 485.111c-48.712 48.713-53.134 123.268-9.876 166.524 43.258 43.26 117.815 38.835 166.527-9.877l485.109-485.108M610.078 257.271l132.305-132.303c48.713-48.713 123.268-53.134 166.524-9.876 43.26 43.258 38.835 117.815-9.877 166.527l-132.301 132.303M610.078 257.271l156.651 156.65M333.929 148.747l-53.799 23.505c-6.89 3.010-6.89 12.784 0 15.794l53.799 23.505c1.99 0.869 3.578 2.457 4.447 4.447l23.505 53.799c3.010 6.89 12.784 6.89 15.794 0l23.505-53.799c0.869-1.99 2.457-3.578 4.447-4.447l53.8-23.505c6.891-3.011 6.891-12.784 0-15.794l-53.8-23.505c-1.99-0.869-3.578-2.457-4.447-4.447l-23.505-53.799c-3.010-6.89-12.784-6.89-15.794 0l-23.505 53.799c-0.869 1.99-2.457 3.578-4.447 4.447zM136.304 397.975l-43.707-5.335c-5.598-0.684-9.325 5.628-6.023 10.2l25.781 35.697c0.954 1.318 1.373 2.948 1.176 4.565l-5.336 43.708c-0.684 5.598 5.629 9.327 10.2 6.025l35.695-25.783c1.32-0.951 2.951-1.374 4.567-1.173l43.708 5.333c5.597 0.683 9.324-5.628 6.023-10.197l-25.781-35.697c-0.953-1.32-1.373-2.951-1.176-4.567l5.335-43.707c0.684-5.598-5.628-9.325-10.2-6.023l-35.695 25.781c-1.32 0.954-2.951 1.373-4.567 1.176zM737.455 775.693l-34.189 47.727c-4.382 6.11 0.623 14.507 8.081 13.568l58.249-7.339c2.155-0.273 4.331 0.277 6.097 1.545l47.727 34.189c6.11 4.382 14.507-0.619 13.568-8.081l-7.339-58.249c-0.273-2.155 0.277-4.331 1.545-6.097l34.189-47.723c4.382-6.114-0.619-14.511-8.081-13.572l-58.249 7.339c-2.155 0.273-4.331-0.277-6.097-1.545l-47.723-34.189c-6.114-4.382-14.511 0.623-13.572 8.081l7.339 58.249c0.273 2.155-0.277 4.331-1.545 6.097z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "magic-wand"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1753,
        "id": 267,
        "name": "magic-wand",
        "prevSize": 32,
        "code": 60029
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 382
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MagicWandIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MagicWandIcon = ({ size = 16, ...props }: MagicWandIconProps) => (
  <IcoMoon iconSet={iconSet} icon="magic-wand" size={size} {...props} />
);

MagicWandIcon.displayName = 'MagicWandIcon';

export default MagicWandIcon;
