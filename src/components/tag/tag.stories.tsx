import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import type { ReactNode } from 'react';

import Icon, { type IconType } from '@components/icon';
import { iconName } from '@components/icon/icon-name';
import { Tag as BaseTag, type TagProps } from '@components/tag';
import { tagPropDefs } from '@components/tag/tag.props';

const prefixSuffixMapping: Record<IconType, React.ReactNode> = iconName.reduce(
  (map, name) => {
    map[name] = <Icon icon={name} size={16} />;
    return map;
  },
  {} as Record<IconType, React.ReactNode>,
);

interface ITag extends TagProps {
  prefix: IconType;
  suffix: IconType;
  children?: ReactNode;
}

const Tag = ({ prefix, suffix, children, ...props }: ITag) => {
  return (
    <BaseTag {...props} className="rt-r-gap-3">
      {prefix}
      {children}
      {suffix}
    </BaseTag>
  );
};

const meta: Meta<typeof Tag> = {
  title: 'DesignSystem/Tag',
  component: Tag,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    variant: { control: 'inline-radio', options: tagPropDefs.variant.values },
    size: { control: 'inline-radio', options: tagPropDefs.size.values },
    radius: { control: 'inline-radio', options: tagPropDefs.radius.values },
    color: { control: 'select', options: tagPropDefs.color.values },
    highContrast: { control: 'boolean' },
    asChild: { control: 'boolean' },
    prefix: {
      control: { type: 'select' },
      options: iconName,
      mapping: prefixSuffixMapping,
    },
    suffix: {
      control: { type: 'select' },
      options: iconName,
      mapping: prefixSuffixMapping,
    },
  },
};

export default meta;
type Story = StoryObj<typeof Tag>;

export const _Tag: Story = {
  args: {
    variant: tagPropDefs.variant.default,
    size: tagPropDefs.size.default,
    radius: tagPropDefs.radius.default,
    color: tagPropDefs.color.default,
    highContrast: false,
    asChild: false,
    prefix: 'screening-mirror',
    suffix: 'check',
    children: 'Popular',
  },
};
