// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 417.971v207.59M512.444 729.353h-0.444M492.676 160.011l-404.529 683.817c-8.185 13.837 2.469 30.839 19.323 30.839h809.061c16.853 0 27.507-17.003 19.324-30.839l-404.531-683.817c-8.422-14.237-30.225-14.237-38.647 0z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "alert-triangle"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1389,
        "id": 631,
        "name": "alert-triangle",
        "prevSize": 32,
        "code": 59665
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 18
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface AlertTriangleIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const AlertTriangleIcon = ({ size = 16, ...props }: AlertTriangleIconProps) => (
  <IcoMoon iconSet={iconSet} icon="alert-triangle" size={size} {...props} />
);

AlertTriangleIcon.displayName = 'AlertTriangleIcon';

export default AlertTriangleIcon;
