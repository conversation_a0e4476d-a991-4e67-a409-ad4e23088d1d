// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M369.778 480c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM654.221 544c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM171.457 211.013l-22.335-22.916-1.261 1.229-1.118 1.359 24.714 20.328zM300.853 129.583c12.656-12.335 12.916-32.595 0.581-45.251s-32.595-12.916-45.251-0.581l44.67 45.833zM171.457 215.654l-24.714 20.328 1.118 1.359 1.261 1.229 22.335-22.916zM256.183 342.916c12.656 12.335 32.916 12.075 45.251-0.581s12.075-32.916-0.581-45.251l-44.67 45.833zM906.667 490.667c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM852.544 812.988l22.332 22.916 1.263-1.229 1.118-1.361-24.713-20.326zM723.145 894.417c-12.655 12.335-12.915 32.593-0.58 45.252 12.335 12.655 32.597 12.915 45.252 0.58l-44.672-45.833zM852.544 808.346l24.713-20.326-1.118-1.361-1.263-1.229-22.332 22.916zM767.817 681.084c-12.655-12.335-32.917-12.075-45.252 0.58-12.335 12.659-12.075 32.917 0.58 45.252l44.672-45.833zM117.333 533.333c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM155.223 796.715v0zM99.284 740.779v0zM868.779 227.284v0zM924.715 283.223v0zM369.778 512v32h284.443v-64h-284.443v32zM171.457 211.013l22.335 22.916 107.061-104.346-44.67-45.833-107.061 104.346 22.335 22.916zM171.457 215.654l-22.335 22.916 107.061 104.346 44.67-45.833-107.061-104.346-22.335 22.916zM171.457 211.013l-24.714-20.328c-5.858 7.122-8.077 15.432-8.077 22.649h64c0 5.537-1.692 12.167-6.495 18.007l-24.714-20.328zM170.666 213.333h-32c0 7.217 2.219 15.527 8.077 22.649l49.428-40.656c4.803 5.84 6.495 12.47 6.495 18.007h-32zM170.666 213.333v32h563.2v-64h-563.2v32zM938.667 418.133h-32v72.533h64v-72.533h-32zM852.544 812.988l-22.336-22.916-107.063 104.346 44.672 45.833 107.059-104.346-22.332-22.916zM852.544 808.346l22.332-22.916-107.059-104.346-44.672 45.833 107.063 104.346 22.336-22.916zM852.544 812.988l24.713 20.326c5.858-7.121 8.077-15.428 8.077-22.647h-64c0-5.538 1.694-12.169 6.494-18.005l24.717 20.326zM853.333 810.667h32c0-7.219-2.219-15.526-8.077-22.647l-49.429 40.653c-4.8-5.837-6.494-12.467-6.494-18.005h32zM853.333 810.667v-32h-563.2v64h563.2v-32zM85.333 605.867h32v-72.533h-64v72.533h32zM290.133 810.667v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM85.333 605.867h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM155.223 796.715l14.528-28.51c-18.063-9.203-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.583 69.923 69.922l14.528-28.514zM733.867 213.333v32c36.369 0 61.726 0.025 81.463 1.638 19.366 1.582 30.494 4.532 38.921 8.826l29.056-57.024c-18.957-9.658-39.441-13.683-62.763-15.589-22.95-1.875-51.362-1.85-86.677-1.85v32zM938.667 418.133h32c0-35.315 0.026-63.727-1.852-86.676-1.903-23.323-5.931-43.809-15.586-62.762l-57.024 29.055c4.292 8.428 7.241 19.553 8.823 38.919 1.613 19.739 1.638 45.093 1.638 81.464h32zM868.779 227.284l-14.528 28.512c18.061 9.204 32.747 23.89 41.954 41.953l57.024-29.055c-15.343-30.106-39.817-54.583-69.922-69.923l-14.528 28.512z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "repeate-minus"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1848,
        "id": 172,
        "name": "repeate-minus",
        "prevSize": 32,
        "code": 60124
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 477
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface RepeateMinusIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const RepeateMinusIcon = ({ size = 16, ...props }: RepeateMinusIconProps) => (
  <IcoMoon iconSet={iconSet} icon="repeate-minus" size={size} {...props} />
);

RepeateMinusIcon.displayName = 'RepeateMinusIcon';

export default RepeateMinusIcon;
