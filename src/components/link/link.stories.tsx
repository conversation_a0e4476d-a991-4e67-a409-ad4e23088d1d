import type { <PERSON>a, StoryObj } from '@storybook/react-vite';
import { fn } from 'storybook/test';

import { iconName } from '@components/icon/icon-name';
import { Link } from '@components/link';
import { linkPropDefs } from '@components/link/link.props';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: 'DesignSystem/Link',
  component: Link,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  // tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    color: { control: 'inline-radio', options: linkPropDefs.color.values },
    size: { control: 'inline-radio', options: linkPropDefs.size.values },
    disabled: { control: 'boolean' },
    title: { control: 'text', type: 'string' },
    prefix: { control: 'select', options: iconName },
    suffix: { control: 'select', options: iconName },
  },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: { onClick: fn() },
} satisfies Meta<typeof Link>;

export default meta;
type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const _link: Story = {
  args: {
    title: 'Link',
    prefix: 'chevron-left',
    suffix: 'chevron-right',
    color: linkPropDefs.color.default,
  },
};
