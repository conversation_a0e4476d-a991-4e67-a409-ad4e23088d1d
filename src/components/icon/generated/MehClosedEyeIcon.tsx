// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M298.667 426.667c9.483-5.672 32.459-17.067 64-17.067s54.517 11.395 64 17.067M597.333 426.667c9.485-5.672 32.461-17.067 64-17.067s54.515 11.395 64 17.067M330.667 661.333h362.667M938.667 512c0 235.639-191.027 426.667-426.667 426.667-235.642 0-426.667-191.027-426.667-426.667 0-235.642 191.025-426.667 426.667-426.667 235.639 0 426.667 191.025 426.667 426.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "meh-closed-eye"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1763,
        "id": 257,
        "name": "meh-closed-eye",
        "prevSize": 32,
        "code": 60039
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 392
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MehClosedEyeIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MehClosedEyeIcon = ({ size = 16, ...props }: MehClosedEyeIconProps) => (
  <IcoMoon iconSet={iconSet} icon="meh-closed-eye" size={size} {...props} />
);

MehClosedEyeIcon.displayName = 'MehClosedEyeIcon';

export default MehClosedEyeIcon;
