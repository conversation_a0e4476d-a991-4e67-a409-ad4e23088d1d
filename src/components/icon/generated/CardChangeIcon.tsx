// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M938.667 621.466l-29.884 53.726c-1.946 3.494-6.054 5.171-9.886 4.032l-59.268-17.596M653.705 789.039l-59.268-17.596c-3.831-1.139-7.94 0.538-9.886 4.032l-29.884 53.726M588.634 678.246c15.433-55.59 57.724-101.658 115.486-117.722 86.656-24.102 175.697 28.356 200.469 117.423M904.7 772.42c-15.433 55.59-57.724 101.658-115.486 117.722-86.528 24.068-175.433-28.198-200.358-117.026M85.333 384v341.333c0 70.694 57.308 128 128 128h213.333M85.333 384v-85.333c0-70.692 57.308-128 128-128h597.333c70.694 0 128 57.308 128 128v85.333M85.333 384h853.333M938.667 384v106.667M234.667 704h106.667"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "card-change"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1520,
        "id": 500,
        "name": "card-change",
        "prevSize": 32,
        "code": 59796
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 149
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CardChangeIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CardChangeIcon = ({ size = 16, ...props }: CardChangeIconProps) => (
  <IcoMoon iconSet={iconSet} icon="card-change" size={size} {...props} />
);

CardChangeIcon.displayName = 'CardChangeIcon';

export default CardChangeIcon;
