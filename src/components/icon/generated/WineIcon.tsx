// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M284.231 357.558c-4.157 41.018-6.898 80.535-6.898 113.427 0 80.346 106.636 155.456 176.141 195.878 18.116 10.534 38.323 15.804 58.526 15.804M284.231 357.558c11.293-111.432 33.038-233.946 38.674-264.843 0.788-4.319 4.658-7.381 9.195-7.381h359.799c4.535 0 8.405 3.063 9.195 7.381 5.636 30.897 27.383 153.411 38.673 264.843M284.231 357.558h455.536M739.767 357.558c4.16 41.018 6.899 80.535 6.899 113.427 0 80.346-106.637 155.456-176.141 195.878-18.116 10.534-38.323 15.804-58.526 15.804M384 938.667h128M640 938.667h-128M512 938.667v-256"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "wine"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 2013,
        "id": 7,
        "name": "wine",
        "prevSize": 32,
        "code": 60289
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 642
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface WineIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const WineIcon = ({ size = 16, ...props }: WineIconProps) => (
  <IcoMoon iconSet={iconSet} icon="wine" size={size} {...props} />
);

WineIcon.displayName = 'WineIcon';

export default WineIcon;
