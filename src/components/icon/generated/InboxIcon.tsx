// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 630.148v171.806c0 64.708 47.301 118.775 108.759 123.409 238.744 18.001 397.34 17.532 635.557-0.171 61.564-4.574 109.018-58.684 109.018-123.499v-171.546M85.333 630.148l78.376-432.122c9.764-53.834 51.936-94.832 103.907-99.769 179.855-17.085 309.259-17.342 488.958-0.109 51.883 4.975 93.935 45.949 103.684 99.695l78.409 432.305M85.333 630.148h184.034c32.483 0 62.179 19.273 76.707 49.783 14.527 30.515 44.223 49.788 76.707 49.788h178.439c32.482 0 62.178-19.273 76.706-49.788 14.528-30.511 44.224-49.783 76.706-49.783h184.034"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "inbox"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1721,
        "id": 299,
        "name": "inbox",
        "prevSize": 32,
        "code": 59997
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 350
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface InboxIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const InboxIcon = ({ size = 16, ...props }: InboxIconProps) => (
  <IcoMoon iconSet={iconSet} icon="inbox" size={size} {...props} />
);

InboxIcon.displayName = 'InboxIcon';

export default InboxIcon;
