// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M248 322.371c66.274 0 120-53.063 120-118.519s-53.726-118.519-120-118.519c-66.274 0-120 53.062-120 118.519s53.726 118.519 120 118.519zM248 322.371v71.111c0 65.454 53.726 118.519 120 118.519h144M776 322.371c66.274 0 120-53.063 120-118.519s-53.726-118.519-120-118.519c-66.274 0-120 53.062-120 118.519s53.726 118.519 120 118.519zM776 322.371v71.111c0 65.454-53.726 118.519-120 118.519h-144M512 701.628c-66.274 0-120 53.065-120 118.519s53.726 118.519 120 118.519c66.274 0 120-53.065 120-118.519s-53.726-118.519-120-118.519zM512 701.628v-189.628"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "git-fork"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1690,
        "id": 330,
        "name": "git-fork",
        "prevSize": 32,
        "code": 59966
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 319
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface GitForkIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const GitForkIcon = ({ size = 16, ...props }: GitForkIconProps) => (
  <IcoMoon iconSet={iconSet} icon="git-fork" size={size} {...props} />
);

GitForkIcon.displayName = 'GitForkIcon';

export default GitForkIcon;
