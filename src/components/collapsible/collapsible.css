.rt-CollapsibleRoot {
  min-width: 300px;
}

.rt-CollapsibleTrigger[data-state='closed'] {
  background-color: var(--background-primary-base);
}
.rt-CollapsibleTrigger[data-state='open']:where(.rt-CollapsibleIcon) {
  transform: rotate(180deg);
}
.rt-CollapsibleTrigger[data-state='open'],
.rt-CollapsibleTrigger:hover {
  background-color: var(--violet-3);
}
.rt-CollapsibleTrigger:focus {
  box-shadow: 0 0 0 2px black;
}

.rt-CollapsibleContent {
  overflow: hidden;
}
.rt-CollapsibleContent[data-state='open'] {
  animation: rt-collapsible-slide-down 300ms cubic-bezier(0.87, 0, 0.13, 1);
}
.rt-CollapsibleContent[data-state='closed'] {
  animation: rt-collapsible-slide-up 300ms cubic-bezier(0.87, 0, 0.13, 1);
}

@keyframes rt-collapsible-slide-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-collapsible-content-height);
  }
}

@keyframes rt-collapsible-slide-up {
  from {
    height: var(--radix-collapsible-content-height);
  }
  to {
    height: 0;
  }
}
