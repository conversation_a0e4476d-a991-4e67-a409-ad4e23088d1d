# 🎨 1. Herond Design System

A scalable, modular React Design System for building consistent, accessible, and efficient interfaces.

# 📄 2. Documents

- [Figma Design System](https://www.figma.com/design/emQ9HHFLdsS54jHtNbeSMZ/Herond-Design-System-2025?node-id=67-161&p=f&t=8AgVZap1Ok3TQajq-0)
- [Git Convention](https://blockbase.sg.larksuite.com/wiki/IhaAwUOrDisUSkkzhtNlBKEqgHe?fromScene=spaceOverview)

# 🚀 3. Deployment Url

- [🌐 Access the Production Storybook here](https://storybook.herond.org/)

# ⚙️ 4. Getting Started (Run the Project)

- ✅ 1. Clone the repository:

```bash
    git clone https://github.com/herondlabs/design-system.git
```

- ✅ 2. Install dependencies:

```bash
    npm i
```

- ✅ 3. Run Storybook (to view and develop UI components):

```bash
    npm run storybook
```

- ✅ 4. **(Optional)** Local Package Linking

This step is useful if you want to **develop and test this design system inside another local project** without publish to private registry.

```bash
    npm install -g yalc
    npm run build
    yalc publish
```

💡 **Note:**: After run this command inside this repository, switch to your target project (the one consuming this design system) and run:

```bash
    yalc add @herondlabs/design-system
```

# 📦 5. Usage Guide (Install & Import the Design System)

In the consuming project:

- ✅ 1. Configure `.npmrc` (for GitHub Packages)

Add the following lines to your `.npmrc` file at the **root of your project** (or globally):

```ini
@herondlabs:registry=https://npm.pkg.github.com/
//npm.pkg.github.com/:_authToken=GITHUB_PERSONAL_ACCESS_TOKEN
```

> 💡 **Note:**
> If you don't have the token, please contact **EM** to request access.

- ✅ 2. Install the Package

```bash
npm install @herondlabs/design-system
```

- ✅ 3. Import Components in Your Project

You can now import components from the design system:

```tsx
import { Button } from '@herondlabs/design-system';
import { Checkbox } from '@herondlabs/design-system';
...
```

```css
import "@herondlabs/design-system/styles.css";
...
```

### 📄 Full Guide: [How to Install npm Private GitHub Packages (Project-Level Setup)](<https://github.com/herondlabs/design-system/wiki/How-to-Install-npm-Private-GitHub-Packages-(Project%E2%80%90Level-Setup)>)

# 🔧 6. Deployment Workflows

- Triggered manually via **workflow_dispatch**.
  - ✅ **Deploy Storybook to Cloudflare Pages**:
    Merge code into the `production` branch first.
    After merging, manually dispatch the workflow from the `production` branch to deploy to the production.

  - ✅ **Publish Private Package to GitHub Packages (npm)**:
    Select the target branch, the workflow will automatically bump the version and publish the package.

# 📜 7. License

This project is proprietary software owned by Herond Labs.

# 📬 8. Contact

For more information, reach out to the **Herond Application** development team.

[![Contributors](https://img.shields.io/github/contributors/herondlabs/design-system.svg?style=for-the-badge)](https://github.com/herondlabs/design-system/graphs/contributors)
