import clsx from 'clsx';
import { Dialog as DialogPrimitive } from 'radix-ui';
import * as React from 'react';

import { Heading } from '@components/heading';
import { Text } from '@components/text';
import { Theme } from '@components/theme';
import type { ComponentPropsAs, ComponentPropsWithout, RemovedProps } from '@helpers';
import { extractProps, requireReactElement } from '@helpers';

import type { DialogContentOwnProps } from './dialog.props';
import { dialogContentPropDefs } from './dialog.props';

interface DialogRootProps extends ComponentPropsWithout<typeof DialogPrimitive.Root, 'modal'> {}
const DialogRoot: React.FC<DialogRootProps> = (props) => <DialogPrimitive.Root {...props} modal />;
DialogRoot.displayName = 'Dialog.Root';

type DialogTriggerElement = React.ComponentRef<typeof DialogPrimitive.Trigger>;
interface DialogTriggerProps extends ComponentPropsWithout<typeof DialogPrimitive.Trigger, RemovedProps> {}
const DialogTrigger = React.forwardRef<DialogTriggerElement, DialogTriggerProps>(
  ({ children, ...props }, forwardedRef) => (
    <DialogPrimitive.Trigger {...props} ref={forwardedRef} asChild>
      {requireReactElement(children)}
    </DialogPrimitive.Trigger>
  ),
);
DialogTrigger.displayName = 'Dialog.Trigger';

type DialogContentElement = React.ComponentRef<typeof DialogPrimitive.Content>;
interface DialogContentProps
  extends ComponentPropsWithout<typeof DialogPrimitive.Content, RemovedProps>,
    DialogContentOwnProps {
  container?: React.ComponentPropsWithoutRef<typeof DialogPrimitive.Portal>['container'];
}
const DialogContent = React.forwardRef<DialogContentElement, DialogContentProps>(
  ({ align, ...props }, forwardedRef) => {
    const { align: alignPropDef, ...propDefs } = dialogContentPropDefs;
    const { className: alignClassName } = extractProps({ align }, { align: alignPropDef });
    const { className, forceMount, container, ...contentProps } = extractProps(props, propDefs);
    return (
      <DialogPrimitive.Portal container={container} forceMount={forceMount}>
        <Theme asChild>
          <DialogPrimitive.Overlay className="rt-BaseDialogOverlay rt-DialogOverlay">
            <div className="rt-BaseDialogScroll rt-DialogScroll">
              <div className={`rt-BaseDialogScrollPadding rt-DialogScrollPadding ${alignClassName}`}>
                <DialogPrimitive.Content
                  {...contentProps}
                  ref={forwardedRef}
                  className={clsx('rt-BaseDialogContent', 'rt-DialogContent', className)}
                />
              </div>
            </div>
          </DialogPrimitive.Overlay>
        </Theme>
      </DialogPrimitive.Portal>
    );
  },
);
DialogContent.displayName = 'Dialog.Content';

type DialogTitleElement = React.ComponentRef<typeof Heading>;
type DialogTitleProps = ComponentPropsWithout<typeof Heading, 'asChild'>;
const DialogTitle = React.forwardRef<DialogTitleElement, DialogTitleProps>((props, forwardedRef) => (
  <DialogPrimitive.Title asChild>
    <Heading size="5" mb="3" trim="start" {...props} asChild={false} ref={forwardedRef} />
  </DialogPrimitive.Title>
));
DialogTitle.displayName = 'Dialog.Title';

type DialogDescriptionElement = HTMLParagraphElement;
type DialogDescriptionProps = ComponentPropsAs<typeof Text, 'p'>;
const DialogDescription = React.forwardRef<DialogDescriptionElement, DialogDescriptionProps>((props, forwardedRef) => (
  <DialogPrimitive.Description asChild>
    <Text as="p" size="3" {...props} asChild={false} ref={forwardedRef} />
  </DialogPrimitive.Description>
));
DialogDescription.displayName = 'Dialog.Description';

type DialogCloseElement = React.ComponentRef<typeof DialogPrimitive.Close>;
interface DialogCloseProps extends ComponentPropsWithout<typeof DialogPrimitive.Close, RemovedProps> {}
const DialogClose = React.forwardRef<DialogCloseElement, DialogCloseProps>(({ children, ...props }, forwardedRef) => (
  <DialogPrimitive.Close {...props} ref={forwardedRef} asChild>
    {requireReactElement(children)}
  </DialogPrimitive.Close>
));
DialogClose.displayName = 'Dialog.Close';

export {
  DialogClose as Close,
  DialogContent as Content,
  DialogDescription as Description,
  DialogRoot as Root,
  DialogTitle as Title,
  DialogTrigger as Trigger,
};

export type {
  DialogCloseProps as CloseProps,
  DialogContentProps as ContentProps,
  DialogDescriptionProps as DescriptionProps,
  DialogRootProps as RootProps,
  DialogTitleProps as TitleProps,
  DialogTriggerProps as TriggerProps,
};
