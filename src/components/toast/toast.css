.rt-ToastViewport {
  --viewport-padding: 25px;
  --viewport-width: 390px;
  position: fixed;
  bottom: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  padding: var(--viewport-padding);
  gap: 10px;
  width: var(--viewport-width);
  max-width: 100vw;
  margin: 0;
  list-style: none;
  z-index: 2147483647;
  outline: none;

  &:where(.rt-align-start) {
    left: 0;
  }

  &:where(.rt-align-center) {
    left: calc(50% - var(--viewport-width) / 2);
  }

  &:where(.rt-align-end) {
    right: 0;
  }

  &:where(.rt-position-top) {
    top: 0;
  }
  &:where(.rt-position-center) {
    top: 50%;
  }
  &:where(.rt-position-bottom) {
    bottom: 0;
  }
}

.rt-ToastViewport:where(.rt-variant-success) .rt-ToastRoot {
  --toast-background: var(--brand-accent);
  --toast-color: var(--brand-gray0);
}

.rt-ToastViewport:where(.rt-variant-warning) .rt-ToastRoot {
  --toast-background: var(--brand-yellow);
  --toast-color: var(--brand-gray0);
}

.rt-ToastViewport:where(.rt-variant-error) .rt-ToastRoot {
  --toast-background: var(--brand-red);
  --toast-color: var(--brand-gray0);
}

.rt-ToastRoot {
  background-color: var(--brand-gray0);
  border-radius: 6px;
  box-shadow:
    hsl(206 22% 7% / 35%) 0px 10px 38px -10px,
    hsl(206 22% 7% / 20%) 0px 10px 20px -15px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  background-color: var(--toast-background);
  color: var(--toast-color);
}
.rt-ToastRoot[data-state='open'] {
  animation: slideIn 150ms cubic-bezier(0.16, 1, 0.3, 1);
}
.rt-ToastRoot[data-state='closed'] {
  animation: hide 100ms ease-in;
}
.rt-ToastRoot[data-swipe='move'] {
  transform: translateX(var(--radix-toast-swipe-move-x));
}
.rt-ToastRoot[data-swipe='cancel'] {
  transform: translateX(0);
  transition: transform 200ms ease-out;
}
.rt-ToastRoot[data-swipe='end'] {
  animation: swipeOut 100ms ease-out;
}

@keyframes hide {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideIn {
  from {
    transform: translateX(calc(100% + var(--viewport-padding)));
  }
  to {
    transform: translateX(0);
  }
}

@keyframes swipeOut {
  from {
    transform: translateX(var(--radix-toast-swipe-end-x));
  }
  to {
    transform: translateX(calc(100% + var(--viewport-padding)));
  }
}

.rt-ToastTitle {
  grid-area: title;
  margin-bottom: 5px;
  font-weight: 500;
  color: var(--slate-12);
  font-size: 15px;
}

.rt-ToastDescription {
  grid-area: description;
  margin: 0;
  color: var(--slate-11);
  font-size: 13px;
  line-height: 1.3;
}

.rt-ToastAction {
  grid-area: action;
}
