// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M132.741 761.894l95.611-23.974c61.488-15.42 126.023-13.547 186.519 5.419 63.401 19.874 131.177 20.958 195.177 3.127l11.081-3.089c68.143-18.987 140.19-18.722 208.192 0.768l61.939 17.749M227.555 896l71.567-19.145c46.091-12.326 94.774-10.833 140.029 4.297 47.42 15.855 98.551 16.721 146.475 2.475l8.222-2.441c50.974-15.151 105.267-14.942 156.126 0.606l46.473 14.208M512 128v106.997M796.446 270.663l-53.158 53.324M227.555 270.663l53.159 53.324M192 555.989h-106.667M938.667 555.989h-106.667M328.345 603.541c-3.9-15.198-5.974-31.134-5.974-47.552 0-105.054 84.9-190.218 189.629-190.218s189.628 85.164 189.628 190.218c0 16.418-2.074 32.354-5.973 47.552"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "cloud-fog"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1566,
        "id": 454,
        "name": "cloud-fog",
        "prevSize": 32,
        "code": 59842
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 195
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CloudFogIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CloudFogIcon = ({ size = 16, ...props }: CloudFogIconProps) => (
  <IcoMoon iconSet={iconSet} icon="cloud-fog" size={size} {...props} />
);

CloudFogIcon.displayName = 'CloudFogIcon';

export default CloudFogIcon;
