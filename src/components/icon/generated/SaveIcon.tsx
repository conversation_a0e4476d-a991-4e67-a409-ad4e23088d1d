// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M277.333 938.667v-93.867c0-71.689 0-107.529 13.951-134.912 12.272-24.085 31.853-43.665 55.938-55.936 27.38-13.952 63.224-13.952 134.911-13.952h59.733c71.689 0 107.529 0 134.912 13.952 24.085 12.271 43.665 31.851 55.936 55.936 13.952 27.383 13.952 63.223 13.952 134.912v93.867M277.333 938.667h469.333M277.333 938.667c-59.64 0-89.461 0-112.983-9.745-31.363-12.988-56.282-37.909-69.273-69.269-9.743-23.526-9.743-53.346-9.743-112.986v-341.836c0-31.308 0-46.961 3.537-61.693 3.136-13.060 8.307-25.546 15.325-36.998 7.916-12.917 18.985-23.986 41.123-46.124l114.697-114.697c22.138-22.138 33.207-33.207 46.124-41.123 11.452-7.018 23.938-12.19 36.998-15.325 14.732-3.537 30.385-3.537 61.693-3.537h329.036c71.689 0 107.529 0 134.912 13.951 24.085 12.272 43.665 31.853 55.936 55.938 13.952 27.38 13.952 63.224 13.952 134.911v456.533c0 59.639 0 89.459-9.745 112.986-12.988 31.36-37.909 56.282-69.269 69.269-23.526 9.745-53.346 9.745-112.986 9.745M341.333 469.333h341.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "save"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1861,
        "id": 159,
        "name": "save",
        "prevSize": 32,
        "code": 60137
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 490
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SaveIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SaveIcon = ({ size = 16, ...props }: SaveIconProps) => (
  <IcoMoon iconSet={iconSet} icon="save" size={size} {...props} />
);

SaveIcon.displayName = 'SaveIcon';

export default SaveIcon;
