// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M597.333 426.667c9.485-5.672 32.461-17.067 64-17.067s54.515 11.395 64 17.067M391.111 618.667v42.667c0 70.694 54.124 128 120.889 128s120.887-57.306 120.887-128v-42.667M391.111 618.667h-39.111M391.111 618.667h241.776M632.887 618.667h39.113M938.667 512c0 235.639-191.027 426.667-426.667 426.667-235.642 0-426.667-191.027-426.667-426.667 0-235.642 191.025-426.667 426.667-426.667 235.639 0 426.667 191.025 426.667 426.667zM426.667 416c0 29.457-23.878 53.333-53.333 53.333s-53.333-23.876-53.333-53.333c0-29.455 23.878-53.333 53.333-53.333s53.333 23.878 53.333 53.333z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "grin-tongue-wink"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1695,
        "id": 325,
        "name": "grin-tongue-wink",
        "prevSize": 32,
        "code": 59971
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 324
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface GrinTongueWinkIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const GrinTongueWinkIcon = ({ size = 16, ...props }: GrinTongueWinkIconProps) => (
  <IcoMoon iconSet={iconSet} icon="grin-tongue-wink" size={size} {...props} />
);

GrinTongueWinkIcon.displayName = 'GrinTongueWinkIcon';

export default GrinTongueWinkIcon;
