// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M300.544 333.657c-4.537-0.299-9.115-0.451-13.729-0.451-111.275 0-201.481 88.365-201.481 197.371 0 83.273 52.648 154.505 127.116 183.488M300.544 333.657c44.844-96.165 143.885-162.991 258.863-162.991 157.094 0 284.446 124.751 284.446 278.639 0 2.637-0.038 5.269-0.111 7.889M300.544 333.657c15.177-0.151 50.271 4.193 69.234 22.769M843.742 457.195c55.296 19.093 94.925 70.729 94.925 131.43 0 55.189-32.759 102.882-80.265 125.44-10.782 5.12-22.323 8.943-34.411 11.268M843.742 457.195c0.034 12.851-4.629 43.196-23.595 61.773",
          "M427.093 645.124h-0.427",
          "M384.427 815.791h-0.427",
          "M635.733 645.124h-0.427",
          "M593.067 815.791h-0.427"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "cloud-snow"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1571,
        "id": 449,
        "name": "cloud-snow",
        "prevSize": 32,
        "code": 59847
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 200
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CloudSnowIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CloudSnowIcon = ({ size = 16, ...props }: CloudSnowIconProps) => (
  <IcoMoon iconSet={iconSet} icon="cloud-snow" size={size} {...props} />
);

CloudSnowIcon.displayName = 'CloudSnowIcon';

export default CloudSnowIcon;
