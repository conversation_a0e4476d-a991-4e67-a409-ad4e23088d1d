// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M702.758 85.333c2.185 0 4.369 0.678 6.037 2.033l187.204 189.967M702.758 85.333c-2.18 0-4.365 0.678-6.033 2.033l-187.204 189.967M702.758 85.333v565.952c0 158.716-128.661 287.381-287.378 287.381-158.716 0-287.38-128.666-287.38-287.381v-373.952"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "arrow-turn-up"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1426,
        "id": 594,
        "name": "arrow-turn-up",
        "prevSize": 32,
        "code": 59702
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 55
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ArrowTurnUpIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ArrowTurnUpIcon = ({ size = 16, ...props }: ArrowTurnUpIconProps) => (
  <IcoMoon iconSet={iconSet} icon="arrow-turn-up" size={size} {...props} />
);

ArrowTurnUpIcon.displayName = 'ArrowTurnUpIcon';

export default ArrowTurnUpIcon;
