// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 321.24c0-2.184 0.678-4.367 2.033-6.034l189.967-187.206M85.333 321.24c0 2.184 0.678 4.368 2.033 6.034l189.967 187.205M85.333 321.24h565.952c158.716 0 287.381 128.663 287.381 287.379s-128.666 287.381-287.381 287.381h-373.952"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "arrow-turn-left"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1424,
        "id": 596,
        "name": "arrow-turn-left",
        "prevSize": 32,
        "code": 59700
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 53
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ArrowTurnLeftIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ArrowTurnLeftIcon = ({ size = 16, ...props }: ArrowTurnLeftIconProps) => (
  <IcoMoon iconSet={iconSet} icon="arrow-turn-left" size={size} {...props} />
);

ArrowTurnLeftIcon.displayName = 'ArrowTurnLeftIcon';

export default ArrowTurnLeftIcon;
