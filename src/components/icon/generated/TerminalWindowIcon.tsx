// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M317.867 358.4c-14.138-10.604-34.196-7.738-44.8 6.4s-7.738 34.196 6.4 44.8l38.4-51.2zM457.954 503.467v0zM457.954 520.533v0zM279.467 614.4c-14.138 10.603-17.004 30.66-6.4 44.8s30.662 17.003 44.8 6.4l-38.4-51.2zM554.667 608c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM725.333 672c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM155.223 882.048v0zM99.284 826.112v0zM924.715 826.112v0zM868.779 882.048v0zM868.779 141.951v0zM924.715 197.889v0zM155.223 141.951v0zM99.284 197.889v0zM298.667 384l-19.2 25.6 159.287 119.467 38.4-51.2-159.287-119.467-19.2 25.6zM457.954 520.533l-19.2-25.6-159.287 119.467 38.4 51.2 159.287-119.467-19.2-25.6zM457.954 503.467l-19.2 25.6c-11.375-8.533-11.375-25.6 0-34.133l38.4 51.2c22.758-17.067 22.758-51.2 0-68.267l-19.2 25.6zM554.667 640v32h170.667v-64h-170.667v32zM290.133 128v32h443.733v-64h-443.733v32zM938.667 332.8h-32v358.4h64v-358.4h-32zM733.867 896v-32h-443.733v64h443.733v-32zM85.333 691.2h32v-358.4h-64v358.4h32zM290.133 896v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM85.333 691.2h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM155.223 882.048l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM938.667 691.2h-32c0 36.373-0.026 61.726-1.638 81.463-1.583 19.366-4.531 30.494-8.823 38.921l57.024 29.056c9.655-18.957 13.683-39.441 15.586-62.763 1.877-22.95 1.852-51.362 1.852-86.677h-32zM733.867 896v32c35.315 0 63.727 0.026 86.677-1.852 23.322-1.903 43.806-5.931 62.763-15.586l-29.056-57.024c-8.427 4.292-19.554 7.241-38.921 8.823-19.738 1.613-45.090 1.638-81.463 1.638v32zM924.715 826.112l-28.51-14.528c-9.207 18.061-23.893 32.747-41.954 41.954l29.056 57.024c30.106-15.343 54.579-39.817 69.922-69.922l-28.514-14.528zM733.867 128v32c36.373 0 61.726 0.025 81.463 1.638 19.366 1.582 30.494 4.532 38.921 8.826l29.056-57.024c-18.957-9.658-39.441-13.683-62.763-15.589-22.95-1.875-51.362-1.85-86.677-1.85v32zM938.667 332.8h32c0-35.315 0.026-63.727-1.852-86.676-1.903-23.323-5.931-43.809-15.586-62.762l-57.024 29.055c4.292 8.428 7.241 19.553 8.823 38.919 1.613 19.739 1.638 45.093 1.638 81.464h32zM868.779 141.951l-14.528 28.512c18.061 9.204 32.747 23.89 41.954 41.953l57.024-29.055c-15.343-30.106-39.817-54.583-69.922-69.923l-14.528 28.512zM290.133 128v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM85.333 332.8h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM155.223 141.951l-14.528-28.512c-30.106 15.34-54.583 39.817-69.923 69.923l57.024 29.055c9.204-18.063 23.89-32.75 41.953-41.953l-14.528-28.512z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "terminal-window"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1936,
        "id": 84,
        "name": "terminal-window",
        "prevSize": 32,
        "code": 60212
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 565
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface TerminalWindowIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const TerminalWindowIcon = ({ size = 16, ...props }: TerminalWindowIconProps) => (
  <IcoMoon iconSet={iconSet} icon="terminal-window" size={size} {...props} />
);

TerminalWindowIcon.displayName = 'TerminalWindowIcon';

export default TerminalWindowIcon;
