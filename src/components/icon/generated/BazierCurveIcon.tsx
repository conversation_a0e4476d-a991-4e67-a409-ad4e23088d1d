// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 298.668v-47.407M85.333 298.668v47.407M85.333 298.668h331.85M938.662 298.668v-47.407M938.662 298.668v47.407M938.662 298.668h-331.849M606.814 298.668c0-52.364-42.453-94.814-94.814-94.814-52.365 0-94.816 42.45-94.816 94.814M606.814 298.668c0 4.408-0.303 8.745-0.883 12.993M417.184 298.668c0 4.408 0.301 8.745 0.883 12.993M418.066 311.661c6.334 46.217 45.976 81.822 93.934 81.822 47.953 0 87.595-35.604 93.931-81.822M418.066 311.661c-140.745 39.898-244.742 167.221-249.593 319.571M605.931 311.661c140.745 39.898 244.74 167.221 249.591 319.571M168.473 631.232c-46.857 5.751-83.14 45.687-83.14 94.101 0 52.365 42.45 94.814 94.814 94.814s94.814-42.449 94.814-94.814c0-52.365-42.449-94.814-94.814-94.814-3.953 0-7.849 0.243-11.674 0.713zM855.522 631.232c-3.823-0.469-7.723-0.713-11.674-0.713-52.365 0-94.814 42.449-94.814 94.814s42.449 94.814 94.814 94.814c52.365 0 94.814-42.449 94.814-94.814 0-48.414-36.284-88.35-83.14-94.101z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bazier-curve"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1459,
        "id": 561,
        "name": "bazier-curve",
        "prevSize": 32,
        "code": 59735
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 88
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BazierCurveIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BazierCurveIcon = ({ size = 16, ...props }: BazierCurveIconProps) => (
  <IcoMoon iconSet={iconSet} icon="bazier-curve" size={size} {...props} />
);

BazierCurveIcon.displayName = 'BazierCurveIcon';

export default BazierCurveIcon;
