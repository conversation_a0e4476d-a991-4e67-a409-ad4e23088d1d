// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M847.095 567.454v0zM697.762 554.859v0zM570.825 122.824v0zM773.175 325.176v0zM197.889 924.715v0zM141.951 868.779v0zM141.951 155.223v0zM197.889 99.284v0zM298.667 544c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM512 608c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM298.667 693.333c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM426.667 757.333c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM799.168 362.667v0zM778.667 448c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM384 970.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM640 873.886h-32c0 18.47-14.69 32.781-32 32.781v64c53.385 0 96-43.695 96-96.781h-32zM576 938.667v-32c-17.31 0-32-14.31-32-32.781h-64c0 53.086 42.615 96.781 96 96.781v-32zM512 873.886h32c0-18.47 14.69-32.781 32-32.781v-64c-53.385 0-96 43.699-96 96.781h32zM576 809.105v32c17.31 0 32 14.31 32 32.781h64c0-53.082-42.615-96.781-96-96.781v32zM896 873.886h-32c0 18.47-14.69 32.781-32 32.781v64c53.385 0 96-43.695 96-96.781h-32zM832 938.667v-32c-17.31 0-32-14.31-32-32.781h-64c0 53.086 42.615 96.781 96 96.781v-32zM768 873.886h32c0-18.47 14.69-32.781 32-32.781v-64c-53.385 0-96 43.699-96 96.781h32zM832 809.105v32c17.31 0 32 14.31 32 32.781h64c0-53.082-42.615-96.781-96-96.781v32zM697.762 554.859l-2.688 31.885 149.333 12.595 5.376-63.774-149.333-12.595-2.688 31.889zM640 873.886h32v-223.219h-64v223.219h32zM640 650.667h32v-42.014h-64v42.014h32zM896 621.252h-32v50.748h64v-50.748h-32zM896 672h-32v201.886h64v-201.886h-32zM640 650.667l-2.658 31.889 256 21.333 5.316-63.778-256-21.333-2.658 31.889zM847.095 567.454l-2.688 31.885c10.752 0.909 19.593 10.146 19.593 21.914h64c0-44.386-33.681-81.929-78.217-85.687l-2.688 31.889zM697.762 554.859l2.688-31.889c-50.172-4.233-92.45 35.78-92.45 85.683h64c0-13.261 11.055-22.925 23.074-21.909l2.688-31.885zM128 733.867h32v-443.733h-64v443.733h32zM570.825 122.824l-22.63 22.627 202.355 202.353 45.252-45.255-202.351-202.353-22.626 22.627zM332.8 85.333v32h147.516v-64h-147.516v32zM332.8 938.667v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM128 733.867h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM197.889 924.715l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM128 290.133h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM332.8 85.333v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM141.951 155.223l28.512 14.528c9.204-18.063 23.89-32.75 41.953-41.953l-29.055-57.024c-30.106 15.34-54.583 39.817-69.923 69.923l28.512 14.528zM298.667 576v32h213.333v-64h-213.333v32zM298.667 725.333v32h128v-64h-128v32zM570.825 122.824l22.626-22.627c-13.679-13.681-29.585-24.629-46.861-32.492l-26.513 58.252c10.368 4.718 19.908 11.285 28.117 19.495l22.63-22.627zM533.333 96.83l13.257-29.126c-20.617-9.381-43.191-14.371-66.274-14.371v64c13.85 0 27.392 2.993 39.761 8.623l13.257-29.126zM533.333 96.83h-32v137.836h64v-137.836h-32zM810.667 415.686h32c0-23.087-4.992-45.66-14.37-66.274l-58.253 26.509c5.632 12.372 8.623 25.913 8.623 39.765h32zM799.168 362.667l29.129-13.254c-7.863-17.277-18.812-33.183-32.495-46.863l-45.252 45.255c8.209 8.209 14.775 17.75 19.494 28.117l29.124-13.254zM661.333 362.667v32h137.835v-64h-137.835v32zM533.333 234.667h-32c0 88.366 71.633 160 160 160v-64c-53.018 0-96-42.981-96-96h-32zM810.667 415.686h-32v32.314h64v-32.314h-32zM384 938.667v-32h-51.2v64h51.2v-32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "music-library-1"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1795,
        "id": 225,
        "name": "music-library-1",
        "prevSize": 32,
        "code": 60071
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 424
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MusicLibrary1IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MusicLibrary1Icon = ({ size = 16, ...props }: MusicLibrary1IconProps) => (
  <IcoMoon iconSet={iconSet} icon="music-library-1" size={size} {...props} />
);

MusicLibrary1Icon.displayName = 'MusicLibrary1Icon';

export default MusicLibrary1Icon;
