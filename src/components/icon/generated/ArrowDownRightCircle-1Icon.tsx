// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M638.673 454.741l1.327 181.257c-0.102 1.003-0.602 1.95-1.327 2.675M454.741 638.673l181.257 1.327c1.003-0.102 1.95-0.602 2.675-1.327M638.673 638.673l-254.673-254.673M938.667 512c0 235.639-191.027 426.667-426.667 426.667-235.642 0-426.667-191.027-426.667-426.667 0-235.642 191.025-426.667 426.667-426.667 235.639 0 426.667 191.025 426.667 426.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "arrow-down-right-circle-1"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1408,
        "id": 612,
        "name": "arrow-down-right-circle-1",
        "prevSize": 32,
        "code": 59684
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 37
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ArrowDownRightCircle-1IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ArrowDownRightCircle-1Icon = ({ size = 16, ...props }: ArrowDownRightCircle-1IconProps) => (
  <IcoMoon iconSet={iconSet} icon="arrow-down-right-circle-1" size={size} {...props} />
);

ArrowDownRightCircle-1Icon.displayName = 'ArrowDownRightCircle-1Icon';

export default ArrowDownRightCircle-1Icon;
