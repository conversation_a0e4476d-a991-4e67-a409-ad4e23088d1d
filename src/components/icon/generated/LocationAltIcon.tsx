// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M723.187 756.574c41.771 14.921 70.217 33.924 81.741 54.618 11.524 20.689 5.611 42.138-16.994 61.628-22.609 19.49-60.89 36.151-110.003 47.872-49.118 11.721-106.859 17.975-165.931 17.975s-116.815-6.255-165.93-17.975c-49.115-11.721-87.396-28.382-110.001-47.872s-28.52-40.939-16.996-61.628c11.524-20.693 39.969-39.697 81.739-54.618M810.667 375.467c0 160.235-298.667 435.2-298.667 435.2s-298.667-274.965-298.667-435.2c0-160.236 133.718-290.133 298.667-290.133s298.667 129.897 298.667 290.133zM624 375.467c0 60.087-50.142 108.8-112 108.8-61.854 0-112-48.713-112-108.8 0-60.089 50.146-108.8 112-108.8 61.858 0 112 48.711 112 108.8z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "location-alt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1742,
        "id": 278,
        "name": "location-alt",
        "prevSize": 32,
        "code": 60018
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 371
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface LocationAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const LocationAltIcon = ({ size = 16, ...props }: LocationAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="location-alt" size={size} {...props} />
);

LocationAltIcon.displayName = 'LocationAltIcon';

export default LocationAltIcon;
