// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M794.453 384c0 0 64 42.667 64 128s-64 128-64 128M279.29 300.354h26.681c27.003 0 53.133-8.876 73.747-25.050l134.897-105.846c55.637-43.654 141.184-12.205 147.661 55.11 19.849 206.251 19.785 366.174-0.154 574.75-6.443 67.379-92.070 98.914-147.755 55.223l-134.65-105.655c-20.614-16.175-46.744-25.050-73.747-25.050h-26.681c-63.055 0-114.17-47.398-114.17-105.869v-211.743c0-58.471 51.116-105.871 114.17-105.871z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "volume-low"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1995,
        "id": 25,
        "name": "volume-low",
        "prevSize": 32,
        "code": 60271
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 624
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface VolumeLowIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const VolumeLowIcon = ({ size = 16, ...props }: VolumeLowIconProps) => (
  <IcoMoon iconSet={iconSet} icon="volume-low" size={size} {...props} />
);

VolumeLowIcon.displayName = 'VolumeLowIcon';

export default VolumeLowIcon;
