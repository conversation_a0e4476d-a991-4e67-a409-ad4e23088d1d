.rt-StrengthMeter {
  gap: var(--spatial-2);
}

.rt-StrengthMeterBar {
  flex: 1;
  height: var(--spatial-2);
  border-radius: var(--radius-2);
  background-color: var(--fills-primary);
  transition: background-color 200ms ease;

  &:where(.rt-level-weak) {
    background-color: var(--labels-error);
  }

  &:where(.rt-level-medium) {
    background-color: var(--labels-warning);
  }

  &:where(.rt-level-strong) {
    background-color: var(--labels-success);
  }
}
