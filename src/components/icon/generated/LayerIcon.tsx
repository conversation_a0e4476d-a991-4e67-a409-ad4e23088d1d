// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M933.141 536.418c-4.949 10.295-13.235 19.26-24.853 25.114l-334.848 168.704c-38.677 19.486-84.203 19.486-122.88 0l-334.848-168.704c-11.616-5.854-19.901-14.818-24.855-25.114M90.857 730.236c4.953 10.291 13.238 19.255 24.855 25.109l334.848 168.708c38.677 19.486 84.203 19.486 122.88 0l334.848-168.708c11.618-5.854 19.904-14.818 24.853-25.109M115.712 268.655l334.848-168.706c38.677-19.487 84.203-19.487 122.88 0l334.848 168.706c40.503 20.407 40.503 78.652 0 99.060l-334.848 168.704c-38.677 19.49-84.203 19.49-122.88 0l-334.848-168.704c-40.504-20.407-40.504-78.652 0-99.060z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "layer"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1728,
        "id": 292,
        "name": "layer",
        "prevSize": 32,
        "code": 60004
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 357
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface LayerIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const LayerIcon = ({ size = 16, ...props }: LayerIconProps) => (
  <IcoMoon iconSet={iconSet} icon="layer" size={size} {...props} />
);

LayerIcon.displayName = 'LayerIcon';

export default LayerIcon;
