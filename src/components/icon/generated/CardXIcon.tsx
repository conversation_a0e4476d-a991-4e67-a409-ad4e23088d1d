// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M707.661 664.994l60.339 60.339M768 725.333l60.339 60.339M768 725.333l60.339-60.339M768 725.333l-60.339 60.339M85.333 384v341.333c0 70.694 57.308 128 128 128h298.667M85.333 384v-85.333c0-70.692 57.308-128 128-128h597.333c70.694 0 128 57.308 128 128v85.333M85.333 384h853.333M938.667 384v106.667M234.667 704h106.667M938.667 725.333c0 94.255-76.412 170.667-170.667 170.667s-170.667-76.412-170.667-170.667c0-94.255 76.412-170.667 170.667-170.667s170.667 76.412 170.667 170.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "card-x"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1528,
        "id": 492,
        "name": "card-x",
        "prevSize": 32,
        "code": 59804
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 157
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CardXIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CardXIcon = ({ size = 16, ...props }: CardXIconProps) => (
  <IcoMoon iconSet={iconSet} icon="card-x" size={size} {...props} />
);

CardXIcon.displayName = 'CardXIcon';

export default CardXIcon;
