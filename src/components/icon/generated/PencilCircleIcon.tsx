// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M544.154 359.111l46.106-46.106c33.34-33.34 87.394-33.34 120.734 0 33.344 33.34 33.344 87.395 0 120.736l-46.106 46.106M544.154 359.111l-199.265 199.263M544.154 359.111c35.051 12.639 59.307 25.397 77.581 43.156 19.085 18.55 31.646 42.554 43.153 77.58M664.887 479.846l-199.262 199.266M344.889 558.374l-15.152 15.155c-0.722 0.721-1.19 1.655-1.335 2.667l-19.020 133.082c-0.445 3.115 2.225 5.786 5.339 5.342l133.083-19.021c1.011-0.145 1.946-0.614 2.667-1.335l15.155-15.151M344.889 558.374l120.737 120.738M938.667 512c0 235.639-191.027 426.667-426.667 426.667-235.642 0-426.667-191.027-426.667-426.667 0-235.642 191.025-426.667 426.667-426.667 235.639 0 426.667 191.025 426.667 426.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "pencil-circle"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1819,
        "id": 201,
        "name": "pencil-circle",
        "prevSize": 32,
        "code": 60095
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 448
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface PencilCircleIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const PencilCircleIcon = ({ size = 16, ...props }: PencilCircleIconProps) => (
  <IcoMoon iconSet={iconSet} icon="pencil-circle" size={size} {...props} />
);

PencilCircleIcon.displayName = 'PencilCircleIcon';

export default PencilCircleIcon;
