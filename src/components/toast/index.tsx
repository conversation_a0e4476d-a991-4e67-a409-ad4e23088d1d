import clsx from 'clsx';
import { Toast as RadixToast } from 'radix-ui';
import React, { useImperativeHandle } from 'react';

import { toastPropsDefs } from '@components/toast/toast.props';
import { extractProps } from '@helpers';
import type { GetPropDefTypes } from '@props';

type ToastOwnProps = GetPropDefTypes<typeof toastPropsDefs>;
export interface ToastProps extends ToastOwnProps {
  title: string | React.ReactNode;
  description?: string | React.ReactNode;
  duration?: number;
}

export interface ToastRef {
  show: () => void;
  hide: () => void;
}

export const Toast = React.forwardRef<ToastRef, ToastProps>((props, ref) => {
  const [open, setOpen] = React.useState(false);
  const { className, description, title } = extractProps(props, toastPropsDefs);
  useImperativeHandle(ref, () => ({ show: () => setOpen(true), hide: () => setOpen(false) }), []);
  return (
    <RadixToast.Provider swipeDirection={props.direction}>
      <RadixToast.Root className={clsx('rt-ToastRoot')} open={open} onOpenChange={setOpen} duration={props.duration}>
        <RadixToast.Title className="rt-ToastTitle">{title}</RadixToast.Title>
        <RadixToast.Description>{description}</RadixToast.Description>
      </RadixToast.Root>
      <RadixToast.Viewport className={clsx('rt-ToastViewport', className)} />
    </RadixToast.Provider>
  );
});
