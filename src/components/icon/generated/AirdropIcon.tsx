// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M696.371 696.371c36.467-36.463 61.299-82.923 71.36-133.504 10.061-50.577 4.898-103.006-14.839-150.648-19.733-47.644-53.154-88.367-96.034-117.017-42.876-28.651-93.286-43.943-144.858-43.943s-101.981 15.292-144.86 43.943c-42.879 28.65-76.298 69.373-96.033 117.017-19.735 47.642-24.898 100.071-14.838 150.648 10.061 50.581 34.894 97.041 71.359 133.504M813.7 813.7c59.669-59.674 100.305-135.697 116.766-218.462 16.465-82.765 8.017-168.555-24.277-246.516-32.294-77.964-86.98-144.599-157.146-191.482s-152.657-71.906-237.043-71.906c-84.386 0-166.878 25.024-237.043 71.906s-124.852 113.519-157.145 191.482c-32.294 77.962-40.743 163.752-24.28 246.517s57.099 158.788 116.769 218.462M606.814 512c0 52.365-42.449 94.814-94.814 94.814s-94.815-42.449-94.815-94.814c0-52.365 42.45-94.815 94.815-94.815s94.814 42.45 94.814 94.815zM373.884 938.667h276.233c43.332 0 63.953-53.338 31.889-82.487l-138.116-125.559c-18.082-16.439-45.696-16.439-63.778 0l-138.117 125.559c-32.063 29.15-11.442 82.487 31.89 82.487z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "airdrop"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1379,
        "id": 641,
        "name": "airdrop",
        "prevSize": 32,
        "code": 59655
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 8
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface AirdropIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const AirdropIcon = ({ size = 16, ...props }: AirdropIconProps) => (
  <IcoMoon iconSet={iconSet} icon="airdrop" size={size} {...props} />
);

AirdropIcon.displayName = 'AirdropIcon';

export default AirdropIcon;
