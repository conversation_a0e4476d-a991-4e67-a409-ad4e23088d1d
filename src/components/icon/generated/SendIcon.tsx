// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M99.908 356.687l810.667-270.223c16.661-5.554 32.516 10.298 26.961 26.961l-270.221 810.667c-6.199 18.594-32.094 19.669-39.812 1.655l-127.023-296.38c-2.88-6.72-2.112-14.447 2.031-20.471l179.247-260.727c2.679-3.896-2.031-8.606-5.926-5.928l-260.727 179.248c-6.025 4.143-13.751 4.911-20.471 2.031l-296.38-127.022c-18.014-7.721-16.937-33.614 1.656-39.811z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "send"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1876,
        "id": 144,
        "name": "send",
        "prevSize": 32,
        "code": 60152
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 505
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SendIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SendIcon = ({ size = 16, ...props }: SendIconProps) => (
  <IcoMoon iconSet={iconSet} icon="send" size={size} {...props} />
);

SendIcon.displayName = 'SendIcon';

export default SendIcon;
