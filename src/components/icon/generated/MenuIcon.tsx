// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M469.333 258.344c14.007-1.549 28.245-2.344 42.667-2.344s28.659 0.795 42.667 2.344M469.333 258.344c-191.998 21.224-341.333 183.999-341.333 381.656v88.977M469.333 258.344v-45.010c0-23.564 19.102-42.667 42.667-42.667s42.667 19.103 42.667 42.667v45.010M554.667 258.344c192 21.224 341.333 183.999 341.333 381.656v88.977M128 728.977c6.673-2.359 13.853-3.644 21.333-3.644h725.333c7.479 0 14.66 1.284 21.333 3.644M128 728.977c-24.858 8.785-42.667 32.491-42.667 60.356 0 35.345 28.654 64 64 64h725.333c35.345 0 64-28.655 64-64 0-27.866-17.809-51.571-42.667-60.356M640 426.667c40.311 20.156 80.623 59.354 107.332 106.667"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "menu"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1766,
        "id": 254,
        "name": "menu",
        "prevSize": 32,
        "code": 60042
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 395
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MenuIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MenuIcon = ({ size = 16, ...props }: MenuIconProps) => (
  <IcoMoon iconSet={iconSet} icon="menu" size={size} {...props} />
);

MenuIcon.displayName = 'MenuIcon';

export default MenuIcon;
