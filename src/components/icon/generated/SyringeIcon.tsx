// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M731.614 85.333l103.526 103.527M835.14 188.86l103.526 103.526M835.14 188.86l-109.807 109.807M552.789 264.158l-34.509-34.509M552.789 264.158l34.509 34.509M552.789 264.158l-135.604 135.604M180.148 843.853l-12.229-134.515c-3.184-35.029 9.355-69.666 34.227-94.537l37.847-37.85M180.148 843.853l134.516 12.228c35.030 3.183 69.664-9.357 94.536-34.227l350.643-350.643M180.148 843.853l-94.815 94.814M759.842 471.211l34.509 34.509M759.842 471.211l-34.509-34.509M587.298 298.667l35.494-35.496c18.513-18.514 48.533-18.514 67.046 0l35.494 35.496M587.298 298.667l138.035 138.035M725.333 436.702l35.494-35.495c18.513-18.514 18.513-48.53 0-67.044l-35.494-35.496M239.993 576.951l58.673 58.675M239.993 576.951l88.657-88.657M328.65 488.294l64.831 64.832M328.65 488.294l88.535-88.533M417.185 399.761l64.833 64.832"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "syringe"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1927,
        "id": 93,
        "name": "syringe",
        "prevSize": 32,
        "code": 60203
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 556
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SyringeIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SyringeIcon = ({ size = 16, ...props }: SyringeIconProps) => (
  <IcoMoon iconSet={iconSet} icon="syringe" size={size} {...props} />
);

SyringeIcon.displayName = 'SyringeIcon';

export default SyringeIcon;
