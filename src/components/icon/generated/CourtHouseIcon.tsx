// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M485.577 85.333v21.070M485.577 298.667v-94.815M485.577 106.403l43.648-8.965c4.962-1.019 10.082-0.193 14.596 2.356l4.749 2.683c4.514 2.548 9.634 3.375 14.596 2.356l32.653-6.706c5.726-1.176 10.995 3.792 10.995 10.365v73.399c0 5.091-3.221 9.454-7.659 10.365l-35.989 7.392c-4.962 1.019-10.082 0.193-14.596-2.356l-3.733-2.109c-5.129-2.898-11.025-3.558-16.559-1.853l-42.701 10.521M485.577 106.403v97.449M85.333 938.667h47.407M132.741 938.667h758.519M132.741 938.667v-47.407c0-39.275 31.837-71.113 71.111-71.113h94.815M891.26 938.667h47.407M891.26 938.667v-47.407c0-39.275-31.838-71.113-71.113-71.113h-94.814M298.667 820.147h142.221M298.667 820.147v-521.481M440.887 820.147h142.225M440.887 820.147v-521.481M583.113 820.147h142.221M583.113 820.147v-521.481M725.333 820.147v-521.481M180.148 820.147v-521.481M843.853 820.147v-521.481M112.561 298.667h67.587M180.148 298.667h118.519M298.667 298.667h142.221M440.887 298.667h142.225M583.113 298.667h142.221M725.333 298.667h118.519M843.853 298.667h67.584"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "court-house"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1586,
        "id": 434,
        "name": "court-house",
        "prevSize": 32,
        "code": 59862
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 215
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CourtHouseIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CourtHouseIcon = ({ size = 16, ...props }: CourtHouseIconProps) => (
  <IcoMoon iconSet={iconSet} icon="court-house" size={size} {...props} />
);

CourtHouseIcon.displayName = 'CourtHouseIcon';

export default CourtHouseIcon;
