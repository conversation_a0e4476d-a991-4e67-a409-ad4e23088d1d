// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M418.133 938.667h187.733M418.133 938.667c-71.687 0-107.53 0-134.911-13.952-24.085-12.271-43.666-31.851-55.938-55.936-13.951-27.383-13.951-63.223-13.951-134.912v-315.733c0-58.396 0-93.007 7.541-118.634M418.133 938.667h-128c-71.687 0-107.53 0-134.911-13.952-24.085-12.271-43.666-31.851-55.938-55.936-13.951-27.383-13.951-63.223-13.951-134.912v-230.4c0-71.689 0-107.53 13.951-134.911 12.272-24.085 31.853-43.666 55.938-55.938 16.68-8.499 36.5-11.82 65.652-13.118M605.867 938.667c71.689 0 107.529 0 134.912-13.952 24.085-12.271 43.665-31.851 55.936-55.936 13.952-27.383 13.952-63.223 13.952-134.912v-315.733c0-58.396 0-93.007-7.539-118.634M605.867 938.667h128c71.689 0 107.529 0 134.912-13.952 24.085-12.271 43.665-31.851 55.936-55.936 13.952-27.383 13.952-63.223 13.952-134.912v-230.4c0-71.689 0-107.53-13.952-134.911-12.271-24.085-31.851-43.666-55.936-55.938-16.683-8.499-36.501-11.82-65.651-13.118M341.68 214.54c0.459-14.615 1.528-25.055 4.015-34.336 11.836-44.172 46.338-78.674 90.508-90.51 16.277-4.361 36.117-4.361 75.797-4.361s59.52 0 75.797 4.361c44.169 11.836 78.673 46.338 90.509 90.51 2.487 9.281 3.554 19.721 4.015 34.336M341.68 214.54c20.052-1.207 44.747-1.207 76.454-1.207h187.733c31.706 0 56.401 0 76.454 1.207M341.68 214.54c-25.284 1.522-43.187 4.963-58.457 12.744-24.085 12.272-43.666 31.853-55.938 55.938-2.586 5.076-4.694 10.444-6.41 16.277M682.321 214.54c25.284 1.522 43.187 4.963 58.458 12.744 24.085 12.272 43.665 31.853 55.936 55.938 2.586 5.076 4.693 10.444 6.413 16.277"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "suitcase"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1921,
        "id": 99,
        "name": "suitcase",
        "prevSize": 32,
        "code": 60197
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 550
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SuitcaseIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SuitcaseIcon = ({ size = 16, ...props }: SuitcaseIconProps) => (
  <IcoMoon iconSet={iconSet} icon="suitcase" size={size} {...props} />
);

SuitcaseIcon.displayName = 'SuitcaseIcon';

export default SuitcaseIcon;
