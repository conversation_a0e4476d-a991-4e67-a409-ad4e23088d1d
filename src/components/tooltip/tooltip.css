.rt-TooltipContent {
  box-sizing: border-box;
  padding: var(--spatial-2) var(--spatial-3);
  background-color: var(--fills-tertiary);
  border-radius: var(--radius-3);

  transform-origin: var(--radix-tooltip-content-transform-origin);

  animation-duration: 140ms;
  animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);

  @media (prefers-reduced-motion: no-preference) {
    &:where([data-state='delayed-open']) {
      &:where([data-side='top']) {
        animation-name: rt-slide-from-top, rt-fade-in;
      }
      &:where([data-side='bottom']) {
        animation-name: rt-slide-from-bottom, rt-fade-in;
      }
      &:where([data-side='left']) {
        animation-name: rt-slide-from-left, rt-fade-in;
      }
      &:where([data-side='right']) {
        animation-name: rt-slide-from-right, rt-fade-in;
      }
    }
  }
}

.rt-TooltipText {
  color: var(--labels-primary);
  user-select: none;
  cursor: default;
}

.rt-TooltipArrow {
  fill: var(--fills-tertiary);
}
