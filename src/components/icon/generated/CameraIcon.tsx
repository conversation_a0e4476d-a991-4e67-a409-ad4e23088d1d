// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M204.75 285.398v0zM210.746 284.732v0zM813.252 284.732v0zM819.251 285.398v0zM307.296 217.409v0zM716.706 217.409v0zM155.223 860.715v0zM99.284 804.779v0zM924.715 804.779v0zM868.779 860.715v0zM938.586 408.677v0zM829.321 286.599v0zM85.414 408.677v0zM194.68 286.599v0zM725.333 373.333c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM768 437.333c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM938.667 418.818h-32v251.049h64v-251.049h-32zM733.867 874.667v-32h-443.733v64h443.733v-32zM85.333 669.867h32v-251.049h-64v251.049h32zM204.75 285.398l3.534 31.804 5.996-0.666-7.068-63.609-5.996 0.666 3.534 31.804zM813.252 284.732l-3.533 31.805 5.999 0.666 7.066-63.608-5.995-0.666-3.537 31.804zM417.445 149.333v32h189.109v-64h-189.109v32zM307.296 217.409l28.622 14.311c15.44-30.88 47.002-50.387 81.527-50.387v-64c-58.767 0-112.49 33.202-138.771 85.765l28.622 14.311zM716.706 217.409l-28.625 14.311c23.539 47.072 69.333 79.004 121.638 84.816l3.533-31.805 3.537-31.804c-30.733-3.414-57.634-22.174-71.462-49.829l-28.621 14.311zM210.746 284.732l3.534 31.805c52.307-5.812 98.101-37.744 121.638-84.816l-57.244-28.622c-13.827 27.655-40.731 46.415-71.462 49.829l3.534 31.804zM716.706 217.409l28.621-14.311c-26.283-52.563-80.004-85.765-138.773-85.765v64c34.526 0 66.091 19.506 81.527 50.387l28.625-14.311zM290.133 874.667v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM85.333 669.867h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM155.223 860.715l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM938.667 669.867h-32c0 36.373-0.026 61.726-1.638 81.463-1.583 19.366-4.531 30.494-8.823 38.921l57.024 29.056c9.655-18.957 13.683-39.441 15.586-62.763 1.877-22.95 1.852-51.362 1.852-86.677h-32zM733.867 874.667v32c35.315 0 63.727 0.026 86.677-1.852 23.322-1.903 43.806-5.931 62.763-15.586l-29.056-57.024c-8.427 4.292-19.554 7.241-38.921 8.823-19.738 1.613-45.090 1.638-81.463 1.638v32zM924.715 804.779l-28.51-14.528c-9.207 18.061-23.893 32.747-41.954 41.954l29.056 57.024c30.106-15.343 54.579-39.817 69.922-69.922l-28.514-14.528zM938.667 418.818h32c0-4.977 0.004-8.292-0.102-11.279l-63.957 2.277c0.055 1.566 0.060 3.494 0.060 9.002h32zM819.251 285.398l-3.533 31.804c5.474 0.608 7.39 0.826 8.939 1.055l9.327-63.317c-2.957-0.436-6.255-0.797-11.2-1.346l-3.533 31.804zM938.586 408.677l31.979-1.139c-2.748-77.157-60.198-141.347-136.58-152.598l-9.327 63.317c45.828 6.751 80.299 45.265 81.95 91.559l31.979-1.139zM85.333 418.818h32c0-5.508 0.005-7.436 0.061-9.002l-63.959-2.278c-0.107 2.988-0.102 6.302-0.102 11.279h32zM204.75 285.398l-3.534-31.804c-4.946 0.55-8.242 0.911-11.199 1.346l9.327 63.317c1.551-0.228 3.466-0.446 8.94-1.055l-3.534-31.804zM85.414 408.677l31.98 1.139c1.648-46.294 36.12-84.808 81.949-91.559l-9.327-63.317c-76.381 11.251-133.834 75.441-136.582 152.598l31.98 1.139zM725.333 405.333v32h42.667v-64h-42.667v32zM512 725.333v32c99.921 0 181.333-81.412 181.333-181.333h-64c0 64.576-52.757 117.333-117.333 117.333v32zM661.333 576h32c0-99.921-81.412-181.333-181.333-181.333v64c64.576 0 117.333 52.757 117.333 117.333h32zM512 426.667v-32c-99.921 0-181.333 81.412-181.333 181.333h64c0-64.576 52.757-117.333 117.333-117.333v-32zM362.667 576h-32c0 99.921 81.412 181.333 181.333 181.333v-64c-64.576 0-117.333-52.757-117.333-117.333h-32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "camera"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1516,
        "id": 504,
        "name": "camera",
        "prevSize": 32,
        "code": 59792
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 145
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CameraIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CameraIcon = ({ size = 16, ...props }: CameraIconProps) => (
  <IcoMoon iconSet={iconSet} icon="camera" size={size} {...props} />
);

CameraIcon.displayName = 'CameraIcon';

export default CameraIcon;
