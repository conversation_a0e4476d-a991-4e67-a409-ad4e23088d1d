// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M853.333 322.371c0-130.912-152.819-237.037-341.333-237.037s-341.333 106.125-341.333 237.037M853.333 322.371v189.629M853.333 322.371c0 33.711-10.133 65.779-28.403 94.815-23.748 37.748-61.248 70.371-108.096 94.815-57.054 29.769-127.979 47.407-204.834 47.407s-147.78-17.638-204.833-47.407c-46.851-24.444-84.349-57.067-108.098-94.815-18.268-29.036-28.402-61.103-28.402-94.815M170.667 322.371v189.629M853.333 512v189.628c0 130.914-152.819 237.039-341.333 237.039s-341.333-106.125-341.333-237.039v-189.628M853.333 512c0 33.711-10.133 65.779-28.403 94.814-52.672 83.716-172.962 142.225-312.93 142.225s-260.26-58.509-312.931-142.225c-18.268-29.035-28.402-61.103-28.402-94.814"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "database"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1596,
        "id": 424,
        "name": "database",
        "prevSize": 32,
        "code": 59872
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 225
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface DatabaseIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const DatabaseIcon = ({ size = 16, ...props }: DatabaseIconProps) => (
  <IcoMoon iconSet={iconSet} icon="database" size={size} {...props} />
);

DatabaseIcon.displayName = 'DatabaseIcon';

export default DatabaseIcon;
