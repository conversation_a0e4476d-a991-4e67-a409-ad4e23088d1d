// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M232.059 791.266v0zM791.94 791.266v0zM362.667 548.092v0zM775.838 863.194v0zM692.245 934.306v0zM248.161 863.194v0zM331.755 934.306v0zM748.8 123.733c-14.14 10.604-17.003 30.662-6.4 44.8s30.66 17.004 44.8 6.4l-38.4-51.2zM872.533 110.933c14.14-10.604 17.003-30.662 6.4-44.8s-30.66-17.004-44.8-6.4l38.4 51.2zM736 277.333v-32c-35.345 0-64-28.654-64-64h-64c0 70.692 57.306 128 128 128v-32zM736 85.333v32c35.345 0 64 28.654 64 64h64c0-70.692-57.306-128-128-128v32zM170.667 448h32c0-170.84 138.493-309.333 309.333-309.333v-64c-206.186 0-373.333 167.147-373.333 373.333h32zM183.235 490.667l25.834-18.884c-3.546-4.851-6.402-11.942-6.402-23.782h-64c0 23.091 5.902 43.994 18.734 61.551l25.834-18.884zM183.235 490.667l-31.586 5.129 48.824 300.599 63.172-10.261-48.823-300.595-31.586 5.129zM853.333 448h-32c0 11.84-2.854 18.931-6.404 23.782l51.669 37.769c12.834-17.557 18.735-38.46 18.735-61.551h-32zM791.94 791.266l31.586 5.129 48.823-300.599-63.172-10.257-48.823 300.595 31.586 5.133zM512 554.667v-32c-61.461 0-97.268-1.399-146.065-6.409l-6.537 63.667c51.614 5.299 89.719 6.741 152.602 6.741v-32zM362.667 548.092l3.268-31.834c-37.271-3.827-73.976-9.651-103.696-18.15-14.846-4.245-27.128-8.926-36.544-13.888-9.655-5.090-14.511-9.545-16.625-12.437l-51.668 37.769c10.003 13.683 24.103 23.718 38.448 31.283 14.584 7.689 31.348 13.82 48.798 18.807 34.873 9.971 75.766 16.282 114.752 20.284l3.268-31.834zM362.667 548.092l-31.579 5.175 64 390.575 63.156-10.351-63.999-390.575-31.579 5.175zM840.764 490.667l-25.835-18.884c-2.112 2.893-6.967 7.347-16.623 12.437-9.417 4.962-21.7 9.643-36.544 13.888-29.722 8.499-66.428 14.323-103.697 18.15l6.537 63.667c38.985-4.002 79.881-10.313 114.752-20.284 17.451-4.988 34.214-11.119 48.798-18.807 14.345-7.565 28.446-17.6 38.447-31.283l-25.835-18.884zM661.333 548.092l-3.268-31.834c-48.798 5.009-84.604 6.409-146.065 6.409v64c62.882 0 100.988-1.442 152.602-6.741l-3.268-31.834zM426.667 938.667v32h170.667v-64h-170.667v32zM661.333 548.092l-31.578-5.175-64 390.575 63.155 10.351 64-390.575-31.578-5.175zM791.94 791.266l-31.586-5.133c-6.438 39.637-8.934 53.35-13.696 63.927l58.364 26.261c9.071-20.16 12.693-44.156 18.505-79.927l-31.586-5.129zM597.333 938.667v32c18.961 0 39.172 0.004 56.708-0.563 16.418-0.529 34.236-1.604 46.485-4.89l-16.563-61.82c-4.028 1.079-14.571 2.185-31.987 2.743-16.299 0.525-35.405 0.529-54.643 0.529v32zM775.838 863.194l-29.18-13.133c-11.831 26.291-34.85 45.871-62.694 53.333l16.563 61.82c46.413-12.437 84.779-45.073 104.495-88.892l-29.184-13.129zM232.059 791.266l-31.586 5.129c5.81 35.772 9.435 59.767 18.507 79.927l58.363-26.261c-4.76-10.577-7.26-24.29-13.698-63.927l-31.586 5.133zM426.667 938.667v-32c-19.239 0-38.345-0.004-54.645-0.529-17.416-0.559-27.957-1.664-31.983-2.743l-16.566 61.82c12.253 3.285 30.067 4.361 46.488 4.89 17.536 0.567 37.746 0.563 56.707 0.563v-32zM248.161 863.194l-29.181 13.129c19.717 43.819 58.081 76.454 104.492 88.892l16.567-61.82c-27.846-7.462-50.865-27.042-62.695-53.333l-29.182 13.133zM768 149.333l19.2 25.6 85.333-64-38.4-51.2-85.333 64 19.2 25.6zM512 106.667v32c44.996 0 87.667 9.588 126.153 26.801l26.133-58.423c-46.545-20.818-98.108-32.378-152.286-32.378v32zM640 181.333h32c0-10.918 2.709-21.108 7.462-30.030l-56.482-30.094c-9.57 17.959-14.98 38.457-14.98 60.124h32zM651.221 136.256l28.241 15.047c10.803-20.28 32.098-33.97 56.538-33.97v-64c-49.015 0-91.541 27.559-113.020 67.876l28.241 15.047zM794.987 257.079l-26.513 17.921c33.374 49.367 52.86 108.859 52.86 173h64c0-77.312-23.535-149.224-63.838-208.842l-26.509 17.921zM832 181.333h-32c0 20.503-9.604 38.747-24.691 50.512l39.356 50.468c29.969-23.371 49.335-59.922 49.335-100.98h-32zM794.987 257.079l-19.678-25.234c-10.854 8.463-24.448 13.488-39.309 13.488v64c29.619 0 56.969-10.099 78.665-27.020l-19.678-25.234z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "cupcake"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1593,
        "id": 427,
        "name": "cupcake",
        "prevSize": 32,
        "code": 59869
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 222
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CupcakeIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CupcakeIcon = ({ size = 16, ...props }: CupcakeIconProps) => (
  <IcoMoon iconSet={iconSet} icon="cupcake" size={size} {...props} />
);

CupcakeIcon.displayName = 'CupcakeIcon';

export default CupcakeIcon;
