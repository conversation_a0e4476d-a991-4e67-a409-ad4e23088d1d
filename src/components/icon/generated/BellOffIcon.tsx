// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M853.333 694.81c0-21.585-9.233-42.142-25.365-56.482l-5.888-5.235c-11.008-9.783-19.537-22.042-24.887-35.759l-82.739-302.519c-19.166-49.134-66.509-81.481-119.249-81.481h-166.409c-18.458 0-36.253 3.962-52.411 11.218M389.433 874.667c26.986 38.686 71.819 64 122.567 64s95.582-25.314 122.569-64M149.333 149.333l157.050 157.050M874.667 874.667l-104.282-104.282M306.383 306.383l-79.574 290.95c-5.352 13.717-13.881 25.975-24.887 35.759l-5.89 5.235c-16.134 14.34-25.365 34.897-25.365 56.482 0 41.737 33.836 75.575 75.574 75.575h524.145M306.383 306.383l464.002 464.002M576 149.333c0 35.346-28.655 64-64 64s-64-28.654-64-64c0-35.346 28.655-64 64-64s64 28.654 64 64z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bell-off"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1467,
        "id": 553,
        "name": "bell-off",
        "prevSize": 32,
        "code": 59743
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 96
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BellOffIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BellOffIcon = ({ size = 16, ...props }: BellOffIconProps) => (
  <IcoMoon iconSet={iconSet} icon="bell-off" size={size} {...props} />
);

BellOffIcon.displayName = 'BellOffIcon';

export default BellOffIcon;
