// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M810.667 213.333h-597.333c-70.692 0-128 57.308-128 128v64c58.91 0 106.667 47.757 106.667 106.667s-47.756 106.667-106.667 106.667v64c0 70.694 57.308 128 128 128h597.333c70.694 0 128-57.306 128-128v-64c-58.91 0-106.667-47.757-106.667-106.667s47.757-106.667 106.667-106.667v-64c0-70.692-57.306-128-128-128z",
          "M640 213.333v597.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "ticket"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1955,
        "id": 65,
        "name": "ticket",
        "prevSize": 32,
        "code": 60231
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 584
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface TicketIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const TicketIcon = ({ size = 16, ...props }: TicketIconProps) => (
  <IcoMoon iconSet={iconSet} icon="ticket" size={size} {...props} />
);

TicketIcon.displayName = 'TicketIcon';

export default TicketIcon;
