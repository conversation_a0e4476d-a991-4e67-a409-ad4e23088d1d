// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M937.946 503.433v0zM814.153 771.034v0zM696.384 854.234v0zM327.616 854.234v0zM209.846 771.034v0zM117.216 632.61v0zM86.056 503.433v0zM663.859 936.341v0zM673.195 927.019v0zM86.915 482.061v0zM96.311 471.898v0zM350.804 927.019v0zM360.141 936.341v0zM342.19 862.528v0zM347.271 870.579v0zM681.809 862.528v0zM676.727 870.579v0zM937.084 482.061v0zM927.689 471.898v0zM212.334 74.682c-17.664 0.552-31.537 15.319-30.985 32.984s15.319 31.537 32.984 30.985l-1.999-63.968zM896.998 117.318c17.664-0.552 31.539-15.319 30.985-32.984-0.55-17.664-15.317-31.537-32.981-30.985l1.997 63.968zM213.333 160c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM896 224c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM330.667 85.333c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM266.667 469.333c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM416 85.333c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM352 469.333c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM501.333 85.333c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM437.333 341.333c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM937.946 503.433l-31.898-2.564c-3.294 41.020-13.001 81.34-28.821 119.484l59.115 24.521c18.389-44.331 29.67-91.2 33.502-138.88l-31.898-2.56zM906.782 632.61l-29.555-12.258c-19.861 47.876-48.977 91.383-85.683 128.034l45.222 45.291c42.654-42.59 76.489-93.154 99.575-148.804l-29.559-12.262zM814.153 771.034l-22.609-22.647c-31.718 31.671-68.57 57.69-108.949 76.975l27.58 57.749c46.912-22.404 89.732-52.629 126.592-89.434l-22.613-22.643zM327.616 854.234l13.791-28.873c-40.379-19.285-77.231-45.303-108.951-76.975l-45.221 45.291c36.859 36.804 79.677 67.029 126.589 89.434l13.791-28.877zM209.846 771.034l22.61-22.647c-36.706-36.651-65.82-80.158-85.682-128.034l-59.115 24.521c23.086 55.65 56.923 106.214 99.576 148.804l22.61-22.643zM117.216 632.61l29.558-12.258c-15.822-38.144-25.527-78.464-28.82-119.484l-63.795 5.124c3.828 47.68 15.11 94.549 33.5 138.88l29.557-12.262zM348.475 886.327h-32v18.206h64v-18.206h-32zM382.66 938.667v32h258.679v-64h-258.679v32zM675.524 904.533h32v-18.206h-64v18.206h32zM641.34 938.667v32c5.457 0 11.469 0.026 16.636-0.397 5.538-0.452 12.851-1.57 20.395-5.406l-29.022-57.045c2.974-1.51 5.009-1.468 3.422-1.335-0.772 0.064-1.997 0.119-4.006 0.149-2.014 0.034-4.361 0.034-7.424 0.034v32zM675.524 904.533h-32c0 3.059 0 5.402-0.030 7.407-0.034 2.010-0.090 3.226-0.154 3.994-0.128 1.574-0.171-0.474 1.353-3.46l57.007 29.090c3.853-7.548 4.975-14.869 5.427-20.407 0.422-5.167 0.397-11.179 0.397-16.623h-32zM663.859 936.341l14.511 28.523c10.035-5.107 18.206-13.261 23.33-23.3l-57.007-29.090c1.024-2.010 2.658-3.635 4.655-4.655l14.511 28.523zM86.056 503.433l31.898-2.564c-0.227-2.825-0.392-4.898-0.501-6.665-0.108-1.762-0.126-2.714-0.118-3.217 0.007-0.465 0.033-0.119-0.116 0.695-0.167 0.917-0.489 2.078-1.030 3.302l-58.547-25.847c-6.211 14.063-4.094 29.252-3.483 36.855l31.897-2.56zM118.874 469.333v-32c-5.364 0-11.493-0.030-16.818 0.448-5.873 0.529-13.3 1.835-20.934 5.952l30.38 56.329c-2.882 1.553-4.964 1.574-3.713 1.463 0.678-0.060 1.814-0.124 3.776-0.158 1.965-0.034 4.26-0.034 7.309-0.034v-32zM86.915 482.061l29.274 12.924c-0.678 1.536-1.398 2.449-1.884 2.974s-1.335 1.31-2.804 2.103l-30.38-56.329c-10.22 5.508-18.785 14.771-23.479 25.404l29.273 12.924zM348.475 904.533h-32c0 5.444-0.025 11.456 0.398 16.623 0.454 5.538 1.576 12.86 5.428 20.407l57.007-29.090c1.523 2.987 1.481 5.035 1.353 3.46-0.063-0.768-0.121-1.984-0.152-3.994-0.032-2.005-0.032-4.348-0.032-7.407h-32zM382.66 938.667v-32c-3.064 0-5.409 0-7.422-0.034-2.013-0.030-3.235-0.085-4.009-0.149-1.588-0.132 0.448-0.175 3.422 1.335l-29.021 57.045c7.544 3.836 14.857 4.954 20.395 5.406 5.166 0.422 11.179 0.397 16.635 0.397v-32zM350.804 927.019l-28.503 14.545c5.123 10.039 13.293 18.193 23.33 23.3l29.021-57.045c1.997 1.020 3.63 2.645 4.656 4.655l-28.503 14.545zM327.616 854.234l-13.791 28.877c2.045 0.977 3.623 1.732 4.973 2.394 1.351 0.666 2.186 1.097 2.729 1.395 1.191 0.653-0.129 0.081-1.905-1.685l45.137-45.372c-4.354-4.331-8.963-7.138-12.415-9.037-3.305-1.813-7.269-3.695-10.936-5.444l-13.791 28.873zM348.475 886.327h32c0-5.658 0.447-16.508-3.014-26.355l-60.382 21.214c-0.77-2.189-0.756-3.58-0.696-2.59 0.030 0.499 0.059 1.301 0.075 2.667s0.017 2.957 0.017 5.065h32zM342.19 862.528l-22.569 22.686c-0.326-0.324-0.894-0.96-1.475-1.882s-0.913-1.707-1.066-2.146l60.382-21.214c-2.715-7.727-6.899-14.353-12.704-20.13l-22.568 22.686zM696.384 854.234l-13.79-28.873c-3.669 1.749-7.633 3.631-10.935 5.444-3.452 1.899-8.064 4.706-12.416 9.037l45.137 45.372c-1.779 1.766-3.098 2.338-1.907 1.685 0.542-0.299 1.378-0.73 2.731-1.395 1.348-0.661 2.927-1.417 4.971-2.394l-13.79-28.877zM675.524 886.327h32c0-2.108 0-3.699 0.017-5.065 0.017-1.361 0.047-2.167 0.077-2.667 0.060-0.99 0.073 0.401-0.7 2.59l-60.382-21.214c-3.46 9.847-3.012 20.698-3.012 26.355h32zM681.809 862.528l-22.566-22.686c-5.807 5.773-9.988 12.403-12.706 20.13l60.382 21.214c-0.154 0.439-0.482 1.225-1.067 2.146-0.58 0.922-1.148 1.557-1.472 1.882l-22.571-22.686zM937.946 503.433l31.898 2.56c0.61-7.603 2.726-22.793-3.486-36.86l-58.547 25.852c-0.542-1.225-0.862-2.385-1.028-3.302-0.149-0.815-0.124-1.161-0.115-0.695 0.004 0.503-0.013 1.455-0.119 3.213-0.107 1.771-0.273 3.844-0.499 6.669l31.898 2.564zM905.126 469.333v32c3.046 0 5.342 0 7.309 0.034 1.963 0.034 3.098 0.098 3.776 0.158 1.25 0.111-0.832 0.090-3.712-1.463l30.379-56.329c-7.633-4.117-15.061-5.423-20.932-5.952-5.329-0.478-11.456-0.448-16.819-0.448v32zM937.084 482.061l29.274-12.924c-4.693-10.633-13.261-19.895-23.479-25.404l-30.379 56.329c-1.468-0.794-2.317-1.579-2.803-2.103s-1.207-1.438-1.886-2.974l29.274-12.924zM118.874 469.333v32h786.252v-64h-786.252v32zM117.216 632.61v32h789.565v-64h-789.565v32zM213.333 106.667l1 31.984 682.665-21.333-1.997-63.968-682.668 21.333 1 31.984zM213.333 192v32h682.667v-64h-682.667v32zM298.667 85.333h-32v384h64v-384h-32zM384 85.333h-32v384h64v-384h-32zM469.333 85.333h-32v256h64v-256h-32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "ramen"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1845,
        "id": 175,
        "name": "ramen",
        "prevSize": 32,
        "code": 60121
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 474
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface RamenIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const RamenIcon = ({ size = 16, ...props }: RamenIconProps) => (
  <IcoMoon iconSet={iconSet} icon="ramen" size={size} {...props} />
);

RamenIcon.displayName = 'RamenIcon';

export default RamenIcon;
