// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 320v328.533c0 71.689 0 107.529 13.951 134.912 12.272 24.085 31.853 43.665 55.938 55.936 27.38 13.952 63.224 13.952 134.911 13.952h443.733c71.689 0 107.529 0 134.912-13.952 24.085-12.271 43.665-31.851 55.936-55.936 13.952-27.383 13.952-63.223 13.952-134.912v-328.533M85.333 320l393.766 216.572c12.015 6.609 18.022 9.911 24.375 11.209 5.623 1.143 11.426 1.143 17.050 0 6.353-1.297 12.361-4.599 24.375-11.209l393.766-216.572M85.333 320c0-19.815 0-29.723 1.095-38.041 7.562-57.438 52.759-102.636 110.197-110.197 8.318-1.095 18.225-1.095 38.041-1.095h554.667c19.814 0 29.722 0 38.042 1.095 57.438 7.562 102.635 52.759 110.195 110.197 1.097 8.318 1.097 18.225 1.097 38.041"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "envelope"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1619,
        "id": 401,
        "name": "envelope",
        "prevSize": 32,
        "code": 59895
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 248
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface EnvelopeIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const EnvelopeIcon = ({ size = 16, ...props }: EnvelopeIconProps) => (
  <IcoMoon iconSet={iconSet} icon="envelope" size={size} {...props} />
);

EnvelopeIcon.displayName = 'EnvelopeIcon';

export default EnvelopeIcon;
