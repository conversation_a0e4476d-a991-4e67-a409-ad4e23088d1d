// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 341.336v170.668l85.333 85.333M130.133 547.166c9.561 93.862 53.345 180.924 122.997 244.565s160.297 99.418 254.641 100.493c94.345 1.079 185.783-32.614 256.87-94.652 71.087-62.033 116.847-148.070 128.55-241.694 11.703-93.619-11.473-188.273-65.101-265.899-53.632-77.626-133.965-132.79-225.668-154.969-91.708-22.179-188.369-9.818-271.543 34.722s-147.041 118.143-179.413 206.766M130.133 163.165v213.333h213.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "round",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "history"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "round",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1709,
        "id": 311,
        "name": "history",
        "prevSize": 32,
        "code": 59985
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 338
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface HistoryIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const HistoryIcon = ({ size = 16, ...props }: HistoryIconProps) => (
  <IcoMoon iconSet={iconSet} icon="history" size={size} {...props} />
);

HistoryIcon.displayName = 'HistoryIcon';

export default HistoryIcon;
