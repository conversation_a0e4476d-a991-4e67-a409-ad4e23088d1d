// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M853.333 266.667l56.508 21.191c13.948 5.23 28.826-5.080 28.826-19.975v-109.099c0-14.895-14.878-25.205-28.826-19.975l-56.508 21.191M597.333 768h136.533c71.689 0 107.529 0 134.912-13.952 24.085-12.271 43.665-31.851 55.936-55.936 11.537-22.643 13.534-51.076 13.879-100.779M597.333 768h-170.667M597.333 768v170.667M426.667 768h-136.533c-71.687 0-107.53 0-134.911-13.952-24.085-12.271-43.666-31.851-55.938-55.936-11.537-22.643-13.533-51.076-13.879-100.779M426.667 768v170.667M85.405 597.333c-0.072-10.398-0.072-21.73-0.072-34.133v-273.067c0-71.687 0-107.53 13.951-134.911 12.272-24.085 31.853-43.666 55.938-55.938 27.38-13.951 63.224-13.951 134.911-13.951h93.867M85.405 597.333h-0.072M85.405 597.333h853.189M938.594 597.333c0.073-10.398 0.073-21.73 0.073-34.133v-136.533M938.594 597.333h0.073M341.333 938.667h85.333M426.667 938.667h170.667M597.333 938.667h85.333M614.4 85.333h136.533c35.844 0 53.764 0 67.456 6.976 12.041 6.136 21.833 15.927 27.968 27.969 6.976 13.69 6.976 31.612 6.976 67.456v51.2c0 35.843 0 53.765-6.976 67.456-6.135 12.042-15.927 21.833-27.968 27.969-13.692 6.976-31.612 6.976-67.456 6.976h-136.533c-35.844 0-53.764 0-67.456-6.976-12.041-6.136-21.833-15.927-27.968-27.969-6.976-13.69-6.976-31.612-6.976-67.456v-51.2c0-35.843 0-53.765 6.976-67.456 6.135-12.042 15.927-21.833 27.968-27.969 13.692-6.976 31.612-6.976 67.456-6.976z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "monitor-recorder"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1786,
        "id": 234,
        "name": "monitor-recorder",
        "prevSize": 32,
        "code": 60062
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 415
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MonitorRecorderIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MonitorRecorderIcon = ({ size = 16, ...props }: MonitorRecorderIconProps) => (
  <IcoMoon iconSet={iconSet} icon="monitor-recorder" size={size} {...props} />
);

MonitorRecorderIcon.displayName = 'MonitorRecorderIcon';

export default MonitorRecorderIcon;
