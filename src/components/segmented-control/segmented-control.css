.herond-themes {
  --segmented-control-transition-duration: 100ms;
  --segmented-control-root-padding: var(--spatial-2);
  --segmented-control-border-radius: var(--radius-full);
}

.rt-SegmentedControlRoot {
  display: inline-grid;
  vertical-align: top;
  grid-auto-flow: column;
  grid-auto-columns: 1fr;
  align-items: stretch;
  background-color: var(--background-grouped-primary-base);
  position: relative;
  min-width: max-content;
  padding: var(--segmented-control-root-padding);

  font-family: var(--default-font-family);
  font-style: normal;
  text-align: center;

  /* Create a new stacking context */
  isolation: isolate;
}

.rt-SegmentedControlRoot:where([data-disabled]) {
  color: var(--labels-tertiary);
  background-color: var(--fills-tertiary);
}

.rt-SegmentedControlItem {
  display: flex;
  align-items: stretch;
  user-select: none;

  &:where(:first-child) {
    border-top-left-radius: inherit;
    border-bottom-left-radius: inherit;
  }
  &:where(:nth-last-child(2)) {
    border-top-right-radius: inherit;
    border-bottom-right-radius: inherit;
  }
  &:where(:focus-visible) {
    border-radius: inherit;
    outline: 2px solid var(--brand-accent-40);
    outline-offset: -1px;
  }
}

.rt-SegmentedControlItemLabel {
  display: flex;
  flex-grow: 1;
  align-items: center;
  justify-content: center;
  border-radius: inherit;

  /* stylelint-disable-next-line selector-max-type */
  & :where(svg) {
    /* Don’t squish icons */
    flex-shrink: 0;
  }

  @media (hover: hover) {
    :where(.rt-SegmentedControlItem[data-state='off']:not([disabled]):hover) & {
      background-color: var(--background-grouped-primary-high);
    }
  }
}

.rt-SegmentedControlItemLabelInactive {
  position: absolute;

  color: var(--labels-secondary);

  transition: opacity calc(0.8 * var(--segmented-control-transition-duration));
  font-weight: var(--font-weight-semibold);
  letter-spacing: var(--tab-inactive-letter-spacing);
  word-spacing: var(--tab-inactive-word-spacing);

  opacity: 1;
  transition-timing-function: ease-out;
  :where(.rt-SegmentedControlItem[data-state='on']) & {
    opacity: 0;
    transition-timing-function: ease-in;
  }
}

.rt-SegmentedControlItemLabelActive {
  transition: opacity calc(0.8 * var(--segmented-control-transition-duration));
  font-weight: var(--font-weight-semibold);
  letter-spacing: var(--tab-active-letter-spacing);
  word-spacing: var(--tab-active-word-spacing);

  opacity: 0;
  transition-timing-function: ease-in;
  :where(.rt-SegmentedControlItem[data-state='on']) & {
    opacity: 1;
    transition-timing-function: ease-out;
  }
}

.rt-SegmentedControlItemSeparator {
  /*
   * Place the separator below the indicator.
   * Be careful not to introduce a stacking context on ".rt-SegmentedControlItem":
   * https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_positioned_layout/Understanding_z-index/Stacking_context
   */
  z-index: -1;

  margin-top: 3px;
  margin-bottom: 3px;
  margin-left: -0.5px;
  margin-right: -0.5px;
  width: 1px;
  background-color: var(--background-primary-elevated);
  transition: opacity calc(0.8 * var(--segmented-control-transition-duration));

  /* Make separators slow to disappear and fast to appear, syncing it well with the indicator motion */
  transition-timing-function: ease-out;

  :where(.rt-SegmentedControlItem:first-child) &,
  :where(.rt-SegmentedControlItem:where([data-state='on'], :focus-visible)) &,
  :where(.rt-SegmentedControlItem:where([data-state='on'], :focus-visible)) + * & {
    opacity: 0;
    transition-timing-function: ease-in;
  }
  /* Don't transition when the separator is hidden because of the focus outline */
  :where(.rt-SegmentedControlRoot:has(:focus-visible)) & {
    transition-duration: 0ms;
  }
}

.rt-SegmentedControlIndicator {
  display: none;
  position: absolute;
  z-index: -1;
  top: var(--segmented-control-root-padding);
  left: var(--segmented-control-root-padding);
  height: calc(100% - (var(--segmented-control-root-padding) * 2));
  pointer-events: none;
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.445, 0.05, 0.55, 0.95);
  transition-duration: var(--segmented-control-transition-duration);

  &::before {
    inset: 1px;
    position: absolute;
    border-radius: max(0.5px, calc(var(--segmented-control-border-radius) - 1px));
    content: '';
  }

  :where(.rt-SegmentedControlItem[data-state='on']) ~ & {
    display: block;
  }

  :where(.rt-SegmentedControlItem[disabled]) ~ & {
    --segmented-control-indicator-background-color: var(--brand-gray300);

    &::before {
      inset: 0px;
      box-shadow: none;
    }
  }

  &:where(:nth-child(2)) {
    width: calc((100% - var(--segmented-control-root-padding) * 2) / 1);
  }
  &:where(:nth-child(3)) {
    width: calc((100% - var(--segmented-control-root-padding) * 2) / 2);
  }
  &:where(:nth-child(4)) {
    width: calc((100% - var(--segmented-control-root-padding) * 2) / 3);
  }
  &:where(:nth-child(5)) {
    width: calc((100% - var(--segmented-control-root-padding) * 2) / 4);
  }
  &:where(:nth-child(6)) {
    width: calc((100% - var(--segmented-control-root-padding) * 2) / 5);
  }
  &:where(:nth-child(7)) {
    width: calc((100% - var(--segmented-control-root-padding) * 2) / 6);
  }
  &:where(:nth-child(8)) {
    width: calc((100% - var(--segmented-control-root-padding) * 2) / 7);
  }
  &:where(:nth-child(9)) {
    width: calc((100% - var(--segmented-control-root-padding) * 2) / 8);
  }
  &:where(:nth-child(10)) {
    width: calc((100% - var(--segmented-control-root-padding) * 2) / 9);
  }
  &:where(:nth-child(11)) {
    width: calc((100% - var(--segmented-control-root-padding) * 2) / 10);
  }

  :where(.rt-SegmentedControlItem[data-state='on']:nth-child(1)) ~ & {
    transform: translateX(0%);
  }
  :where(.rt-SegmentedControlItem[data-state='on']:nth-child(2)) ~ & {
    transform: translateX(100%);
  }
  :where(.rt-SegmentedControlItem[data-state='on']:nth-child(3)) ~ & {
    transform: translateX(200%);
  }
  :where(.rt-SegmentedControlItem[data-state='on']:nth-child(4)) ~ & {
    transform: translateX(300%);
  }
  :where(.rt-SegmentedControlItem[data-state='on']:nth-child(5)) ~ & {
    transform: translateX(400%);
  }
  :where(.rt-SegmentedControlItem[data-state='on']:nth-child(6)) ~ & {
    transform: translateX(500%);
  }
  :where(.rt-SegmentedControlItem[data-state='on']:nth-child(7)) ~ & {
    transform: translateX(600%);
  }
  :where(.rt-SegmentedControlItem[data-state='on']:nth-child(8)) ~ & {
    transform: translateX(700%);
  }
  :where(.rt-SegmentedControlItem[data-state='on']:nth-child(9)) ~ & {
    transform: translateX(800%);
  }
  :where(.rt-SegmentedControlItem[data-state='on']:nth-child(10)) ~ & {
    transform: translateX(900%);
  }
}

/* * * * * * * * * * * * * * * * * * * */
/*                                     */
/*                Sizes                */
/*                                     */
/* * * * * * * * * * * * * * * * * * * */

.rt-SegmentedControlItemLabel {
  box-sizing: border-box;
}
.rt-SegmentedControlRoot {
  border-radius: var(--segmented-control-border-radius);
}

@breakpoints {
  .rt-SegmentedControlRoot {
    &:where(.rt-r-size-1) {
      height: var(--spatial-7);

      & :where(.rt-SegmentedControlItemLabel) {
        font-size: var(--font-size-2);
        letter-spacing: var(--letter-spacing-2);
        padding-left: var(--spatial-4);
        padding-right: var(--spatial-4);
        gap: var(--spatial-3);
      }
    }
    &:where(.rt-r-size-2) {
      height: var(--spatial-8);

      & :where(.rt-SegmentedControlItemLabel) {
        font-size: var(--font-size-3);
        letter-spacing: var(--letter-spacing-3);
        padding-left: var(--spatial-5);
        padding-right: var(--spatial-5);
        gap: var(--spatial-4);
      }
    }
    &:where(.rt-r-size-3) {
      height: var(--spatial-9);

      & :where(.rt-SegmentedControlItemLabel) {
        font-size: var(--font-size-4);
        letter-spacing: var(--letter-spacing-4);
        padding-left: var(--spatial-5);
        padding-right: var(--spatial-5);
        gap: var(--spatial-4);
      }
    }
  }
}

/* * * * * * * * * * * * * * * * * * * */
/*                                     */
/*         Variants.                   */
/*                                     */
/* * * * * * * * * * * * * * * * * * * */

.rt-SegmentedControlRoot:where(.rt-variant-primary) {
  & :where(.rt-SegmentedControlItem:not([disabled])) {
    .rt-SegmentedControlItemLabelActive {
      color: var(--grays-white);
    }

    & ~ :where(.rt-SegmentedControlIndicator) {
      &::before {
        background: var(--brand-accent);
        box-shadow: 0px 0px 40px 0px rgba(0, 0, 0, 0.1);
      }
    }
  }
  & :where(.rt-SegmentedControlItem[aria-checked='true']) {
    &:has(.rt-SegmentedControlItemLabelActive):hover ~ :where(.rt-SegmentedControlIndicator)::before {
      background: linear-gradient(0deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.1) 100%), var(--brand-accent);
    }
  }
}

.rt-SegmentedControlRoot:where(.rt-variant-primary-light) {
  & :where(.rt-SegmentedControlItem:not([disabled])) {
    .rt-SegmentedControlItemLabelActive {
      color: var(--brand-accent);
    }

    & ~ :where(.rt-SegmentedControlIndicator) {
      &::before {
        background: var(--brand-accent-button-bezeled-fill);
        box-shadow: 0px 0px 40px 0px rgba(0, 0, 0, 0.1);
      }
    }
  }
  & :where(.rt-SegmentedControlItem[aria-checked='true']) {
    &:has(.rt-SegmentedControlItemLabelActive):hover ~ :where(.rt-SegmentedControlIndicator)::before {
      background: var(--brand-accent-button-bezeled-fill-hover);
    }
  }
}

.rt-SegmentedControlRoot:where(.rt-variant-secondary) {
  & :where(.rt-SegmentedControlItem:not([disabled])) {
    .rt-SegmentedControlItemLabelActive {
      color: var(--labels-primary);
    }

    & ~ :where(.rt-SegmentedControlIndicator) {
      &::before {
        background: var(--background-grouped-primary-high);
        box-shadow: 0px 0px 40px 0px rgba(0, 0, 0, 0.1);
      }
    }
  }
  & :where(.rt-SegmentedControlItem[aria-checked='true']) {
    &:has(.rt-SegmentedControlItemLabelActive):hover ~ :where(.rt-SegmentedControlIndicator)::before {
      background: var(--background-secondary-base);
    }
  }
}

.rt-SegmentedControlRoot:where(.rt-variant-neutral) {
  & :where(.rt-SegmentedControlItem:not([disabled])) {
    .rt-SegmentedControlItemLabelActive {
      color: var(--grays-white);
    }

    & ~ :where(.rt-SegmentedControlIndicator) {
      &::before {
        background: var(--labels-primary);
        box-shadow: 0px 0px 40px 0px rgba(0, 0, 0, 0.1);
      }
    }
  }
}
