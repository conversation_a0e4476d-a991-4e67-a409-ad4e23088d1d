// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M261.889 924.715v0zM205.951 868.779v0zM818.048 868.779v0zM762.112 924.715v0zM788.467 110.817v0zM576 170.932v0zM234.668 110.817v0zM447.134 170.932v0zM256 275.056l-23.791 21.401 0.214 0.238 0.219 0.233 23.358-21.872zM495.177 275.056h32v-3.302l-0.674-3.232-31.326 6.534zM527.962 275.056l-31.326-6.534-0.674 3.232v3.302h32zM768 275.056l23.351 21.879 0.222-0.237 0.218-0.241-23.791-21.401zM896 320h-32v128h64v-128h-32zM128 448h32v-128h-64v128h32zM170.667 490.667v-32c-5.891 0-10.667-4.774-10.667-10.667h-64c0 41.237 33.429 74.667 74.667 74.667v-32zM896 448h-32c0 5.892-4.774 10.667-10.667 10.667v64c41.237 0 74.667-33.429 74.667-74.667h-32zM853.333 277.333v32c5.892 0 10.667 4.776 10.667 10.667h64c0-41.237-33.429-74.667-74.667-74.667v32zM170.667 277.333v-32c-41.237 0-74.667 33.429-74.667 74.667h64c0-5.891 4.776-10.667 10.667-10.667v-32zM396.8 938.667v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM192 733.867h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM261.889 924.715l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM832 733.867h-32c0 36.373-0.026 61.726-1.638 81.463-1.583 19.366-4.531 30.494-8.823 38.921l57.024 29.056c9.655-18.957 13.683-39.441 15.586-62.763 1.877-22.95 1.852-51.362 1.852-86.677h-32zM627.2 938.667v32c35.315 0 63.727 0.026 86.677-1.852 23.322-1.903 43.806-5.931 62.763-15.586l-29.056-57.024c-8.427 4.292-19.554 7.241-38.921 8.823-19.738 1.613-45.090 1.638-81.463 1.638v32zM818.048 868.779l-28.51-14.528c-9.203 18.061-23.893 32.747-41.954 41.954l29.056 57.024c30.106-15.343 54.579-39.817 69.922-69.922l-28.514-14.528zM788.467 110.817l21.828-23.4c-37.065-34.574-86.767-40.859-133.385-27.669-46.276 13.094-91.409 45.48-126.451 91.906l51.081 38.557c28.147-37.289 62.174-60.217 92.796-68.881 30.281-8.568 55.215-3.053 72.307 12.887l21.824-23.4zM234.668 110.817l21.827 23.4c17.089-15.94 42.025-21.455 72.306-12.887 30.62 8.663 64.648 31.592 92.794 68.881l51.084-38.557c-35.046-46.426-80.175-78.812-126.453-91.906-46.617-13.19-96.32-6.905-133.385 27.669l21.827 23.4zM256 275.056l23.791-21.401c-17.064-18.969-29.293-45.365-33.131-70.081-3.979-25.627 1.821-41.882 9.834-49.356l-43.654-46.8c-30.106 28.082-34.972 70.231-29.422 105.976 5.692 36.657 23.188 74.602 48.791 103.064l23.791-21.401zM447.134 170.932l-25.539 19.279c21.784 28.862 35.89 60.869 42.256 91.379l62.652-13.067c-8.337-39.959-26.475-80.636-53.824-116.869l-25.545 19.279zM576 170.932l-25.54-19.279c-27.349 36.233-45.491 76.91-53.824 116.869l62.652 13.067c6.362-30.51 20.467-62.517 42.253-91.379l-25.54-19.279zM768 275.056l23.791 21.401c25.685-28.552 42.953-66.591 48.393-103.281 5.308-35.811 0.085-77.799-29.888-105.759l-43.652 46.8c8.141 7.596 14.020 24.012 10.231 49.574-3.657 24.682-15.68 50.985-32.666 69.863l23.791 21.401zM627.2 938.667v-32h-115.2v64h115.2v-32zM512 938.667v-32h-115.2v64h115.2v-32zM512 490.667h-32v448h64v-448h-32zM853.333 490.667v-32h-21.333v64h21.333v-32zM832 490.667v-32h-320v64h320v-32zM832 490.667h-32v243.2h64v-243.2h-32zM512 490.667v-32h-320v64h320v-32zM192 490.667v-32h-21.333v64h21.333v-32zM192 733.867h32v-243.2h-64v243.2h32zM170.667 277.333v32h87.467v-64h-87.467v32zM256 275.056l-23.358 21.872 2.132 2.278 46.717-43.744-2.133-2.278-23.358 21.872zM765.867 277.333v32h87.467v-64h-87.467v32zM768 275.056l-23.351-21.879-2.133 2.278 46.703 43.758 2.133-2.278-23.351-21.879zM258.133 277.333v32h237.043v-64h-237.043v32zM495.177 275.056h-32v2.278h64v-2.278h-32zM495.177 277.333v32h32.785v-64h-32.785v32zM527.962 277.333v32h237.905v-64h-237.905v32zM527.962 275.056h-32v2.278h64v-2.278h-32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "gift"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1686,
        "id": 334,
        "name": "gift",
        "prevSize": 32,
        "code": 59962
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 315
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface GiftIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const GiftIcon = ({ size = 16, ...props }: GiftIconProps) => (
  <IcoMoon iconSet={iconSet} icon="gift" size={size} {...props} />
);

GiftIcon.displayName = 'GiftIcon';

export default GiftIcon;
