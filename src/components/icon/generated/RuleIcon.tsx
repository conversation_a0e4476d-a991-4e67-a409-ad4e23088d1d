// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M384.772 384.772l127.228 127.228M384.772 384.772l-106.281 106.279M384.772 384.772l107.051-107.053M598.878 170.667l72.158-72.158c17.566-17.567 46.046-17.567 63.612 0l190.844 190.842c17.566 17.567 17.566 46.048 0 63.614l-572.527 572.527c-17.567 17.566-46.048 17.566-63.614 0l-190.842-190.844c-17.567-17.566-17.567-46.046 0-63.612l73.702-73.702M598.878 170.667l126.455 126.456M598.878 170.667l-107.055 107.052M172.21 597.333l126.456 126.455M172.21 597.333l106.281-106.283M278.491 491.051l74.474 74.475M491.823 277.719l75.247 75.246"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "rule"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1855,
        "id": 165,
        "name": "rule",
        "prevSize": 32,
        "code": 60131
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 484
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface RuleIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const RuleIcon = ({ size = 16, ...props }: RuleIconProps) => (
  <IcoMoon iconSet={iconSet} icon="rule" size={size} {...props} />
);

RuleIcon.displayName = 'RuleIcon';

export default RuleIcon;
