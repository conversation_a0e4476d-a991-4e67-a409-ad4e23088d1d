.herond-themes {
  --shawdow-sm: 0px 0px 40px 0px rgba(0, 0, 0, 0.1);
  --shawdow-md: 0px 0px 5px 0px rgba(0, 0, 0, 0.1), 0px 6px 12px 0px rgba(0, 0, 0, 0.1);
  --shawdow-lg: 0px 0px 40px 0px rgba(0, 0, 0, 0.1), 0px 4px 24px 0px rgba(0, 0, 0, 0.25);
  --shadow-1:
    inset 0 0 0 1px var(--brand-gray500), inset 0 1.5px 2px 0 var(--brand-gray200),
    inset 0 1.5px 2px 0 hexToRgba(var(--grays-black), 0.2);
}

.rt-shadow-blur-sm {
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(50px);
}

.rt-shadow-blur-lg {
  box-shadow:
    0px 7px 10px 0px rgba(0, 0, 0, 0.09),
    0px 40px 80px 0px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(50px);
}

.rt-shadow-recessed {
  box-shadow:
    0px -0.5px 1px 0px rgba(255, 255, 255, 0.3) inset,
    0px -0.5px 1px 0px rgba(255, 255, 255, 0.25) inset,
    0px 1.5px 4px 0px rgba(0, 0, 0, 0.08) inset,
    0px 1.5px 4px 0px rgba(0, 0, 0, 0.1) inset;
}

.rt-shadow-recessed-highlight {
  box-shadow:
    0px -0.5px 1px 0px rgba(94, 94, 94, 0.3) inset,
    0px -0.5px 1px 0px rgba(255, 255, 255, 0.2) inset;
}
