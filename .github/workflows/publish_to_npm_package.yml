name: Publish Private Package to GitHub Packages (npm)

on:
  workflow_dispatch:
jobs:
  publish:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Automated Version Bump
        id: version-bump
        uses: 'phips28/gh-action-bump-version@master'
        env:
          GITHUB_TOKEN: ${{ secrets.PACKAGE_PAT }}
        with:
          tag-prefix: ''
          commit-message: 'ci: bump version to {{version}}'
          default: patch
          minor-wording: 'MINOR'
          major-wording: 'MAJOR'
          patch-wording: 'PATCH'

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          registry-url: 'https://npm.pkg.github.com'

      - name: Install dependencies
        run: npm install

      - name: Build the Package
        run: npm run build

      - name: Build CLI
        run: npm run build:cli

      - name: Publish to GitHub Packages (npm.pkg.github.com)
        env:
          NODE_AUTH_TOKEN: ${{ secrets.PACKAGE_PAT }}
        run: npm publish
