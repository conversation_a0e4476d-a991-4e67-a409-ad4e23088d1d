// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M417.185 219.898h521.481M417.185 416.82h521.481M85.333 613.743h853.333M85.333 810.667h853.333M227.555 453.743l-141.232-139.183c-1.32-1.3-1.32-3.409 0-4.71l141.232-139.183"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "text-outdent"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1950,
        "id": 70,
        "name": "text-outdent",
        "prevSize": 32,
        "code": 60226
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 579
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface TextOutdentIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const TextOutdentIcon = ({ size = 16, ...props }: TextOutdentIconProps) => (
  <IcoMoon iconSet={iconSet} icon="text-outdent" size={size} {...props} />
);

TextOutdentIcon.displayName = 'TextOutdentIcon';

export default TextOutdentIcon;
