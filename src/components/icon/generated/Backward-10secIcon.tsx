// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M248.644 299.672c-61.22 61.443-99.42 142.188-108.091 228.477-8.671 86.285 12.723 172.779 60.538 244.74s119.091 124.937 201.684 149.901c82.592 24.969 171.395 20.373 251.271-12.988 79.876-33.365 145.886-93.44 186.782-169.988 40.9-76.548 54.153-164.834 37.504-249.813s-62.17-161.394-128.802-216.227c-65.536-53.926-147.507-83.694-232.358-84.465-2.364-0.022-3.029-1.296-1.075-2.624 24.077-16.383 175.714-101.352 175.714-101.352M343.393 452.407h24.688c21.283 0 38.537 17.075 38.537 38.135v212.143M596.297 452.407c46.366 0 84.301 42.103 84.301 93.559v63.155c0 51.46-37.935 93.564-84.301 93.564s-84.301-42.103-84.301-93.564v-63.155c0-51.925 37.935-93.559 84.301-93.559z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "round",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "backward-10sec"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "round",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1444,
        "id": 576,
        "name": "backward-10sec",
        "prevSize": 32,
        "code": 59720
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 73
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface Backward-10secIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const Backward-10secIcon = ({ size = 16, ...props }: Backward-10secIconProps) => (
  <IcoMoon iconSet={iconSet} icon="backward-10sec" size={size} {...props} />
);

Backward-10secIcon.displayName = 'Backward-10secIcon';

export default Backward-10secIcon;
