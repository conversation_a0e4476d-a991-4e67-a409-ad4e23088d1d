// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M843.853 749.039c-52.365 0-94.814 42.449-94.814 94.814s42.449 94.814 94.814 94.814c52.365 0 94.814-42.449 94.814-94.814s-42.449-94.814-94.814-94.814zM843.853 749.039c0-52.365-42.449-94.818-94.814-94.818h-237.039M85.333 85.333h23.704c65.456 0 118.518 53.062 118.518 118.519v308.148M227.555 227.555h497.778c39.275 0 71.113 31.838 71.113 71.111M677.926 227.555v426.665M227.555 393.481h450.371M512 725.333c0 117.82-95.513 213.333-213.333 213.333s-213.333-95.514-213.333-213.333c0-117.82 95.513-213.333 213.333-213.333s213.333 95.514 213.333 213.333zM369.778 725.333c0 39.275-31.838 71.113-71.111 71.113s-71.111-31.838-71.111-71.113c0-39.275 31.838-71.113 71.111-71.113s71.111 31.838 71.111 71.113z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "wheel-chair"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 2008,
        "id": 12,
        "name": "wheel-chair",
        "prevSize": 32,
        "code": 60284
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 637
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface WheelChairIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const WheelChairIcon = ({ size = 16, ...props }: WheelChairIconProps) => (
  <IcoMoon iconSet={iconSet} icon="wheel-chair" size={size} {...props} />
);

WheelChairIcon.displayName = 'WheelChairIcon';

export default WheelChairIcon;
