// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M389.433 864c26.986 38.686 71.819 64 122.567 64s95.582-25.314 122.569-64M747.349 338.694l47.646 237.054c1.463 7.266 3.358 14.494 6.852 21.030 5.167 9.647 12.006 18.334 20.233 25.647l5.888 5.235c16.132 14.34 25.365 34.897 25.365 56.482 0 41.737-33.835 75.575-75.575 75.575h-531.517c-41.738 0-75.574-33.839-75.574-75.575 0-21.585 9.231-42.142 25.365-56.482l5.89-5.235c8.223-7.313 15.063-16 20.229-25.647 3.498-6.537 5.393-13.764 6.853-21.030l63.553-316.201c28.941-97.039 118.18-163.548 219.443-163.548 19.567 0 38.686 2.483 57.007 7.19M853.333 202.667h-213.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bell-plus-1"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1468,
        "id": 552,
        "name": "bell-plus-1",
        "prevSize": 32,
        "code": 59744
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 97
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BellPlus-1IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BellPlus-1Icon = ({ size = 16, ...props }: BellPlus-1IconProps) => (
  <IcoMoon iconSet={iconSet} icon="bell-plus-1" size={size} {...props} />
);

BellPlus-1Icon.displayName = 'BellPlus-1Icon';

export default BellPlus-1Icon;
