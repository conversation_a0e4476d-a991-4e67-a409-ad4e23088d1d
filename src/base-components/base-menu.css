.rt-BaseMenuContent {
  --scrollarea-scrollbar-vertical-margin-top: var(--base-menu-content-padding);
  --scrollarea-scrollbar-vertical-margin-bottom: var(--base-menu-content-padding);
  --scrollarea-scrollbar-horizontal-margin-left: var(--base-menu-content-padding);
  --scrollarea-scrollbar-horizontal-margin-right: var(--base-menu-content-padding);

  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden;
  background-color: var(--base-menu-bg);
  backdrop-filter: blur(25px);
}

.rt-BaseMenuViewport {
  flex: 1 1 0%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  padding: var(--base-menu-content-padding);
  box-sizing: border-box;

  :where(.rt-BaseMenuContent:has(.rt-ScrollAreaScrollbar[data-orientation='vertical'])) & {
    padding-right: var(--spatial-3);
  }
}

.rt-BaseMenuItem {
  display: flex;
  align-items: center;
  gap: var(--spatial-2);
  height: var(--base-menu-item-height);
  padding-left: var(--base-menu-item-padding-left);
  padding-right: var(--base-menu-item-padding-right);
  box-sizing: border-box;
  position: relative;
  outline: none;
  scroll-margin: var(--base-menu-content-padding) 0;

  /* Fix sticky text highlighting after selection in Firefox */
  user-select: none;

  /* Cursors */
  cursor: var(--cursor-menu-item);
  &:where([data-disabled]) {
    cursor: default;
  }
}

.rt-BaseMenuShortcut {
  display: flex;
  align-items: center;
  margin-left: auto;
  padding-left: var(--spatial-4);
}

.rt-BaseMenuSubTriggerIcon {
  color: var(--brand-gray950);
  margin-right: calc(-2px * var(--scaling));
}

.rt-BaseMenuItemIndicator {
  position: absolute;
  left: 0;
  width: var(--base-menu-item-padding-left);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.rt-BaseMenuSeparator {
  height: 1px;
  margin-top: var(--spatial-2);
  margin-bottom: var(--spatial-2);
  margin-left: var(--base-menu-item-padding-left);
  margin-right: var(--base-menu-item-padding-right);
}

.rt-BaseMenuLabel {
  display: flex;
  align-items: center;
  height: var(--base-menu-item-height);
  padding-left: var(--base-menu-item-padding-left);
  padding-right: var(--base-menu-item-padding-right);
  box-sizing: border-box;
  color: var(--brand-gray900);
  user-select: none;
  cursor: default;

  :where(.rt-BaseMenuItem) + & {
    margin-top: var(--spatial-2);
  }
}

.rt-BaseMenuArrow {
  fill: var(--base-menu-bg);
}

/***************************************************************************************************
 *                                                                                                 *
 * SIZES                                                                                           *
 *                                                                                                 *
 ***************************************************************************************************/

@breakpoints {
  .rt-BaseMenuContent {
    &:where(.rt-r-size-1) {
      --base-menu-content-padding: var(--spatial-1);
      --base-menu-item-padding-left: calc(var(--spatial-5) / 1.2);
      --base-menu-item-padding-right: var(--spatial-2);
      --base-menu-item-height: var(--spatial-5);
      border-radius: var(--radius-3);

      & :where(.rt-BaseMenuItem) {
        font-size: var(--font-size-2);
        line-height: var(--line-height-2);
        letter-spacing: var(--letter-spacing-2);
        border-radius: var(--radius-2);
      }

      & :where(.rt-BaseMenuLabel) {
        font-size: var(--font-size-2);
        line-height: var(--line-height-2);
        letter-spacing: var(--letter-spacing-2);
      }

      & :where(.rt-BaseMenuItemIndicatorIcon, .rt-BaseMenuSubTriggerIcon) {
        width: calc(8px * var(--scaling));
        height: calc(8px * var(--scaling));
      }

      /* reset with :not:has so we still support browsers without :has */
      &:where(:not(:has(.rt-BaseMenuCheckboxItem, .rt-BaseMenuRadioItem))) {
        --base-menu-item-padding-left: var(--spatial-2);
      }
      &:where(:has(.rt-BaseMenuCheckboxItem, .rt-BaseMenuRadioItem)) {
        --base-menu-item-padding-left: calc(var(--spatial-5) / 1.2);
      }
    }

    &:where(.rt-r-size-2) {
      --base-menu-content-padding: var(--spatial-2);
      --base-menu-item-padding-left: var(--spatial-3);
      --base-menu-item-padding-right: var(--spatial-3);
      --base-menu-item-height: var(--spatial-6);
      border-radius: var(--radius-4);

      & :where(.rt-BaseMenuItem) {
        font-size: var(--font-size-3);
        line-height: var(--line-height-3);
        letter-spacing: var(--letter-spacing-3);
        border-radius: var(--radius-3);
      }

      & :where(.rt-BaseMenuLabel) {
        font-size: var(--font-size-3);
        line-height: var(--line-height-3);
        letter-spacing: var(--letter-spacing-3);
      }

      & :where(.rt-BaseMenuItemIndicatorIcon, .rt-BaseMenuSubTriggerIcon) {
        width: calc(10px * var(--scaling));
        height: calc(10px * var(--scaling));
      }

      /* reset with :not:has so we still support browsers without :has */
      &:where(:not(:has(.rt-BaseMenuCheckboxItem, .rt-BaseMenuRadioItem))) {
        --base-menu-item-padding-left: var(--spatial-3);
      }
      &:where(:has(.rt-BaseMenuCheckboxItem, .rt-BaseMenuRadioItem)) {
        --base-menu-item-padding-left: var(--spatial-5);
      }
    }

    &:where(.rt-r-size-3) {
      --base-menu-content-padding: var(--spatial-2);
      --base-menu-item-padding-left: var(--spatial-4);
      --base-menu-item-padding-right: var(--spatial-4);
      --base-menu-item-height: var(--spatial-7);
      border-radius: var(--radius-5);

      & :where(.rt-BaseMenuItem) {
        font-size: var(--font-size-4);
        line-height: var(--line-height-4);
        letter-spacing: var(--letter-spacing-4);
        border-radius: var(--radius-4);
      }

      & :where(.rt-BaseMenuLabel) {
        font-size: var(--font-size-4);
        line-height: var(--line-height-4);
        letter-spacing: var(--letter-spacing-4);
      }

      & :where(.rt-BaseMenuItemIndicatorIcon, .rt-BaseMenuSubTriggerIcon) {
        width: calc(12px * var(--scaling));
        height: calc(12px * var(--scaling));
      }

      /* reset with :not:has so we still support browsers without :has */
      &:where(:not(:has(.rt-BaseMenuCheckboxItem, .rt-BaseMenuRadioItem))) {
        --base-menu-item-padding-left: var(--spatial-4);
      }
      &:where(:has(.rt-BaseMenuCheckboxItem, .rt-BaseMenuRadioItem)) {
        --base-menu-item-padding-left: var(--spatial-6);
      }
    }
  }
}

/***************************************************************************************************
 *                                                                                                 *
 * VARIANTS                                                                                        *
 *                                                                                                 *
 ***************************************************************************************************/

.rt-BaseMenuContent {
  --base-menu-bg: var(--materials-thin);
  box-shadow:
    0 0 0 1px var(--brand-gray300),
    0 12px 60px rgba(var(--grays-black-a), 0.3),
    0 12px 32px -16px var(--brand-gray500);
}
.rt-BaseMenuItem:where([data-accent-color]) {
  color: var(--brand-accent);
}
.rt-BaseMenuItem:where([data-disabled]) {
  color: var(--fills-tertiary);
}
.rt-BaseMenuShortcut {
  color: var(--brand-gray900);
}
.rt-BaseMenuItem:where([data-disabled], [data-highlighted]),
.rt-BaseMenuSubTrigger:where([data-state='open']) {
  & :where(.rt-BaseMenuShortcut) {
    color: inherit;
  }
}
.rt-BaseMenuSeparator {
  background-color: var(--brand-gray600);
}

/* solid */

.rt-BaseMenuContent:where(.rt-variant-solid) {
  & :where(.rt-BaseMenuSubTrigger[data-state='open']) {
    background-color: var(--brand-gray300);
  }
  & :where(.rt-BaseMenuItem[data-highlighted]) {
    background-color: var(--brand-accent);
    color: var(--grays-white);

    & :where(.rt-BaseMenuSubTriggerIcon) {
      color: var(--grays-white);
    }
  }
  &:where(.rt-high-contrast) {
    & :where(.rt-BaseMenuItem[data-highlighted]) {
      background-color: var(--brand-gray100);
      color: var(--brand-gray900);

      & :where(.rt-BaseMenuSubTriggerIcon) {
        color: var(--brand-gray900);
      }

      &:where([data-accent-color]) {
        background-color: var(--brand-accent-40);
        color: var(--brand-accent);
      }
    }
  }
}

/* soft */

.rt-BaseMenuContent:where(.rt-variant-soft) {
  & :where(.rt-BaseMenuSubTrigger[data-state='open']) {
    background-color: var(--brand-accent-button-bezeled-fill-hover);
  }
  & :where(.rt-BaseMenuItem[data-highlighted]) {
    background-color: var(--brand-accent-40);
  }
}
