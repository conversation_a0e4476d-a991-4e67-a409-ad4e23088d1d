// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M128 488.294v71.113c0 65.455 53.726 118.519 120 118.519h139.439c28.225 0 50.36 23.936 47.804 51.699l-10.551 114.615c-4.667 50.705 35.759 94.426 87.308 94.426s91.977-43.721 87.309-94.426l-10.551-114.615c-2.556-27.763 19.58-51.699 47.804-51.699h139.439c66.274 0 120-53.065 120-118.519v-71.113M128 488.294v-355.554c0-26.182 21.49-47.407 48-47.407h124.748c11.249 0 20.988 7.716 23.428 18.562l39.138 173.946c1.114 4.95 8.258 4.95 9.371 0l39.138-173.946c2.44-10.845 12.18-18.562 23.427-18.562h340.749c66.274 0 120 53.062 120 118.519v284.442M128 488.294h768"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "paint-brush-broad"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1808,
        "id": 212,
        "name": "paint-brush-broad",
        "prevSize": 32,
        "code": 60084
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 437
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface PaintBrushBroadIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const PaintBrushBroadIcon = ({ size = 16, ...props }: PaintBrushBroadIconProps) => (
  <IcoMoon iconSet={iconSet} icon="paint-brush-broad" size={size} {...props} />
);

PaintBrushBroadIcon.displayName = 'PaintBrushBroadIcon';

export default PaintBrushBroadIcon;
