// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M533.146 245.929c46.511-65.549 95.501-123.286 122.893-154.39 9.707-11.020 27.209-6.547 33.404 6.991 58.359 127.535 249.225 259.803 249.225 399.838 0 142.686-103.505 259.93-235.814 273.135M533.146 245.929c-41.54-48.669-79.014-96.706-101.303-144.214-7.884-16.806-30.161-22.359-42.513-8.678-79.874 88.458-303.995 350.44-303.995 505.030 0 188.109 148.575 340.599 331.852 340.599 121.567 0 227.866-67.089 285.667-167.164M533.146 245.929c96.887 113.516 215.893 230.466 215.893 352.138 0 63.334-16.845 122.637-46.187 173.436M419.171 796.446c-35.471 0-70.23-9.95-100.329-28.715-30.099-18.769-54.332-45.602-69.944-77.449"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "blood"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1472,
        "id": 548,
        "name": "blood",
        "prevSize": 32,
        "code": 59748
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 101
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BloodIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BloodIcon = ({ size = 16, ...props }: BloodIconProps) => (
  <IcoMoon iconSet={iconSet} icon="blood" size={size} {...props} />
);

BloodIcon.displayName = 'BloodIcon';

export default BloodIcon;
