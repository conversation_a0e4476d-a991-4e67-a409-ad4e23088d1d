// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M155.223 924.715v0zM99.284 868.779v0zM924.715 868.779v0zM868.779 924.715v0zM868.779 227.284v0zM924.715 283.223v0zM155.223 227.284v0zM99.284 283.223v0zM655.445 99.284v0zM711.381 155.223v0zM368.556 99.284v0zM312.618 155.223v0zM424.886 577.724v0zM599.113 577.724v0zM489.621 603.264v0zM534.379 603.264v0zM469.333 394.667c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM554.667 458.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM938.667 418.133h-32v315.733h64v-315.733h-32zM733.867 938.667v-32h-443.733v64h443.733v-32zM85.333 733.867h32v-315.733h-64v315.733h32zM290.133 938.667v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM85.333 733.867h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM155.223 924.715l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM938.667 733.867h-32c0 36.373-0.026 61.726-1.638 81.463-1.583 19.366-4.531 30.494-8.823 38.921l57.024 29.056c9.655-18.957 13.683-39.441 15.586-62.763 1.877-22.95 1.852-51.362 1.852-86.677h-32zM733.867 938.667v32c35.315 0 63.727 0.026 86.677-1.852 23.322-1.903 43.806-5.931 62.763-15.586l-29.056-57.024c-8.427 4.292-19.554 7.241-38.921 8.823-19.738 1.613-45.090 1.638-81.463 1.638v32zM924.715 868.779l-28.51-14.528c-9.207 18.061-23.893 32.747-41.954 41.954l29.056 57.024c30.106-15.343 54.579-39.817 69.922-69.922l-28.514-14.528zM733.867 213.333v32c36.373 0 61.726 0.025 81.463 1.638 19.366 1.582 30.494 4.532 38.921 8.826l29.056-57.024c-18.957-9.658-39.441-13.683-62.763-15.589-22.95-1.875-51.362-1.85-86.677-1.85v32zM938.667 418.133h32c0-35.315 0.026-63.727-1.852-86.676-1.903-23.323-5.931-43.809-15.586-62.762l-57.024 29.055c4.292 8.428 7.241 19.553 8.823 38.919 1.613 19.739 1.638 45.093 1.638 81.464h32zM868.779 227.284l-14.528 28.512c18.061 9.204 32.747 23.89 41.954 41.953l57.024-29.055c-15.343-30.106-39.817-54.583-69.922-69.923l-14.528 28.512zM290.133 213.333v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM85.333 418.133h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM155.223 227.284l-14.528-28.512c-30.106 15.34-54.583 39.817-69.923 69.923l57.024 29.055c9.204-18.063 23.89-32.75 41.953-41.953l-14.528-28.512zM503.467 85.333v32h17.067v-64h-17.067v32zM520.533 85.333v32c36.373 0 61.726 0.025 81.463 1.638 19.366 1.582 30.494 4.532 38.921 8.826l29.056-57.024c-18.957-9.658-39.441-13.683-62.763-15.589-22.95-1.875-51.362-1.85-86.677-1.85v32zM655.445 99.284l-14.528 28.512c18.061 9.204 32.747 23.89 41.954 41.953l57.024-29.055c-15.343-30.106-39.817-54.583-69.922-69.923l-14.528 28.512zM503.467 85.333v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.738-1.613 45.091-1.638 81.464-1.638v-32zM368.556 99.284l-14.528-28.512c-30.106 15.34-54.583 39.817-69.923 69.923l57.024 29.055c9.204-18.063 23.89-32.75 41.953-41.953l-14.528-28.512zM299.895 213.333l31.941 1.95c1.434-23.496 4.506-36.134 9.295-45.533l-57.024-29.055c-10.701 21.002-14.521 43.981-16.151 70.688l31.94 1.95zM290.133 213.333v32h9.761v-64h-9.761v32zM299.895 213.333v32h424.21v-64h-424.21v32zM724.105 213.333v32h9.762v-64h-9.762v32zM724.105 213.333l31.94-1.95c-1.63-26.707-5.449-49.686-16.149-70.688l-57.024 29.055c4.787 9.399 7.859 22.037 9.293 45.533l31.94-1.95zM85.333 418.133l-13.612 28.962 339.553 159.59 27.223-57.924-339.553-159.589-13.612 28.961zM599.113 577.724l13.615 28.962 339.55-159.59-27.221-57.923-339.554 159.589 13.611 28.962zM424.886 577.724l-13.612 28.962c30.445 14.306 50.886 24.201 72.749 28.083l5.598-31.505 5.594-31.509c-11.554-2.052-23.125-7.206-56.717-22.993l-13.612 28.962zM599.113 577.724l-13.611-28.962c-33.591 15.787-45.163 20.941-56.717 22.993l5.594 31.509 5.598 31.505c21.862-3.883 42.304-13.777 72.751-28.083l-13.615-28.962zM489.621 603.264l-5.598 31.505c18.509 3.285 37.444 3.285 55.953 0l-5.598-31.505-5.594-31.509c-11.102 1.971-22.468 1.971-33.57 0l-5.594 31.509zM469.333 426.667v32h85.333v-64h-85.333v32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "briefcase"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1491,
        "id": 529,
        "name": "briefcase",
        "prevSize": 32,
        "code": 59767
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 120
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BriefcaseIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BriefcaseIcon = ({ size = 16, ...props }: BriefcaseIconProps) => (
  <IcoMoon iconSet={iconSet} icon="briefcase" size={size} {...props} />
);

BriefcaseIcon.displayName = 'BriefcaseIcon';

export default BriefcaseIcon;
