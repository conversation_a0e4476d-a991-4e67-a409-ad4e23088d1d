// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M316.208 379.514c-6.356 0.572-12.787 1.17-19.298 1.792-38.947 3.724-70.047 37.768-73.65 80.979-11.483 137.719-12.302 243.401-6.041 365.914 3.176 62.148 57.661 110.285 122.992 110.315l344.039 0.154c65.331 0.030 119.868-48.055 122.918-110.212 6.063-123.588 4.45-228.958-6.451-365.658-3.465-43.439-34.603-77.824-73.741-81.585-6.737-0.647-13.389-1.267-19.959-1.859M316.208 379.514l3.299-35.754c2.402-43.211 23.136-77.254 49.1-80.979 53.254-7.639 98.385-11.477 143.394-11.511M316.208 379.514c143.291-12.899 248.742-12.963 390.809-0.161M707.017 379.352l-2.539-35.080c-2.308-43.44-23.070-77.825-49.161-81.586-53.047-7.646-98.121-11.45-143.317-11.416M512 85.333h-99.555c-46.137 0-84.944 13.2-96.237 48.388M512 85.333h149.333M512 85.333v165.936"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "hand-soap"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1697,
        "id": 323,
        "name": "hand-soap",
        "prevSize": 32,
        "code": 59973
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 326
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface HandSoapIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const HandSoapIcon = ({ size = 16, ...props }: HandSoapIconProps) => (
  <IcoMoon iconSet={iconSet} icon="hand-soap" size={size} {...props} />
);

HandSoapIcon.displayName = 'HandSoapIcon';

export default HandSoapIcon;
