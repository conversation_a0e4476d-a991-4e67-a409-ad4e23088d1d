// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M646.95 378.777c-34.385-34.825-82.146-56.407-134.95-56.407s-100.567 21.582-134.949 56.407M646.95 378.777c33.809 34.246 54.677 81.297 54.677 133.223s-20.868 98.978-54.677 133.222M646.95 378.777l167.612-167.612M814.562 211.165c-77.282-77.719-184.303-125.832-302.562-125.832s-225.282 48.113-302.561 125.832M814.562 211.165c76.702 77.142 124.105 183.453 124.105 300.835 0 117.38-47.403 223.693-124.105 300.834M377.051 378.777c-33.81 34.246-54.681 81.297-54.681 133.223s20.87 98.978 54.681 133.222M377.051 378.777l-167.612-167.612M209.439 211.165c-76.705 77.142-124.106 183.453-124.106 300.835 0 117.38 47.401 223.693 124.106 300.834M377.051 645.222c34.382 34.825 82.144 56.405 134.949 56.405s100.565-21.581 134.95-56.405M377.051 645.222l-167.612 167.612M209.439 812.834c77.279 77.722 184.301 125.833 302.561 125.833s225.28-48.111 302.562-125.833M646.95 645.222l167.612 167.612"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "lifebuoy"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1729,
        "id": 291,
        "name": "lifebuoy",
        "prevSize": 32,
        "code": 60005
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 358
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface LifebuoyIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const LifebuoyIcon = ({ size = 16, ...props }: LifebuoyIconProps) => (
  <IcoMoon iconSet={iconSet} icon="lifebuoy" size={size} {...props} />
);

LifebuoyIcon.displayName = 'LifebuoyIcon';

export default LifebuoyIcon;
