/// <reference types="vitest/config" />
import react from '@vitejs/plugin-react-swc';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import { defineConfig } from 'vite';
import dts from 'vite-plugin-dts';
import { viteStaticCopy } from 'vite-plugin-static-copy';

const dirname = typeof __dirname !== 'undefined' ? __dirname : path.dirname(fileURLToPath(import.meta.url));

const externals = [
  /^react(-dom)?$/, // react, react-dom
  /^react\/jsx-runtime$/, // react/jsx-runtime
];

export default defineConfig({
  plugins: [
    react(),
    dts({
      tsconfigPath: './tsconfig.json',
      entryRoot: 'src',
      outDir: 'dist',
      copyDtsFiles: true,
      exclude: ['src/stories/**/*', '**/*.stories.tsx', '**/*.stories.ts', 'src/vite-env.d.ts'],
    }),
    viteStaticCopy({
      targets: [
        {
          src: 'src/styles/fonts/sf_pro',
          dest: 'fonts',
        },
      ],
    }),
  ],

  resolve: {
    alias: {
      '@base-components': path.resolve(dirname, 'src/base-components'),
      '@components': path.resolve(dirname, 'src/components'),
      '@helpers': path.resolve(dirname, 'src/helpers'),
      '@props': path.resolve(dirname, 'src/props'),
    },
  },

  build: {
    lib: {
      entry: path.resolve(dirname, 'src/index.ts'),
      fileName: (format, entryName) => {
        const ext = format === 'es' ? 'js' : 'cjs';
        return entryName === 'index' ? `index.${ext}` : `${entryName}/index.${ext}`;
      },
      formats: ['es', 'cjs'],
    },
    rollupOptions: {
      external: (id) => externals.some((ext) => ext.test(id)) || id.includes('src/stories'),
      input: {
        index: path.resolve(dirname, 'src/index.ts'),
        components: path.resolve(dirname, 'src/components/index.ts'),
      },
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM',
          'react/jsx-runtime': 'jsxRuntime',
        },
      },
      treeshake: {
        moduleSideEffects: false,
        propertyReadSideEffects: false,
      },
    },
    minify: true,
    sourcemap: true,
    emptyOutDir: true,
  },
});
