.rt-Tag {
  display: inline-flex;
  align-items: center;
  white-spatial: nowrap;
  font-family: var(--default-font-family);
  font-weight: var(--font-weight-medium);
  font-style: normal;
  flex-shrink: 0;
  line-height: 1;

  /* Make sure that the height is not stretched in a Flex/Grid layout */
  height: fit-content;

  &:where([data-disabled]) {
    color: var(--labels-tertiary);
    background-color: var(--fills-tertiary);
    outline: none;
    filter: none;
  }
}

/***************************************************************************************************
 *                                                                                                 *
 * SIZES                                                                                           *
 *                                                                                                 *
 ***************************************************************************************************/

@breakpoints {
  .rt-Tag {
    &:where(.rt-r-size-1) {
      font-size: var(--font-size-2);
      line-height: var(--line-height-2);
      letter-spacing: var(--letter-spacing-2);
      padding: var(--spatial-0) var(--spatial-3);
      gap: var(--spatial-1);
      border-radius: var(--radius-5);
      height: var(--spatial-6);
    }
    &:where(.rt-r-size-2) {
      font-size: var(--font-size-3);
      line-height: var(--line-height-3);
      letter-spacing: var(--letter-spacing-3);
      padding: var(--spatial-2) var(--spatial-3);
      gap: var(--spatial-2);
      border-radius: var(--radius-5);
      height: var(--spatial-7);

      svg {
        min-height: 14px;
        min-width: 14px;
      }
    }
    &:where(.rt-r-size-3) {
      font-size: var(--font-size-4);
      line-height: var(--line-height-4);
      letter-spacing: var(--letter-spacing-4);
      padding: var(--spatial-3) var(--spatial-3);
      gap: var(--spatial-3);
      border-radius: var(--radius-5);
      height: var(--spatial-8);

      svg {
        min-height: var(--spatial-5);
        min-width: var(--spatial-5);
      }
    }
    &:where(.rt-r-size-4) {
      font-size: var(--font-size-5);
      line-height: var(--line-height-5);
      letter-spacing: var(--letter-spacing-5);
      padding: var(--spatial-3) var(--spatial-4);
      gap: var(--spatial-4);
      border-radius: var(--radius-6);
      height: var(--spatial-9);

      svg {
        min-height: var(--spatial-5);
        min-width: var(--spatial-5);
      }
    }
    &:where(.rt-r-size-5) {
      font-size: var(--font-size-6);
      line-height: var(--line-height-6);
      letter-spacing: var(--letter-spacing-6);
      padding: var(--spatial-4) var(--spatial-4);
      gap: var(--spatial-5);
      border-radius: var(--radius-7);
      height: var(--spatial-10);

      svg {
        min-height: var(--spatial-6);
        min-width: var(--spatial-6);
      }
    }
  }
}

/***************************************************************************************************
 *                                                                                                 *
 * VARIANTS                                                                                        *
 *                                                                                                 *
 ***************************************************************************************************/

/* primary */

.rt-Tag:where(.rt-variant-primary) {
  background-color: var(--brand-accent);
  color: var(--grays-white);

  &:hover {
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.1) 100%), var(--brand-accent);
  }

  &::selection {
    background-color: var(--brand-accent);
    color: var(--grays-white);
  }

  &:where(.rt-high-contrast) {
    background-color: var(--brand-accent-button-bezeled-fill-hover);
    color: var(--brand-accent-40);

    &::selection {
      background-color: var(--brand-accent-button-bezeled-fill-hover);
      color: var(--brand-accent-40);
    }
  }
}

/* primary light */
.rt-Tag:where(.rt-variant-primary-light) {
  background-color: var(--brand-accent-button-bezeled-fill);
  color: var(--brand-accent);

  &:hover {
    background: var(--brand-accent-button-bezeled-fill-hover);
  }

  &::selection {
    background-color: var(--brand-accent-40);
    color: var(--brand-accent);
  }

  &:where(.rt-high-contrast) {
    background-color: var(--brand-accent-button-bezeled-fill-hover);
    color: var(--brand-accent-40);

    &::selection {
      background-color: var(--brand-accent-button-bezeled-fill-hover);
      color: var(--brand-accent-40);
    }
  }
}

/* primary outline */

.rt-Tag:where(.rt-variant-primary-outline) {
  background-color: var(--background-primary-base);
  box-shadow: inset 0 0 0 1px var(--brand-accent-40);
  color: var(--brand-accent);

  &:hover {
    background: var(--brand-accent-button-bezeled-fill);
  }

  &:where(.rt-high-contrast) {
    color: var(--brand-accent-40);
  }
}

/* secondary */

.rt-Tag:where(.rt-variant-secondary) {
  background-color: var(--background-primary-base);
  box-shadow: inset 0 0 0 1px var(--separators-non-opaque);
  color: var(--labels-primary);

  &:where(.rt-high-contrast) {
    box-shadow:
      inset 0 0 0 1px var(--brand-accent-40),
      inset 0 0 0 1px var(--brand-gray700);
    color: var(--brand-accent);
  }
}

/* tertiary */
.rt-Tag:where(.rt-variant-tertiary) {
  background-color: var(--fills-secondary);
  color: var(--labels-primary);
  &:where(.rt-high-contrast) {
    box-shadow:
      inset 0 0 0 1px var(--brand-accent-40),
      inset 0 0 0 1px var(--brand-gray700);
    color: var(--brand-accent);
  }
}
