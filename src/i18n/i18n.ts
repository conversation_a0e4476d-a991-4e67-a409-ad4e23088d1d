import i18next, { type InitOptions } from 'i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';
import { initReactI18next } from 'react-i18next';

const DEFAULT_LANGUAGE = 'en';
const LOCAL_STORAGE_LANGUAGE_KEY = 'i18nextLng';

const initializeI18n = async (options: InitOptions) => {
  await i18next
    .use(Backend)
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
      detection: { lookupLocalStorage: LOCAL_STORAGE_LANGUAGE_KEY },
      fallbackLng: DEFAULT_LANGUAGE,
      interpolation: { escapeValue: false },
      ...options,
    });
  return i18next;
};

export { initializeI18n };
