import clsx from 'clsx';
import { Slot } from 'radix-ui';
import * as React from 'react';

import Icon, { type IconType } from '@components/icon';
import { linkPropDefs } from '@components/link/link.props';
import { type ComponentPropsWithout, type RemovedProps, extractProps } from '@helpers';
import type { GetPropDefTypes } from '@props';

type LinkElement = React.ComponentRef<'a'>;
type LinkOwnProps = GetPropDefTypes<typeof linkPropDefs>;
interface LinkProps extends LinkOwnProps, ComponentPropsWithout<'a', RemovedProps> {
  title: string;
  disabled?: boolean;
  prefix?: IconType;
  suffix?: IconType;
  as?: React.ElementType;
}
const Link = React.forwardRef<LinkElement, LinkProps>((props, forwardedRef) => {
  const { className, as, ...linkProps } = extractProps(props, linkPropDefs);
  const Comp = as ?? 'a';
  return (
    <Slot.Root
      data-disabled={props.disabled ? 'disabled' : ''}
      {...linkProps}
      ref={forwardedRef}
      className={clsx('rt-reset', 'rt-Link', className)}
    >
      <Comp href={props.href}>
        {props.prefix && <Icon icon={props.prefix} />}
        {props.title}
        {props.suffix && <Icon icon={props.suffix} />}
      </Comp>
    </Slot.Root>
  );
});
Link.displayName = 'Link';

export { Link };
export type { LinkProps };
