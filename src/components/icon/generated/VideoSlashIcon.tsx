// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M768 618.667l113.020 42.381c27.891 10.462 57.647-10.159 57.647-39.949v-218.197c0-29.79-29.756-50.41-57.647-39.95l-113.020 42.382M234.667 405.333h29.048M85.333 85.333l170.738 170.738M938.667 938.667l-208.158-208.158M256.072 256.072c10.38-0.072 21.687-0.072 34.062-0.072h273.067c71.689 0 107.529 0 134.912 13.951 24.085 12.272 43.665 31.853 55.936 55.938 13.952 27.38 13.952 63.224 13.952 134.911v102.4c0 71.689 0 107.529-13.952 134.912-6.135 12.041-14.097 22.959-23.539 32.397M256.072 256.072l474.437 474.437M138.521 280.139c-16.458 11.855-29.943 27.511-39.236 45.75-13.951 27.38-13.951 63.224-13.951 134.911v102.4c0 71.689 0 107.529 13.951 134.912 12.272 24.085 31.853 43.665 55.938 55.936 27.38 13.952 63.224 13.952 134.911 13.952h273.067c24.772 0 45.265 0 62.605-0.576"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "video-slash"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1987,
        "id": 33,
        "name": "video-slash",
        "prevSize": 32,
        "code": 60263
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 616
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface VideoSlashIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const VideoSlashIcon = ({ size = 16, ...props }: VideoSlashIconProps) => (
  <IcoMoon iconSet={iconSet} icon="video-slash" size={size} {...props} />
);

VideoSlashIcon.displayName = 'VideoSlashIcon';

export default VideoSlashIcon;
