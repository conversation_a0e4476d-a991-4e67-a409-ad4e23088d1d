// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M417.185 770.133c0 51.84-42.45 93.867-94.815 93.867s-94.815-42.027-94.815-93.867M417.185 770.133c0-51.84-42.45-93.867-94.815-93.867s-94.815 42.027-94.815 93.867M417.185 770.133h189.629M227.555 770.133h-23.703c-65.456 0-118.519-52.531-118.519-117.333v-70.4M796.446 770.133c0 51.84-42.453 93.867-94.818 93.867s-94.814-42.027-94.814-93.867M796.446 770.133c0-51.84-42.453-93.867-94.818-93.867s-94.814 42.027-94.814 93.867M796.446 770.133h23.701c65.455 0 118.519-52.531 118.519-117.333v-70.4M630.519 282.027h66.628c33.075 0 64.649 13.685 87.087 37.748l61.5 65.96M630.519 282.027v-4.693c0-64.801-53.065-117.333-118.519-117.333h-260.741c-36.214 0-68.635 16.080-90.375 41.422M630.519 282.027v426.018M845.734 385.735l61.5 65.96c20.211 21.675 31.433 50.086 31.433 79.586v51.119M845.734 385.735h-49.289c-26.185 0-47.407 21.012-47.407 46.935v55.863c0 51.84 42.449 93.867 94.814 93.867h94.814M85.333 336h341.333M158.476 468.002h243.809"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "shipping-truck"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1886,
        "id": 134,
        "name": "shipping-truck",
        "prevSize": 32,
        "code": 60162
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 515
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ShippingTruckIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ShippingTruckIcon = ({ size = 16, ...props }: ShippingTruckIconProps) => (
  <IcoMoon iconSet={iconSet} icon="shipping-truck" size={size} {...props} />
);

ShippingTruckIcon.displayName = 'ShippingTruckIcon';

export default ShippingTruckIcon;
