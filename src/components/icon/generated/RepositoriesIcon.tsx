// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M534.754 701.628v230.473c0 5.713-7.266 8.691-11.75 4.817l-60.275-52.087c-10.726-9.267-27.264-9.267-37.992 0l-60.275 52.087c-4.485 3.874-11.751 0.896-11.751-4.817v-230.473M853.333 701.628v-568.887c0-26.182-20.378-47.407-45.513-47.407h-523.376c-62.838 0-113.778 53.062-113.778 118.519v568.888M853.333 701.628h-614.4c-37.703 0-68.267 31.838-68.267 71.113M853.333 701.628v94.818c0 26.18-20.378 47.407-45.513 47.407h-147.908M170.667 772.74c0 39.275 30.564 71.113 68.267 71.113"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "repositories"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1851,
        "id": 169,
        "name": "repositories",
        "prevSize": 32,
        "code": 60127
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 480
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface RepositoriesIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const RepositoriesIcon = ({ size = 16, ...props }: RepositoriesIconProps) => (
  <IcoMoon iconSet={iconSet} icon="repositories" size={size} {...props} />
);

RepositoriesIcon.displayName = 'RepositoriesIcon';

export default RepositoriesIcon;
