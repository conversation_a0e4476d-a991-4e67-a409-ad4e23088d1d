// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M189.295 511.108c-0.208-86.371 29.648-170 84.48-236.636s131.248-112.156 216.227-128.804c84.979-16.648 173.265-3.395 249.813 37.503s136.623 106.908 169.988 186.784c33.361 79.875 37.956 168.677 12.988 251.271-24.964 82.59-77.939 153.869-149.901 201.681-71.962 47.817-158.455 69.21-244.74 60.54-86.289-8.67-167.035-46.874-228.477-108.092M189.295 511.108c-3.706 0-103.961-178.919-103.961-178.919M189.295 511.108c3.706 0 179.074-102.73 179.074-102.73"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "rotate-left"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1853,
        "id": 167,
        "name": "rotate-left",
        "prevSize": 32,
        "code": 60129
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 482
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface RotateLeftIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const RotateLeftIcon = ({ size = 16, ...props }: RotateLeftIconProps) => (
  <IcoMoon iconSet={iconSet} icon="rotate-left" size={size} {...props} />
);

RotateLeftIcon.displayName = 'RotateLeftIcon';

export default RotateLeftIcon;
