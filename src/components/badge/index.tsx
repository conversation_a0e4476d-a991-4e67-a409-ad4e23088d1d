'use client';

import clsx from 'clsx';
import { Slot } from 'radix-ui';
import * as React from 'react';

import { type ComponentPropsWithout, type RemovedProps, extractProps } from '@helpers';
import { type GetPropDefTypes, type MarginProps, marginPropDefs } from '@props';

import { badgePropDefs } from './badge.props';

type BadgeElement = React.ComponentRef<'span'>;
type BadgeOwnProps = GetPropDefTypes<typeof badgePropDefs>;
interface BadgeProps extends ComponentPropsWithout<'span', RemovedProps>, MarginProps, BadgeOwnProps {
  count?: React.ReactNode;
  overflowCount?: number;
  dot?: boolean;
  showZero?: boolean;
  offset?: [number, number];
}

const ScrollDigit = ({ digit }: { digit: string }) => {
  const digitRef = React.useRef<HTMLDivElement>(null);
  const containerRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const digitNum = parseInt(digit, 10);

    if (!isNaN(digitNum) && digitRef.current && containerRef.current) {
      const computedStyle = getComputedStyle(containerRef.current);
      const height = computedStyle.getPropertyValue('--badge-indicator-size').trim();

      const heightValue = parseFloat(height);

      const finalHeight = isNaN(heightValue) ? containerRef.current.clientHeight : heightValue;

      digitRef.current.style.transform = `translateY(-${digitNum * finalHeight}px)`;
    }
  }, [digit]);

  return (
    <div className="rt-ScrollDigitContainer" ref={containerRef}>
      <div className="rt-ScrollDigitList" ref={digitRef}>
        {Array.from({ length: 10 }).map((_, i) => (
          <div className="rt-ScrollDigitItem" key={i}>
            {i}
          </div>
        ))}
      </div>
    </div>
  );
};

const ScrollNumber: React.FC<{ count: React.ReactNode }> = ({ count }) => {
  if (typeof count !== 'string' && typeof count !== 'number') return <>{count}</>;

  const digits = `${count}`.split('');

  return (
    <div className={clsx('rt-ScrollNumber', { ['rt-MultipleWords']: digits.length > 1 })}>
      {digits.map((digit, idx) =>
        /\d/.test(digit) ? <ScrollDigit key={idx} digit={digit} /> : <span key={idx}>{digit}</span>,
      )}
    </div>
  );
};

const Badge = React.forwardRef<BadgeElement, BadgeProps>((props, forwardedRef) => {
  const {
    count = 0,
    overflowCount = 99,
    dot = false,
    showZero = false,
    offset = [0, 0],
    asChild,
    className,
    color,
    ...badgeProps
  } = extractProps(props, badgePropDefs, marginPropDefs);

  const Comp = asChild ? Slot.Root : 'span';

  const isNumber = typeof count === 'number';
  const isEmpty = count == null || (isNumber && (count < 0 || (count === 0 && !showZero)));
  const displayCount = isNumber && count > overflowCount ? `${overflowCount}+` : count;

  if (!isEmpty) {
    return (
      <span className={clsx('rt-BadgeWrapper', className)}>
        <Comp data-accent-color={color} {...badgeProps} ref={forwardedRef} className={clsx('rt-reset', 'rt-Badge')} />
        <span
          data-accent-color={color}
          className={clsx('rt-BadgeIndicator', { 'rt-BadgeDot': dot })}
          style={{ top: offset[1], right: offset[0] }}
        >
          {!dot && <ScrollNumber count={displayCount} />}
        </span>
      </span>
    );
  }

  return <Comp {...badgeProps} ref={forwardedRef} className={clsx('rt-reset', 'rt-Badge', className)} />;
});

Badge.displayName = 'Badge';

export { Badge };
export type { BadgeProps };
