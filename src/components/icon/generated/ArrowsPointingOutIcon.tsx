// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M160 160v192M160 160h192M160 160l224 224M160 864v-192M160 864h192M160 864l224-224M864 160h-192M864 160v192M864 160l-224 224M864 864h-192M864 864v-192M864 864l-224-224"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "round",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "arrows-pointing-out"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "round",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1439,
        "id": 581,
        "name": "arrows-pointing-out",
        "prevSize": 32,
        "code": 59715
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 68
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ArrowsPointingOutIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ArrowsPointingOutIcon = ({ size = 16, ...props }: ArrowsPointingOutIconProps) => (
  <IcoMoon iconSet={iconSet} icon="arrows-pointing-out" size={size} {...props} />
);

ArrowsPointingOutIcon.displayName = 'ArrowsPointingOutIcon';

export default ArrowsPointingOutIcon;
