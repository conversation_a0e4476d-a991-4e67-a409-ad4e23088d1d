// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M131.926 876.032v0zM94.634 838.741v0zM131.926 595.968v0zM94.634 633.259v0zM277.333 704c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM447.573 768c17.677 0 32-14.327 32-32s-14.323-32-32-32v64zM131.926 428.032v0zM94.634 390.74v0zM929.365 390.74v0zM892.075 428.032v0zM892.075 147.968v0zM929.365 185.26v0zM131.926 147.968v0zM94.634 185.26v0zM277.333 256c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM490.24 320c17.677 0 32-14.327 32-32s-14.323-32-32-32v64zM618.671 320c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM618.244 256c-17.677 0-32 14.327-32 32s14.323 32 32 32v-64zM746.671 320c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM746.244 256c-17.677 0-32 14.327-32 32s14.323 32 32 32v-64zM490.24 618.667c17.677 0 32-14.327 32-32s-14.323-32-32-32v64zM448 917.333c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM632.806 770.394c-5.133-16.909-23.006-26.458-39.915-21.325-16.913 5.137-26.458 23.006-21.325 39.919l61.239-18.594zM662.417 860.365v0zM760.751 884.578v0zM875.085 864.060c12.548-12.446 12.629-32.708 0.183-45.252-12.446-12.548-32.708-12.629-45.257-0.183l45.073 45.436zM630.379 831.010v0zM717.257 882.534v0zM817.626 867.533v0zM526.78 792.23c-8.67 15.398-3.209 34.914 12.19 43.58 15.403 8.67 34.914 3.213 43.584-12.19l-55.774-31.39zM652.267 805.090c15.266 8.905 34.859 3.746 43.763-11.52 8.9-15.266 3.742-34.859-11.524-43.763l-32.239 55.283zM897.886 837.436c9.971-14.592 6.225-34.5-8.367-44.471-14.588-9.971-34.5-6.229-44.471 8.363l52.838 36.109zM862.955 640.99v0zM776.077 589.466v0zM675.708 604.467v0zM966.554 679.77c8.67-15.398 3.209-34.91-12.19-43.58-15.403-8.67-34.914-3.213-43.584 12.19l55.774 31.39zM841.067 666.91c-15.266-8.905-34.859-3.746-43.763 11.52-8.9 15.266-3.742 34.859 11.524 43.763l32.239-55.283zM860.527 701.606c5.133 16.913 23.006 26.458 39.915 21.325 16.913-5.137 26.458-23.006 21.325-39.919l-61.239 18.594zM830.916 611.635v0zM732.582 587.422v0zM618.249 607.94c-12.548 12.446-12.629 32.708-0.183 45.252 12.446 12.548 32.708 12.629 45.257 0.183l-45.073-45.436zM595.447 634.564c-9.971 14.592-6.225 34.5 8.367 44.471 14.588 9.971 34.5 6.229 44.471-8.363l-52.838-36.109zM85.333 748.8h32v-25.6h-64v25.6h32zM221.867 885.333v-32c-24.424 0-40.817-0.026-53.441-1.058-12.251-0.998-18.108-2.786-21.972-4.753l-29.055 57.024c14.39 7.33 29.607 10.193 45.815 11.52 15.835 1.293 35.285 1.267 58.653 1.267v-32zM85.333 748.8h-32c0 23.369-0.025 42.816 1.269 58.654 1.324 16.209 4.188 31.424 11.52 45.815l57.024-29.056c-1.969-3.866-3.756-9.724-4.756-21.973-1.032-12.625-1.056-29.018-1.056-53.44h-32zM131.926 876.032l14.528-28.51c-10.035-5.116-18.194-13.274-23.308-23.309l-57.024 29.056c11.249 22.076 29.199 40.026 51.276 51.277l14.528-28.514zM221.867 586.667v-32c-23.368 0-42.818-0.026-58.653 1.267-16.209 1.327-31.426 4.19-45.815 11.52l29.055 57.024c3.864-1.967 9.721-3.755 21.972-4.753 12.624-1.033 29.017-1.058 53.441-1.058v-32zM85.333 723.2h32c0-24.422 0.025-40.815 1.056-53.44 1.001-12.25 2.788-18.108 4.756-21.973l-57.024-29.056c-7.332 14.391-10.195 29.606-11.52 45.815-1.294 15.838-1.269 35.285-1.269 58.654h32zM131.926 595.968l-14.528-28.514c-22.078 11.251-40.028 29.201-51.277 51.277l57.024 29.056c5.113-10.035 13.272-18.193 23.308-23.309l-14.528-28.51zM277.333 736v32h170.24v-64h-170.24v32zM221.867 138.667v32h580.267v-64h-580.267v32zM938.667 275.2h-32v25.6h64v-25.6h-32zM85.333 300.8h32v-25.6h-64v25.6h32zM221.867 437.333v-32c-24.424 0-40.817-0.025-53.441-1.056-12.251-1.001-18.108-2.788-21.972-4.756l-29.055 57.026c14.39 7.33 29.607 10.193 45.815 11.52 15.835 1.293 35.285 1.267 58.653 1.267v-32zM85.333 300.8h-32c0 23.368-0.025 42.818 1.269 58.653 1.324 16.208 4.188 31.426 11.52 45.815l57.024-29.055c-1.969-3.864-3.756-9.721-4.756-21.972-1.032-12.624-1.056-29.017-1.056-53.441h-32zM131.926 428.032l14.528-28.512c-10.035-5.113-18.194-13.272-23.308-23.308l-57.024 29.055c11.249 22.077 29.199 40.027 51.277 51.278l14.528-28.514zM938.667 300.8h-32c0 24.424-0.026 40.817-1.058 53.441-0.998 12.251-2.786 18.108-4.753 21.972l57.024 29.055c7.33-14.39 10.193-29.607 11.52-45.815 1.293-15.835 1.267-35.285 1.267-58.653h-32zM802.133 437.333v32c23.369 0 42.816 0.026 58.654-1.267 16.209-1.327 31.424-4.19 45.815-11.52l-29.056-57.026c-3.866 1.969-9.724 3.756-21.973 4.756-12.625 1.032-29.018 1.056-53.44 1.056v32zM929.365 390.74l-28.51-14.528c-5.116 10.035-13.274 18.194-23.309 23.308l29.056 57.026c22.076-11.251 40.026-29.201 51.277-51.278l-28.514-14.528zM802.133 138.667v32c24.422 0 40.815 0.025 53.44 1.056 12.25 1.001 18.108 2.788 21.973 4.756l29.056-57.024c-14.391-7.332-29.606-10.195-45.815-11.52-15.838-1.294-35.285-1.269-58.654-1.269v32zM938.667 275.2h32c0-23.368 0.026-42.818-1.267-58.653-1.327-16.208-4.19-31.426-11.52-45.815l-57.024 29.055c1.967 3.864 3.755 9.721 4.753 21.972 1.033 12.624 1.058 29.017 1.058 53.441h32zM892.075 147.968l-14.528 28.512c10.035 5.113 18.193 13.272 23.309 23.308l57.024-29.056c-11.251-22.077-29.201-40.027-51.277-51.276l-14.528 28.512zM221.867 138.667v-32c-23.368 0-42.818-0.025-58.653 1.269-16.209 1.324-31.426 4.188-45.815 11.52l29.055 57.024c3.864-1.969 9.721-3.756 21.972-4.756 12.624-1.032 29.017-1.056 53.441-1.056v-32zM85.333 275.2h32c0-24.424 0.025-40.817 1.056-53.441 1.001-12.251 2.788-18.108 4.756-21.972l-57.024-29.055c-7.332 14.39-10.195 29.607-11.52 45.815-1.294 15.835-1.269 35.285-1.269 58.653h32zM131.926 147.968l-14.528-28.512c-22.078 11.249-40.028 29.199-51.277 51.277l57.024 29.055c5.113-10.035 13.272-18.194 23.308-23.308l-14.528-28.512zM277.333 288v32h212.907v-64h-212.907v32zM618.671 288v-32h-0.427v64h0.427v-32zM746.671 288v-32h-0.427v64h0.427v-32zM221.867 586.667v32h268.373v-64h-268.373v32zM448 885.333v-32h-226.133v64h226.133v-32zM602.189 779.691l-30.622 9.297c12.207 40.196 38.093 74.846 73.195 98.065l35.311-53.38c-22.729-15.031-39.407-37.402-47.266-63.279l-30.618 9.297zM662.417 860.365l-17.655 26.688c30.093 19.908 65.353 30.353 101.244 30.281l-0.064-32-0.068-32c-23.398 0.047-46.31-6.767-65.801-19.661l-17.655 26.692zM745.941 885.333l0.064 32c5.965-0.013 11.951-0.316 17.929-0.913l-3.183-31.842-3.187-31.842c-3.9 0.393-7.799 0.589-11.691 0.597l0.068 32zM760.751 884.578l3.183 31.842c41.963-4.198 81.242-22.69 111.151-52.361l-45.073-45.436c-19.46 19.307-45.069 31.373-72.448 34.112l3.187 31.842zM596.437 736.358l-32-0.081c-0.102 42.027 14.494 82.714 41.276 115.119l49.331-40.772c-17.276-20.902-26.671-47.117-26.607-74.189l-32-0.077zM630.379 831.010l-24.666 20.386c26.782 32.401 64.051 54.477 105.417 62.545l12.254-62.818c-26.923-5.248-51.055-19.588-68.339-40.499l-24.666 20.386zM717.257 882.534l-6.127 31.407c11.511 2.244 23.147 3.366 34.743 3.392l0.068-32 0.064-32c-7.556-0.017-15.13-0.747-22.622-2.21l-6.127 31.411zM745.941 885.333l-0.068 32c30.067 0.064 59.908-7.258 86.78-21.551l-30.054-56.503c-17.523 9.323-36.988 14.097-56.593 14.054l-0.064 32zM596.437 736.358v-32c-7.479 0-12.902 2.607-15.049 3.746-2.556 1.357-4.39 2.778-5.355 3.571-1.941 1.596-3.281 3.081-3.81 3.674-1.199 1.357-2.095 2.594-2.483 3.132-0.922 1.284-1.822 2.667-2.53 3.785-1.515 2.377-3.409 5.508-5.44 8.93-4.117 6.929-9.391 16.051-14.541 25.028-5.163 9.015-10.274 18.014-14.093 24.751-1.907 3.371-3.494 6.178-4.604 8.149-0.559 0.981-0.994 1.758-1.293 2.287-0.149 0.265-0.265 0.469-0.341 0.606-0.038 0.068-0.068 0.119-0.090 0.158-0.009 0.017-0.017 0.030-0.021 0.038-0.004 0.004-0.004 0.009-0.004 0.013 0 0-0.004 0-0.004 0 0 0.004 0 0.004 27.887 15.697 27.887 15.697 27.887 15.697 27.887 15.697s0 0 0-0.004c0 0 0.004-0.004 0.004-0.009 0.004-0.009 0.013-0.021 0.021-0.034 0.021-0.034 0.047-0.085 0.085-0.154 0.077-0.132 0.188-0.329 0.333-0.589 0.294-0.521 0.721-1.284 1.271-2.253 1.097-1.946 2.671-4.727 4.561-8.068 3.785-6.682 8.836-15.573 13.926-24.457 5.107-8.913 10.185-17.681 14.037-24.171 1.954-3.29 3.452-5.747 4.429-7.283 0.533-0.845 0.649-0.994 0.482-0.759-0.013 0.013-0.597 0.849-1.557 1.933-0.41 0.461-1.647 1.843-3.503 3.371-0.926 0.755-2.722 2.15-5.244 3.49-2.116 1.122-7.509 3.725-14.963 3.725v-32zM668.386 777.451c16.119-27.644 16.119-27.644 16.119-27.644s0-0.004-0.004-0.004c0 0-0.004 0-0.009-0.004-0.009-0.004-0.021-0.013-0.038-0.021-0.034-0.021-0.085-0.051-0.154-0.090-0.137-0.081-0.333-0.196-0.593-0.346-0.521-0.303-1.28-0.747-2.249-1.31-1.937-1.131-4.702-2.739-8.026-4.672-6.647-3.861-15.531-9.015-24.469-14.174-8.922-5.146-17.967-10.342-24.909-14.268-3.452-1.95-6.515-3.661-8.828-4.919-1.122-0.606-2.3-1.237-3.349-1.762-0.474-0.239-1.297-0.649-2.231-1.058-0.431-0.192-1.348-0.585-2.517-0.994-0.576-0.205-1.604-0.546-2.914-0.87-0.905-0.226-3.887-0.956-7.778-0.956v64c-3.87 0-6.822-0.725-7.684-0.943-1.267-0.316-2.249-0.644-2.773-0.828-1.050-0.367-1.822-0.704-2.078-0.815-0.585-0.256-0.922-0.435-0.802-0.371 0.149 0.073 0.636 0.324 1.587 0.841 1.822 0.99 4.506 2.487 7.872 4.39 6.686 3.78 15.535 8.862 24.414 13.986 8.866 5.116 17.69 10.231 24.303 14.076 3.302 1.92 6.054 3.524 7.979 4.642 0.96 0.559 1.715 0.998 2.227 1.297 0.256 0.149 0.452 0.265 0.585 0.341 0.068 0.038 0.115 0.068 0.149 0.090 0.017 0.009 0.030 0.017 0.038 0.021 0.004 0 0.004 0.004 0.009 0.004 0 0 0 0 0 0 0.004 0 0.004 0 16.124-27.639zM817.626 867.533l15.027 28.25c26.317-13.995 48.653-34.078 65.233-58.347l-52.838-36.109c-10.765 15.753-25.289 28.826-42.449 37.952l15.027 28.254zM896.896 735.642l32 0.081c0.102-42.027-14.494-82.714-41.276-115.119l-49.331 40.772c17.276 20.902 26.671 47.117 26.607 74.189l32 0.077zM862.955 640.99l24.666-20.386c-26.782-32.401-64.051-54.477-105.417-62.545l-12.254 62.818c26.923 5.248 51.055 19.588 68.339 40.499l24.666-20.386zM776.077 589.466l6.127-31.407c-11.516-2.244-23.147-3.366-34.748-3.392l-0.064 32-0.068 32c7.556 0.017 15.134 0.747 22.626 2.21l6.127-31.411zM747.392 586.667l0.064-32c-30.067-0.064-59.908 7.258-86.775 21.551l30.054 56.503c17.519-9.323 36.983-14.093 56.589-14.054l0.068-32zM896.896 735.642v32c7.479 0 12.902-2.607 15.049-3.746 2.556-1.357 4.39-2.778 5.355-3.571 1.941-1.596 3.281-3.081 3.81-3.674 1.199-1.357 2.095-2.594 2.483-3.132 0.922-1.284 1.822-2.667 2.534-3.785 1.51-2.377 3.405-5.508 5.436-8.93 4.117-6.929 9.391-16.051 14.541-25.028 5.163-9.015 10.274-18.014 14.093-24.751 1.907-3.371 3.494-6.178 4.604-8.149 0.559-0.981 0.994-1.758 1.293-2.287 0.149-0.265 0.265-0.469 0.341-0.606 0.038-0.068 0.068-0.119 0.090-0.158 0.009-0.017 0.017-0.030 0.021-0.038 0.004-0.004 0.004-0.009 0.004-0.013 0 0 0.004 0 0.004 0 0-0.004 0-0.004-27.887-15.697-27.887-15.697-27.887-15.697-27.887-15.697s0 0 0 0.004c0 0-0.004 0.004-0.004 0.009-0.004 0.009-0.013 0.021-0.021 0.034-0.021 0.034-0.047 0.085-0.085 0.154-0.077 0.132-0.188 0.329-0.333 0.589-0.294 0.521-0.721 1.284-1.271 2.253-1.097 1.946-2.671 4.727-4.561 8.068-3.785 6.682-8.836 15.573-13.926 24.457-5.107 8.913-10.185 17.681-14.037 24.171-1.954 3.29-3.452 5.747-4.429 7.283-0.533 0.845-0.649 0.994-0.482 0.759 0.013-0.013 0.597-0.849 1.557-1.933 0.41-0.461 1.647-1.843 3.503-3.366 0.926-0.759 2.722-2.155 5.244-3.494 2.116-1.122 7.509-3.725 14.963-3.725v32zM824.947 694.549c-16.119 27.644-16.119 27.644-16.119 27.644s0 0.004 0.004 0.004c0 0 0.004 0 0.009 0.004 0.009 0.004 0.021 0.013 0.038 0.021 0.034 0.021 0.085 0.051 0.154 0.090 0.137 0.081 0.333 0.196 0.593 0.346 0.521 0.303 1.28 0.747 2.249 1.31 1.937 1.131 4.702 2.739 8.026 4.672 6.647 3.861 15.531 9.015 24.469 14.174 8.922 5.146 17.967 10.342 24.909 14.268 3.452 1.95 6.515 3.661 8.828 4.919 1.122 0.606 2.3 1.237 3.349 1.762 0.474 0.239 1.297 0.649 2.231 1.058 0.431 0.192 1.348 0.585 2.517 0.994 0.576 0.205 1.604 0.546 2.914 0.87 0.905 0.226 3.887 0.956 7.778 0.956v-64c3.87 0 6.822 0.725 7.684 0.943 1.267 0.316 2.249 0.644 2.773 0.828 1.050 0.367 1.822 0.704 2.078 0.815 0.585 0.256 0.922 0.435 0.802 0.371-0.149-0.073-0.636-0.324-1.587-0.841-1.822-0.99-4.506-2.487-7.872-4.39-6.686-3.78-15.535-8.862-24.414-13.986-8.866-5.116-17.69-10.231-24.303-14.076-3.302-1.92-6.054-3.52-7.979-4.642-0.96-0.559-1.715-0.998-2.227-1.297-0.256-0.149-0.452-0.265-0.585-0.341-0.068-0.038-0.115-0.068-0.149-0.090-0.017-0.009-0.030-0.017-0.038-0.021-0.004 0-0.004-0.004-0.009-0.004 0 0 0 0 0 0-0.004 0-0.004 0-16.124 27.639zM891.145 692.309l30.622-9.297c-12.207-40.196-38.093-74.846-73.195-98.065l-35.311 53.38c22.729 15.031 39.407 37.402 47.266 63.279l30.618-9.297zM830.916 611.635l17.655-26.688c-30.093-19.908-65.353-30.353-101.248-30.281l0.068 32 0.064 32c23.403-0.047 46.315 6.763 65.805 19.661l17.655-26.692zM747.392 586.667l-0.068-32c-5.965 0.013-11.947 0.316-17.924 0.913l3.183 31.842 3.187 31.842c3.9-0.393 7.799-0.589 11.686-0.597l-0.064-32zM732.582 587.422l-3.183-31.842c-41.963 4.198-81.242 22.69-111.151 52.361l45.073 45.436c19.46-19.307 45.069-31.373 72.448-34.112l-3.187-31.842zM675.708 604.467l-15.027-28.25c-26.317 13.995-48.653 34.078-65.233 58.347l52.838 36.109c10.765-15.753 25.289-28.826 42.449-37.952l-15.027-28.254zM802.133 437.333v-32h-580.267v64h580.267v-32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "driver-refresh"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1610,
        "id": 410,
        "name": "driver-refresh",
        "prevSize": 32,
        "code": 59886
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 239
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface DriverRefreshIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const DriverRefreshIcon = ({ size = 16, ...props }: DriverRefreshIconProps) => (
  <IcoMoon iconSet={iconSet} icon="driver-refresh" size={size} {...props} />
);

DriverRefreshIcon.displayName = 'DriverRefreshIcon';

export default DriverRefreshIcon;
