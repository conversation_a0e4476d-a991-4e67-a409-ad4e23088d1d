// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M789.333 432.384c-0.644-0.371-1.297-0.73-1.963-1.067-9.126-4.651-21.073-4.651-44.971-4.651h-76.8c-23.898 0-35.844 0-44.971 4.651-8.030 4.092-14.554 10.615-18.645 18.645-4.651 9.126-4.651 21.073-4.651 44.971v34.133c0 23.898 0 35.844 4.651 44.971 4.092 8.030 10.615 14.554 18.645 18.645 9.126 4.651 21.073 4.651 44.971 4.651h76.8c23.898 0 35.844 0 44.971-4.651 0.666-0.337 1.318-0.695 1.963-1.067M789.333 432.384c7.13 4.113 12.932 10.214 16.683 17.579 4.651 9.126 4.651 21.073 4.651 44.971v34.133c0 23.898 0 35.844-4.651 44.971-3.75 7.364-9.553 13.466-16.683 17.579M789.333 432.384v-129.451c0-23.895 0-35.843-4.651-44.97-4.092-8.028-10.615-14.555-18.645-18.646-9.126-4.65-21.073-4.65-44.971-4.65h-418.133c-23.895 0-35.843 0-44.97 4.65-8.028 4.091-14.555 10.618-18.646 18.646-4.65 9.127-4.65 21.075-4.65 44.97v418.133c0 23.898 0 35.844 4.65 44.971 4.091 8.030 10.618 14.554 18.646 18.645 9.127 4.651 21.075 4.651 44.97 4.651h418.133c23.898 0 35.844 0 44.971-4.651 8.030-4.092 14.554-10.615 18.645-18.645 4.651-9.126 4.651-21.073 4.651-44.971v-129.451M290.133 938.667h443.733c71.689 0 107.529 0 134.912-13.952 24.085-12.271 43.665-31.851 55.936-55.936 13.952-27.383 13.952-63.223 13.952-134.912v-443.733c0-71.687 0-107.53-13.952-134.911-12.271-24.085-31.851-43.666-55.936-55.938-27.383-13.951-63.223-13.951-134.912-13.951h-443.733c-71.687 0-107.53 0-134.911 13.951-24.085 12.272-43.666 31.853-55.938 55.938-13.951 27.38-13.951 63.224-13.951 134.911v443.733c0 71.689 0 107.529 13.951 134.912 12.272 24.085 31.853 43.665 55.938 55.936 27.38 13.952 63.224 13.952 134.911 13.952z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "locker"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1747,
        "id": 273,
        "name": "locker",
        "prevSize": 32,
        "code": 60023
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 376
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface LockerIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const LockerIcon = ({ size = 16, ...props }: LockerIconProps) => (
  <IcoMoon iconSet={iconSet} icon="locker" size={size} {...props} />
);

LockerIcon.displayName = 'LockerIcon';

export default LockerIcon;
