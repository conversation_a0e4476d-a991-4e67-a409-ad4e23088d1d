// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M369.778 184.561c-30.769 1.813-62.776 4.184-96.768 7.114-43.274 3.73-77.83 37.82-81.833 81.091-3.15 34.048-5.578 66.139-7.343 97.012M369.778 184.561v-99.228M369.778 184.561c49.573-2.921 95.937-4.392 142.222-4.413M512 180.148v-94.815M512 180.148c46.929-0.021 93.777 1.449 143.808 4.413M655.808 184.561c30.276 1.793 61.722 4.133 95.053 7.020 43.486 3.766 78.089 38.199 81.937 81.699 2.991 33.833 5.355 65.749 7.117 96.499M655.808 184.561v-99.228M839.915 369.778h98.752M839.915 369.778c2.825 49.384 4.096 95.758 3.921 142.222M843.836 512h94.831M843.836 512c-0.171 46.293-1.779 92.676-4.71 142.221M839.125 654.221c-1.801 30.447-4.1 62.089-6.878 95.633-3.639 43.985-38.473 79.019-82.432 82.748-32.969 2.795-64.081 5.069-94.007 6.822M839.125 654.221h99.541M369.778 839.569c-30.292-1.758-61.724-4.058-95.020-6.895-44.063-3.759-78.898-38.98-82.497-83.076-2.727-33.404-5.049-64.956-6.909-95.377M369.778 839.569v99.098M369.778 839.569c49.384 2.867 95.741 4.297 142.222 4.284M512 843.853v94.814M512 843.853c46.746-0.017 93.615-1.489 143.808-4.429M655.808 839.424v99.243M185.352 654.221h-100.019M185.352 654.221c-3.018-49.361-4.817-95.74-5.149-142.221M180.204 512h-94.87M180.204 512c-0.329-46.298 0.798-92.69 3.631-142.222M183.834 369.778h-98.501M376.433 695.241c99.837 8.516 170.331 8.533 271.464-0.043 25.118-2.129 45.022-22.144 47.1-47.279 8.371-101.167 9.19-172.006 0.316-272.331-2.197-24.857-21.973-44.533-46.822-46.685-100.83-8.732-171.409-8.707-273.056 0.054-24.728 2.131-44.474 21.612-46.762 46.338-9.374 101.318-7.555 172.332 0.619 272.475 2.057 25.199 21.963 45.325 47.142 47.471z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "cpu"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1589,
        "id": 431,
        "name": "cpu",
        "prevSize": 32,
        "code": 59865
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 218
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CpuIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CpuIcon = ({ size = 16, ...props }: CpuIconProps) => (
  <IcoMoon iconSet={iconSet} icon="cpu" size={size} {...props} />
);

CpuIcon.displayName = 'CpuIcon';

export default CpuIcon;
