// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M412.658 412.592l-237.037-237.037M412.658 412.592c39.72-39.708 90.228-68.624 146.749-81.976M412.658 412.592c-39.741 39.73-68.683 90.26-82.042 146.815M175.621 175.555c39.719-39.707 90.227-68.624 146.749-81.975M175.621 175.555c-39.742 39.73-68.683 90.263-82.042 146.816M559.407 330.616c22.827-5.392 46.635-8.245 71.113-8.245 170.185 0 308.147 137.964 308.147 308.149 0 24.474-2.854 48.282-8.247 71.108M559.407 330.616l-237.036-237.037M322.371 93.579c22.828-5.392 46.636-8.246 71.111-8.246 170.184 0 308.146 137.963 308.146 308.148 0 24.475-2.85 48.285-8.243 71.112M693.385 464.593l237.035 237.035M693.385 464.593c-13.252 56.107-41.847 106.287-81.105 145.873M930.419 701.628c-13.252 56.111-41.843 106.291-81.101 145.877M330.616 559.407c-5.392 22.827-8.245 46.635-8.245 71.113 0 170.185 137.964 308.147 308.149 308.147 24.474 0 48.282-2.854 71.108-8.247M330.616 559.407l-237.037-237.036M93.579 322.371c-5.392 22.828-8.246 46.636-8.246 71.111 0 170.184 137.963 308.146 308.148 308.146 24.475 0 48.285-2.85 71.112-8.243M464.593 693.385l237.035 237.035M464.593 693.385c56.969-13.457 107.827-42.726 147.686-82.918M701.628 930.419c56.969-13.453 107.827-42.722 147.691-82.914M612.279 610.466l237.039 237.039"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "exclude-selection"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1621,
        "id": 399,
        "name": "exclude-selection",
        "prevSize": 32,
        "code": 59897
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 250
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ExcludeSelectionIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ExcludeSelectionIcon = ({ size = 16, ...props }: ExcludeSelectionIconProps) => (
  <IcoMoon iconSet={iconSet} icon="exclude-selection" size={size} {...props} />
);

ExcludeSelectionIcon.displayName = 'ExcludeSelectionIcon';

export default ExcludeSelectionIcon;
