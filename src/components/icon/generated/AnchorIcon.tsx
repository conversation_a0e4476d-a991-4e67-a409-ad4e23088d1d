// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512.145 274.963c52.386 0 94.848-42.45 94.848-94.815s-42.462-94.815-94.848-94.815c-52.382 0-94.846 42.45-94.846 94.815s42.464 94.815 94.846 94.815zM512.145 274.963v142.223M512.145 938.667c43.597 0 86.763-8.585 127.040-25.259 40.277-16.678 76.873-41.122 107.699-71.94 30.822-30.814 55.275-67.396 71.957-107.657 16.683-40.265 25.271-83.418 25.271-126.997 0-12.668 0-40.444 0-50.167M512.145 938.667c-43.593 0-86.76-8.585-127.036-25.259-40.276-16.678-76.872-41.122-107.697-71.94-30.826-30.814-55.279-67.396-71.961-107.661-16.683-40.26-25.269-83.413-25.269-126.993 0.015-9.813 1.382-33.037 6.735-47.407M512.145 938.667v-521.481M85.333 650.526l95.101-88.576c1.829-1.702 4.157-2.547 6.481-2.543M281.981 650.526l-88.374-88.341c-1.844-1.843-4.266-2.773-6.691-2.778M742.020 647.753l95.1-88.576c1.963-1.83 4.501-2.671 6.993-2.53M938.667 647.753l-88.375-88.346c-1.715-1.715-3.925-2.637-6.178-2.761M512.145 417.185h-118.56M512.145 417.185h118.558"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "anchor"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1396,
        "id": 624,
        "name": "anchor",
        "prevSize": 32,
        "code": 59672
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 25
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface AnchorIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const AnchorIcon = ({ size = 16, ...props }: AnchorIconProps) => (
  <IcoMoon iconSet={iconSet} icon="anchor" size={size} {...props} />
);

AnchorIcon.displayName = 'AnchorIcon';

export default AnchorIcon;
