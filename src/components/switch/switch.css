:where(.herond-themes) {
  --switch-disabled-blend-mode: multiply;
  --switch-high-contrast-checked-color-overlay: var(--brand-green);
  --switch-high-contrast-checked-active-before-filter: contrast(0.82) saturate(1.2) brightness(1.16);
}

:is(.dark, .dark-theme),
:is(.dark, .dark-theme) :where(.herond-themes:not(.light, .light-theme)) {
  --switch-disabled-blend-mode: screen;
  --switch-high-contrast-checked-color-overlay: transparent;
  --switch-high-contrast-checked-active-before-filter: brightness(1.08);
}

.rt-SwitchRoot {
  position: relative;
  display: inline-flex;
  align-items: center;
  vertical-align: top;
  flex-shrink: 0;

  /* Unless in a skeleton, align with text line height when possible and fall back to own height */
  height: var(--skeleton-height, var(--line-height, var(--switch-height)));
  --skeleton-height-override: var(--switch-height);

  /* Set root radius when in a skeleton */
  border-radius: var(--skeleton-radius);
  --skeleton-radius-override: var(--switch-border-radius);

  --switch-thumb-inset: 2px;
  --switch-thumb-size: calc(var(--switch-height) - var(--switch-thumb-inset) * 2);
  --switch-thumb-translate-x: calc(var(--switch-width) - var(--switch-height));

  &::before {
    content: '';
    display: block;
    width: var(--switch-width);
    height: var(--switch-height);
    border-radius: var(--switch-border-radius);
    transition: background-position, background-color, box-shadow, filter;
    transition-timing-function: linear, ease-in-out, ease-in-out, ease-in-out;
    background-repeat: no-repeat;

    /*
     * Length of the gradient is: switch width for the checked part, plus switch
     * height for the thumb part, plus another switch width for the unchecked part
     */
    background-size: calc(var(--switch-width) * 2 + var(--switch-height)) 100%;
  }

  &:where([data-state='unchecked'])::before {
    transition-duration: 120ms, 140ms, 140ms, 140ms;
    background-position-x: 100%;
  }

  &:where([data-state='unchecked']) {
    &:where(:focus, :focus-visible) {
      outline: var(--border-width-thicker) solid var(--fills-secondary);
      border-radius: var(--switch-border-radius);
    }
  }

  &:where([data-state='checked']) {
    &:where(:focus, :focus-visible) {
      outline: var(--border-width-thicker) solid var(--brand-accent-40);
      border-radius: var(--switch-border-radius);
    }
  }

  &:where([data-state='checked'])::before {
    transition-duration: 160ms, 140ms, 140ms, 140ms;
    background-position: 0%;
  }

  &:where(:active)::before {
    transition-duration: 30ms;
  }

  /* Cursors */
  &::before {
    cursor: var(--cursor-switch);
  }

  &:where([data-disabled])::before {
    cursor: var(--cursor-disabled);
  }
}

.rt-SwitchThumb {
  background-color: white;
  position: absolute;
  left: var(--switch-thumb-inset);
  width: var(--switch-thumb-size);
  height: var(--switch-thumb-size);
  border-radius: calc(var(--switch-border-radius) - var(--switch-thumb-inset));
  transition:
    transform 140ms cubic-bezier(0.45, 0.05, 0.55, 0.95),
    box-shadow 140ms ease-in-out;

  &:where([data-state='checked']) {
    transform: translateX(var(--switch-thumb-translate-x));
  }

  &:where([data-disabled]) {
    background-color: var(--fills-secondary);
  }
}

/***************************************************************************************************
 *                                                                                                 *
 * SIZES                                                                                           *
 *                                                                                                 *
 ***************************************************************************************************/

@breakpoints {
  .rt-SwitchRoot {
    &:where(.rt-r-size-1) {
      --switch-height: var(--spatial-6);
      --switch-width: var(--spatial-8);
      --switch-border-radius: max(var(--radius-1), var(--radius-thumb));
    }

    &:where(.rt-r-size-2) {
      --switch-height: calc(var(--spatial-7));
      --switch-width: var(--spatial-9);
      --switch-border-radius: max(var(--radius-2), var(--radius-thumb));
    }

    &:where(.rt-r-size-3) {
      --switch-height: calc(var(--spatial-7) + var(--spatial-2));
      --switch-width: calc(var(--spatial-9) + var(--spatial-1) * 3);
      --switch-border-radius: max(var(--radius-2), var(--radius-thumb));
    }
  }
}

/***************************************************************************************************
 *                                                                                                 *
 * VARIANTS                                                                                        *
 *                                                                                                 *
 ***************************************************************************************************/

/* surface */

:where(.herond-themes) {
  --switch-surface-checked-active-filter: brightness(0.92) saturate(1.1);
}

:is(.dark, .dark-theme),
:is(.dark, .dark-theme) :where(.herond-themes:not(.light, .light-theme)) {
  --switch-surface-checked-active-filter: brightness(1.08);
}

.rt-SwitchRoot:where(.rt-variant-surface) {
  &::before {
    background-color: var(--fills-tertiary);
    background-image: linear-gradient(to right, var(--brand-accent) 40%, transparent 60%);
  }

  &:where(:active)::before {
    background-color: var(--fills-tertiary);
  }

  &:where([data-state='checked']:active)::before {
    filter: var(--switch-surface-checked-active-filter);
  }

  &:where(.rt-high-contrast) {
    &::before {
      /* prettier-ignore */
      background-image:
                linear-gradient(to right, var(--switch-high-contrast-checked-color-overlay) 40%, transparent 60%),
                linear-gradient(to right, var(--brand-accent) 40%, transparent 60%);
    }

    &:where([data-state='checked']:active)::before {
      filter: var(--switch-high-contrast-checked-active-before-filter);
    }
  }

  &:where([data-disabled]) {
    mix-blend-mode: var(--switch-disabled-blend-mode);

    &::before {
      filter: none;
      background-image: none;
      background-color: var(--fills-tertiary);
    }
  }
}

/* classic */

:where(.herond-themes) {
  --switch-surface-checked-active-filter: brightness(0.92) saturate(1.1);
}

:is(.dark, .dark-theme),
:is(.dark, .dark-theme) :where(.herond-themes:not(.light, .light-theme)) {
  --switch-surface-checked-active-filter: brightness(1.08);
}

.rt-SwitchRoot:where(.rt-variant-classic) {
  &::before {
    background-image: linear-gradient(to right, var(--brand-accent) 40%, transparent 60%);
    background-color: var(--fills-tertiary);
    box-shadow: var(--shadow-1);
  }

  &:where([data-state='checked']:active)::before {
    filter: var(--switch-surface-checked-active-filter);
  }

  &:where(.rt-high-contrast) {
    &::before {
      background-image:
        linear-gradient(to right, var(--switch-high-contrast-checked-color-overlay) 40%, transparent 60%),
        linear-gradient(to right, var(--brand-accent) 40%, transparent 60%);
    }

    &:where([data-state='checked']:active)::before {
      filter: var(--switch-high-contrast-checked-active-before-filter);
    }
  }

  &:where([data-disabled]) {
    mix-blend-mode: var(--switch-disabled-blend-mode);

    &::before {
      filter: none;
      background-image: none;
      background-color: var(--fills-tertiary);
      box-shadow: var(--shadow-1);
      opacity: 0.5;
    }
  }
}

/* soft */

.rt-SwitchRoot:where(.rt-variant-soft) {
  /* prettier-ignore */
  &::before {
        background-image:
            linear-gradient(to right, var(--brand-accent-40) 40%, transparent 60%),
            linear-gradient(to right, var(--brand-accent-40) 40%, transparent 60%),
            linear-gradient(to right, var(--brand-accent-40) 40%, #fffb 60%),
            linear-gradient(to right, var(--fills-tertiary) 40%, var(--fills-tertiary) 60%);
    }

  &:where([data-state='unchecked'])::before {
    background-color: var(--fills-tertiary);
  }

  &:where(:active)::before {
    background-color: var(--fills-tertiary);
  }

  &:where(.rt-high-contrast) {
    &::before {
      /* prettier-ignore */
      background-image:
                linear-gradient(to right, var(--switch-high-contrast-checked-color-overlay) 40%, transparent 60%),
                linear-gradient(to right, var(--brand-accent-40) 40%, transparent 60%),
                linear-gradient(to right, var(--brand-accent-40) 40%, transparent 60%),
                linear-gradient(to right, var(--brand-accent-40) 40%, var(--white-a1) 60%),
                linear-gradient(to right, var(--brand-accent-button-bezeled-fill) 40%, var(--fills-tertiary) 60%);
    }

    &:where([data-state='checked']:active)::before {
      filter: var(--switch-high-contrast-checked-active-before-filter);
    }
  }

  &:where([data-disabled]) {
    mix-blend-mode: var(--switch-disabled-blend-mode);

    &::before {
      filter: none;
      background-image: none;
      background-color: var(--fills-tertiary);
    }
  }
}

.rt-switch-label:where(.rt-r-size-1) {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--font-size-4);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-4);
}

.rt-switch-caption:where(.rt-r-size-1) {
  font-size: var(--font-size-3, 12px);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-3);
}

.rt-switch-label:where(.rt-r-size-2) {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--font-size-5);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-5);
}

.rt-switch-caption:where(.rt-r-size-2) {
  font-size: var(--font-size-4);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-4);
}

.rt-switch-label:where(.rt-r-size-3) {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--font-size-5);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-5);
}

.rt-switch-caption:where(.rt-r-size-3) {
  font-size: var(--font-size-4);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-4);
}
