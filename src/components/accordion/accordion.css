.rt-AccordionRoot {
  min-width: 300px;
  background-color: var(--background-primary-base);
  box-shadow: 0 2px 10px var(--separators-card-photo-stroke);
}

.rt-AccordionItem {
  overflow: hidden;
  margin-top: var(--spatial-1);
}
.rt-AccordionItem:first-child {
  margin-top: 0;
  border-top-left-radius: var(--radius-2);
  border-top-right-radius: var(--radius-2);
}
.rt-AccordionItem:last-child {
  border-bottom-left-radius: var(--radius-2);
  border-bottom-right-radius: var(--radius-2);
}
.rt-AccordionItem:focus-within {
  position: relative;
  z-index: 1;
  box-shadow: 0 0 0 2px var(--brand-accent-40);
}
.rt-AccordionItem:has(.rt-AccordionTrigger[data-state='closed']) {
  box-shadow: 0 1px 0 var(--separators-card-photo-stroke);
}

.rt-AccordionHeader {
  display: flex;
}

.rt-AccordionTrigger {
  border: inherit;
  font-family: inherit;
  padding: var(--spatial-0) var(--spatial-5);
  height: var(--spatial-10);
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: var(--font-size-4);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-4);
  color: var(--labels-primary);
  box-shadow: 0 1px 0 var(--separators-card-photo-stroke);
  background-color: var(--background-primary-base);
}
.rt-AccordionTrigger:hover {
  background-color: var(--brand-accent-40);

  & > span {
    color: var(--brand-accent);
  }
}
.rt-AccordionTrigger[data-state='open'] > .rt-AccordionIconTrigger {
  transform: rotate(180deg);
}
.rt-AccordionIconTrigger {
  color: var(--brand-accent);
  transition: transform 300ms cubic-bezier(0.87, 0, 0.13, 1);
}

.rt-AccordionContent {
  overflow: hidden;
  font-size: var(--font-size-4);
  line-height: var(--line-height-4);
  color: var(--labels-primary);
  background-color: var(--background-primary-base);
}

.rt-AccordionContent[data-state='open'] {
  animation: rt-accordion-slide-down 300ms cubic-bezier(0.87, 0, 0.13, 1);
}
.rt-AccordionContent[data-state='closed'] {
  animation: rt-accordion-slide-up 300ms cubic-bezier(0.87, 0, 0.13, 1);
}

/* Keyframes */
@keyframes rt-accordion-slide-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes rt-accordion-slide-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}
