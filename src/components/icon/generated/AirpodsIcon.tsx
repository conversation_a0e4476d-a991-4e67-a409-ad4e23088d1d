// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M820.147 251.259h-0.474M203.852 369.778h-0.474M227.555 203.852c104.73 0 213.332 74.287 213.332 165.926v497.776c0 39.275-31.836 71.113-71.11 71.113s-71.111-31.838-71.111-71.113v-336.994c0-3.2-3.11-5.483-6.186-4.595-22.109 6.379-44.361 9.741-64.925 9.741-42.182 0-81.146-20.339-112.643-44.949-19.927-15.573-29.58-40.388-29.58-65.678v-110.601c0-25.289 9.652-50.107 29.58-65.678 31.497-24.611 70.461-44.948 112.643-44.948zM796.446 85.333c-104.73 0-213.333 74.287-213.333 165.926v497.78c0 39.27 31.838 71.108 71.108 71.108 39.275 0 71.113-31.838 71.113-71.108v-336.995c0-3.202 3.11-5.484 6.187-4.596 22.11 6.38 44.361 9.738 64.926 9.738 42.18 0 81.143-20.337 112.64-44.948 19.93-15.571 29.581-40.389 29.581-65.678v-110.601c0-25.289-9.651-50.107-29.581-65.678-31.497-24.611-70.46-44.948-112.64-44.948z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "airpods"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1385,
        "id": 635,
        "name": "airpods",
        "prevSize": 32,
        "code": 59661
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 14
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface AirpodsIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const AirpodsIcon = ({ size = 16, ...props }: AirpodsIconProps) => (
  <IcoMoon iconSet={iconSet} icon="airpods" size={size} {...props} />
);

AirpodsIcon.displayName = 'AirpodsIcon';

export default AirpodsIcon;
