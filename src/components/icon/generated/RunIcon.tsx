// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M250.874 499.072l19.859-48.021c20.301-49.085 69.456-81.273 124.119-81.273h84.179M853.333 421.495l-68.39 26.458c-40.87 15.812-87.172 11.149-123.797-12.467l-48.435-31.23M479.031 369.778h39.731c26.394 0 52.194 7.555 74.15 21.715l19.797 12.763M479.031 369.778l-75.416 182.346c-18.75 45.338-4.803 97.203 34.409 127.957M585.975 938.667v-89.075c0-41.498-20.595-80.474-55.369-104.785l-92.582-64.725M612.71 404.256l-93.001 198.249M438.025 680.081l-67.257 97.579c-24.793 35.968-66.532 57.574-111.229 57.574h-88.873M678.652 175.838c0 49.985-41.899 90.505-93.577 90.505-51.682 0-93.577-40.52-93.577-90.505s41.894-90.505 93.577-90.505c51.678 0 93.577 40.521 93.577 90.505z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "run"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1857,
        "id": 163,
        "name": "run",
        "prevSize": 32,
        "code": 60133
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 486
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface RunIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const RunIcon = ({ size = 16, ...props }: RunIconProps) => (
  <IcoMoon iconSet={iconSet} icon="run" size={size} {...props} />
);

RunIcon.displayName = 'RunIcon';

export default RunIcon;
