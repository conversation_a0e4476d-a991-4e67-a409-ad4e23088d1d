// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M369.778 160l-201.193-45.268c-28.458-6.403-42.687-9.604-53.858-5.948-9.795 3.206-18.097 9.848-23.375 18.7-6.019 10.096-6.019 24.68-6.019 53.85v628.053c0 19.49 0 29.235 3.449 37.286 3.042 7.1 7.955 13.239 14.213 17.766 7.095 5.133 16.603 7.27 35.619 11.55l231.163 52.011M369.778 160v768M369.778 160l284.443-64M369.778 928l284.443-64M654.221 96v768M654.221 96l231.164 52.012c19.017 4.279 28.523 6.418 35.618 11.55 6.259 4.527 11.174 10.667 14.217 17.766 3.447 8.049 3.447 17.795 3.447 37.286v628.053c0 29.171 0 43.755-6.020 53.85-5.278 8.853-13.581 15.497-23.373 18.701-11.17 3.657-25.399 0.452-53.858-5.948l-201.195-45.269"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "map-alt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1754,
        "id": 266,
        "name": "map-alt",
        "prevSize": 32,
        "code": 60030
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 383
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MapAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MapAltIcon = ({ size = 16, ...props }: MapAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="map-alt" size={size} {...props} />
);

MapAltIcon.displayName = 'MapAltIcon';

export default MapAltIcon;
