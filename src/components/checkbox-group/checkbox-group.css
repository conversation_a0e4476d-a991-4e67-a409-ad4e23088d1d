.rt-CheckboxGroupRoot {
  display: flex;
  flex-direction: column;
  gap: var(--spatial-2);
}

.rt-CheckboxGroupItem {
  display: flex;
  align-items: center;
  gap: 0.5em;

  /* Make sure whitespace to the right of the text is not clickable */
  width: fit-content;
}

.rt-CheckboxGroupItemInner {
  /* Make layouts with text truncation possible */
  min-width: 0;
  display: flex;
  align-items: center;
}

.rt-checkbox-group-label:where(.rt-r-size-1) {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--font-size-4);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-4);
}

.rt-checkbox-group-label:where(.rt-r-size-2) {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--font-size-5);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-5);
}

.rt-checkbox-group-label:where(.rt-r-size-3) {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--font-size-5);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-5);
}

.rt-checkbox-group-label:where(.rt-r-size-4) {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--font-size-5);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-5);
}

.rt-checkbox-group-label:where(.rt-r-size-5) {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--font-size-5);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-5);
}
