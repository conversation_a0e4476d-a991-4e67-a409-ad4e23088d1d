// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M682.667 362.667l121.967 121.967c1.664 1.664 3.849 2.5 6.033 2.5M938.667 362.667l-121.967 121.967c-1.664 1.664-3.849 2.5-6.033 2.5M810.667 487.134v-359.134M85.333 384v341.333c0 70.694 57.308 128 128 128h597.333c70.694 0 128-57.306 128-128v-170.667M85.333 384v-85.333c0-70.692 57.308-128 128-128h448M85.333 384h469.333M234.667 704h106.667M469.333 704h213.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "card-receive"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1525,
        "id": 495,
        "name": "card-receive",
        "prevSize": 32,
        "code": 59801
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 154
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CardReceiveIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CardReceiveIcon = ({ size = 16, ...props }: CardReceiveIconProps) => (
  <IcoMoon iconSet={iconSet} icon="card-receive" size={size} {...props} />
);

CardReceiveIcon.displayName = 'CardReceiveIcon';

export default CardReceiveIcon;
