// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M228.693 938.667c-50.181 0-75.271 0-94.438-9.766-16.859-8.589-30.566-22.298-39.156-39.155-9.766-19.166-9.766-44.258-9.766-94.438M682.667 795.307c0 50.18 0 75.273-9.766 94.438-8.589 16.858-22.298 30.566-39.155 39.155-19.166 9.766-44.258 9.766-94.438 9.766M539.307 341.333c50.18 0 75.273 0 94.438 9.766 16.858 8.59 30.566 22.297 39.155 39.156 9.766 19.166 9.766 44.257 9.766 94.438M228.693 341.333c-50.181 0-75.271 0-94.438 9.766-16.859 8.59-30.566 22.297-39.156 39.156-9.766 19.166-9.766 44.257-9.766 94.438M324.267 341.333h17.067M443.733 341.333h-102.4M85.333 699.733v-119.467M682.667 580.267v102.4M682.667 699.733v-17.067M443.733 938.667h-119.467M341.333 341.333v-112.64c0-50.181 0-75.271 9.766-94.438 8.59-16.859 22.297-30.566 39.156-39.156 19.166-9.766 44.257-9.766 94.438-9.766h310.613c50.18 0 75.273 0 94.438 9.766 16.858 8.59 30.566 22.297 39.155 39.156 9.766 19.166 9.766 44.257 9.766 94.438v310.613c0 50.18 0 75.273-9.766 94.438-8.589 16.858-22.298 30.566-39.155 39.155-18.453 9.404-42.394 9.754-88.926 9.766h-118.153"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "selection-foreground"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1870,
        "id": 150,
        "name": "selection-foreground",
        "prevSize": 32,
        "code": 60146
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 499
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SelectionForegroundIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SelectionForegroundIcon = ({ size = 16, ...props }: SelectionForegroundIconProps) => (
  <IcoMoon iconSet={iconSet} icon="selection-foreground" size={size} {...props} />
);

SelectionForegroundIcon.displayName = 'SelectionForegroundIcon';

export default SelectionForegroundIcon;
