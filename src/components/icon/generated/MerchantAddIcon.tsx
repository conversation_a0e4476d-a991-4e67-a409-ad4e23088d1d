// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M682.667 768h85.333M768 768h85.333M768 768v-85.333M768 768v85.333M315.376 432.401c-26.090 20.019-60.543 32.192-98.312 32.192-35.039 0-67.223-10.475-92.524-27.972-64.463-44.582-37.491-134.356-3.824-203.624l38.615-79.448c20.233-41.628 63.602-68.215 111.272-68.215h93.929M315.376 432.401c26.090 20.019 60.542 32.192 98.312 32.192 37.768 0 72.221-12.173 98.312-32.192M315.376 432.401l49.156-347.068M708.625 432.401c26.091 20.019 60.54 32.192 98.313 32.192 35.038 0 67.221-10.475 92.523-27.972 64.461-44.582 37.491-134.356 3.823-203.624l-38.613-79.448c-20.233-41.627-63.603-68.215-111.27-68.215h-93.931M708.625 432.401c-26.091 20.019-60.544 32.192-98.313 32.192s-72.222-12.173-98.313-32.192M708.625 432.401l-49.156-347.068M512 432.401v-347.068M364.532 85.333h147.468M512 85.333h147.469M167.908 464.593v355.554c0 65.455 55.020 118.519 122.89 118.519h242.535M856.094 464.593v46.204M389.11 938.667v-189.628c0-65.459 55.020-118.519 122.89-118.519M938.667 768c0 94.255-76.412 170.667-170.667 170.667s-170.667-76.412-170.667-170.667c0-94.255 76.412-170.667 170.667-170.667s170.667 76.412 170.667 170.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "merchant-add"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1767,
        "id": 253,
        "name": "merchant-add",
        "prevSize": 32,
        "code": 60043
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 396
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MerchantAddIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MerchantAddIcon = ({ size = 16, ...props }: MerchantAddIconProps) => (
  <IcoMoon iconSet={iconSet} icon="merchant-add" size={size} {...props} />
);

MerchantAddIcon.displayName = 'MerchantAddIcon';

export default MerchantAddIcon;
