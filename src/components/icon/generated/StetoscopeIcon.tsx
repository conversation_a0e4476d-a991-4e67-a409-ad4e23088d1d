// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M463.236 115.893c53.862 0 97.527 42.099 97.527 94.031v258.585c0 64.917-54.579 117.538-121.907 117.538h-73.142M463.236 115.893v-30.559M463.236 115.893v30.561M268.188 115.893c-53.86 0-97.521 42.098-97.521 94.029v258.587c0 64.917 54.579 117.538 121.905 117.538h73.143M268.188 115.893v-30.56M268.188 115.893v30.56M780.19 397.987c40.397 0 73.143-31.574 73.143-70.523s-32.747-70.523-73.143-70.523c-40.397 0-73.143 31.574-73.143 70.523s32.747 70.523 73.143 70.523zM780.19 397.987v340.863c0 110.357-92.783 199.817-207.236 199.817-114.458 0-207.239-89.459-207.239-199.817v-152.802"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "stetoscope"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1915,
        "id": 105,
        "name": "stetoscope",
        "prevSize": 32,
        "code": 60191
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 544
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface StetoscopeIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const StetoscopeIcon = ({ size = 16, ...props }: StetoscopeIconProps) => (
  <IcoMoon iconSet={iconSet} icon="stetoscope" size={size} {...props} />
);

StetoscopeIcon.displayName = 'StetoscopeIcon';

export default StetoscopeIcon;
