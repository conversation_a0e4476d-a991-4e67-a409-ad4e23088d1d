// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M492.092 938.667l-67.464-0.691c-51.438-2.022-121.418-6.293-172.065-11.648-65.84-6.963-111.447-67.563-101.988-132.527l3.136-21.542c16.393-112.597 104.325-204.608 218.778-212.873 65.394-4.723 126.22-4.732 191.487-0.026 8.055 0.58 15.983 1.574 23.757 2.965M806.528 847.556c20.71-20.493 33.523-48.823 33.523-80.124 0-62.524-51.132-113.212-114.202-113.212-63.074 0-114.206 50.688-114.206 113.212 0 62.528 51.132 113.212 114.206 113.212 31.497 0 60.019-12.642 80.678-33.088zM806.528 847.556l68.139 67.55M635.558 251.259c0 91.638-74.94 165.926-167.377 165.926-92.441 0-167.379-74.288-167.379-165.926s74.938-165.926 167.379-165.926c92.437 0 167.377 74.287 167.377 165.926z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "user-search"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1981,
        "id": 39,
        "name": "user-search",
        "prevSize": 32,
        "code": 60257
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 610
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface UserSearchIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const UserSearchIcon = ({ size = 16, ...props }: UserSearchIconProps) => (
  <IcoMoon iconSet={iconSet} icon="user-search" size={size} {...props} />
);

UserSearchIcon.displayName = 'UserSearchIcon';

export default UserSearchIcon;
