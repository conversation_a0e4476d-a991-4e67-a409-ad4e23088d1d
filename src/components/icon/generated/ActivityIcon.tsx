// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 513.937h159.414c3.923 0 7.441-2.547 8.849-6.404l105.059-287.795c3.357-9.196 15.985-8.261 18.088 1.34l127.439 581.845c2.172 9.911 15.373 10.462 18.283 0.764l126.532-422.171c2.257-7.532 11.529-9.455 16.329-3.387l104.572 132.212c1.801 2.278 4.467 3.597 7.283 3.597h161.485"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "activity"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1375,
        "id": 645,
        "name": "activity",
        "prevSize": 32,
        "code": 59651
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 4
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ActivityIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ActivityIcon = ({ size = 16, ...props }: ActivityIconProps) => (
  <IcoMoon iconSet={iconSet} icon="activity" size={size} {...props} />
);

ActivityIcon.displayName = 'ActivityIcon';

export default ActivityIcon;
