// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M256 426.667c94.257 0 170.667-76.41 170.667-170.667s-76.41-170.667-170.667-170.667c-94.257 0-170.667 76.41-170.667 170.667s76.41 170.667 170.667 170.667z",
          "M256 938.667c94.257 0 170.667-76.412 170.667-170.667s-76.41-170.667-170.667-170.667c-94.257 0-170.667 76.412-170.667 170.667s76.41 170.667 170.667 170.667z",
          "M768 426.667c94.255 0 170.667-76.41 170.667-170.667s-76.412-170.667-170.667-170.667c-94.255 0-170.667 76.41-170.667 170.667s76.412 170.667 170.667 170.667z",
          "M768 938.667c94.255 0 170.667-76.412 170.667-170.667s-76.412-170.667-170.667-170.667c-94.255 0-170.667 76.412-170.667 170.667s76.412 170.667 170.667 170.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "categories"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1532,
        "id": 488,
        "name": "categories",
        "prevSize": 32,
        "code": 59808
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 161
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CategoriesIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CategoriesIcon = ({ size = 16, ...props }: CategoriesIconProps) => (
  <IcoMoon iconSet={iconSet} icon="categories" size={size} {...props} />
);

CategoriesIcon.displayName = 'CategoriesIcon';

export default CategoriesIcon;
