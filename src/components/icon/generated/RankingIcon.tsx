// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M384 583.113h-149.333c-58.91 0-106.667 53.060-106.667 118.515v118.519c0 65.455 47.756 118.519 106.667 118.519h149.333M384 583.113v-23.706c0-65.455 47.757-118.519 106.667-118.519h42.667c58.91 0 106.667 53.065 106.667 118.519v94.814M384 583.113v355.554M640 938.667h-256M640 938.667v-284.446M640 938.667h149.333c58.91 0 106.667-53.065 106.667-118.519v-47.407c0-65.455-47.757-118.519-106.667-118.519h-149.333M463.821 157.62l-66.048 12.797c-5.507 1.067-7.683 8.072-3.821 12.298l46.082 50.43c2.94 3.215 4.326 7.642 3.772 12.044l-8.636 68.863c-0.721 5.765 4.958 10.082 10.005 7.603l60.809-29.864c3.81-1.872 8.222-1.872 12.032 0l60.809 29.864c5.047 2.479 10.726-1.838 10.005-7.603l-8.636-68.863c-0.555-4.402 0.832-8.829 3.772-12.044l46.080-50.43c3.866-4.226 1.69-11.231-3.819-12.298l-66.048-12.797c-4.16-0.806-7.753-3.514-9.792-7.378l-32.201-61.095c-2.679-5.084-9.694-5.084-12.373 0l-32.201 61.095c-2.039 3.864-5.632 6.572-9.792 7.378z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "ranking"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1846,
        "id": 174,
        "name": "ranking",
        "prevSize": 32,
        "code": 60122
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 475
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface RankingIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const RankingIcon = ({ size = 16, ...props }: RankingIconProps) => (
  <IcoMoon iconSet={iconSet} icon="ranking" size={size} {...props} />
);

RankingIcon.displayName = 'RankingIcon';

export default RankingIcon;
