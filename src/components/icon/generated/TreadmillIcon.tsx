// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M772.74 623.484l-612.672 91.477c-42.883 6.404-74.735 44.787-74.735 90.061 0 50.249 38.972 90.978 87.046 90.978h647.768c65.455 0 118.519-55.458 118.519-123.87v-30.596c0-75.383-63.881-133.286-135.279-122.624l-30.647 4.574zM772.74 623.484l-165.926-445.935M606.814 177.548l71.113-49.548M606.814 177.548l-71.108 49.548M701.628 763.708h101.628"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "treadmill"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1962,
        "id": 58,
        "name": "treadmill",
        "prevSize": 32,
        "code": 60238
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 591
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface TreadmillIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const TreadmillIcon = ({ size = 16, ...props }: TreadmillIconProps) => (
  <IcoMoon iconSet={iconSet} icon="treadmill" size={size} {...props} />
);

TreadmillIcon.displayName = 'TreadmillIcon';

export default TreadmillIcon;
