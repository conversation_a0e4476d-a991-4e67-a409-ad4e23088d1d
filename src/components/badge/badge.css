.rt-BadgeWrapper {
  position: relative;
  display: inline-block;
  transition: all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46);
}

.rt-Badge {
  position: relative;
  display: inline-flex;
  align-items: center;
  border-radius: calc(var(--badge-indicator-size) / 2);
  height: var(--badge-indicator-size);
}

.rt-BadgeIndicator {
  position: absolute;
  transform: translate(50%, -50%);
  transform-origin: 100% 0%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  white-space: nowrap;
  border-radius: calc(var(--badge-indicator-size) / 2);
  background-color: var(--brand-accent);
  color: var(--grays-white);
  box-shadow: 0 0 0 1px var(--grays-white);
  height: var(--badge-indicator-size);
  min-width: var(--badge-indicator-size);
  transition: background 0.2s;
}

.rt-BadgeDot {
  width: var(--spatial-3);
  height: var(--spatial-3);
  padding: 0;
  min-width: 0;
  min-height: 0;
  border-radius: 50%;
}

.rt-ScrollNumber {
  display: inline-flex;
  justify-content: center;
}

.rt-ScrollDigitContainer {
  overflow: hidden;
  height: var(--badge-indicator-size);
  width: auto;
  display: inline-block;
  position: relative;
}

.rt-ScrollDigitList {
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease-in-out;
}

.rt-ScrollDigitItem {
  height: var(--badge-indicator-size);
  text-align: center;
  font-variant-numeric: tabular-nums;
  display: flex;
  align-items: center;
}

/***************************************************************************************************
 *                                                                                                 *
 * SIZES                                                                                           *
 *                                                                                                 *
 ***************************************************************************************************/

@breakpoints {
  .rt-BadgeWrapper {
    &:where(.rt-r-size-1) .rt-BadgeIndicator {
      --badge-indicator-size: var(--spatial-4);

      font-size: var(--font-size-1);
      line-height: var(--line-height-1);
      letter-spacing: var(--letter-spacing-1);

      & .rt-MultipleWords {
        padding-left: var(--spatial-2);
        padding-right: var(--spatial-2);
      }
    }
    &:where(.rt-r-size-2) .rt-BadgeIndicator {
      --badge-indicator-size: var(--spatial-5);

      font-size: var(--font-size-2);
      line-height: var(--line-height-2);
      letter-spacing: var(--letter-spacing-2);

      & .rt-MultipleWords {
        padding-left: var(--spatial-3);
        padding-right: var(--spatial-3);
      }
    }
    &:where(.rt-r-size-3) .rt-BadgeIndicator {
      --badge-indicator-size: var(--spatial-6);

      font-size: var(--font-size-3);
      line-height: var(--line-height-3);
      letter-spacing: var(--letter-spacing-3);

      & .rt-MultipleWords {
        padding-left: var(--spatial-3);
        padding-right: var(--spatial-3);
      }
    }
    &:where(.rt-r-size-4) .rt-BadgeIndicator {
      --badge-indicator-size: var(--spatial-7);

      font-size: var(--font-size-4);
      line-height: var(--line-height-4);
      letter-spacing: var(--letter-spacing-4);

      & .rt-MultipleWords {
        padding-left: var(--spatial-4);
        padding-right: var(--spatial-4);
      }
    }
  }

  .rt-Badge {
    &:where(.rt-r-size-1) {
      --badge-indicator-size: var(--spatial-6);

      font-size: var(--font-size-3);
      line-height: var(--line-height-3);
      letter-spacing: var(--letter-spacing-3);
      padding: var(--spatial-1) var(--spatial-2);
      gap: var(--spatial-1);

      svg {
        min-height: var(--spatial-4);
        min-width: var(--spatial-4);
      }
    }
    &:where(.rt-r-size-2) {
      --badge-indicator-size: var(--spatial-7);

      font-size: var(--font-size-4);
      line-height: var(--line-height-4);
      letter-spacing: var(--letter-spacing-4);
      padding: var(--spatial-2) var(--spatial-3);
      gap: var(--spatial-2);

      svg {
        min-height: 14px;
        min-width: 14px;
      }
    }
    &:where(.rt-r-size-3) {
      --badge-indicator-size: var(--spatial-8);

      font-size: var(--font-size-5);
      line-height: var(--line-height-5);
      letter-spacing: var(--letter-spacing-5);
      padding: var(--spatial-3) var(--spatial-4);
      gap: var(--spatial-3);

      svg {
        min-height: var(--spatial-5);
        min-width: var(--spatial-5);
      }
    }
    &:where(.rt-r-size-4) {
      --badge-indicator-size: var(--spatial-9);

      font-size: var(--font-size-6);
      line-height: var(--line-height-6);
      letter-spacing: var(--letter-spacing-6);
      padding: var(--spatial-4) var(--spatial-5);
      gap: var(--spatial-3);

      svg {
        min-height: var(--spatial-6);
        min-width: var(--spatial-6);
      }
    }
  }
}

/***************************************************************************************************
 *                                                                                                 *
 * VARIANTS                                                                                        *
 *                                                                                                 *
 ***************************************************************************************************/

/* solid */

.rt-BadgeWrapper:where(.rt-variant-solid) .rt-BadgeIndicator,
.rt-Badge:where(.rt-variant-solid) {
  background-color: var(--brand-accent);
  color: var(--grays-white);

  &::selection {
    background-color: var(--brand-accent);
    color: var(--grays-white);
  }

  &:where(.rt-high-contrast) {
    background-color: var(--brand-accent-button-bezeled-fill-hover);
    color: var(--brand-accent-40);

    &::selection {
      background-color: var(--brand-accent-button-bezeled-fill-hover);
      color: var(--brand-accent-40);
    }
  }
}

.rt-Badge:where(.rt-variant-solid) {
  font-weight: var(--font-weight-semibold);
}

/* soft */

.rt-BadgeWrapper:where(.rt-variant-soft) .rt-BadgeIndicator,
.rt-Badge:where(.rt-variant-soft) {
  background-color: var(--brand-accent-button-bezeled-fill);
  color: var(--brand-accent);
  &:where(.rt-high-contrast) {
    color: var(--grays-gray200);
  }
}

.rt-Badge:where(.rt-variant-soft) {
  font-weight: var(--font-weight-semibold);
}

/* surface */

.rt-BadgeWrapper:where(.rt-variant-surface) .rt-BadgeIndicator,
.rt-Badge:where(.rt-variant-surface) {
  background-color: var(--brand-accent-button-bezeled-fill);
  box-shadow: inset 0 0 0 1px var(--brand-accent-40);
  color: var(--brand-accent);
  &:where(.rt-high-contrast) {
    color: var(--grays-gray200);
  }
}

.rt-Badge:where(.rt-variant-surface) {
  font-weight: var(--font-weight-semibold);
}

/* text */

.rt-Badge:where(.rt-variant-text) {
  font-weight: var(--font-weight-semibold);
}

/* text, ghost */
.rt-Badge:where(.rt-variant-text),
.rt-Badge:where(.rt-variant-ghost) {
  color: var(--brand-accent);
  padding: 0;
  margin: 0;
  &:where(.rt-high-contrast) {
    color: var(--grays-gray200);
  }
}

/* status */

.rt-status-info {
  --brand-accent: var(--labels-info);
  --brand-accent-40: var(--labels-info-40);
  --brand-accent-button-bezeled-fill: var(--labels-info-button-bezeled-fill);
  --brand-accent-button-bezeled-fill-hover: var(--labels-info-button-bezeled-fill-hover);
}

.rt-status-success {
  --brand-accent: var(--labels-success);
  --brand-accent-40: var(--labels-success-40);
  --brand-accent-button-bezeled-fill: var(--labels-success-button-bezeled-fill);
  --brand-accent-button-bezeled-fill-hover: var(--labels-success-button-bezeled-fill-hover);
}

.rt-status-warning {
  --brand-accent: var(--labels-warning);
  --brand-accent-40: var(--labels-warning-40);
  --brand-accent-button-bezeled-fill: var(--labels-warning-button-bezeled-fill);
  --brand-accent-button-bezeled-fill-hover: var(--labels-warning-button-bezeled-fill-hover);
}

.rt-status-error {
  --brand-accent: var(--labels-error);
  --brand-accent-40: var(--labels-error-40);
  --brand-accent-button-bezeled-fill: var(--labels-error-button-bezeled-fill);
  --brand-accent-button-bezeled-fill-hover: var(--labels-error-button-bezeled-fill-hover);
}
