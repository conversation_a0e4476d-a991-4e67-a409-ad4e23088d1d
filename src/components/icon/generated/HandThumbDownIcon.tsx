// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M794.679 167.697h-107.268c-12.57 0-25.058-2.186-36.975-6.473l-160.009-57.564c-11.917-4.287-24.401-6.473-36.975-6.473h-172.899c-42.767 0-82.452 25.061-101.268 66.205-59.046 129.12-83.866 226.288-93.561 348.215-5.502 69.193 47.78 126.153 112.599 126.153h165.225M363.548 637.76h65.333M363.548 637.76c-35.643 76.932-42.118 59.43-55.229 153.579-6.701 48.119 18.556 94.707 60.25 113.993l40.199 18.594c20.514 9.485 44.19-5.457 48.841-28.864 9.63-48.457 31.684-118.861 80.158-139.789 108.885-47.006 146.244-109.568 233.357-203.58M908.557 596.352l-121.66 21.726c-5.222 0.93-10.121-3.042-10.726-8.674-19.379-181.069-0.047-296.004 23.078-477.113 0.708-5.564 5.572-9.454 10.743-8.531l120.909 21.589c5.005 0.893 8.41 5.975 7.663 11.351-22.976 165.945-29.052 263.118-22.246 429.323 0.209 5.035-3.119 9.502-7.761 10.33z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "hand-thumb-down"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1698,
        "id": 322,
        "name": "hand-thumb-down",
        "prevSize": 32,
        "code": 59974
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 327
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface HandThumbDownIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const HandThumbDownIcon = ({ size = 16, ...props }: HandThumbDownIconProps) => (
  <IcoMoon iconSet={iconSet} icon="hand-thumb-down" size={size} {...props} />
);

HandThumbDownIcon.displayName = 'HandThumbDownIcon';

export default HandThumbDownIcon;
