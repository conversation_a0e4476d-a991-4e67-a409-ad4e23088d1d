// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 234.667h853.333M85.333 419.555h853.333M85.333 604.446h853.333M85.333 789.333h853.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "text-align-justify-center"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1940,
        "id": 80,
        "name": "text-align-justify-center",
        "prevSize": 32,
        "code": 60216
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 569
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface TextAlignJustifyCenterIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const TextAlignJustifyCenterIcon = ({ size = 16, ...props }: TextAlignJustifyCenterIconProps) => (
  <IcoMoon iconSet={iconSet} icon="text-align-justify-center" size={size} {...props} />
);

TextAlignJustifyCenterIcon.displayName = 'TextAlignJustifyCenterIcon';

export default TextAlignJustifyCenterIcon;
