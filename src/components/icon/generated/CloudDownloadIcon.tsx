// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M796.446 746.667c78.545 0 142.221-66.859 142.221-149.333 0-65.067-39.629-120.41-94.925-140.877 4.224-159.536-120.038-307.123-284.335-307.123-114.978 0-214.019 71.629-258.863 174.705-119.055-8.412-215.211 94.932-215.211 211.074 0 101.239 67.728 185.869 158.205 206.66M362.667 743.979l145.493 129.276c1.062 0.943 2.449 1.412 3.84 1.412M661.333 743.979l-145.493 129.276c-1.062 0.943-2.449 1.412-3.84 1.412M512 874.667v-384"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "cloud-download"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1564,
        "id": 456,
        "name": "cloud-download",
        "prevSize": 32,
        "code": 59840
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 193
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CloudDownloadIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CloudDownloadIcon = ({ size = 16, ...props }: CloudDownloadIconProps) => (
  <IcoMoon iconSet={iconSet} icon="cloud-download" size={size} {...props} />
);

CloudDownloadIcon.displayName = 'CloudDownloadIcon';

export default CloudDownloadIcon;
