@import './accent.css';
@import './blue.css';
@import './brown.css';
@import './cyan.css';
@import './gray.css';
@import './green.css';
@import './indigo.css';
@import './mint.css';
@import './orange.css';
@import './pink.css';
@import './purple.css';
@import './red.css';
@import './teal.css';
@import './yellow.css';

/* * * * * * * * * * * * * * * * * * * */
/*                                     */
/*            Transparency             */
/*                                     */
/* * * * * * * * * * * * * * * * * * * */

/* Because Chrome is buggy with box-shadow transitions from "transparent" keyword and/or RGB color into P3 colors. */
/* Note: using `:where` here to guarantee that the P3 color will take over regardless of the output rule order. */
:where(.herond-themes) {
  --color-transparent: rgb(0 0 0 / 0);
}
@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    .herond-themes {
      --color-transparent: color(display-p3 0 0 0 / 0);
    }
  }
}

/* * * * * * * * * * * * * * * * * * * */
/*                                     */
/*            Color scheme             */
/*                                     */
/* * * * * * * * * * * * * * * * * * * */

/*
 * Make sure that forced light/dark appearance also sets corresponding browser colors,
 * like input autofill color and body scrollbar
 */
.herond-themes:where(.light, .light-theme) {
  &,
  :root:where(:has(&[data-is-root-theme='true'])) {
    color-scheme: light;
  }
}
.herond-themes:where(.dark, .dark-theme) {
  &,
  :root:where(:has(&[data-is-root-theme='true'])) {
    color-scheme: dark;
  }
}

/* * * * * * * * * * * * * * * * * * * */
/*                                     */
/*   Focus, selection, and autofill    */
/*                                     */
/* * * * * * * * * * * * * * * * * * * */

.herond-themes ::selection {
  background-color: var(--brand-accent-40);
}

/* * * * * * * * * * * * * * * * * * * */
/*                                     */
/*            Accent color             */
/*                                     */
/* * * * * * * * * * * * * * * * * * * */

[data-accent-color='blue'] {
  --brand-accent: var(--brand-blue);
  --brand-accent-40: hexToRgba(var(--brand-blue) 0.4);
  --brand-accent-button-bezeled-fill: hexToRgba(var(--brand-blue) 0.1);
  --brand-accent-button-bezeled-fill-hover: hexToRgba(var(--brand-blue) 0.2);
}

[data-accent-color='brown'] {
  --brand-accent: var(--brand-brown);
  --brand-accent-40: hexToRgba(var(--brand-brown) 0.4);
  --brand-accent-button-bezeled-fill: hexToRgba(var(--brand-brown) 0.1);
  --brand-accent-button-bezeled-fill-hover: hexToRgba(var(--brand-brown) 0.2);
}

[data-accent-color='cyan'] {
  --brand-accent: var(--brand-cyan);
  --brand-accent-40: hexToRgba(var(--brand-cyan) 0.4);
  --brand-accent-button-bezeled-fill: hexToRgba(var(--brand-cyan) 0.1);
  --brand-accent-button-bezeled-fill-hover: hexToRgba(var(--brand-cyan) 0.2);
}

[data-accent-color='green'] {
  --brand-accent: var(--brand-green);
  --brand-accent-40: hexToRgba(var(--brand-green) 0.4);
  --brand-accent-button-bezeled-fill: hexToRgba(var(--brand-green) 0.1);
  --brand-accent-button-bezeled-fill-hover: hexToRgba(var(--brand-green) 0.2);
}

[data-accent-color='indigo'] {
  --brand-accent: var(--brand-indigo);
  --brand-accent-40: hexToRgba(var(--brand-indigo) 0.4);
  --brand-accent-button-bezeled-fill: hexToRgba(var(--brand-indigo) 0.1);
  --brand-accent-button-bezeled-fill-hover: hexToRgba(var(--brand-indigo) 0.2);
}

[data-accent-color='mint'] {
  --brand-accent: var(--brand-mint);
  --brand-accent-40: hexToRgba(var(--brand-mint) 0.4);
  --brand-accent-button-bezeled-fill: hexToRgba(var(--brand-mint) 0.1);
  --brand-accent-button-bezeled-fill-hover: hexToRgba(var(--brand-mint) 0.2);
}

[data-accent-color='orange'] {
  --brand-accent: var(--brand-orange);
  --brand-accent-40: hexToRgba(var(--brand-orange) 0.4);
  --brand-accent-button-bezeled-fill: hexToRgba(var(--brand-orange) 0.1);
  --brand-accent-button-bezeled-fill-hover: hexToRgba(var(--brand-orange) 0.2);
}

[data-accent-color='pink'] {
  --brand-accent: var(--brand-pink);
  --brand-accent-40: hexToRgba(var(--brand-pink) 0.4);
  --brand-accent-button-bezeled-fill: hexToRgba(var(--brand-pink) 0.1);
  --brand-accent-button-bezeled-fill-hover: hexToRgba(var(--brand-pink) 0.2);
}

[data-accent-color='purple'] {
  --brand-accent: var(--brand-purple);
  --brand-accent-40: hexToRgba(var(--brand-purple) 0.4);
  --brand-accent-button-bezeled-fill: hexToRgba(var(--brand-purple) 0.1);
  --brand-accent-button-bezeled-fill-hover: hexToRgba(var(--brand-purple) 0.2);
}

[data-accent-color='red'] {
  --brand-accent: var(--brand-red);
  --brand-accent-40: var(--brand-red-40);
  --brand-accent-button-bezeled-fill: var(--brand-red-button-bezeled-fill);
  --brand-accent-button-bezeled-fill-hover: var(--brand-red-button-bezeled-fill-hover);
}

[data-accent-color='teal'] {
  --brand-accent: var(--brand-teal);
  --brand-accent-40: hexToRgba(var(--brand-teal) 0.4);
  --brand-accent-button-bezeled-fill: hexToRgba(var(--brand-teal) 0.1);
  --brand-accent-button-bezeled-fill-hover: hexToRgba(var(--brand-teal) 0.2);
}

[data-accent-color='yellow'] {
  --brand-accent: var(--brand-yellow);
  --brand-accent-40: hexToRgba(var(--brand-yellow) 0.4);
  --brand-accent-button-bezeled-fill: hexToRgba(var(--brand-yellow) 0.1);
  --brand-accent-button-bezeled-fill-hover: hexToRgba(var(--brand-yellow) 0.2);
}

[data-accent-color='yellow-acid'] {
  --brand-accent: var(--brand-yellow-acid);
  --brand-accent-40: hexToRgba(var(--brand-yellow-acid) 0.4);
  --brand-accent-button-bezeled-fill: hexToRgba(var(--brand-yellow-acid) 0.1);
  --brand-accent-button-bezeled-fill-hover: hexToRgba(var(--brand-yellow-acid) 0.2);
}
