// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M144.695 319.702l311.408-216.934c33.37-23.246 78.426-23.246 111.795-0l311.407 216.934c38.921 27.112 61.167 71.232 59.247 117.504l-15.296 368.849c-3.072 74.082-66.227 132.612-143.095 132.612h-101.086c-13.18 0-23.868-10.308-23.868-23.023v-190.31c0-76.288-64.115-138.133-143.206-138.133s-143.208 61.845-143.208 138.133v190.31c0 12.715-10.686 23.023-23.868 23.023h-101.086c-76.865 0-140.021-58.53-143.094-132.612l-15.296-368.849c-1.919-46.272 20.327-90.391 59.247-117.504z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "home-01"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1711,
        "id": 309,
        "name": "home-01",
        "prevSize": 32,
        "code": 59987
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 340
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface Home-01IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const Home-01Icon = ({ size = 16, ...props }: Home-01IconProps) => (
  <IcoMoon iconSet={iconSet} icon="home-01" size={size} {...props} />
);

Home-01Icon.displayName = 'Home-01Icon';

export default Home-01Icon;
