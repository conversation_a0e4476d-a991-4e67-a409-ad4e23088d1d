import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import { useArgs } from 'storybook/internal/preview-api';
import { fn } from 'storybook/test';

import { AlertDialog as BaseAlertDialog, Box, Button, Checkbox, Flex, Icon } from '@components';
import { type Responsive, colorPropDef } from '@props';

interface AlertDialogProps {
  open?: boolean;
  size?: Responsive<'1' | '2' | '3' | '4'>;
  title: string;
  colorTitle?: (typeof colorPropDef.color.values)[number];
  description: string;
  triggerText?: string;
  skipText?: string;
  cancelText?: string;
  actionText?: string;
  checkboxText?: string;
  maxWidth?: Responsive<string>;
  onCancel?: () => void;
  onConfirm?: () => void;
}

const AlertDialog = ({
  open,
  size,
  title,
  colorTitle,
  description,
  triggerText,
  skipText,
  cancelText,
  actionText,
  checkboxText,
  maxWidth,
  onCancel,
  onConfirm,
}: AlertDialogProps) => {
  return (
    <BaseAlertDialog.Root open={open}>
      <BaseAlertDialog.Trigger>
        <Button>{triggerText}</Button>
      </BaseAlertDialog.Trigger>
      <BaseAlertDialog.Content size={size} maxWidth={maxWidth}>
        <Flex direction="column" align="center" justify="center" gap="5">
          <Icon icon="info-circle" size={24} />
          <Box>
            <BaseAlertDialog.Title align="center" color={colorTitle}>
              {title}
            </BaseAlertDialog.Title>
            <BaseAlertDialog.Description align="center">{description}</BaseAlertDialog.Description>
          </Box>
          <Flex justify="center" gap="4">
            <BaseAlertDialog.Action>
              <Button size="3" variant="tertiary" onClick={onConfirm}>
                {actionText}
              </Button>
            </BaseAlertDialog.Action>
            <BaseAlertDialog.Cancel>
              <Button size="3" variant="secondary" onClick={onCancel}>
                {cancelText}
              </Button>
            </BaseAlertDialog.Cancel>
          </Flex>
          <BaseAlertDialog.Cancel>
            <Button size="3" variant="ghost" onClick={onCancel}>
              {skipText}
            </Button>
          </BaseAlertDialog.Cancel>
          <Checkbox label={checkboxText} />
        </Flex>
      </BaseAlertDialog.Content>
    </BaseAlertDialog.Root>
  );
};

const meta = {
  title: 'DesignSystem/AlertDialog',
  component: AlertDialog,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    title: { control: 'text' },
    colorTitle: { control: 'select', options: colorPropDef.color.values },
    description: { control: 'text' },
    open: { control: 'boolean' },
    skipText: { control: 'text' },
    triggerText: { control: 'text' },
    cancelText: { control: 'text' },
    actionText: { control: 'text' },
    checkboxText: { control: 'text' },
    maxWidth: { control: 'text' },
    onCancel: { action: 'onCancel' },
    onConfirm: { action: 'onConfirm' },
  },
} satisfies Meta<typeof AlertDialog>;

export default meta;
type Story = StoryObj<typeof meta>;

export const _alertDialog: Story = {
  args: {
    open: false,
    title: 'Notification text',
    colorTitle: 'accent',
    description:
      'Description text about this alert is shown here, explaining to users what the options \n underneath are about and what to do.',
    skipText: 'Skip',
    triggerText: 'Open',
    cancelText: 'Continue',
    actionText: 'Read more',
    checkboxText: 'Agree',
    maxWidth: '320px',
    onCancel: fn(),
    onConfirm: fn(),
  },
  render: (args) => {
    const [, updateArgs] = useArgs();

    return (
      <AlertDialog
        {...args}
        onCancel={() => {
          args.onCancel?.();
          updateArgs({ open: false });
        }}
        onConfirm={() => {
          args.onConfirm?.();
          updateArgs({ open: false });
        }}
      />
    );
  },
};
