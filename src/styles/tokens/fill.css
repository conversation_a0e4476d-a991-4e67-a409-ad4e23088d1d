:root {
  --fills-primary: hexT<PERSON><PERSON><PERSON><PERSON>(#787880, 0.2);
  --fills-secondary: hexT<PERSON><PERSON><PERSON><PERSON>(#787880, 0.16);
  --fills-tertiary: hexTo<PERSON><PERSON><PERSON>(#767680, 0.12);
  --fills-quaternary: hexT<PERSON><PERSON><PERSON><PERSON>(#747480, 0.08);
  --fills-vibrant-primary: hexT<PERSON><PERSON><PERSON><PERSON>(#7f7f7f, 0.5);
  --fills-vibrant-secondary: hexTo<PERSON><PERSON><PERSON>(#7f7f7f, 0.4);
  --fills-vibrant-tertiary: hexToRgba(#7f7f7f, 0.2);
}

.dark,
.dark-theme {
  --fills-primary: hexTo<PERSON>g<PERSON>(#787880, 0.36);
  --fills-secondary: hexToRgba(#787880, 0.32);
  --fills-tertiary: hexTo<PERSON>gba(#767680, 0.24);
  --fills-quaternary: hexTo<PERSON><PERSON><PERSON>(#747480, 0.18);
  --fills-vibrant-primary: hex<PERSON><PERSON><PERSON><PERSON><PERSON>(#7f7f7f, 0.5);
  --fills-vibrant-secondary: hexTo<PERSON>g<PERSON>(#7f7f7f, 0.4);
  --fills-vibrant-tertiary: hexToRg<PERSON>(#7f7f7f, 0.2);
}
