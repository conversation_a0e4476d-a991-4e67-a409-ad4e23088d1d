'use client';

import clsx from 'clsx';
import { Checkbox as CheckboxPrimitive } from 'radix-ui';
import { useControllableState } from 'radix-ui/internal';
import * as React from 'react';

import { ThickCheckIcon, ThickDividerHorizontalIcon } from '@base-components/icons';
import { checkboxPropDefs } from '@components/checkbox/checkbox.props';
import { Flex } from '@components/flex';
import { Text } from '@components/text';
import { type ComponentPropsWithout, extractProps } from '@helpers';
import { type GetPropDefTypes, type MarginProps, marginPropDefs } from '@props';

type CheckboxElement = React.ComponentRef<typeof CheckboxPrimitive.Root>;
type CheckboxOwnProps = GetPropDefTypes<typeof checkboxPropDefs>;
interface CheckboxProps
  extends ComponentPropsWithout<typeof CheckboxPrimitive.Root, 'asChild' | 'color' | 'defaultValue' | 'children'>,
    MarginProps,
    CheckboxOwnProps {
  label?: React.ReactNode;
  caption?: React.ReactNode;
  labelAlign?: 'left' | 'right';
}
const Checkbox = React.forwardRef<CheckboxElement, CheckboxProps>((props, forwardedRef) => {
  const {
    label,
    labelAlign = 'right',
    caption,
    className,
    color,
    checked: checkedProp,
    defaultChecked: defaultCheckedProp,
    onCheckedChange,
    ...checkboxProps
  } = extractProps(props, checkboxPropDefs, marginPropDefs);

  const [checked, setChecked] = useControllableState({
    prop: checkedProp,
    defaultProp: defaultCheckedProp ?? false,
    onChange: onCheckedChange,
  });

  const renderCheckbox = () => (
    <CheckboxPrimitive.Root
      data-accent-color={color}
      {...checkboxProps}
      defaultChecked={defaultCheckedProp}
      checked={checked}
      onCheckedChange={setChecked}
      asChild={false}
      ref={forwardedRef}
      className={clsx('rt-reset', 'rt-BaseCheckboxRoot', 'rt-CheckboxRoot', className)}
    >
      <CheckboxPrimitive.Indicator asChild className="rt-BaseCheckboxIndicator rt-CheckboxIndicator">
        {checked === 'indeterminate' ? <ThickDividerHorizontalIcon /> : <ThickCheckIcon />}
      </CheckboxPrimitive.Indicator>
    </CheckboxPrimitive.Root>
  );

  return label ? (
    <Flex
      gap="3"
      align={caption ? 'start' : 'center'}
      justify={labelAlign === 'left' ? 'end' : 'start'}
      direction={labelAlign === 'left' ? 'row-reverse' : 'row'}
    >
      {renderCheckbox()}
      <Flex gap={'1'} direction={'column'} align={labelAlign === 'left' ? 'end' : 'start'}>
        <Text as="label" size={props.size} className="rt-checkbox-label">
          {label}
        </Text>
        {caption && (
          <Text className="rt-checkbox-caption" size={props.size} color="secondary">
            {caption}
          </Text>
        )}
      </Flex>
    </Flex>
  ) : (
    renderCheckbox()
  );
});
Checkbox.displayName = 'Checkbox';

export { Checkbox };
export type { CheckboxProps };
