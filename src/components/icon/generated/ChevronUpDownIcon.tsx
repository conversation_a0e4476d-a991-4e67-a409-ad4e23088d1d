// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M341.333 362.666l164.634-164.633c3.332-3.332 8.734-3.332 12.066 0l164.634 164.633M341.333 661.333l164.634 164.634c3.332 3.332 8.734 3.332 12.066 0l164.634-164.634"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "chevron-up-down"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1555,
        "id": 465,
        "name": "chevron-up-down",
        "prevSize": 32,
        "code": 59831
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 184
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ChevronUpDownIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ChevronUpDownIcon = ({ size = 16, ...props }: ChevronUpDownIconProps) => (
  <IcoMoon iconSet={iconSet} icon="chevron-up-down" size={size} {...props} />
);

ChevronUpDownIcon.displayName = 'ChevronUpDownIcon';

export default ChevronUpDownIcon;
