// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M251.259 938.662c-91.638 0-165.926-74.287-165.926-165.926v-568.884c0-65.456 53.062-118.518 118.519-118.518h94.815c65.456 0 118.519 53.062 118.519 118.518v232.364M251.259 938.662c91.638 0 165.926-74.287 165.926-165.926v-165.922M251.259 938.662h568.888c65.455 0 118.519-53.060 118.519-118.519v-94.814c0-65.455-53.065-118.515-118.519-118.515h-80.201M119.644 873.762c55.796 72.691 159.958 86.391 232.652 30.592M417.185 436.215l184.334-141.487c51.925-39.854 126.327-30.070 166.178 21.854l57.732 75.213c39.855 51.925 30.071 126.323-21.854 166.178l-63.629 48.841M417.185 436.215v170.598M739.947 606.814h-322.761",
          "M247.469 768h-0.427"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "swatches"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1925,
        "id": 95,
        "name": "swatches",
        "prevSize": 32,
        "code": 60201
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 554
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SwatchesIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SwatchesIcon = ({ size = 16, ...props }: SwatchesIconProps) => (
  <IcoMoon iconSet={iconSet} icon="swatches" size={size} {...props} />
);

SwatchesIcon.displayName = 'SwatchesIcon';

export default SwatchesIcon;
