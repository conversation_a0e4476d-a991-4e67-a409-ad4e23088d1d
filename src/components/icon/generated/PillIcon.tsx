// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M549.538 333.875c7.731-10.609 15.962-20.597 24.623-29.947 34.078-36.787 74.803-63.695 117.73-79.71M318.335 365.375l340.289 340.289c3.439 3.439 9.421 3.034 13.359-0.909l174.788-174.788c111.633-111.633 123.17-281.096 25.762-378.502-97.404-97.406-266.867-85.871-378.5 25.765l-174.789 174.787c-3.94 3.94-4.347 9.921-0.909 13.359zM329.747 376.787l317.463 317.463-161.707 161.711c-100.471 100.471-252.986 110.852-340.652 23.185-87.665-87.663-77.284-240.179 23.188-340.651l161.708-161.709z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "pill"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1823,
        "id": 197,
        "name": "pill",
        "prevSize": 32,
        "code": 60099
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 452
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface PillIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const PillIcon = ({ size = 16, ...props }: PillIconProps) => (
  <IcoMoon iconSet={iconSet} icon="pill" size={size} {...props} />
);

PillIcon.displayName = 'PillIcon';

export default PillIcon;
