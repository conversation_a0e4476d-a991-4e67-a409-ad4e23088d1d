import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import * as React from 'react';

import { Accordion as BaseAccordion } from '@components';
import { Box } from '@components/box';
import { Flex } from '@components/flex';
import Icon from '@components/icon';
import { Text } from '@components/text';

type Props = React.ComponentProps<typeof BaseAccordion.Root> & {
  triggerAsChild?: BaseAccordion.TriggerProps['asChild'];
  contentForceMount?: BaseAccordion.ContentProps['forceMount'];
  contentAsChild?: BaseAccordion.ContentProps['asChild'];
};

const Template = ({ triggerAsChild, contentForceMount, contentAsChild, ...props }: Props) => (
  <BaseAccordion.Root {...props}>
    <BaseAccordion.Item value="item-1">
      <BaseAccordion.Header>
        <BaseAccordion.Trigger asChild={triggerAsChild}>
          <Flex justify="between" align="center">
            <Text>Accessibility?</Text>
            <Icon icon="chevron-down" className="rt-AccordionIconTrigger" aria-hidden />
          </Flex>
        </BaseAccordion.Trigger>
      </BaseAccordion.Header>
      <BaseAccordion.Content forceMount={contentForceMount} asChild={contentAsChild}>
        <Box p="4">Yes. It follows WAI‑ARIA patterns.</Box>
      </BaseAccordion.Content>
    </BaseAccordion.Item>

    <BaseAccordion.Item value="item-2">
      <BaseAccordion.Header>
        <BaseAccordion.Trigger asChild={triggerAsChild}>
          <Flex justify="between" align="center">
            <Text>Unstyled?</Text>
            <Icon icon="chevron-down" className="rt-AccordionIconTrigger" aria-hidden />
          </Flex>
        </BaseAccordion.Trigger>
      </BaseAccordion.Header>
      <BaseAccordion.Content forceMount={contentForceMount} asChild={contentAsChild}>
        <Box p="4">Yes. Radix gives you full control over styling.</Box>
      </BaseAccordion.Content>
    </BaseAccordion.Item>

    <BaseAccordion.Item value="item-3">
      <BaseAccordion.Header>
        <BaseAccordion.Trigger asChild={triggerAsChild}>
          <Flex justify="between" align="center">
            <Text>Animated?</Text>
            <Icon icon="chevron-down" className="rt-AccordionIconTrigger" aria-hidden />
          </Flex>
        </BaseAccordion.Trigger>
      </BaseAccordion.Header>
      <BaseAccordion.Content forceMount={contentForceMount} asChild={contentAsChild}>
        <Box p="4">Yes! You can animate opening & closing.</Box>
      </BaseAccordion.Content>
    </BaseAccordion.Item>
  </BaseAccordion.Root>
);

const meta: Meta<typeof Template> = {
  title: 'DesignSystem/Accordion',
  component: Template,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    type: {
      control: false,
      description:
        'Specifies whether the accordion allows a single item or multiple items to be open at once. "single" behaves like a typical dropdown (only one item open), while "multiple" allows several items to be expanded simultaneously.',
      table: {
        type: {
          summary: '"single" | "multiple"',
          detail: '"single" for one open item at a time.\n"multiple" for multiple open items.',
        },
        disable: true,
      },
    },
    defaultValue: {
      control: 'object',
      description: 'For "single": `string`, for "multiple": `string[]` (uncontrolled mode).',
      table: {
        type: {
          summary: 'string | string[]',
          detail: 'string (for single), string[] (for multiple)',
        },
      },
    },
    value: {
      control: 'object',
      description: 'For "single": `string`, for "multiple": `string[]` (controlled mode).',
      table: {
        type: {
          summary: 'string | string[]',
          detail: 'string (for single), string[] (for multiple)',
        },
      },
    },
    collapsible: {
      control: 'boolean',
      if: { arg: 'type', eq: 'single' },
      description: 'Only applies when type is "single".',
    },
    disabled: { control: 'boolean' },
    orientation: {
      control: { type: 'select' },
      options: ['vertical', 'horizontal'],
    },
    dir: {
      control: { type: 'select' },
      options: ['ltr', 'rtl'],
    },
    triggerAsChild: {
      control: 'boolean',
      description: 'Passes `asChild` prop to Trigger.',
    },
    contentAsChild: {
      control: 'boolean',
      description: 'Whether to render Content as a child component.',
    },
    contentForceMount: {
      control: 'boolean',
      description: 'Force mount the Content when collapsed.',
    },
  },
};

export default meta;

type Story = StoryObj<typeof Template>;

export const _single: Story = {
  args: {
    type: 'single',
    defaultValue: 'item-1',
    collapsible: true,
    disabled: false,
    orientation: 'vertical',
    dir: 'ltr',
    contentForceMount: undefined,
    contentAsChild: false,
    triggerAsChild: true,
  },
  render: Template,
};

export const _multiple: Story = {
  args: {
    type: 'multiple',
    defaultValue: ['item-1'],
    disabled: false,
    orientation: 'vertical',
    dir: 'ltr',
    contentForceMount: undefined,
    contentAsChild: false,
    triggerAsChild: true,
  },
  render: Template,
};
