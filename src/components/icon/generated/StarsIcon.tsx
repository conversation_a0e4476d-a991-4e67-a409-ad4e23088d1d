// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M403.767 91.794c3.188-8.615 15.373-8.615 18.56 0l82.683 223.444c1.003 2.708 3.136 4.844 5.845 5.846l223.445 82.682c8.614 3.188 8.614 15.373 0 18.56l-223.445 82.683c-2.709 1.003-4.843 3.136-5.845 5.845l-82.683 223.445c-3.188 8.614-15.372 8.614-18.56 0l-82.682-223.445c-1.002-2.709-3.138-4.843-5.846-5.845l-223.445-82.683c-8.615-3.188-8.615-15.372 0-18.56l223.444-82.682c2.708-1.002 4.844-3.138 5.846-5.846l82.682-223.445z",
          "M750.101 586.556c3.187-8.614 15.373-8.614 18.56 0l42.594 115.106c0.998 2.709 3.136 4.843 5.845 5.845l115.106 42.594c8.614 3.187 8.614 15.373 0 18.56l-115.106 42.594c-2.709 0.998-4.847 3.136-5.845 5.845l-42.594 115.106c-3.187 8.614-15.373 8.614-18.56 0l-42.594-115.106c-1.003-2.709-3.136-4.847-5.845-5.845l-115.106-42.594c-8.614-3.187-8.614-15.373 0-18.56l115.106-42.594c2.709-1.003 4.843-3.136 5.845-5.845l42.594-115.106z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "stars"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1914,
        "id": 106,
        "name": "stars",
        "prevSize": 32,
        "code": 60190
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 543
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface StarsIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const StarsIcon = ({ size = 16, ...props }: StarsIconProps) => (
  <IcoMoon iconSet={iconSet} icon="stars" size={size} {...props} />
);

StarsIcon.displayName = 'StarsIcon';

export default StarsIcon;
