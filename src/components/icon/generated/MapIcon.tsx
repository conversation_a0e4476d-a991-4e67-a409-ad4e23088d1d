// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M639.945 600.465v0zM573.589 600.465v0zM608 437.333c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM607.573 373.333c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM99.284 868.779v0zM155.223 924.715v0zM924.715 868.779v0zM868.779 924.715v0zM868.779 99.284v0zM924.715 155.223v0zM155.223 99.284v0zM99.284 155.223v0zM748.932 410.722h-32c0 6.613-2.342 17.144-8.559 31.74-6.029 14.14-14.758 29.956-25.216 46.468-20.907 33.015-47.142 66.368-67.755 90.999l49.088 41.071c21.397-25.574 49.655-61.385 72.738-97.83 11.537-18.219 22.178-37.214 30.020-55.625 7.654-17.958 13.683-37.726 13.683-56.824h-32zM573.589 600.465l24.542-20.535c-20.612-24.631-46.844-57.984-67.755-90.999-10.458-16.512-19.187-32.329-25.212-46.468-6.221-14.596-8.563-25.127-8.563-31.74h-64c0 19.098 6.029 38.865 13.683 56.824 7.846 18.411 18.483 37.406 30.020 55.625 23.083 36.446 51.341 72.256 72.742 97.83l24.542-20.535zM464.602 410.722h32c0-55.854 47.893-103.66 110.165-103.66v-64c-94.758 0-174.165 73.668-174.165 167.66h32zM606.767 275.062v32c62.272 0 110.165 47.806 110.165 103.66h64c0-93.992-79.407-167.66-174.165-167.66v32zM639.945 600.465l-24.542-20.535c-2.65 3.174-5.807 4.326-8.636 4.326s-5.982-1.152-8.636-4.326l-49.084 41.071c30.404 36.343 85.035 36.343 115.443 0l-24.546-20.535zM608 405.333v-32h-0.427v64h0.427v-32zM290.133 85.333v32h443.733v-64h-443.733v32zM938.667 290.133h-32v443.733h64v-443.733h-32zM85.333 733.867h32v-349.867h-64v349.867h32zM85.333 384h32v-93.867h-64v93.867h32zM733.867 938.667v-32h-93.867v64h93.867v-32zM640 938.667v-32h-349.867v64h349.867v-32zM85.333 384l-22.627 22.627 554.668 554.665 45.252-45.252-554.665-554.668-22.627 22.627zM85.333 733.867h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM290.133 938.667v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM99.284 868.779l-28.512 14.528c15.34 30.106 39.817 54.579 69.923 69.922l29.055-57.024c-18.063-9.207-32.75-23.893-41.953-41.954l-28.512 14.528zM938.667 733.867h-32c0 36.373-0.026 61.726-1.638 81.463-1.583 19.366-4.531 30.494-8.823 38.921l57.024 29.056c9.655-18.957 13.683-39.441 15.586-62.763 1.877-22.95 1.852-51.362 1.852-86.677h-32zM733.867 938.667v32c35.315 0 63.727 0.026 86.677-1.852 23.322-1.903 43.806-5.931 62.763-15.586l-29.056-57.024c-8.427 4.292-19.554 7.241-38.921 8.823-19.738 1.613-45.090 1.638-81.463 1.638v32zM924.715 868.779l-28.51-14.528c-9.207 18.061-23.893 32.747-41.954 41.954l29.056 57.024c30.106-15.343 54.579-39.817 69.922-69.922l-28.514-14.528zM733.867 85.333v32c36.373 0 61.726 0.025 81.463 1.638 19.366 1.582 30.494 4.532 38.921 8.826l29.056-57.024c-18.957-9.658-39.441-13.683-62.763-15.589-22.95-1.875-51.362-1.85-86.677-1.85v32zM938.667 290.133h32c0-35.315 0.026-63.727-1.852-86.676-1.903-23.323-5.931-43.809-15.586-62.762l-57.024 29.055c4.292 8.428 7.241 19.553 8.823 38.919 1.613 19.739 1.638 45.093 1.638 81.464h32zM868.779 99.284l-14.528 28.512c18.061 9.204 32.747 23.89 41.954 41.953l57.024-29.055c-15.343-30.106-39.817-54.583-69.922-69.923l-14.528 28.512zM290.133 85.333v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM85.333 290.133h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM155.223 99.284l-14.528-28.512c-30.106 15.34-54.583 39.817-69.923 69.923l57.024 29.055c9.204-18.063 23.89-32.75 41.953-41.953l-14.528-28.512zM85.333 384l-22.627 22.627 277.333 277.332 45.255-45.252-277.333-277.335-22.627 22.627zM362.667 661.333l-22.627 22.626 277.335 277.333 45.252-45.252-277.332-277.333-22.627 22.626zM99.284 868.779l-28.512 14.528c7.67 15.053 17.624 28.698 29.424 40.499l45.255-45.257c-7.080-7.083-13.053-15.266-17.654-24.299l-28.512 14.528zM122.824 901.175l-22.627 22.63c11.799 11.797 25.446 21.751 40.499 29.423l29.055-57.024c-9.032-4.604-17.219-10.577-24.299-17.655l-22.627 22.626zM362.667 661.333l-22.627-22.626-239.843 239.842 45.255 45.257 239.843-239.846-22.627-22.626z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "map"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1755,
        "id": 265,
        "name": "map",
        "prevSize": 32,
        "code": 60031
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 384
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MapIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MapIcon = ({ size = 16, ...props }: MapIconProps) => (
  <IcoMoon iconSet={iconSet} icon="map" size={size} {...props} />
);

MapIcon.displayName = 'MapIcon';

export default MapIcon;
