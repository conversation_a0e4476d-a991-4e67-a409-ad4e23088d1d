.rt-Link {
  /* Override the cursor on the HTML element even if rendering a <button> */
  /* stylelint-disable selector-max-type */
  display: flex;
  gap: var(--spatial-2);
  align-items: center;

  &:where(:any-link, button) {
    cursor: var(--cursor-link);
  }

  &:where(.rt-r-size-1) {
    font-size: var(--font-size-2, 11px);
    font-weight: var(--font-weight-semibold, 600);
    line-height: var(--line-height-2, 14px);

    > svg {
      width: var(--icon-size-1) !important;
      height: var(--icon-size-1) !important;
    }
  }

  &:where(.rt-r-size-2) {
    font-size: var(--font-size-3, 12px);
    font-weight: var(--font-weight-semibold, 600);
    line-height: var(--line-height-3, 15px);

    > svg {
      width: var(--icon-size-2) !important;
      height: var(--icon-size-2) !important;
    }
  }

  &:where(.rt-r-size-3) {
    font-size: var(--font-size-5, 15px);
    font-weight: var(--font-weight-semibold, 600);
    line-height: var(--line-height-5, 20px);

    > svg {
      width: var(--icon-size-3) !important;
      height: var(--icon-size-3) !important;
    }
  }

  &:where(.rt-r-size-4) {
    font-size: var(--font-size-6, 17px);
    font-weight: var(--font-weight-bold, 700);
    line-height: var(--line-height-6, 22px);

    > svg {
      width: var(--icon-size-4) !important;
      height: var(--icon-size-4) !important;
    }
  }

  &:where(.rt-r-size-5) {
    font-size: var(--font-size-7, 22px);
    font-weight: var(--font-weight-bold, 700);
    line-height: var(--line-height-7, 26px);

    > svg {
      width: var(--icon-size-5) !important;
      height: var(--icon-size-5) !important;
    }
  }

  &:where(.rt-color-primary) {
    color: var(--brand-accent);

    &:hover:not([data-disabled='disabled']) {
      color: var(--brand-accent-hover);
    }
  }

  &:where(.rt-color-quaternary) {
    color: var(--labels-primary);
  }
}

.rt-Link:where([data-disabled='disabled']) {
  cursor: var(--cursor-disabled);
  color: var(--labels-secondary);
}

.rt-Link:not([data-disabled='disabled']) {
  cursor: var(--cursor-link);
}

&:where(.rt-r-size-1) {
  font-size: var(--font-size-2, 11px);
  font-weight: var(--font-weight-semibold, 600);
  line-height: var(--line-height-2, 14px);

  > svg {
    width: var(--icon-size-1) !important;
    height: var(--icon-size-1) !important;
  }
}

&:where(.rt-r-size-2) {
  font-size: var(--font-size-3, 12px);
  font-weight: var(--font-weight-semibold, 600);
  line-height: var(--line-height-3, 15px);

  > svg {
    width: var(--icon-size-2) !important;
    height: var(--icon-size-2) !important;
  }
}

&:where(.rt-r-size-3) {
  font-size: var(--font-size-5, 15px);
  font-weight: var(--font-weight-semibold, 600);
  line-height: var(--line-height-5, 20px);

  > svg {
    width: var(--icon-size-3) !important;
    height: var(--icon-size-3) !important;
  }
}

&:where(.rt-r-size-4) {
  font-size: var(--font-size-6, 17px);
  font-weight: var(--font-weight-bold, 700);
  line-height: var(--line-height-6, 22px);

  > svg {
    width: var(--icon-size-4) !important;
    height: var(--icon-size-4) !important;
  }
}

&:where(.rt-r-size-5) {
  font-size: var(--font-size-7, 22px);
  font-weight: var(--font-weight-bold, 700);
  line-height: var(--line-height-7, 26px);

  > svg {
    width: var(--icon-size-5) !important;
    height: var(--icon-size-5) !important;
  }
}

&:where(.rt-color-primary) {
  color: var(--brand-accent);

  &:hover:not([data-disabled='disabled']) {
    color: var(--brand-accent-hover);
  }
}

&:where(.rt-color-quaternary) {
  color: var(--labels-primary);
}

.rt-Link:where([data-disabled='disabled']) {
  cursor: var(--cursor-disabled);
  color: var(--labels-secondary);
}

.rt-Link:not([data-disabled='disabled']) {
  cursor: var(--cursor-link);
}
