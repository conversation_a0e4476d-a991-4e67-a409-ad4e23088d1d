import React, { type JSX } from 'react';

import Icon from '@components/icon';
import type { Orientation, TabsItem } from '@components/tabs/type';

import { Content, List, type ListProps, Root, Trigger } from './tabs-base';

interface TabsProps extends ListProps {
  defaultActiveKey?: string;
  items: TabsItem[];
  onTabChange?: (key: string) => void;
  orientation?: Orientation;
}

const Tabs: React.FC<TabsProps> = ({ items, defaultActiveKey, onTabChange, ...props }): JSX.Element => {
  return (
    <Root orientation={props.orientation} defaultValue={defaultActiveKey} onValueChange={onTabChange}>
      <List {...props}>
        {items.map((item) => {
          return (
            <Trigger key={item.key} value={item.key}>
              {item.prefix && <Icon icon={item.prefix} />}
              {item.title}
              {item.suffix && <Icon icon={item.suffix} />}
            </Trigger>
          );
        })}
      </List>

      {items
        .filter((x) => x.content)
        .map((item) => {
          return (
            <Content key={item.key} value={item.key}>
              {item.content}
            </Content>
          );
        })}
    </Root>
  );
};

export { Tabs, type TabsItem, type TabsProps };
