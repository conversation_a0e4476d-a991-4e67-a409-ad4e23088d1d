// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 595.597v175.723c0 92.425 76.41 167.347 170.667 167.347h512c94.255 0 170.667-74.923 170.667-167.347v-175.723M284.445 459.418l219.512 215.241c2.219 2.18 5.133 3.268 8.043 3.268M739.554 459.418l-219.507 215.241c-2.223 2.18-5.133 3.268-8.047 3.268M512 677.926v-592.593"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "download"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1608,
        "id": 412,
        "name": "download",
        "prevSize": 32,
        "code": 59884
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 237
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface DownloadIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const DownloadIcon = ({ size = 16, ...props }: DownloadIconProps) => (
  <IcoMoon iconSet={iconSet} icon="download" size={size} {...props} />
);

DownloadIcon.displayName = 'DownloadIcon';

export default DownloadIcon;
