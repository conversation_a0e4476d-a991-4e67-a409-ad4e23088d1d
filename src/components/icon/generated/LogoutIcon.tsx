// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M707.533 341.333l164.634 164.634c1.668 1.664 2.5 3.849 2.5 6.033M707.533 682.667l164.634-164.634c1.668-1.664 2.5-3.849 2.5-6.033M874.667 512h-551.132M702.229 832c-10.155 60.54-62.805 106.667-126.229 106.667h-298.667c-70.692 0-128-57.306-128-128v-597.333c0-70.692 57.308-128 128-128h298.667c63.424 0 116.075 46.129 126.229 106.667"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "logout"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1749,
        "id": 271,
        "name": "logout",
        "prevSize": 32,
        "code": 60025
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 378
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface LogoutIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const LogoutIcon = ({ size = 16, ...props }: LogoutIconProps) => (
  <IcoMoon iconSet={iconSet} icon="logout" size={size} {...props} />
);

LogoutIcon.displayName = 'LogoutIcon';

export default LogoutIcon;
