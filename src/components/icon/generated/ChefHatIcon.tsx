// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M192 814.31c6.673-2.359 13.853-3.644 21.333-3.644h149.333M192 814.31c-24.858 8.785-42.667 32.491-42.667 60.356 0 35.345 28.654 64 64 64h597.333c35.345 0 64-28.655 64-64 0-27.866-17.809-51.571-42.667-60.356M192 814.31v-308.493c-15.412-4.587-29.781-11.597-42.667-20.582-38.688-26.987-64-71.822-64-122.569 0-82.475 66.859-149.333 149.333-149.333 17.801 0 34.874 3.115 50.705 8.829 42.871-81.361 128.273-136.829 226.629-136.829s183.757 55.468 226.628 136.829c15.829-5.714 32.905-8.829 50.705-8.829 82.475 0 149.333 66.859 149.333 149.333 0 50.746-25.314 95.582-64 122.569-12.885 8.986-27.255 15.996-42.667 20.582v308.493M832 814.31c-6.673-2.359-13.854-3.644-21.333-3.644h-149.333M512 810.667v-256M512 810.667h-149.333M512 810.667h149.333M362.667 810.667v-170.667M661.333 810.667v-170.667"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "chef-hat"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1543,
        "id": 477,
        "name": "chef-hat",
        "prevSize": 32,
        "code": 59819
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 172
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ChefHatIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ChefHatIcon = ({ size = 16, ...props }: ChefHatIconProps) => (
  <IcoMoon iconSet={iconSet} icon="chef-hat" size={size} {...props} />
);

ChefHatIcon.displayName = 'ChefHatIcon';

export default ChefHatIcon;
