// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 725.333l82.577-371.599c7.742-34.839 30.772-64.335 62.693-80.296l94.893-47.447c28.334-14.167 49.818-39.087 59.655-69.2l23.346-71.459M938.667 725.333l-82.577-371.599c-7.744-34.839-30.771-64.335-62.694-80.296l-94.891-47.447c-28.335-14.167-49.818-39.087-59.657-69.2l-23.347-71.459M440.887 322.371v43.133c0 18.399-4.284 36.546-12.51 53.003l-34.896 69.788M583.113 322.371v43.133c0 18.399 4.284 36.546 12.51 53.003l34.897 69.788M251.259 535.706l63.565 169.506c4.99 13.303 7.546 27.401 7.546 41.613v191.842M772.74 535.706l-63.565 169.506c-4.988 13.303-7.548 27.401-7.548 41.613v191.842"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "body-back"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1476,
        "id": 544,
        "name": "body-back",
        "prevSize": 32,
        "code": 59752
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 105
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BodyBackIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BodyBackIcon = ({ size = 16, ...props }: BodyBackIconProps) => (
  <IcoMoon iconSet={iconSet} icon="body-back" size={size} {...props} />
);

BodyBackIcon.displayName = 'BodyBackIcon';

export default BodyBackIcon;
