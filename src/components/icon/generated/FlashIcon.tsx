// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M128 180.148h333.803M128 843.853h333.803M199.529 369.778h166.902M128 654.221h166.902M717.295 89.751l-294.32 435.096c-20.537 30.362-3.514 72.235 32.048 78.835l148.378 27.537c27.23 5.056 45.188 31.821 39.962 59.567l-44.416 235.763c-2.001 10.62 11.486 16.653 17.779 7.949l312.171-431.714c21.871-30.242 5.094-73.412-31.134-80.136l-149.269-27.703c-25.805-4.789-43.537-29.201-40.529-55.796l27.405-242.462c1.19-10.509-12.186-15.642-18.074-6.937z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "flash"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1660,
        "id": 360,
        "name": "flash",
        "prevSize": 32,
        "code": 59936
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 289
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FlashIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FlashIcon = ({ size = 16, ...props }: FlashIconProps) => (
  <IcoMoon iconSet={iconSet} icon="flash" size={size} {...props} />
);

FlashIcon.displayName = 'FlashIcon';

export default FlashIcon;
