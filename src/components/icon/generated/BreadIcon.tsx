// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M362.667 213.333c-153.167 0-277.333 72.002-277.333 160.82 0 31.523 15.64 60.927 42.667 85.746v282.5c0 23.898 0 35.844 4.65 44.971 4.091 8.030 10.618 14.554 18.646 18.645 9.127 4.651 21.075 4.651 44.97 4.651h332.8c23.898 0 35.844 0 44.971-4.651 8.030-4.092 14.554-10.615 18.645-18.645 4.651-9.126 4.651-21.073 4.651-44.971v-282.5c27.025-24.819 42.667-54.223 42.667-85.746 0-56.936-51.021-106.961-128-135.539-43.106-16.003-94.351-25.281-149.333-25.281zM362.667 213.333h298.667c153.169 0 277.333 72.002 277.333 160.82 0 31.523-15.642 60.927-42.667 85.746v282.5c0 23.898 0 35.844-4.651 44.971-4.092 8.030-10.615 14.554-18.645 18.645-9.126 4.651-21.073 4.651-44.971 4.651h-294.4"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bread"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1488,
        "id": 532,
        "name": "bread",
        "prevSize": 32,
        "code": 59764
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 117
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BreadIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BreadIcon = ({ size = 16, ...props }: BreadIconProps) => (
  <IcoMoon iconSet={iconSet} icon="bread" size={size} {...props} />
);

BreadIcon.displayName = 'BreadIcon';

export default BreadIcon;
