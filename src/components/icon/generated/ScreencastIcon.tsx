// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 522.667c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM352 853.333c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM85.333 650.667c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM224 853.333c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM106.667 864c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM106.24 800c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM924.715 783.445v0zM868.779 839.381v0zM868.779 184.618v0zM924.715 240.556v0zM155.223 184.618v0zM99.284 240.556v0zM53.333 426.667c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM512 821.333c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM85.333 554.667v32h21.333v-64h-21.333v32zM384 832h-32v21.333h64v-21.333h-32zM106.667 554.667v32c135.494 0 245.333 109.841 245.333 245.333h64c0-170.842-138.493-309.333-309.333-309.333v32zM85.333 682.667v32h21.333v-64h-21.333v32zM256 832h-32v21.333h64v-21.333h-32zM106.667 682.667v32c64.801 0 117.333 52.531 117.333 117.333h64c0-100.147-81.186-181.333-181.333-181.333v32zM106.667 832v-32h-0.427v64h0.427v-32zM290.133 170.667v32h443.733v-64h-443.733v32zM938.667 375.467h-32v273.067h64v-273.067h-32zM938.667 648.533h-32c0 36.373-0.026 61.726-1.638 81.463-1.583 19.366-4.531 30.494-8.823 38.921l57.024 29.056c9.655-18.957 13.683-39.441 15.586-62.763 1.877-22.95 1.852-51.362 1.852-86.677h-32zM733.867 853.333v32c35.315 0 63.727 0.026 86.677-1.852 23.322-1.903 43.806-5.931 62.763-15.586l-29.056-57.024c-8.427 4.292-19.554 7.241-38.921 8.823-19.738 1.613-45.090 1.638-81.463 1.638v32zM924.715 783.445l-28.51-14.528c-9.207 18.061-23.893 32.751-41.954 41.954l29.056 57.024c30.106-15.343 54.579-39.817 69.922-69.922l-28.514-14.528zM733.867 170.667v32c36.373 0 61.726 0.025 81.463 1.638 19.366 1.582 30.494 4.532 38.921 8.826l29.056-57.024c-18.957-9.658-39.441-13.683-62.763-15.589-22.95-1.875-51.362-1.85-86.677-1.85v32zM938.667 375.467h32c0-35.315 0.026-63.727-1.852-86.676-1.903-23.323-5.931-43.809-15.586-62.762l-57.024 29.055c4.292 8.428 7.241 19.553 8.823 38.919 1.613 19.739 1.638 45.093 1.638 81.464h32zM868.779 184.618l-14.528 28.512c18.061 9.204 32.747 23.89 41.954 41.953l57.024-29.055c-15.343-30.106-39.817-54.583-69.922-69.923l-14.528 28.512zM290.133 170.667v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM85.333 375.467h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM155.223 184.618l-14.528-28.512c-30.106 15.34-54.583 39.817-69.923 69.923l57.024 29.055c9.204-18.063 23.89-32.75 41.953-41.953l-14.528-28.512zM85.333 426.667h32v-51.2h-64v51.2h32zM733.867 853.333v-32h-221.867v64h221.867v-32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "screencast"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1865,
        "id": 155,
        "name": "screencast",
        "prevSize": 32,
        "code": 60141
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 494
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ScreencastIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ScreencastIcon = ({ size = 16, ...props }: ScreencastIconProps) => (
  <IcoMoon iconSet={iconSet} icon="screencast" size={size} {...props} />
);

ScreencastIcon.displayName = 'ScreencastIcon';

export default ScreencastIcon;
