// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 85.333v106.667M796.446 227.555l-53.158 53.159M796.446 796.446l-53.158-53.158M227.555 796.446l53.159-53.158M227.555 227.555l53.159 53.159M512 832v106.667M192 512h-106.667M938.667 512h-106.667M701.628 512c0 104.73-84.898 189.628-189.628 189.628s-189.629-84.898-189.629-189.628c0-104.73 84.9-189.629 189.629-189.629s189.628 84.9 189.628 189.629z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "sun"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1923,
        "id": 97,
        "name": "sun",
        "prevSize": 32,
        "code": 60199
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 552
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SunIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SunIcon = ({ size = 16, ...props }: SunIconProps) => (
  <IcoMoon iconSet={iconSet} icon="sun" size={size} {...props} />
);

SunIcon.displayName = 'SunIcon';

export default SunIcon;
