// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M417.185 802.91c0 51.413-42.45 93.090-94.815 93.090s-94.815-41.677-94.815-93.090M417.185 802.91c0-51.413-42.45-93.090-94.815-93.090s-94.815 41.677-94.815 93.090M417.185 802.91h189.629M227.555 802.91h-23.703c-65.456 0-118.519-52.1-118.519-116.365l15.343-120.516M796.446 802.91c0 51.413-42.453 93.090-94.818 93.090s-94.814-41.677-94.814-93.090M796.446 802.91c0-51.413-42.453-93.090-94.818-93.090s-94.814 41.677-94.814 93.090M796.446 802.91h23.701c65.455 0 118.519-52.1 118.519-116.365v-69.82M630.519 318.836h66.628c33.075 0 64.649 13.572 87.087 37.437l61.5 65.415M630.519 318.836v-4.655c0-38.070-18.62-71.87-47.407-93.1M630.519 318.836v422.497M845.734 421.688l61.5 65.416c20.211 21.495 31.433 49.673 31.433 78.925v50.697M845.734 421.688h-49.289c-26.185 0-47.407 20.838-47.407 46.545v55.403c0 51.413 42.449 93.090 94.814 93.090h94.814M201.482 325.818l47.622 49.449c1.993 2.31 5.507 2.337 7.533 0.057l98.918-119.324M474.074 314.182c0 102.825-84.899 186.183-189.629 186.183s-189.629-83.357-189.629-186.183c0-102.825 84.9-186.182 189.629-186.182s189.629 83.357 189.629 186.182z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "truck-check"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1965,
        "id": 55,
        "name": "truck-check",
        "prevSize": 32,
        "code": 60241
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 594
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface TruckCheckIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const TruckCheckIcon = ({ size = 16, ...props }: TruckCheckIconProps) => (
  <IcoMoon iconSet={iconSet} icon="truck-check" size={size} {...props} />
);

TruckCheckIcon.displayName = 'TruckCheckIcon';

export default TruckCheckIcon;
