.rt-BaseDialogOverlay {
  position: fixed;
  inset: 0;

  &::before {
    position: fixed;
    content: '';
    inset: 0;
    background-color: var(--overlay-black60-40);
  }
}

.rt-BaseDialogScroll {
  display: flex;
  overflow: auto;
  position: absolute;
  inset: 0;
}

.rt-BaseDialogScrollPadding {
  flex-grow: 1;
  margin: auto;
  padding-top: var(--spatial-6);
  padding-bottom: max(var(--spatial-6), 6vh);
  padding-left: var(--spatial-4);
  padding-right: var(--spatial-4);
}

@breakpoints {
  .rt-BaseDialogScrollPadding:where(.rt-r-align-start) {
    margin-top: 0;
  }
  .rt-BaseDialogScrollPadding:where(.rt-r-align-center) {
    margin-top: auto;
  }
}

.rt-BaseDialogContent {
  margin: auto;
  width: 100%;
  min-width: 320;
  z-index: 1;
  position: relative;
  box-sizing: border-box;
  overflow: auto;

  --inset-padding-top: var(--dialog-content-padding);
  --inset-padding-right: var(--dialog-content-padding);
  --inset-padding-bottom: var(--dialog-content-padding);
  --inset-padding-left: var(--dialog-content-padding);
  padding: var(--dialog-content-padding);
  box-sizing: border-box;

  background-color: var(--materials-thin);
  backdrop-filter: blur(50px);
  box-shadow:
    0px 7px 10px 0px rgba(0, 0, 0, 0.09),
    0px 40px 80px 0px rgba(0, 0, 0, 0.3);
  outline: none;
}

/***************************************************************************************************
 *                                                                                                 *
 * SIZES                                                                                           *
 *                                                                                                 *
 ***************************************************************************************************/

@breakpoints {
  .rt-BaseDialogContent {
    &:where(.rt-r-size-1) {
      --dialog-content-padding: var(--spatial-5);
      border-radius: var(--radius-4);
    }
    &:where(.rt-r-size-2) {
      --dialog-content-padding: var(--spatial-6);
      border-radius: var(--radius-4);
    }
    &:where(.rt-r-size-3) {
      --dialog-content-padding: var(--spatial-7);
      border-radius: var(--radius-5);
    }
    &:where(.rt-r-size-4) {
      --dialog-content-padding: var(--spatial-8);
      border-radius: var(--radius-5);
    }
  }
}

@media (prefers-reduced-motion: no-preference) {
  @keyframes rt-dialog-overlay-no-op {
    from {
      opacity: 1;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes rt-dialog-content-show {
    from {
      opacity: 0;
      transform: translateY(5px) scale(0.97);
    }
    to {
      opacity: 1;
      transform: translateY(0px) scale(1);
    }
  }

  @keyframes rt-dialog-content-hide {
    from {
      opacity: 1;
      transform: translateY(0px) scale(1);
    }
    to {
      opacity: 0;
      transform: translateY(5px) scale(0.99);
    }
  }

  .rt-BaseDialogOverlay {
    /* Keep the overlay mounted until the children have animated */
    &:where([data-state='closed']) {
      animation: rt-dialog-overlay-no-op 160ms cubic-bezier(0.16, 1, 0.3, 1);
    }
    &:where([data-state='open'])::before {
      animation: rt-fade-in 200ms cubic-bezier(0.16, 1, 0.3, 1);
    }
    &:where([data-state='closed'])::before {
      opacity: 0;
      animation: rt-fade-out 160ms cubic-bezier(0.16, 1, 0.3, 1);
    }
  }

  .rt-BaseDialogContent {
    &:where([data-state='open']) {
      animation: rt-dialog-content-show 200ms cubic-bezier(0.16, 1, 0.3, 1);
    }
    &:where([data-state='closed']) {
      opacity: 0;
      animation: rt-dialog-content-hide 100ms cubic-bezier(0.16, 1, 0.3, 1);
    }
  }
}
