// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M310.985 85.333h75.381M386.365 85.333v301.63c0 19.831-5.275 39.344-15.341 56.753l-85.164 147.29M386.365 85.333h251.271M637.636 85.333h75.379M637.636 85.333v301.63c0 19.831 5.274 39.344 15.339 56.753l74.547 128.926M285.86 591.006l-99.676 172.39c-45.665 78.976 14.93 175.27 110.294 175.27h431.043c95.364 0 155.959-96.294 110.293-175.27l-110.293-190.754M285.86 591.006l117.598-36.975c36.781-11.563 77.156-6.46 109.417 13.824l37.116 23.343c38.498 24.213 87.991 26.505 128.819 5.956l48.713-24.512"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "flask"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1661,
        "id": 359,
        "name": "flask",
        "prevSize": 32,
        "code": 59937
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 290
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FlaskIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FlaskIcon = ({ size = 16, ...props }: FlaskIconProps) => (
  <IcoMoon iconSet={iconSet} icon="flask" size={size} {...props} />
);

FlaskIcon.displayName = 'FlaskIcon';

export default FlaskIcon;
