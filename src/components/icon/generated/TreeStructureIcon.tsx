// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M369.778 512v-23.706c0-65.454-53.063-118.516-118.519-118.516h-47.407c-65.456 0-118.519 53.062-118.519 118.516v47.411c0 65.455 53.062 118.515 118.519 118.515h47.407c65.456 0 118.519-53.060 118.519-118.515v-23.706zM369.778 512h142.222M512 512v-165.926c0-65.456 53.065-118.519 118.519-118.519h23.701M512 512v165.926c0 65.455 53.065 118.519 118.519 118.519h23.701M654.221 227.555v23.704c0 65.456 53.065 118.519 118.519 118.519h47.407c65.455 0 118.519-53.063 118.519-118.519v-47.407c0-65.456-53.065-118.519-118.519-118.519h-47.407c-65.455 0-118.519 53.062-118.519 118.519v23.703zM654.221 796.446v23.701c0 65.455 53.065 118.519 118.519 118.519h47.407c65.455 0 118.519-53.065 118.519-118.519v-47.407c0-65.455-53.065-118.519-118.519-118.519h-47.407c-65.455 0-118.519 53.065-118.519 118.519v23.706z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "tree-structure"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1963,
        "id": 57,
        "name": "tree-structure",
        "prevSize": 32,
        "code": 60239
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 592
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface TreeStructureIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const TreeStructureIcon = ({ size = 16, ...props }: TreeStructureIconProps) => (
  <IcoMoon iconSet={iconSet} icon="tree-structure" size={size} {...props} />
);

TreeStructureIcon.displayName = 'TreeStructureIcon';

export default TreeStructureIcon;
