import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import * as React from 'react';

import { ChevronDownIcon } from '@base-components/icons';
import { Collapsible as BaseCollapsible, Button, Icon } from '@components';
import { Avatar } from '@components/avatar';
import { Card } from '@components/card';
import { Flex } from '@components/flex';
import { IconButton } from '@components/icon-button';
import { Text } from '@components/text';

interface CollapsibleProps {
  open: boolean;
  title?: string;
  address?: string;
  hash?: string;
  avatars?: string[];
  showLabel?: string;
  copyLabel?: string;
}

const Collapsible = ({
  open: argsOpen,
  title = 'Account 1 - EVM',
  address = '0x32Be...2D88',
  hash = '480b430af97d5cd579fa1e9a4d6e16fed3425aa4771ee4ab0b20f30bd58d56da',
  avatars = [
    'https://i.pravatar.cc/128?u=1',
    'https://i.pravatar.cc/128?u=2',
    'https://i.pravatar.cc/128?u=3',
    'https://i.pravatar.cc/128?u=4',
  ],
  showLabel = 'show',
  copyLabel = 'copy',
}: CollapsibleProps) => {
  const [open, setOpen] = React.useState(argsOpen);

  const handleOpenChange = (open: boolean) => {
    setOpen(open);
  };

  return (
    <BaseCollapsible.Root open={open} onOpenChange={handleOpenChange} maxWidth="300px">
      <Card size="3" variant="surface" radius="extra-large">
        <Flex align="center" justify="between">
          <Flex gap="2" align="center">
            <Avatar src={avatars[0]} />
            <Flex direction="column" gap="2">
              <Text size="5" weight="bold">
                {title}
              </Text>
              <Flex gap="2" align="center">
                <Avatar.Group size="1" maxOptions={{ count: 2, variant: 'ghost' }}>
                  {avatars.slice(1).map((src, i) => (
                    <Avatar key={i} src={src} />
                  ))}
                </Avatar.Group>
                <Text size="3" color="secondary">
                  {address}
                </Text>
              </Flex>
            </Flex>
          </Flex>
          <BaseCollapsible.Trigger asChild>
            <IconButton className="rt-CollapsibleIcon" size="1" radius="medium" variant="secondary">
              <ChevronDownIcon />
            </IconButton>
          </BaseCollapsible.Trigger>
        </Flex>

        <BaseCollapsible.Content mt="4">
          <Card mask>
            <Text>{hash}</Text>
          </Card>

          <Flex gap="2" mt="4">
            <Button variant="tertiary" size="3" style={{ flex: 1 }}>
              <Icon icon="eye" />
              {showLabel}
            </Button>
            <Button variant="tertiary" size="3" style={{ flex: 1 }}>
              <Icon icon="copy" />
              {copyLabel}
            </Button>
          </Flex>
        </BaseCollapsible.Content>
      </Card>
    </BaseCollapsible.Root>
  );
};

const meta: Meta<typeof Collapsible> = {
  title: 'DesignSystem/Collapsible',
  component: Collapsible,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    open: {
      control: 'boolean',
      description: 'Controls the open state of the collapsible section.',
    },
    title: { control: 'text' },
    address: { control: 'text' },
    hash: { control: 'text' },
    showLabel: { control: 'text' },
    copyLabel: { control: 'text' },
    avatars: { control: 'object' },
  },
};

export default meta;

type Story = StoryObj<typeof Collapsible>;

export const _collapsible: Story = {
  args: {
    open: true,
    title: 'Account 1 - EVM',
    address: '0x32Be...2D88',
    hash: '480b430af97d5cd579fa1e9a4d6e16fed3425aa4771ee4ab0b20f30bd58d56da',
    showLabel: 'show',
    copyLabel: 'copy',
    avatars: [
      'https://i.pravatar.cc/128?u=1',
      'https://i.pravatar.cc/128?u=2',
      'https://i.pravatar.cc/128?u=3',
      'https://i.pravatar.cc/128?u=4',
    ],
  },
  render: Collapsible,
};
