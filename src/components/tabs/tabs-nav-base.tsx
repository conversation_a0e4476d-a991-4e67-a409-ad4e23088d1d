import clsx from 'clsx';
import { NavigationMenu } from 'radix-ui';
import * as React from 'react';

import { tabNavLinkPropDefs, tabsListPropDefs } from '@components/tabs/tabs.props';
import { type ComponentPropsWithout, type RemovedProps, extractProps, getSubtree } from '@helpers';
import { type GetPropDefTypes, type MarginProps, marginPropDefs } from '@props';

type TabNavRootElement = React.ComponentRef<typeof NavigationMenu.Root>;
type TabNavRootElementProps = ComponentPropsWithout<'nav', RemovedProps>;
type TabNavOwnProps = GetPropDefTypes<typeof tabsListPropDefs>;
interface TabNavRootProps
  extends Omit<TabNavRootElementProps, 'defaultValue' | 'dir' | 'color'>,
    MarginProps,
    TabNavOwnProps {}
const TabNavRoot = React.forwardRef<TabNavRootElement, TabNavRootProps>((props, forwardedRef) => {
  const { children, className, color, ...rootProps } = extractProps(props, tabsListPropDefs, marginPropDefs);
  return (
    <NavigationMenu.Root
      className="rt-TabNavRoot"
      data-accent-color={color}
      {...rootProps}
      asChild={false}
      ref={forwardedRef}
      data-orientation={props.orientation}
    >
      <NavigationMenu.List
        data-orientation={props.orientation}
        className={clsx('rt-reset', 'rt-BaseTabList', 'rt-TabNavList', className)}
      >
        {children}
      </NavigationMenu.List>
    </NavigationMenu.Root>
  );
});
TabNavRoot.displayName = 'TabNav.Root';

type TabNavLinkElement = React.ComponentRef<typeof NavigationMenu.Link>;
type TabNavLinkOwnProps = GetPropDefTypes<typeof tabNavLinkPropDefs>;
interface TabNavLinkProps
  extends ComponentPropsWithout<typeof NavigationMenu.Link, RemovedProps | 'onSelect'>,
    TabNavLinkOwnProps {}
const TabNavLink = React.forwardRef<TabNavLinkElement, TabNavLinkProps>((props, forwardedRef) => {
  const { asChild, children, className, ...linkProps } = props;

  return (
    <NavigationMenu.Item className="rt-TabNavItem">
      <NavigationMenu.Link
        {...linkProps}
        ref={forwardedRef}
        className={clsx('rt-reset', 'rt-BaseTabListTrigger', 'rt-TabNavLink', className)}
        onSelect={undefined}
        asChild={asChild}
      >
        {getSubtree({ asChild, children }, (children) => (
          <span className="rt-BaseTabListTriggerInner rt-TabNavLinkInner">{children}</span>
        ))}
      </NavigationMenu.Link>
    </NavigationMenu.Item>
  );
});
TabNavLink.displayName = 'TabNav.Link';

export { TabNavRoot, TabNavLink };
export type { TabNavRootProps, TabNavLinkProps };
