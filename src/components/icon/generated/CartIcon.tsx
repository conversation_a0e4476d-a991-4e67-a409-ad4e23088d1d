// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M193.607 224h680.078c40.529 0 71.164 36.163 63.915 75.449l-53.295 288.784c-11.234 60.86-65.041 105.101-127.834 105.101h-367.81c-62.793 0-116.6-44.241-127.833-105.101l-67.222-364.233zM193.607 224l-20.958-103.237c-0.405-1.994-2.182-3.43-4.247-3.43h-83.069M410.155 373.333h151.581M453.466 842.667c0 35.345-29.086 64-64.965 64s-64.965-28.655-64.965-64c0-35.345 29.086-64 64.965-64s64.965 28.655 64.965 64zM799.94 842.667c0 35.345-29.086 64-64.964 64s-64.964-28.655-64.964-64c0-35.345 29.086-64 64.964-64s64.964 28.655 64.964 64z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "cart"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1531,
        "id": 489,
        "name": "cart",
        "prevSize": 32,
        "code": 59807
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 160
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CartIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CartIcon = ({ size = 16, ...props }: CartIconProps) => (
  <IcoMoon iconSet={iconSet} icon="cart" size={size} {...props} />
);

CartIcon.displayName = 'CartIcon';

export default CartIcon;
