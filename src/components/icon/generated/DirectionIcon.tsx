// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M176.105 808.166l281.184-643.615c21.291-48.736 88.132-48.736 109.423 0l281.186 643.615c18.321 41.941-12.1 88.175-54.874 87.834-5.047-0.043-9.963-1.468-14.669-3.294l-227.763-88.397c-24.828-9.634-52.356-9.634-77.184 0l-227.764 88.397c-4.706 1.826-9.62 3.251-14.668 3.294-42.773 0.341-73.196-45.892-54.871-87.834z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "direction"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1598,
        "id": 422,
        "name": "direction",
        "prevSize": 32,
        "code": 59874
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 227
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface DirectionIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const DirectionIcon = ({ size = 16, ...props }: DirectionIconProps) => (
  <IcoMoon iconSet={iconSet} icon="direction" size={size} {...props} />
);

DirectionIcon.displayName = 'DirectionIcon';

export default DirectionIcon;
