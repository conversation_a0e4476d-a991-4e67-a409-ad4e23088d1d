import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import { useArgs } from 'storybook/internal/preview-api';
import { fn } from 'storybook/test';

import { Dialog as BaseDialog, Box, Button, Checkbox, Flex, Icon } from '@components';
import type { Responsive } from '@props';

interface DialogProps {
  open?: boolean;
  size?: Responsive<'1' | '2' | '3' | '4'>;
  title: string;
  colorTitle?:
    | 'accent'
    | 'gray'
    | 'brown'
    | 'yellow'
    | 'orange'
    | 'red'
    | 'pink'
    | 'purple'
    | 'indigo'
    | 'blue'
    | 'cyan'
    | 'teal'
    | 'green'
    | 'mint';
  description: string;
  triggerText?: string;
  skipText?: string;
  cancelText?: string;
  actionText?: string;
  checkboxText?: string;
  maxWidth?: Responsive<string>;
  onCancel?: () => void;
  onConfirm?: () => void;
}

const Dialog = ({
  open,
  size,
  title,
  colorTitle,
  description,
  triggerText,
  skipText,
  cancelText,
  actionText,
  checkboxText,
  maxWidth,
  onCancel,
  onConfirm,
}: DialogProps) => {
  return (
    <BaseDialog.Root open={open}>
      <BaseDialog.Trigger>
        <Button>{triggerText}</Button>
      </BaseDialog.Trigger>
      <BaseDialog.Content size={size} maxWidth={maxWidth}>
        <Flex direction="column" align="center" justify="center" gap="5">
          <Icon icon="info-circle" size={24} />
          <Box>
            <BaseDialog.Title align="center" color={colorTitle}>
              {title}
            </BaseDialog.Title>
            <BaseDialog.Description align="center">{description}</BaseDialog.Description>
          </Box>
          <Flex justify="center" gap="4">
            <BaseDialog.Close>
              <Button size="3" variant="tertiary" onClick={onConfirm}>
                {actionText}
              </Button>
            </BaseDialog.Close>
            <BaseDialog.Close>
              <Button size="3" variant="secondary" onClick={onCancel}>
                {cancelText}
              </Button>
            </BaseDialog.Close>
          </Flex>
          <BaseDialog.Close>
            <Button size="3" variant="ghost" onClick={onCancel}>
              {skipText}
            </Button>
          </BaseDialog.Close>
          <Checkbox label={checkboxText} />
        </Flex>
      </BaseDialog.Content>
    </BaseDialog.Root>
  );
};

const meta = {
  title: 'DesignSystem/Dialog',
  component: Dialog,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    title: { control: 'text' },
    colorTitle: {
      control: 'select',
      options: [
        'accent',
        'gray',
        'brown',
        'yellow',
        'orange',
        'red',
        'pink',
        'purple',
        'indigo',
        'blue',
        'cyan',
        'teal',
        'green',
        'mint',
      ],
    },
    description: { control: 'text' },
    open: { control: 'boolean' },
    skipText: { control: 'text' },
    triggerText: { control: 'text' },
    cancelText: { control: 'text' },
    actionText: { control: 'text' },
    checkboxText: { control: 'text' },
    maxWidth: { control: 'text' },
    onCancel: { action: 'onCancel' },
    onConfirm: { action: 'onConfirm' },
  },
} satisfies Meta<typeof Dialog>;

export default meta;
type Story = StoryObj<typeof meta>;

export const _dialog: Story = {
  args: {
    open: false,
    title: 'Notification text',
    colorTitle: 'accent',
    description:
      'Description text about this alert is shown here, explaining to users what the options \n underneath are about and what to do.',
    skipText: 'Skip',
    triggerText: 'Open',
    cancelText: 'Continue',
    actionText: 'Read more',
    checkboxText: 'Agree',
    maxWidth: '320px',
    onCancel: fn(),
    onConfirm: fn(),
  },
  render: (args) => {
    const [, updateArgs] = useArgs();

    return (
      <Dialog
        {...args}
        onCancel={() => {
          args.onCancel?.();
          updateArgs({ open: false });
        }}
        onConfirm={() => {
          args.onConfirm?.();
          updateArgs({ open: false });
        }}
      />
    );
  },
};
