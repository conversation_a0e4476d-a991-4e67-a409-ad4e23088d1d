// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 512l377.869-213.333M512 512l-377.87-213.333M512 512v426.667M889.869 298.667c3.942 6.633 6.131 14.333 6.131 22.356v381.953c0 15.723-8.401 30.208-21.948 37.845l-341.333 192.397c-6.443 3.631-13.581 5.449-20.719 5.449M889.869 298.667c-3.78-6.365-9.182-11.747-15.817-15.486l-341.333-192.399c-12.885-7.264-28.553-7.264-41.438 0l-341.335 192.399c-6.633 3.739-12.033 9.121-15.816 15.486M134.13 298.667c-3.942 6.633-6.13 14.333-6.13 22.356v381.953c0 15.723 8.401 30.208 21.946 37.845l341.335 192.397c6.443 3.631 13.581 5.449 20.719 5.449"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "package"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1807,
        "id": 213,
        "name": "package",
        "prevSize": 32,
        "code": 60083
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 436
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface PackageIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const PackageIcon = ({ size = 16, ...props }: PackageIconProps) => (
  <IcoMoon iconSet={iconSet} icon="package" size={size} {...props} />
);

PackageIcon.displayName = 'PackageIcon';

export default PackageIcon;
