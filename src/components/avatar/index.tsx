'use client';

import clsx from 'clsx';
import { Avatar as AvatarPrimitive } from 'radix-ui';
import * as React from 'react';

import { VerificationIcon } from '@base-components/icons';
import { avatarPropDefs } from '@components/avatar/avatar.props';
import { Flex } from '@components/flex';
import { Tooltip } from '@components/tooltip';
import { type ComponentPropsWithout, type RemovedProps, extractProps, getSubtree } from '@helpers';
import { type GetPropDefTypes, type MarginProps, marginPropDefs } from '@props';

interface CloneAvatarProps {
  child: React.ReactNode;
  idx: number;
  total: number;
  sharedProps: {
    size?: AvatarOwnProps['size'];
    variant?: AvatarOwnProps['variant'];
    shape?: AvatarOwnProps['shape'];
    radius?: AvatarOwnProps['radius'];
    spacing?: string | number;
  };
}

const cloneAvatar = ({ child, idx, total, sharedProps }: CloneAvatarProps) => {
  if (!React.isValidElement<AvatarProps>(child)) return null;

  return React.cloneElement(child, {
    key: idx,
    size: sharedProps.size,
    variant: sharedProps.variant,
    shape: sharedProps.shape,
    radius: sharedProps.radius,
    style: {
      zIndex: total - idx,
      marginLeft: idx === 0 ? 0 : sharedProps.spacing,
      ...child.props.style,
    },
  });
};

// ---------- Avatar Implementation ----------

interface AvatarImplProps
  extends ComponentPropsWithout<typeof AvatarPrimitive.Image, RemovedProps>,
    GetPropDefTypes<typeof avatarPropDefs> {
  fallback?: React.ReactNode;
}

type AvatarImplElement = React.ComponentRef<typeof AvatarPrimitive.Image>;

const AvatarImpl = React.forwardRef<AvatarImplElement, AvatarImplProps>(({ fallback, ...imageProps }, forwardedRef) => {
  const [status, setStatus] = React.useState<'idle' | 'loading' | 'loaded' | 'error'>('idle');

  return (
    <>
      {(status === 'idle' || status === 'loading') && <span className="rt-AvatarFallback" />}
      {status === 'error' && (
        <AvatarPrimitive.Fallback
          delayMs={0}
          className={clsx('rt-AvatarFallback', {
            'rt-one-letter': typeof fallback === 'string' && fallback.length === 1,
            'rt-two-letters': typeof fallback === 'string' && fallback.length === 2,
          })}
        >
          {fallback}
        </AvatarPrimitive.Fallback>
      )}
      <AvatarPrimitive.Image
        ref={forwardedRef}
        className="rt-AvatarImage"
        {...imageProps}
        onLoadingStatusChange={(s) => {
          imageProps.onLoadingStatusChange?.(s);
          setStatus(s);
        }}
      />
    </>
  );
});
AvatarImpl.displayName = 'AvatarImpl';

// ---------- Avatar Root ----------

interface AvatarProps extends MarginProps, AvatarImplProps {
  isVerify?: boolean;
  isOnline?: boolean;
}

const InternalAvatar = React.forwardRef<AvatarImplElement, AvatarProps>((props, forwardedRef) => {
  const { isVerify, isOnline, asChild, children, className, style, color, radius, ...imageProps } = extractProps(
    props,
    avatarPropDefs,
    marginPropDefs,
  );

  return (
    <AvatarPrimitive.Root
      asChild={asChild}
      data-accent-color={color}
      data-radius={radius}
      className={clsx('rt-reset', 'rt-AvatarRoot', className)}
      style={style}
    >
      {isVerify && <VerificationIcon className="rt-AvatarVerificationIcon" />}
      {isOnline && <span className="rt-AvatarStatus" />}
      {getSubtree({ asChild, children }, <AvatarImpl ref={forwardedRef} {...imageProps} />)}
    </AvatarPrimitive.Root>
  );
});
InternalAvatar.displayName = 'Avatar';

// ---------- Avatar Group ----------

type AvatarOwnProps = GetPropDefTypes<typeof avatarPropDefs>;
type AvatarGroupElement = React.ComponentRef<typeof Flex>;

interface AvatarGroupProps {
  children: React.ReactNode;
  spacing?: string | number;
  size?: AvatarOwnProps['size'];
  variant?: AvatarOwnProps['variant'];
  shape?: AvatarOwnProps['shape'];
  radius?: AvatarOwnProps['radius'];
  maxOptions?: {
    count?: number;
    style?: React.CSSProperties;
    variant?: AvatarOwnProps['variant'];
    side?: 'top' | 'right' | 'bottom' | 'left';
  };
}

const Group = React.forwardRef<AvatarGroupElement, AvatarGroupProps>((props, forwardedRef) => {
  const {
    children,
    className,
    spacing,
    radius,
    maxOptions = {},
    ...rest
  } = extractProps(props, avatarPropDefs, marginPropDefs);

  const { count, style: maxStyle, variant: hiddenVariant, side = 'top' } = maxOptions;

  const avatars = React.Children.toArray(children).filter(Boolean);
  const visible = typeof count === 'number' ? avatars.slice(0, count) : avatars;
  const hidden = typeof count === 'number' ? avatars.slice(count) : [];

  const sharedProps = {
    size: props.size,
    variant: props.variant,
    shape: props.shape,
    radius,
    spacing,
  };

  return (
    <Flex ref={forwardedRef} align="center" className={clsx('rt-AvatarGroup', className)} {...rest}>
      {visible.map((child, idx) => cloneAvatar({ child, idx, total: avatars.length, sharedProps }))}

      {hidden.length > 0 && (
        <Tooltip
          side={side}
          content={
            <Flex gap="3">
              {hidden.map((child, idx) => cloneAvatar({ child, idx, total: hidden.length, sharedProps }))}
            </Flex>
          }
        >
          <span>
            <Avatar
              className="rt-AvatarHidden"
              fallback={`+${hidden.length}`}
              size={props.size}
              variant={hiddenVariant ?? props.variant}
              shape={props.shape}
              radius={radius}
              style={maxStyle}
            />
          </span>
        </Tooltip>
      )}
    </Flex>
  );
});
Group.displayName = 'Avatar.Group';

// ---------- Export ----------

const Avatar = InternalAvatar as typeof InternalAvatar & {
  Group: typeof Group;
};

Avatar.Group = Group;

export { Avatar };
export type { AvatarProps };
