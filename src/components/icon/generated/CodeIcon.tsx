// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M282.847 341.333l-134.712 125.41c-26.847 24.994-26.847 65.519 0 90.513l134.712 125.41M741.154 341.333l134.711 125.41c26.846 24.994 26.846 65.519 0 90.513l-134.711 125.41M626.577 256l-229.154 512"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "code"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1577,
        "id": 443,
        "name": "code",
        "prevSize": 32,
        "code": 59853
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 206
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CodeIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CodeIcon = ({ size = 16, ...props }: CodeIconProps) => (
  <IcoMoon iconSet={iconSet} icon="code" size={size} {...props} />
);

CodeIcon.displayName = 'CodeIcon';

export default CodeIcon;
