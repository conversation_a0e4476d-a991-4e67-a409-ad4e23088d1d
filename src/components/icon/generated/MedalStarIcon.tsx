// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M280.579 661.12c60.83 54.579 142.121 87.919 231.421 87.919 89.301 0 170.59-33.34 231.42-87.919M280.579 661.12c-67.584-60.634-109.913-147.477-109.913-243.935 0-183.277 152.82-331.852 341.333-331.852s341.333 148.575 341.333 331.852c0 96.457-42.33 183.301-109.914 243.935M280.579 661.12l-12.309 224.444c-2.013 36.698 34.271 63.1 67.958 49.451l128.873-52.22c30.016-12.164 63.782-12.164 93.798 0l128.875 52.22c33.685 13.649 69.969-12.753 67.955-49.451l-12.309-224.444M453.269 361.707l-80.518 15.357c-6.713 1.28-9.365 9.686-4.657 14.758l56.175 60.518c3.583 3.857 5.273 9.169 4.599 14.451l-10.528 82.637c-0.881 6.916 6.045 12.096 12.196 9.122l74.129-35.836c4.642-2.249 10.027-2.249 14.669 0l74.129 35.836c6.153 2.974 13.077-2.206 12.194-9.122l-10.526-82.637c-0.674-5.282 1.015-10.594 4.599-14.451l56.175-60.518c4.706-5.072 2.057-13.477-4.659-14.758l-80.516-15.357c-5.069-0.967-9.451-4.217-11.934-8.854l-39.258-73.314c-3.264-6.101-11.814-6.101-15.078 0l-39.258 73.314c-2.483 4.637-6.865 7.887-11.934 8.854z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "medal-star"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1758,
        "id": 262,
        "name": "medal-star",
        "prevSize": 32,
        "code": 60034
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 387
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MedalStarIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MedalStarIcon = ({ size = 16, ...props }: MedalStarIconProps) => (
  <IcoMoon iconSet={iconSet} icon="medal-star" size={size} {...props} />
);

MedalStarIcon.displayName = 'MedalStarIcon';

export default MedalStarIcon;
