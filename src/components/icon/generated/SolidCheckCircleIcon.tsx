// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M938.667 512c0 235.639-191.027 426.667-426.667 426.667-235.642 0-426.667-191.027-426.667-426.667 0-235.642 191.025-426.667 426.667-426.667 235.639 0 426.667 191.025 426.667 426.667zM713.455 403.156c11.891-13.077 10.927-33.315-2.15-45.204s-33.314-10.924-45.205 2.153l-213.001 234.301-94.881-107.529c-11.693-13.252-31.915-14.515-45.167-2.825-13.252 11.695-14.516 31.915-2.823 45.167l111.514 126.383c16.35 18.534 45.176 18.748 61.799 0.461l229.914-252.908z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "check-circle"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 2021,
        "id": 649,
        "name": "solid-check-circle",
        "prevSize": 32,
        "code": 60297
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 0
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SolidCheckCircleIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SolidCheckCircleIcon = ({ size = 16, ...props }: SolidCheckCircleIconProps) => (
  <IcoMoon iconSet={iconSet} icon="solid-check-circle" size={size} {...props} />
);

SolidCheckCircleIcon.displayName = 'SolidCheckCircleIcon';

export default SolidCheckCircleIcon;
