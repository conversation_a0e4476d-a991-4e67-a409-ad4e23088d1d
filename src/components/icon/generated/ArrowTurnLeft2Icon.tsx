// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 702.758c0 2.185 0.678 4.369 2.033 6.037l189.967 187.204M85.333 702.758c0-2.18 0.678-4.365 2.033-6.033l189.967-187.204M85.333 702.758h565.952c158.716 0 287.381-128.661 287.381-287.378 0-158.716-128.666-287.38-287.381-287.38h-373.952"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "arrow-turn-left-2"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1423,
        "id": 597,
        "name": "arrow-turn-left-2",
        "prevSize": 32,
        "code": 59699
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 52
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ArrowTurnLeft2IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ArrowTurnLeft2Icon = ({ size = 16, ...props }: ArrowTurnLeft2IconProps) => (
  <IcoMoon iconSet={iconSet} icon="arrow-turn-left-2" size={size} {...props} />
);

ArrowTurnLeft2Icon.displayName = 'ArrowTurnLeft2Icon';

export default ArrowTurnLeft2Icon;
