// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 228.788l59.474 58.105c1.993 1.947 5.507 1.97 7.534 0.048l122.621-116.274M85.333 511.27l59.474 58.103c1.993 1.95 5.507 1.971 7.534 0.051l122.621-116.275M85.333 793.754l59.474 58.103c1.993 1.95 5.507 1.971 7.534 0.051l122.621-116.275M440.887 241.287h497.779M440.887 523.772h497.779M440.887 806.255h497.779"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "list-check"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1738,
        "id": 282,
        "name": "list-check",
        "prevSize": 32,
        "code": 60014
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 367
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ListCheckIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ListCheckIcon = ({ size = 16, ...props }: ListCheckIconProps) => (
  <IcoMoon iconSet={iconSet} icon="list-check" size={size} {...props} />
);

ListCheckIcon.displayName = 'ListCheckIcon';

export default ListCheckIcon;
