import type { Meta, StoryObj } from '@storybook/react-vite';

import { TabsNav } from '@components/tabs';

import { tabsListPropDefs } from './tabs.props';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: 'DesignSystem/Tabs/TabsNav',
  component: TabsNav,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  // tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    defaultActiveKey: {
      control: 'text',
    },
    items: {
      control: 'object',
    },
    size: {
      control: 'inline-radio',
      options: tabsListPropDefs.size.values,
    },
    wrap: {
      control: 'inline-radio',
      options: tabsListPropDefs.wrap.values,
    },
    justify: {
      control: 'inline-radio',
      options: tabsListPropDefs.justify.values,
    },
    orientation: {
      control: 'inline-radio',
      options: tabsListPropDefs.orientation.values,
    },
  },
} satisfies Meta<typeof TabsNav>;

export default meta;
type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const _tabsNav: Story = {
  args: {
    defaultActiveKey: '1',
    orientation: 'vertical',
    items: [
      {
        key: '1',
        title: 'Tab 1',
        prefix: 'chevron-left',
        suffix: 'chevron-right',
        href: '#1',
      },
      {
        key: '2',
        title: 'Tab 2',
        prefix: 'chevron-left',
        suffix: 'chevron-right',
        href: '#2',
      },
      {
        key: '3',
        title: 'Tab 3',
        prefix: 'chevron-left',
        suffix: 'chevron-right',
        href: '#3',
      },
      {
        key: '4',
        title: 'Tab 4',
        prefix: 'chevron-left',
        suffix: 'chevron-right',
        href: '#4',
      },
    ],
  },
};
