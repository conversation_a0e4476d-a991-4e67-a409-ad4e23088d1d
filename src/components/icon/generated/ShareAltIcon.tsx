// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M155.223 926.11v0zM99.284 875.767v0zM924.715 875.767v0zM868.779 926.11v0zM155.223 183.223v0zM99.284 233.567v0zM384 202.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM970.667 469.333c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM733.867 938.667v-32h-443.733v64h443.733v-32zM85.333 754.347h32v-399.36h-64v399.36h32zM290.133 938.667v-32c-36.319 0-61.825-0.021-81.723-1.485-19.612-1.442-31.111-4.151-39.849-8.158l-26.677 58.176c18.643 8.546 38.756 12.113 61.833 13.811 22.791 1.677 51.049 1.655 86.417 1.655v-32zM85.333 754.347h-32c0 31.671-0.031 57.566 1.875 78.558 1.95 21.474 6.115 40.764 16.23 58.628l55.694-31.531c-3.837-6.78-6.647-15.945-8.185-32.887-1.582-17.425-1.613-39.923-1.613-72.768h-32zM155.223 926.11l13.338-29.086c-18.382-8.431-32.69-21.585-41.43-37.022l-55.694 31.531c15.804 27.913 40.658 50.005 70.446 63.667l13.339-29.090zM938.667 754.347h-32c0 32.845-0.030 55.343-1.613 72.768-1.54 16.943-4.348 26.108-8.183 32.887l55.693 31.531c10.112-17.865 14.281-37.154 16.23-58.628 1.903-20.992 1.873-46.886 1.873-78.558h-32zM733.867 938.667v32c35.366 0 63.625 0.021 86.417-1.655 23.074-1.698 43.187-5.265 61.833-13.811l-26.679-58.176c-8.734 4.006-20.233 6.716-39.846 8.158-19.9 1.463-45.406 1.485-81.724 1.485v32zM924.715 875.767l-27.844-15.765c-8.742 15.437-23.049 28.591-41.434 37.022l26.679 58.176c29.786-13.662 54.643-35.755 70.447-63.667l-27.849-15.765zM290.133 170.667v-32c-35.368 0-63.626-0.020-86.417 1.655-23.076 1.697-43.189 5.264-61.833 13.813l26.677 58.175c8.738-4.007 20.237-6.718 39.849-8.16 19.898-1.463 45.404-1.484 81.723-1.484v-32zM85.333 354.987h32c0-32.845 0.031-55.342 1.613-72.77 1.538-16.943 4.348-26.107 8.185-32.884l-55.694-31.53c-10.114 17.865-14.28 37.152-16.23 58.629-1.905 20.991-1.875 46.883-1.875 78.556h32zM155.223 183.223l-13.339-29.087c-29.788 13.66-54.642 35.75-70.446 63.666l55.694 31.53c8.739-15.437 23.048-28.593 41.43-37.022l-13.338-29.088zM290.133 170.667v32h93.867v-64h-93.867v32zM938.667 469.333h-32v285.013h64v-285.013h-32z",
          "M725.103 91.41v68.59c0 3.586-2.944 6.446-6.366 6.368-38.537-0.878-240.567 10.408-291.989 338.349-1.092 6.967 9.050 10.185 12.267 3.972 86.178-166.332 249.502-168.298 280.823-167.073 2.999 0.117 5.265 2.714 5.265 5.856v69.734c0 5.034 5.521 7.877 9.331 4.805l201.975-162.897c3.008-2.428 3.008-7.182 0-9.61l-201.975-162.898c-3.81-3.072-9.331-0.229-9.331 4.805z"
        ],
        "attrs": [
          {},
          {
            "fill": "none",
            "stroke": "rgb(0, 0, 0)",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": true,
        "grid": 0,
        "tags": [
          "share-alt"
        ]
      },
      "attrs": [
        {},
        {
          "fill": "none",
          "stroke": "rgb(0, 0, 0)",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1879,
        "id": 141,
        "name": "share-alt",
        "prevSize": 32,
        "code": 60155
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 508
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ShareAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ShareAltIcon = ({ size = 16, ...props }: ShareAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="share-alt" size={size} {...props} />
);

ShareAltIcon.displayName = 'ShareAltIcon';

export default ShareAltIcon;
