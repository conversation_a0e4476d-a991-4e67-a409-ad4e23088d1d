// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 906.667c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM286.815 290.216c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM334.222 354.216c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM286.815 456.183c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM334.222 520.183c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM452.74 290.216c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM500.147 354.216c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM452.74 456.183c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM500.147 520.183c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM938.667 970.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM260.935 130.926v0zM526.029 86.733v0zM545.51 85.52v0zM241.453 132.939v0zM85.333 938.667v32h47.407v-64h-47.407v32zM132.741 938.667v32h165.926v-64h-165.926v32zM298.667 938.667v32h189.628v-64h-189.628v32zM488.294 938.667v32h165.926v-64h-165.926v32zM298.667 938.667h32v-189.679h-64v189.679h32zM369.778 677.862v32h47.407v-64h-47.407v32zM488.294 748.988h-32v189.679h64v-189.679h-32zM417.185 677.862v32c21.594 0 39.109 17.51 39.109 39.125h64c0-56.947-46.157-103.125-103.109-103.125v32zM298.667 748.988h32c0-21.615 17.518-39.125 39.111-39.125v-64c-56.954 0-103.111 46.178-103.111 103.125h32zM286.815 322.216v32h47.407v-64h-47.407v32zM286.815 488.183v32h47.407v-64h-47.407v32zM452.74 322.216v32h47.407v-64h-47.407v32zM452.74 488.183v32h47.407v-64h-47.407v32zM654.221 938.667v32h284.446v-64h-284.446v32zM914.961 583.023h-32v94.839h64v-94.839h-32zM772.74 677.862h32v-94.839h-64v94.839h32zM843.853 938.667h32v-189.679h-64v189.679h32zM772.74 677.862h-32c0 56.947 46.157 103.125 103.113 103.125v-64c-21.594 0-39.113-17.51-39.113-39.125h-32zM914.961 677.862h-32c0 21.615-17.515 39.125-39.108 39.125v64c56.951 0 103.108-46.178 103.108-103.125h-32zM843.853 511.893v32c21.594 0 39.108 17.51 39.108 39.13h64c0-56.951-46.157-103.13-103.108-103.13v32zM843.853 511.893v-32c-56.956 0-103.113 46.178-103.113 103.13h64c0-21.619 17.519-39.13 39.113-39.13v-32zM132.741 938.667h32v-687.579h-64v687.579h32zM260.935 130.926l5.262 31.564 265.093-44.193-10.522-63.129-265.095 44.193 5.262 31.564zM545.51 85.52l-2.611 31.893c44.39 3.637 79.322 40.876 79.322 86.255h64c0-78.96-60.757-143.705-138.099-150.041l-2.611 31.893zM132.741 251.087h32c0-45.379 34.931-82.618 79.325-86.255l-2.612-31.893-2.613-31.893c-77.342 6.336-138.1 71.081-138.1 150.041h32zM526.029 86.733l5.261 31.564c5.299-0.883 8.879-1.108 11.61-0.884l5.222-63.786c-10.283-0.843-19.772 0.278-27.354 1.542l5.261 31.564zM260.935 130.926l-5.262-31.564c-4.743 0.791-9.424 1.077-16.832 1.684l2.613 31.893 2.612 31.893c5.604-0.459 13.996-0.986 22.131-2.342l-5.262-31.564zM132.741 938.667v32h521.48v-64h-521.48v32zM654.221 203.668h-32v734.999h64v-734.999h-32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "apartment"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1402,
        "id": 618,
        "name": "apartment",
        "prevSize": 32,
        "code": 59678
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 31
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ApartmentIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ApartmentIcon = ({ size = 16, ...props }: ApartmentIconProps) => (
  <IcoMoon iconSet={iconSet} icon="apartment" size={size} {...props} />
);

ApartmentIcon.displayName = 'ApartmentIcon';

export default ApartmentIcon;
