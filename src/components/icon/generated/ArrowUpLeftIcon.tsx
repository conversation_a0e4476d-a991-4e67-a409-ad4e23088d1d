// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M195.317 655.142l-3.317-453.138c0.258-2.505 1.506-4.877 3.317-6.688M655.142 195.317l-453.138-3.317c-2.505 0.258-4.877 1.506-6.688 3.317M195.317 195.317l636.683 636.683"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "arrow-up-left"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1428,
        "id": 592,
        "name": "arrow-up-left",
        "prevSize": 32,
        "code": 59704
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 57
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ArrowUpLeftIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ArrowUpLeftIcon = ({ size = 16, ...props }: ArrowUpLeftIconProps) => (
  <IcoMoon iconSet={iconSet} icon="arrow-up-left" size={size} {...props} />
);

ArrowUpLeftIcon.displayName = 'ArrowUpLeftIcon';

export default ArrowUpLeftIcon;
