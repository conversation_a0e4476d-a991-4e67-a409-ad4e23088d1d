import * as BaseCollapsible from '@radix-ui/react-collapsible';
import clsx from 'clsx';
import * as React from 'react';

import { collapsibleContentPropDefs, collapsibleRootPropDefs } from '@components/collapsible/collapsible.props';
import { type ComponentPropsWithout, type RemovedProps, extractProps, requireReactElement } from '@helpers';
import { type GetPropDefTypes, type MarginProps, marginPropDefs } from '@props';

type CollapsibleRootElement = React.ComponentRef<typeof BaseCollapsible.Root>;
type CollapsibleRootOwnProps = GetPropDefTypes<typeof collapsibleRootPropDefs>;
interface CollapsibleRootProps
  extends ComponentPropsWithout<typeof BaseCollapsible.Root, RemovedProps>,
    MarginProps,
    CollapsibleRootOwnProps {}
const CollapsibleRoot = React.forwardRef<CollapsibleRootElement, CollapsibleRootProps>((props, forwardedRef) => {
  const { className, ...rootProps } = extractProps(props, collapsibleRootPropDefs, marginPropDefs);

  return <BaseCollapsible.Root ref={forwardedRef} {...rootProps} className={clsx('rt-CollapsibleRoot', className)} />;
});
CollapsibleRoot.displayName = 'Collapsible.Root';

type CollapsibleTriggerElement = React.ComponentRef<typeof BaseCollapsible.Trigger>;
type CollapsibleTriggerProps = ComponentPropsWithout<
  typeof BaseCollapsible.Trigger,
  'defaultChecked' | 'defaultValue' | 'color'
>;
const CollapsibleTrigger = React.forwardRef<CollapsibleTriggerElement, CollapsibleTriggerProps>(
  ({ children, className, ...props }, forwardedRef) => (
    <BaseCollapsible.Trigger ref={forwardedRef} className={clsx('rt-CollapsibleTrigger', className)} {...props}>
      {requireReactElement(children)}
    </BaseCollapsible.Trigger>
  ),
);
CollapsibleTrigger.displayName = 'Collapsible.Trigger';

type CollapsibleContentElement = React.ComponentRef<typeof BaseCollapsible.Content>;
type CollapsibleContentOwnProps = GetPropDefTypes<typeof collapsibleContentPropDefs>;
interface CollapsibleContentProps
  extends ComponentPropsWithout<typeof BaseCollapsible.Content, RemovedProps>,
    MarginProps,
    CollapsibleContentOwnProps {}
const CollapsibleContent = React.forwardRef<CollapsibleContentElement, CollapsibleContentProps>(
  (props, forwardedRef) => {
    const { className, children, ...contentProps } = extractProps(props, collapsibleContentPropDefs, marginPropDefs);

    return (
      <BaseCollapsible.Content
        ref={forwardedRef}
        {...contentProps}
        className={clsx('rt-CollapsibleContent', className)}
      >
        {children}
      </BaseCollapsible.Content>
    );
  },
);
CollapsibleContent.displayName = 'Collapsible.Content';

export { CollapsibleContent as Content, CollapsibleRoot as Root, CollapsibleTrigger as Trigger };

export type {
  CollapsibleContentProps as ContentProps,
  CollapsibleRootProps as RootProps,
  CollapsibleTriggerProps as TriggerProps,
};
