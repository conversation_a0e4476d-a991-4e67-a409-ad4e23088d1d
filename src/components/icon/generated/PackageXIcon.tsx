// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M707.661 707.661l60.339 60.339M768 768l60.339 60.339M768 768l60.339-60.339M768 768l-60.339 60.339M491.281 933.218l-341.335-192.397c-13.545-7.637-21.946-22.123-21.946-37.845v-381.953c0-8.023 2.188-15.723 6.13-22.356M889.869 298.667c3.942 6.633 6.131 14.333 6.131 22.356v169.644M889.869 298.667c-3.78-6.365-9.182-11.747-15.817-15.486l-341.333-192.399c-12.885-7.264-28.553-7.264-41.438 0l-341.335 192.399c-6.633 3.739-12.033 9.121-15.816 15.486M889.869 298.667l-346.406 195.571c-19.524 11.025-43.401 11.025-62.925 0l-346.408-195.571M938.667 768c0 94.255-76.412 170.667-170.667 170.667s-170.667-76.412-170.667-170.667c0-94.255 76.412-170.667 170.667-170.667s170.667 76.412 170.667 170.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "package-x"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1806,
        "id": 214,
        "name": "package-x",
        "prevSize": 32,
        "code": 60082
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 435
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface PackageXIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const PackageXIcon = ({ size = 16, ...props }: PackageXIconProps) => (
  <IcoMoon iconSet={iconSet} icon="package-x" size={size} {...props} />
);

PackageXIcon.displayName = 'PackageXIcon';

export default PackageXIcon;
