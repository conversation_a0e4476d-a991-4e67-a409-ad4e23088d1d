// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M197.889 924.356v0zM141.951 866.987v0zM826.112 924.356v0zM882.048 866.987v0zM826.112 165.283v0zM882.048 222.656v0zM197.889 165.283v0zM141.951 222.656v0zM373.333 85.333c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM309.333 216.615c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM714.667 85.333c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM650.667 216.615c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM332.8 150.974v32h358.4v-64h-358.4v32zM691.2 938.667v-32h-358.4v64h358.4v-32zM128 728.614h32v-358.836h-64v358.836h32zM128 369.778h32v-8.752h-64v8.752h32zM896 361.026h-32v8.752h64v-8.752h-32zM896 369.778h-32v358.836h64v-358.836h-32zM128 369.778v32h768v-64h-768v32zM332.8 938.667v-32c-36.385 0-61.7-0.026-81.398-1.677-19.3-1.617-30.335-4.625-38.692-8.994l-29.642 56.721c19.024 9.946 39.601 14.089 62.99 16.051 22.99 1.924 51.44 1.899 86.742 1.899v-32zM128 728.614h-32c0 36.25-0.023 65.31 1.845 88.768 1.897 23.808 5.894 44.608 15.448 63.842l57.317-28.476c-4.397-8.853-7.375-20.471-8.967-40.448-1.619-20.326-1.643-46.409-1.643-83.686h-32zM197.889 924.356l14.821-28.361c-18.001-9.408-32.786-24.499-42.101-43.247l-57.317 28.476c15.229 30.652 39.607 55.727 69.775 71.492l14.821-28.361zM691.2 938.667v32c35.302 0 63.753 0.026 86.741-1.899 23.39-1.963 43.968-6.106 62.989-16.051l-29.641-56.721c-8.358 4.369-19.392 7.377-38.69 8.994-19.699 1.651-45.013 1.677-81.399 1.677v32zM896 728.614h-32c0 37.278-0.026 63.36-1.643 83.686-1.591 19.977-4.57 31.595-8.969 40.448l57.318 28.476c9.553-19.234 13.551-40.034 15.45-63.842 1.869-23.458 1.843-52.518 1.843-88.768h-32zM826.112 924.356l14.818 28.361c30.17-15.765 54.549-40.841 69.777-71.492l-57.318-28.476c-9.314 18.748-24.098 33.839-42.099 43.247l14.822 28.361zM691.2 150.974v32c36.386 0 61.7 0.026 81.399 1.677 19.298 1.617 30.332 4.626 38.69 8.993l29.641-56.721c-19.021-9.942-39.599-14.088-62.989-16.048-22.989-1.926-51.439-1.9-86.741-1.9v32zM896 361.026h32c0-36.247 0.026-65.31-1.843-88.767-1.899-23.808-5.897-44.608-15.45-63.84l-57.318 28.474c4.399 8.851 7.377 20.473 8.969 40.448 1.617 20.326 1.643 46.407 1.643 83.685h32zM826.112 165.283l-14.822 28.361c18.001 9.408 32.785 24.5 42.099 43.249l57.318-28.474c-15.228-30.656-39.607-55.73-69.777-71.496l-14.818 28.361zM332.8 150.974v-32c-35.302 0-63.752-0.026-86.742 1.9-23.389 1.96-43.966 6.106-62.99 16.048l29.642 56.721c8.357-4.367 19.392-7.375 38.692-8.993 19.698-1.651 45.013-1.677 81.398-1.677v-32zM128 361.026h32c0-37.277 0.023-63.359 1.643-83.685 1.591-19.976 4.57-31.598 8.967-40.448l-57.317-28.474c-9.554 19.232-13.551 40.032-15.448 63.84-1.868 23.457-1.845 52.52-1.845 88.767h32zM197.889 165.283l-14.821-28.361c-30.168 15.766-54.546 40.841-69.775 71.496l57.317 28.474c9.315-18.749 24.099-33.841 42.101-43.249l-14.821-28.361zM341.333 85.333h-32v131.282h64v-131.282h-32zM682.667 85.333h-32v131.282h64v-131.282h-32z",
          "M341.333 543.454c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM341.76 607.454c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM341.333 696.614c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM341.76 760.614c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM512 543.454c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM512.427 607.454c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM512 696.614c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM512.427 760.614c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM682.667 543.454c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM683.093 607.454c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM682.667 696.614c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM683.093 760.614c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM341.333 575.454v32h0.427v-64h-0.427v32zM341.333 728.614v32h0.427v-64h-0.427v32zM512 575.454v32h0.427v-64h-0.427v32zM512 728.614v32h0.427v-64h-0.427v32zM682.667 575.454v32h0.427v-64h-0.427v32zM682.667 728.614v32h0.427v-64h-0.427v32z"
        ],
        "attrs": [
          {},
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "calendar-01"
        ]
      },
      "attrs": [
        {},
        {}
      ],
      "properties": {
        "order": 1502,
        "id": 518,
        "name": "calendar-01",
        "prevSize": 32,
        "code": 59778
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 131
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface Calendar01IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const Calendar01Icon = ({ size = 16, ...props }: Calendar01IconProps) => (
  <IcoMoon iconSet={iconSet} icon="calendar-01" size={size} {...props} />
);

Calendar01Icon.displayName = 'Calendar01Icon';

export default Calendar01Icon;
