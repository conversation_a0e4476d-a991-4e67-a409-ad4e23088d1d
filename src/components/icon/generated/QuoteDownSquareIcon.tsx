// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M363.137 559.795v0zM343.616 676.207v0zM353.697 680.239v0zM428.57 571.597v0zM469.052 429.402v0zM661.803 559.795v0zM642.283 676.207v0zM652.365 680.239v0zM727.236 571.597v0zM767.718 429.402v0zM155.223 924.715v0zM99.284 868.779v0zM924.715 868.779v0zM868.779 924.715v0zM868.779 99.284v0zM924.715 155.223v0zM155.223 99.284v0zM99.284 155.223v0zM256 438.131h-32v11.379h64v-11.379h-32zM375.985 341.333v-32h-22.592v64h22.592v-32zM363.137 559.795l-31.559-5.291-19.521 116.407 63.119 10.586 19.521-116.412-31.559-5.291zM353.697 680.239l26.349 18.159 74.875-108.642-52.698-36.318-74.874 108.642 26.349 18.159zM343.616 676.207l-31.559-5.295c-6.772 40.384 45.652 59.9 67.989 27.486l-52.698-36.318c15.604-22.639 52.64-9.28 47.827 19.418l-31.56-5.291zM341.943 534.929v32c-6.277 0-11.494-5.7-10.366-12.425l63.119 10.581c5.494-32.759-19.922-62.157-52.753-62.157v32zM469.052 429.402l31.962-1.583c-3.29-66.529-58.458-118.485-125.028-118.485v64c32.797 0 59.518 25.534 61.105 57.647l31.962-1.579zM469.052 429.402l-31.962 1.579c2.15 43.49-10.086 86.498-34.868 122.458l52.698 36.318c32.738-47.505 48.939-104.384 46.093-161.937l-31.962 1.583zM256 449.51h-32c0 65.033 52.99 117.419 117.943 117.419v-64c-29.977 0-53.943-24.098-53.943-53.419h-32zM256 438.131h32c0-35.602 29.092-64.798 65.393-64.798v-64c-71.277 0-129.393 57.481-129.393 128.798h32zM554.667 438.131h-32v11.379h64v-11.379h-32zM674.654 341.333v-32h-22.592v64h22.592v-32zM661.803 559.795l-31.561-5.291-19.52 116.407 63.121 10.586 19.52-116.412-31.561-5.291zM652.365 680.239l26.347 18.159 74.876-108.642-52.698-36.318-74.876 108.642 26.351 18.159zM642.283 676.207l-31.561-5.295c-6.771 40.384 45.653 59.9 67.989 27.486l-52.698-36.318c15.603-22.639 52.642-9.28 47.829 19.418l-31.561-5.291zM640.61 534.929v32c-6.276 0-11.494-5.7-10.368-12.425l63.121 10.581c5.491-32.759-19.921-62.157-52.753-62.157v32zM767.718 429.402l31.962-1.583c-3.29-66.529-58.458-118.485-125.026-118.485v64c32.794 0 59.516 25.534 61.103 57.647l31.962-1.579zM767.718 429.402l-31.962 1.579c2.15 43.49-10.086 86.498-34.867 122.458l52.698 36.318c32.738-47.505 48.939-104.384 46.093-161.937l-31.962 1.583zM554.667 449.51h-32c0 65.033 52.992 117.419 117.943 117.419v-64c-29.978 0-53.943-24.098-53.943-53.419h-32zM554.667 438.131h32c0-35.602 29.094-64.798 65.395-64.798v-64c-71.279 0-129.395 57.48-129.395 128.798h32zM290.133 85.333v32h443.733v-64h-443.733v32zM938.667 290.133h-32v443.733h64v-443.733h-32zM733.867 938.667v-32h-443.733v64h443.733v-32zM85.333 733.867h32v-443.733h-64v443.733h32zM290.133 938.667v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM85.333 733.867h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM155.223 924.715l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM938.667 733.867h-32c0 36.373-0.026 61.726-1.638 81.463-1.583 19.366-4.531 30.494-8.823 38.921l57.024 29.056c9.655-18.957 13.683-39.441 15.586-62.763 1.877-22.95 1.852-51.362 1.852-86.677h-32zM733.867 938.667v32c35.315 0 63.727 0.026 86.677-1.852 23.322-1.903 43.806-5.931 62.763-15.586l-29.056-57.024c-8.427 4.292-19.554 7.241-38.921 8.823-19.738 1.613-45.090 1.638-81.463 1.638v32zM924.715 868.779l-28.51-14.528c-9.207 18.061-23.893 32.747-41.954 41.954l29.056 57.024c30.106-15.343 54.579-39.817 69.922-69.922l-28.514-14.528zM733.867 85.333v32c36.373 0 61.726 0.025 81.463 1.638 19.366 1.582 30.494 4.532 38.921 8.826l29.056-57.024c-18.957-9.658-39.441-13.683-62.763-15.589-22.95-1.875-51.362-1.85-86.677-1.85v32zM938.667 290.133h32c0-35.315 0.026-63.727-1.852-86.676-1.903-23.323-5.931-43.809-15.586-62.762l-57.024 29.055c4.292 8.428 7.241 19.553 8.823 38.919 1.613 19.739 1.638 45.093 1.638 81.464h32zM868.779 99.284l-14.528 28.512c18.061 9.204 32.747 23.89 41.954 41.953l57.024-29.055c-15.343-30.106-39.817-54.583-69.922-69.923l-14.528 28.512zM290.133 85.333v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM85.333 290.133h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM155.223 99.284l-14.528-28.512c-30.106 15.34-54.583 39.817-69.923 69.923l57.024 29.055c9.204-18.063 23.89-32.75 41.953-41.953l-14.528-28.512z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "quote-down-square"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1839,
        "id": 181,
        "name": "quote-down-square",
        "prevSize": 32,
        "code": 60115
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 468
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface QuoteDownSquareIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const QuoteDownSquareIcon = ({ size = 16, ...props }: QuoteDownSquareIconProps) => (
  <IcoMoon iconSet={iconSet} icon="quote-down-square" size={size} {...props} />
);

QuoteDownSquareIcon.displayName = 'QuoteDownSquareIcon';

export default QuoteDownSquareIcon;
