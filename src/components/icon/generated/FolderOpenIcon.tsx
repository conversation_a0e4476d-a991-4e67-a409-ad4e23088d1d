// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M832 384h-562.665c-19.342 0-29.013 0-37.059 3.331-7.099 2.939-13.274 7.689-17.889 13.763-5.231 6.883-7.576 16.128-12.268 34.615l-100.192 394.889M832 384h30.639c30.246 0 45.372 0 55.714 6.175 9.067 5.413 15.804 13.914 18.931 23.892 3.571 11.386-0.094 25.843-7.433 54.758l-69.022 272.047c-14.076 55.467-21.111 83.2-36.804 103.846-13.845 18.219-32.371 32.474-53.666 41.289-24.137 9.993-53.15 9.993-111.177 9.993h-365.999c-72.754 0-109.132 0-136.92-13.952-22.907-11.499-41.801-29.419-54.336-51.452M832 384v-51.2c0-54.571-44.591-98.133-99.977-98.133h-84.403c-17.054 0-25.583 0-33.813-1.041-18.953-2.396-37.137-8.883-53.252-18.997-7.002-4.394-13.551-9.772-26.654-20.529l-22.69-18.631c-21.41-17.581-32.115-26.371-44.126-32.628-10.658-5.551-22.046-9.613-33.835-12.070-13.295-2.77-27.23-2.77-55.101-2.77h-84.966c-72.754 0-109.132 0-136.92 13.951-24.443 12.272-44.317 31.853-56.771 55.938-14.159 27.38-14.159 63.224-14.159 134.911v358.4c0 71.689 0 107.529 14.159 134.912 0.783 1.51 1.594 3.008 2.435 4.484"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "folder-open"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1668,
        "id": 352,
        "name": "folder-open",
        "prevSize": 32,
        "code": 59944
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 297
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FolderOpenIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FolderOpenIcon = ({ size = 16, ...props }: FolderOpenIconProps) => (
  <IcoMoon iconSet={iconSet} icon="folder-open" size={size} {...props} />
);

FolderOpenIcon.displayName = 'FolderOpenIcon';

export default FolderOpenIcon;
