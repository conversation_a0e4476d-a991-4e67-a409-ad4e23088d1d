// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 311.835h-160.626c-48.514 0-87.843 42.522-87.843 94.975v57.783M512 311.835h160.627c48.516 0 87.846 42.522 87.846 94.975v57.783M512 311.835v626.831M512 938.667c-111.791 0-206.341-79.825-237.548-189.628M512 938.667c111.795 0 206.345-79.825 237.551-189.628M602.355 161.704c-25.984-18.318-57.020-28.963-90.355-28.963-33.331 0-64.367 10.645-90.351 28.963M602.355 161.704c45.329 31.956 75.294 87.26 75.294 150.131M602.355 161.704l70.272-76.371M346.355 311.835c0-62.871 29.964-118.176 75.294-150.131M421.649 161.704l-75.294-76.371M263.531 464.593l-127.985-57.783M263.531 464.593v142.221M263.531 606.814v63.211c0 27.499 3.821 54.033 10.921 79.014M263.531 606.814h-135.531M274.452 749.039l-123.862 71.108M760.474 464.593l127.949-57.783M760.474 464.593l-0.004 142.221M760.469 606.814l0.004 63.211c0 27.499-3.823 54.033-10.923 79.014M760.469 606.814h135.531M749.551 749.039l123.861 71.108"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bug-beetle"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1493,
        "id": 527,
        "name": "bug-beetle",
        "prevSize": 32,
        "code": 59769
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 122
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BugBeetleIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BugBeetleIcon = ({ size = 16, ...props }: BugBeetleIconProps) => (
  <IcoMoon iconSet={iconSet} icon="bug-beetle" size={size} {...props} />
);

BugBeetleIcon.displayName = 'BugBeetleIcon';

export default BugBeetleIcon;
