import type { <PERSON>a, StoryObj } from '@storybook/react-vite';

import { Button } from '@components/button';
import { Tooltip } from '@components/tooltip';

const meta: Meta<typeof Tooltip> = {
  title: 'DesignSystem/Tooltip',
  component: Tooltip,
  argTypes: {
    content: { control: 'text' },
    width: { control: 'text' },
    minWidth: { control: 'text' },
    maxWidth: { control: 'text' },
    delayDuration: { control: 'number' },
    disableHoverableContent: { control: 'boolean' },
    forceMount: { control: 'boolean' },
  },
  parameters: {
    layout: 'centered',
  },
};

export default meta;

type Story = StoryObj<typeof Tooltip>;

export const _tooltip: Story = {
  args: {
    content: 'Tooltip with full configuration.',
    width: 'auto',
    minWidth: 'auto',
    maxWidth: 'auto',
    delayDuration: 100,
    disableHoverableContent: false,
    forceMount: undefined,
  },
  render: (args) => (
    <Tooltip {...args}>
      <Button>Hover to test full props</Button>
    </Tooltip>
  ),
};
