// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M373.333 435.2c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM714.667 563.2c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM359.015 727.339c11.322 13.568 31.502 15.39 45.071 4.070 13.57-11.324 15.392-31.505 4.069-45.073l-49.14 41.003zM650.517 428.305c0.764 17.655 15.697 31.347 33.357 30.583 17.655-0.764 31.347-15.697 30.583-33.354l-63.94 2.771zM500.023 735.458c-17.604-1.591-33.165 11.383-34.756 28.983-1.596 17.6 11.379 33.165 28.979 34.756l5.777-63.74zM369.611 284.897c-12.317 12.674-12.029 32.933 0.645 45.25s32.933 12.029 45.251-0.645l-45.895-44.605zM332.8 970.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM155.223 924.715v0zM117.333 691.2c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM99.284 868.779v0zM970.667 691.2c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM924.715 868.779v0zM691.2 906.667c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM868.779 924.715v0zM691.2 53.333c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM868.779 99.284v0zM906.667 332.8c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM924.715 155.223v0zM332.8 117.333c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM155.223 99.284v0zM53.333 332.8c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM99.284 155.223v0zM341.333 588.8h32v-153.6h-64v153.6h32zM682.667 588.8h-32c0 82.786-63.535 147.2-138.667 147.2v64c113.382 0 202.667-96.047 202.667-211.2h-32zM682.667 563.2h-32v25.6h64v-25.6h-32zM383.585 706.837l24.57-20.501c-21.579-25.86-34.822-59.934-34.822-97.536h-64c0 52.817 18.647 101.346 49.681 138.539l24.57-20.501zM512 256v32c73.041 0 135.074 60.824 138.517 140.305l63.94-2.771c-4.8-110.771-92.275-201.534-202.458-201.534v32zM512 768v-32c-4.045 0-8.038-0.183-11.977-0.542l-5.777 63.74c5.858 0.533 11.78 0.802 17.754 0.802v-32zM512 256v-32c-55.727 0-105.999 23.455-142.389 60.897l45.895 44.605c25.172-25.902 59.216-41.503 96.494-41.503v-32zM576 465.455h-32v93.090h64v-93.090h-32zM448 558.545h32v-93.090h-64v93.090h32zM512 628.365v-32c-15.108 0-32-14.255-32-37.82h-64c0 53.555 40.418 101.82 96 101.82v-32zM576 558.545h-32c0 23.565-16.892 37.82-32 37.82v64c55.582 0 96-48.265 96-101.82h-32zM512 395.636v31.999c15.108 0 32 14.255 32 37.82h64c0-53.555-40.418-101.818-96-101.818v32zM512 395.636v-32c-55.582 0-96 48.263-96 101.818h64c0-23.565 16.892-37.82 32-37.82v-31.999zM332.8 938.667v-32c-36.080 0-72.695-0.009-103.663-1.698-15.462-0.841-28.867-2.069-39.67-3.789-11.427-1.818-17.446-3.819-19.717-4.975l-29.055 57.024c11.419 5.815 25.482 9.050 38.716 11.153 13.859 2.206 29.696 3.593 46.245 4.493 33.054 1.801 71.538 1.792 107.144 1.792v-32zM85.333 691.2h-32c0 35.605-0.009 74.091 1.791 107.145 0.902 16.55 2.287 32.384 4.492 46.242 2.106 13.235 5.337 27.298 11.156 38.72l57.024-29.056c-1.157-2.27-3.157-8.29-4.975-19.716-1.719-10.803-2.95-24.209-3.792-39.671-1.687-30.967-1.696-67.584-1.696-103.663h-32zM155.223 924.715l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM938.667 691.2h-32c0 36.079-0.009 72.695-1.698 103.663-0.841 15.462-2.069 28.868-3.789 39.671-1.818 11.426-3.819 17.446-4.975 19.716l57.024 29.056c5.815-11.422 9.050-25.485 11.153-38.72 2.206-13.858 3.593-29.692 4.493-46.242 1.801-33.054 1.792-71.539 1.792-107.145h-32zM691.2 938.667v32c35.605 0 74.091 0.009 107.145-1.792 16.55-0.9 32.384-2.287 46.242-4.493 13.235-2.103 27.298-5.338 38.72-11.153l-29.056-57.024c-2.27 1.156-8.29 3.157-19.716 4.975-10.803 1.719-24.209 2.948-39.671 3.789-30.967 1.69-67.584 1.698-103.663 1.698v32zM924.715 868.779l-28.51-14.528c-9.207 18.061-23.893 32.747-41.954 41.954l29.056 57.024c30.106-15.343 54.579-39.817 69.922-69.922l-28.514-14.528zM691.2 85.333v32c36.079 0 72.695 0.009 103.663 1.696 15.462 0.842 28.868 2.073 39.671 3.792 11.426 1.818 17.446 3.818 19.716 4.975l29.056-57.024c-11.422-5.819-25.485-9.050-38.72-11.156-13.858-2.205-29.692-3.59-46.242-4.492-33.054-1.801-71.539-1.791-107.145-1.791v32zM938.667 332.8h32c0-35.606 0.009-74.090-1.792-107.144-0.9-16.549-2.287-32.386-4.493-46.245-2.103-13.234-5.338-27.297-11.153-38.716l-57.024 29.055c1.156 2.271 3.157 8.29 4.975 19.717 1.719 10.803 2.948 24.208 3.789 39.67 1.69 30.968 1.698 67.583 1.698 103.663h32zM868.779 99.284l-14.528 28.512c18.061 9.204 32.747 23.89 41.954 41.953l57.024-29.055c-15.343-30.106-39.817-54.583-69.922-69.923l-14.528 28.512zM332.8 85.333v-32c-35.606 0-74.090-0.009-107.144 1.791-16.549 0.902-32.386 2.287-46.245 4.492-13.234 2.106-27.297 5.337-38.716 11.156l29.055 57.024c2.271-1.157 8.29-3.157 19.717-4.975 10.803-1.719 24.208-2.95 39.67-3.792 30.968-1.687 67.583-1.696 103.663-1.696v-32zM85.333 332.8h32c0-36.080 0.009-72.695 1.696-103.663 0.842-15.462 2.073-28.867 3.792-39.67 1.818-11.427 3.818-17.446 4.975-19.717l-57.024-29.055c-5.819 11.419-9.050 25.482-11.156 38.716-2.205 13.859-3.59 29.696-4.492 46.245-1.801 33.054-1.791 71.538-1.791 107.144h32zM155.223 99.284l-14.528-28.512c-30.106 15.34-54.583 39.817-69.923 69.923l57.024 29.055c9.204-18.063 23.89-32.75 41.953-41.953l-14.528-28.512z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "finger-scan"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1654,
        "id": 366,
        "name": "finger-scan",
        "prevSize": 32,
        "code": 59930
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 283
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FingerScanIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FingerScanIcon = ({ size = 16, ...props }: FingerScanIconProps) => (
  <IcoMoon iconSet={iconSet} icon="finger-scan" size={size} {...props} />
);

FingerScanIcon.displayName = 'FingerScanIcon';

export default FingerScanIcon;
