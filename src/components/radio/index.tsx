'use client';

import clsx from 'clsx';
import { composeEventHandlers, composeRefs } from 'radix-ui/internal';
import * as React from 'react';

import { Flex } from '@components/flex';
import { radioPropDefs } from '@components/radio/radio.props';
import { Text } from '@components/text';
import { type ComponentPropsWithout, type NotInputRadioAttributes, extractProps } from '@helpers';
import { type GetPropDefTypes, type MarginProps, marginPropDefs } from '@props';

type RadioElement = React.ComponentRef<'input'>;
type RadioOwnProps = GetPropDefTypes<typeof radioPropDefs> & {
  value: string;
  onValueChange?: (value: string) => void;
};
type RadioInputProps = ComponentPropsWithout<'input', NotInputRadioAttributes | 'color' | 'defaultValue' | 'value'>;
interface RadioProps extends RadioInputProps, MarginProps, RadioOwnProps {
  label?: React.ReactNode;
  caption?: React.ReactNode;
  labelAlign?: 'left' | 'right';
}

const Radio = React.forwardRef<RadioElement, RadioProps>((props, forwardedRef) => {
  const ref = React.useRef<RadioElement>(null);
  const {
    label,
    labelAlign = 'right',
    caption,
    className,
    color,
    onChange,
    onValueChange,
    ...radioProps
  } = extractProps(props, radioPropDefs, marginPropDefs);

  const renderRadio = () => {
    return (
      <input
        type="radio"
        data-accent-color={color}
        {...radioProps}
        onChange={composeEventHandlers(onChange, (event) => onValueChange?.(event.currentTarget.value))}
        ref={composeRefs(ref, forwardedRef)}
        className={clsx('rt-reset', 'rt-BaseRadioRoot', 'rt-RadioRoot', className)}
      />
    );
  };

  return label ? (
    <Flex
      gap="3"
      align={caption ? 'start' : 'center'}
      justify={labelAlign === 'left' ? 'end' : 'start'}
      direction={labelAlign === 'left' ? 'row-reverse' : 'row'}
    >
      {renderRadio()}
      <Flex gap="1" direction="column" align={labelAlign === 'left' ? 'end' : 'start'}>
        <Text as="label" size={props.size} className="rt-radio-label">
          {label}
        </Text>
        {caption && (
          <Text className="rt-radio-caption" size={props.size} color="secondary">
            {caption}
          </Text>
        )}
      </Flex>
    </Flex>
  ) : (
    renderRadio()
  );
});
Radio.displayName = 'Radio';

export { Radio };
export type { RadioProps };
