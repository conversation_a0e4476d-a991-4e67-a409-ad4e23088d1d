// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M938.667 492.843c0-71.688 0-107.532-13.952-134.912-12.271-24.085-31.851-43.666-55.936-55.938-27.383-13.951-63.223-13.951-134.912-13.951h-84.787c-26.718 0-40.077 0-52.203-3.683-10.735-3.261-20.723-8.606-29.389-15.731-9.792-8.048-17.199-19.165-32.017-41.398l-5.342-8.012c-7.091-10.639-13.052-19.581-18.355-27.218M511.774 192c-11.328-16.303-19.669-26.658-29.67-34.879-13.001-10.687-27.981-18.705-44.083-23.596-18.191-5.524-38.23-5.524-78.308-5.524h-69.579c-71.687 0-107.53 0-134.911 13.951-24.085 12.272-43.666 31.853-55.938 55.938-13.951 27.38-13.951 63.224-13.951 134.911v358.4c0 71.689 0 107.529 13.951 134.912 12.272 24.085 31.853 43.665 55.938 55.936 27.38 13.952 63.224 13.952 134.911 13.952h243.2M511.774 192h298.893c70.694 0 128 57.308 128 128v128M699.477 658.761l-93.935 18.428c-7.829 1.536-10.927 11.622-5.431 17.707l65.536 72.619c4.181 4.634 6.153 11.008 5.367 17.348l-12.284 99.162c-1.028 8.299 7.053 14.515 14.229 10.948l86.481-43.004c5.419-2.697 11.699-2.697 17.118 0l86.481 43.004c7.177 3.567 15.258-2.65 14.229-10.948l-12.284-99.162c-0.785-6.34 1.186-12.715 5.367-17.348l65.536-72.619c5.495-6.084 2.398-16.171-5.431-17.707l-93.935-18.428c-5.918-1.161-11.029-5.060-13.926-10.628l-45.798-87.974c-3.814-7.322-13.781-7.322-17.596 0l-45.798 87.974c-2.897 5.568-8.009 9.468-13.926 10.628z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "folder-star"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1669,
        "id": 351,
        "name": "folder-star",
        "prevSize": 32,
        "code": 59945
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 298
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FolderStarIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FolderStarIcon = ({ size = 16, ...props }: FolderStarIconProps) => (
  <IcoMoon iconSet={iconSet} icon="folder-star" size={size} {...props} />
);

FolderStarIcon.displayName = 'FolderStarIcon';

export default FolderStarIcon;
