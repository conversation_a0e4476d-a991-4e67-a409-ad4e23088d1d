// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M376.222 209.972v0zM439.509 100.645v0zM584.495 100.646v0zM647.782 209.969v0zM128 906.667c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM896 970.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM416 583.625l31.821-3.388-39.779-373.652-63.641 6.775 39.778 373.652 31.82-3.388zM439.509 100.645l10.735 30.147c22.686-8.078 44.753-13.458 61.756-13.458v-64c-28.006 0-58.287 8.287-83.226 17.165l10.735 30.146zM512 85.333v32c17.007 0 39.070 5.381 61.76 13.459l21.47-60.292c-24.939-8.879-55.223-17.167-83.23-17.167v32zM584.495 100.646l-10.735 30.146c28.335 10.088 45.948 40.601 42.202 75.79l63.642 6.772c6.238-58.611-22.874-120.956-84.373-142.854l-10.735 30.146zM376.222 209.972l31.82-3.388c-3.747-35.191 13.867-65.705 42.202-75.793l-21.47-60.293c-61.505 21.899-90.612 84.248-84.373 142.861l31.82-3.387zM128 938.667v32h768v-64h-768v32zM647.782 209.969l-31.821-3.386-39.761 373.654 63.642 6.771 39.761-373.653-31.821-3.386zM608.021 583.625l-31.821-3.392-0.021 0.213 63.642 6.78 0.021-0.213-31.821-3.388zM416 583.625v32h192.021v-64h-192.021v32zM608.021 583.625v32h215.979v-64h-215.979v32zM872 631.031h-32v118.007h64v-118.007h-32zM824 796.446v-32h-624v64h624v-32zM152 749.039h32v-118.007h-64v118.007h32zM200 583.625v32h216v-64h-216v32zM152 631.031h32c0-8.137 6.787-15.407 16-15.407v-64c-43.807 0-80 35.179-80 79.407h32zM200 796.446v-32c-9.213 0-16-7.275-16-15.407h-64c0 44.228 36.193 79.407 80 79.407v-32zM872 749.039h-32c0 8.132-6.788 15.407-16 15.407v64c43.806 0 80-35.179 80-79.407h-32zM824 583.625v32c9.212 0 16 7.27 16 15.407h64c0-44.228-36.194-79.407-80-79.407v32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "stamp"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1912,
        "id": 108,
        "name": "stamp",
        "prevSize": 32,
        "code": 60188
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 541
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface StampIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const StampIcon = ({ size = 16, ...props }: StampIconProps) => (
  <IcoMoon iconSet={iconSet} icon="stamp" size={size} {...props} />
);

StampIcon.displayName = 'StampIcon';

export default StampIcon;
