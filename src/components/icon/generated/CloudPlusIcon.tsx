// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M300.544 296.466c-4.537-0.309-9.115-0.466-13.729-0.466-111.275 0-201.481 91.334-201.481 204.002 0 112.666 90.206 203.998 201.481 203.998h154.073M300.544 296.466c44.844-99.396 143.885-168.466 258.863-168.466 157.094 0 284.446 128.942 284.446 288M300.544 296.466c15.177-0.156 50.271 4.334 69.234 23.534M654.221 704h94.818M749.039 704h94.814M749.039 704v-96M749.039 704v96M938.667 704c0 106.039-84.898 192-189.628 192s-189.632-85.961-189.632-192c0-106.039 84.902-192 189.632-192s189.628 85.961 189.628 192z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "cloud-plus"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1570,
        "id": 450,
        "name": "cloud-plus",
        "prevSize": 32,
        "code": 59846
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 199
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CloudPlusIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CloudPlusIcon = ({ size = 16, ...props }: CloudPlusIconProps) => (
  <IcoMoon iconSet={iconSet} icon="cloud-plus" size={size} {...props} />
);

CloudPlusIcon.displayName = 'CloudPlusIcon';

export default CloudPlusIcon;
