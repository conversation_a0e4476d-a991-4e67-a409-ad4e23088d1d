// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M834.705 511.108c0.209-86.371-29.649-170-84.48-236.636s-131.247-112.156-216.226-128.804c-84.979-16.648-173.265-3.395-249.813 37.503s-136.622 106.908-169.987 186.784c-33.365 79.875-37.956 168.677-12.991 251.271 24.965 82.59 77.94 153.869 149.902 201.681 71.961 47.817 158.454 69.21 244.741 60.54 86.289-8.67 167.036-46.874 228.476-108.092M834.705 511.108c3.708 0 103.962-178.919 103.962-178.919M834.705 511.108c-3.703 0-179.072-102.73-179.072-102.73"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "rotate-right"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1854,
        "id": 166,
        "name": "rotate-right",
        "prevSize": 32,
        "code": 60130
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 483
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface RotateRightIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const RotateRightIcon = ({ size = 16, ...props }: RotateRightIconProps) => (
  <IcoMoon iconSet={iconSet} icon="rotate-right" size={size} {...props} />
);

RotateRightIcon.displayName = 'RotateRightIcon';

export default RotateRightIcon;
