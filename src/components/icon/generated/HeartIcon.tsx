// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M320 128c-129.603 0-234.667 102.59-234.667 229.142 0 193.527 367.121 494.045 420.279 536.546 3.857 3.085 8.917 3.085 12.774 0 53.158-42.5 420.279-343.019 420.279-536.546 0-126.551-105.062-229.142-234.667-229.142-73.929 0-139.874 33.381-182.886 85.55-4.629 5.613-13.598 5.613-18.227 0-43.012-52.169-108.958-85.55-182.886-85.55z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "heart"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1706,
        "id": 314,
        "name": "heart",
        "prevSize": 32,
        "code": 59982
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 335
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface HeartIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const HeartIcon = ({ size = 16, ...props }: HeartIconProps) => (
  <IcoMoon iconSet={iconSet} icon="heart" size={size} {...props} />
);

HeartIcon.displayName = 'HeartIcon';

export default HeartIcon;
