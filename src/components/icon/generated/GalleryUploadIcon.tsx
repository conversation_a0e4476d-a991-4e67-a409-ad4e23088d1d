// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M682.667 676.634l121.967-121.967c1.664-1.668 3.849-2.5 6.033-2.5M938.667 676.634l-121.967-121.967c-1.664-1.668-3.849-2.5-6.033-2.5M810.667 552.166v386.5M85.337 746.662c-0.003-4.134-0.003-8.397-0.003-12.796v-443.733c0-71.687 0-107.53 13.951-134.911 12.272-24.085 31.853-43.666 55.938-55.938 27.38-13.951 63.224-13.951 134.911-13.951h443.733c71.689 0 107.529 0 134.912 13.951 24.085 12.272 43.665 31.853 55.936 55.938 13.952 27.38 13.952 63.224 13.952 134.911v200.533M85.337 746.662c0.049 63.258 0.852 96.414 13.948 122.116 12.272 24.085 31.853 43.665 55.938 55.936 27.38 13.952 63.224 13.952 134.911 13.952h371.2M85.337 746.662l253.408-253.41c24.994-24.994 65.516-24.994 90.511 0l131.657 131.661c8.333 8.333 21.841 8.333 30.174 0M661.333 362.665c0 35.346-28.655 64-64 64s-64-28.654-64-64c0-35.346 28.655-64 64-64s64 28.654 64 64z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "gallery-upload"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1682,
        "id": 338,
        "name": "gallery-upload",
        "prevSize": 32,
        "code": 59958
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 311
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface GalleryUploadIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const GalleryUploadIcon = ({ size = 16, ...props }: GalleryUploadIconProps) => (
  <IcoMoon iconSet={iconSet} icon="gallery-upload" size={size} {...props} />
);

GalleryUploadIcon.displayName = 'GalleryUploadIcon';

export default GalleryUploadIcon;
