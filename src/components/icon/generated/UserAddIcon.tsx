// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M504.017 938.667l-67.712-0.691c-51.625-2.022-121.863-6.293-172.696-11.648-66.082-6.963-111.857-67.563-102.363-132.527l3.148-21.542c16.454-112.597 104.708-204.608 219.582-212.873 65.632-4.723 126.684-4.732 192.19-0.026 14.114 1.011 27.823 3.29 41.045 6.716M576.013 796.446h143.996M720.009 796.446h143.991M720.009 796.446v-142.225M720.009 796.446v142.221M648.009 251.259c0 91.638-75.213 165.926-167.991 165.926s-167.991-74.288-167.991-165.926c0-91.639 75.213-165.926 167.991-165.926s167.991 74.287 167.991 165.926z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "user-add"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1972,
        "id": 48,
        "name": "user-add",
        "prevSize": 32,
        "code": 60248
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 601
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface UserAddIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const UserAddIcon = ({ size = 16, ...props }: UserAddIconProps) => (
  <IcoMoon iconSet={iconSet} icon="user-add" size={size} {...props} />
);

UserAddIcon.displayName = 'UserAddIcon';

export default UserAddIcon;
