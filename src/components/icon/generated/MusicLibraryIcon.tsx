// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M155.223 914.048v0zM99.284 858.112v0zM924.715 858.112v0zM868.779 914.048v0zM924.715 400.556v0zM868.779 344.618v0zM99.284 400.556v0zM155.223 344.618v0zM740.779 216.618v0zM796.715 272.556v0zM283.223 216.618v0zM227.284 272.556v0zM320 64c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM704 128c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM639.198 470.033v0zM506.458 458.837v0zM290.133 330.667v32h443.733v-64h-443.733v32zM938.667 535.467h-32v187.733h64v-187.733h-32zM733.867 928v-32h-443.733v64h443.733v-32zM85.333 723.2h32v-187.733h-64v187.733h32zM290.133 928v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM85.333 723.2h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM155.223 914.048l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM938.667 723.2h-32c0 36.373-0.026 61.726-1.638 81.463-1.583 19.366-4.531 30.494-8.823 38.921l57.024 29.056c9.655-18.957 13.683-39.441 15.586-62.763 1.877-22.95 1.852-51.362 1.852-86.677h-32zM733.867 928v32c35.315 0 63.727 0.026 86.677-1.852 23.322-1.903 43.806-5.931 62.763-15.586l-29.056-57.024c-8.427 4.292-19.554 7.241-38.921 8.823-19.738 1.613-45.090 1.638-81.463 1.638v32zM924.715 858.112l-28.51-14.528c-9.207 18.061-23.893 32.747-41.954 41.954l29.056 57.024c30.106-15.343 54.579-39.817 69.922-69.922l-28.514-14.528zM938.667 535.467h32c0-35.315 0.026-63.727-1.852-86.677-1.903-23.322-5.931-43.808-15.586-62.761l-57.024 29.055c4.292 8.428 7.241 19.553 8.823 38.92 1.613 19.738 1.638 45.090 1.638 81.463h32zM868.779 344.618l-14.528 28.512c18.061 9.204 32.747 23.89 41.954 41.953l57.024-29.055c-15.343-30.106-39.817-54.583-69.922-69.923l-14.528 28.512zM85.333 535.467h32c0-36.373 0.025-61.726 1.638-81.463 1.582-19.366 4.532-30.492 8.826-38.92l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.761-1.875 22.95-1.85 51.362-1.85 86.677h32zM155.223 344.618l-14.528-28.512c-30.106 15.34-54.583 39.817-69.923 69.923l57.024 29.055c9.204-18.063 23.89-32.75 41.953-41.953l-14.528-28.512zM418.133 202.667v32h187.733v-64h-187.733v32zM605.867 202.667v32c36.373 0 61.726 0.025 81.463 1.638 19.366 1.582 30.494 4.532 38.921 8.826l29.056-57.024c-18.957-9.658-39.441-13.683-62.763-15.589-22.95-1.875-51.362-1.85-86.677-1.85v32zM740.779 216.618l-14.528 28.512c18.061 9.204 32.747 23.89 41.954 41.953l57.024-29.055c-15.343-30.106-39.817-54.583-69.922-69.923l-14.528 28.512zM418.133 202.667v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM283.223 216.618l-14.528-28.512c-30.106 15.34-54.583 39.817-69.923 69.923l57.024 29.055c9.204-18.063 23.89-32.75 41.953-41.953l-14.528-28.512zM214.492 331.825l31.946 1.86c1.408-24.175 4.492-37.050 9.359-46.602l-57.024-29.055c-10.861 21.315-14.639 44.684-16.226 71.937l31.946 1.86zM290.133 330.667v-32c-30.929 0-56.454-0.012-77.501 1.213l1.86 31.946 1.86 31.946c18.765-1.093 42.15-1.105 73.781-1.105v-32zM214.492 331.825l-1.86-31.946c-27.253 1.587-50.622 5.365-71.937 16.226l29.055 57.024c9.552-4.867 22.427-7.951 46.602-9.359l-1.86-31.946zM809.506 331.825l31.949-1.86c-1.587-27.253-5.367-50.622-16.226-71.937l-57.024 29.055c4.864 9.552 7.949 22.427 9.357 46.602l31.945-1.86zM733.867 330.667v32c31.633 0 55.014 0.012 73.779 1.105l3.721-63.892c-21.043-1.225-46.571-1.213-77.5-1.213v32zM809.506 331.825l-1.86 31.946c24.175 1.408 37.052 4.492 46.605 9.359l29.056-57.024c-21.316-10.861-44.685-14.639-71.94-16.226l-1.86 31.946zM320 96v32h384v-64h-384v32zM455.113 742.417h-32.001c0 14.494-11.508 25.583-24.889 25.583v64c49.458 0 88.89-40.474 88.89-89.583h-32zM398.222 800v-32c-13.381 0-24.889-11.089-24.889-25.583h-64c0 49.109 39.433 89.583 88.889 89.583v-32zM341.333 742.417h32c0-14.494 11.508-25.583 24.889-25.583v-64c-49.456 0-88.889 40.474-88.889 89.583h32zM398.222 684.834v32c13.382 0 24.889 11.089 24.889 25.583h64.001c0-49.109-39.433-89.583-88.89-89.583v32zM682.667 742.417h-32c0 14.494-11.507 25.583-24.887 25.583v64c49.455 0 88.887-40.474 88.887-89.583h-32zM625.779 800v-32c-13.385 0-24.892-11.089-24.892-25.583h-64c0 49.109 39.433 89.583 88.892 89.583v-32zM568.887 742.417h32c0-14.494 11.507-25.583 24.892-25.583v-64c-49.459 0-88.892 40.474-88.892 89.583h32zM625.779 684.834v32c13.38 0 24.887 11.089 24.887 25.583h64c0-49.109-39.433-89.583-88.887-89.583v32zM506.458 458.837l-2.692 31.885 132.74 11.196 2.692-31.885 2.688-31.889-132.74-11.196-2.688 31.889zM455.113 742.417h32v-198.417h-64.001v198.417h32.001zM455.113 544h32v-37.346h-64.001v37.346h32.001zM682.667 517.854h-32v45.107h64v-45.107h-32zM682.667 562.961h-32v179.456h64v-179.456h-32zM455.113 544l-2.658 31.889 227.554 18.965 5.316-63.782-227.554-18.961-2.658 31.889zM639.198 470.033l-2.692 31.885c7.68 0.649 14.161 7.287 14.161 15.936h64c0-41.267-31.322-76.211-72.781-79.71l-2.688 31.889zM506.458 458.837l2.688-31.889c-46.716-3.94-86.034 33.314-86.034 79.706h64.001c0-9.754 8.090-16.653 16.653-15.932l2.692-31.885z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "music-library"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1796,
        "id": 224,
        "name": "music-library",
        "prevSize": 32,
        "code": 60072
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 425
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MusicLibraryIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MusicLibraryIcon = ({ size = 16, ...props }: MusicLibraryIconProps) => (
  <IcoMoon iconSet={iconSet} icon="music-library" size={size} {...props} />
);

MusicLibraryIcon.displayName = 'MusicLibraryIcon';

export default MusicLibraryIcon;
