// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M267.463 938.667h489.068M309.529 789.559h404.941c53.995 0 101.598-36.309 117.111-89.327l106.014-362.264c5.884-20.102-13.495-38.274-32.503-30.479l-159.232 65.294c-22.093 9.060-47.343 0.502-59.814-20.274l-153.22-255.245c-9.549-15.907-32.102-15.907-41.651 0l-153.22 255.245c-12.472 20.777-37.721 29.335-59.816 20.274l-159.229-65.294c-19.008-7.795-38.387 10.377-32.504 30.479l106.013 362.264c15.515 53.018 63.116 89.327 117.111 89.327z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "crown"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1591,
        "id": 429,
        "name": "crown",
        "prevSize": 32,
        "code": 59867
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 220
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CrownIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CrownIcon = ({ size = 16, ...props }: CrownIconProps) => (
  <IcoMoon iconSet={iconSet} icon="crown" size={size} {...props} />
);

CrownIcon.displayName = 'CrownIcon';

export default CrownIcon;
