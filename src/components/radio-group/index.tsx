'use client';

import clsx from 'clsx';
import { RadioGroup as RadioGroupPrimitive } from 'radix-ui';
import { Context } from 'radix-ui/internal';
import * as React from 'react';

import { Flex } from '@components/flex';
import { radioGroupRootPropDefs } from '@components/radio-group/radio-group.props';
import { Text } from '@components/text';
import { type ComponentPropsWithout, type RemovedProps, extractProps } from '@helpers';
import { type GetPropDefTypes, type MarginProps, type Options, marginPropDefs } from '@props';

const RADIO_GROUP_NAME = 'RadioGroup';

type ScopedProps<P> = P & { __scopeRadioGroup?: Context.Scope };
const [createRadioGroupContext] = Context.createContextScope(RADIO_GROUP_NAME, [
  RadioGroupPrimitive.createRadioGroupScope,
]);
const useRadioGroupScope = RadioGroupPrimitive.createRadioGroupScope();

type RadioGroupRootOwnProps = GetPropDefTypes<typeof radioGroupRootPropDefs>;
type RadioGroupContextValue = RadioGroupRootOwnProps;

const [RadioGroupProvider, useRadioGroupContext] = createRadioGroupContext<RadioGroupContextValue>(RADIO_GROUP_NAME);

type RadioGroupRootElement = React.ComponentRef<typeof RadioGroupPrimitive.Root>;
interface RadioGroupRootProps
  extends ComponentPropsWithout<typeof RadioGroupPrimitive.Root, 'asChild' | 'color' | 'defaultChecked'>,
    MarginProps,
    RadioGroupRootOwnProps {
  labelAlign?: 'left' | 'right';
  options?: Options<string>[];
  orientation?: 'horizontal' | 'vertical';
}
const RadioGroupRoot = React.forwardRef<RadioGroupRootElement, RadioGroupRootProps>(
  (
    {
      color = radioGroupRootPropDefs.color.default,
      highContrast = radioGroupRootPropDefs.highContrast.default,
      size = radioGroupRootPropDefs.size.default,
      variant = radioGroupRootPropDefs.variant.default,
      labelAlign = 'right',
      ...props
    }: ScopedProps<RadioGroupRootProps>,
    forwardedRef,
  ) => {
    const { __scopeRadioGroup, className, ...rootProps } = extractProps(props, marginPropDefs);
    const radioGroupScope = useRadioGroupScope(__scopeRadioGroup);
    return (
      <RadioGroupProvider
        scope={__scopeRadioGroup}
        color={color}
        highContrast={highContrast}
        size={size}
        labelAlign={labelAlign}
        variant={variant}
      >
        <RadioGroupPrimitive.Root
          {...radioGroupScope}
          {...rootProps}
          ref={forwardedRef}
          className={clsx('rt-RadioGroupRoot', className)}
        />
      </RadioGroupProvider>
    );
  },
);
RadioGroupRoot.displayName = 'RadioGroup.Root';

const RadioGroup: React.FC<RadioGroupRootProps> = (props) => {
  const { options } = props;
  return (
    <RadioGroupRoot {...props} className={clsx(`rt-${props.orientation}`, props.className)}>
      {options?.map((option) => (
        <RadioGroupItem {...option} />
      ))}
    </RadioGroupRoot>
  );
};

type RadioGroupItemElement = React.ComponentRef<typeof RadioGroupItemRadio>;
interface RadioGroupItemProps extends ComponentPropsWithout<typeof RadioGroupItemRadio, RemovedProps>, MarginProps {
  label?: React.ReactNode;
  caption?: React.ReactNode;
  labelAlign?: 'left' | 'right';
}
const RadioGroupItem = React.forwardRef<RadioGroupItemElement, RadioGroupItemProps>(
  (_props: ScopedProps<RadioGroupItemProps>, forwardedRef) => {
    const { __scopeRadioGroup, label, labelAlign, caption, className, style, ...props } = _props;
    const { size, labelAlign: labelAlignContext } = useRadioGroupContext('RadioGroupItem', __scopeRadioGroup);

    // Render `<Text as="label">` if children are provided, otherwise render
    // the solo radio button to allow building out your custom layouts with it.
    if (label) {
      return (
        <Flex
          gap="3"
          align={caption ? 'start' : 'center'}
          justify={(labelAlign ?? labelAlignContext === 'left') ? 'end' : 'start'}
          direction={(labelAlign ?? labelAlignContext === 'left') ? 'row-reverse' : 'row'}
        >
          <RadioGroupItemRadio __scopeRadioGroup={__scopeRadioGroup} {...props} ref={forwardedRef} />
          <Flex gap="1" direction="column" align={(labelAlign ?? labelAlignContext === 'left') ? 'end' : 'start'}>
            <Text as="label" size={size} className="rt-radio-label">
              {label}
            </Text>
            {caption && (
              <Text className="rt-radio-caption" size={size} color="secondary">
                {caption}
              </Text>
            )}
          </Flex>
        </Flex>
      );
    }

    return (
      <RadioGroupItemRadio
        __scopeRadioGroup={__scopeRadioGroup}
        {...props}
        ref={forwardedRef}
        className={className}
        style={style}
      />
    );
  },
);
RadioGroupItem.displayName = 'RadioGroup.Item';

type RadioGroupItemRadioElement = React.ComponentRef<typeof RadioGroupPrimitive.Item>;
interface RadioGroupItemRadioProps extends React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item> {}
const RadioGroupItemRadio = React.forwardRef<RadioGroupItemRadioElement, ScopedProps<RadioGroupItemRadioProps>>(
  ({ __scopeRadioGroup, ...props }, forwardedRef) => {
    const context = useRadioGroupContext('RadioGroupItemRadio', __scopeRadioGroup);
    const radioGroupScope = useRadioGroupScope(__scopeRadioGroup);
    const { color, className } = extractProps({ ...props, ...context }, radioGroupRootPropDefs, marginPropDefs);
    return (
      <RadioGroupPrimitive.Item
        {...radioGroupScope}
        data-accent-color={color}
        {...props}
        asChild={false}
        ref={forwardedRef}
        className={clsx('rt-reset', 'rt-BaseRadioRoot', className)}
      />
    );
  },
);
RadioGroupItemRadio.displayName = 'RadioGroup.ItemRadio';

export { RadioGroupItem as Item, RadioGroup, RadioGroupRoot as Root };
export type { RadioGroupItemProps as ItemProps, RadioGroupRootProps as RootProps };
