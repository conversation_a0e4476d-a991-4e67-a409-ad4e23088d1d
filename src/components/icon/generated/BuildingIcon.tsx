// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 938.667h47.407M132.741 938.667v-734.815c0-65.456 53.062-118.519 118.519-118.519h237.035c65.459 0 118.519 53.062 118.519 118.519v165.926M132.741 938.667h142.222M274.963 938.667h189.63M274.963 938.667v-189.628c0-39.275 31.838-71.113 71.111-71.113h47.407c39.274 0 71.112 31.838 71.112 71.113v189.628M464.593 938.667h142.221M606.814 938.667v-568.889M606.814 938.667h284.446M263.111 322.371h47.407M263.111 488.294h47.407M429.039 322.371h47.407M429.039 488.294h47.407M606.814 369.778l185.412 30.902c57.148 9.524 99.034 58.968 99.034 116.905v421.082M891.26 938.667h47.407M749.039 606.814v94.814"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "building"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1497,
        "id": 523,
        "name": "building",
        "prevSize": 32,
        "code": 59773
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 126
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BuildingIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BuildingIcon = ({ size = 16, ...props }: BuildingIconProps) => (
  <IcoMoon iconSet={iconSet} icon="building" size={size} {...props} />
);

BuildingIcon.displayName = 'BuildingIcon';

export default BuildingIcon;
