// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 85.333h-63.211M512 85.333h63.211M512 85.333v131.688M938.667 512v-63.211M938.667 512v63.211M938.667 512h-131.686M512 938.667h-63.211M512 938.667h63.211M512 938.667v-131.686M85.333 512v-63.211M85.333 512v63.211M85.333 512h131.688M596.279 406.65h0.422M490.931 490.931h0.422M659.49 533.069h0.422M769.003 165.605l44.698 44.696M858.394 254.997l-44.693-44.696M813.7 813.7l44.693-44.698M813.7 813.7l-44.698 44.693M813.7 813.7l-93.12-93.12M210.301 813.7l-44.696-44.698M210.301 813.7l44.696 44.693M210.301 813.7l93.117-93.12M210.301 210.301l44.696-44.696M210.301 210.301l-44.696 44.696M210.301 210.301l93.117 93.117M806.98 512c0 81.455-33.015 155.2-86.4 208.58M806.98 512c0-81.455-33.015-155.201-86.4-208.582M512.222 217.021c39.91 0.029 77.969 7.985 112.683 22.378M512.222 217.021c38.345 0.029 76.689 7.488 112.683 22.378M512.222 217.021h-0.222M624.905 239.399c36.058 14.95 68.506 36.847 95.676 64.019M303.418 720.58c-57.598-57.596-86.398-133.090-86.397-208.58M303.418 720.58c57.598 57.6 133.091 86.4 208.582 86.4M303.418 303.418c-57.598 57.598-86.397 133.091-86.397 208.582M303.418 303.418c57.598-57.598 133.091-86.397 208.582-86.397M512 806.98c38.426 0 76.847-7.462 112.913-22.383 36.053-14.95 68.497-36.843 95.667-64.017M813.7 210.301l-93.12 93.117"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "virus"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1991,
        "id": 29,
        "name": "virus",
        "prevSize": 32,
        "code": 60267
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 620
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface VirusIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const VirusIcon = ({ size = 16, ...props }: VirusIconProps) => (
  <IcoMoon iconSet={iconSet} icon="virus" size={size} {...props} />
);

VirusIcon.displayName = 'VirusIcon';

export default VirusIcon;
