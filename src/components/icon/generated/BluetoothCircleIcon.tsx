// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M369.778 388.238l148.366 123.762M518.144 512l127.386 106.261c11.588 9.664 11.588 25.335 0 35.004l-112.188 93.581c-5.606 4.681-15.198 1.365-15.198-5.248v-229.598zM518.144 512l-148.366 123.763M518.144 512l127.386-106.259c11.588-9.667 11.588-25.339 0-35.005l-112.188-93.583c-5.606-4.678-15.198-1.365-15.198 5.251v229.597zM938.667 512c0 235.639-191.027 426.667-426.667 426.667-235.642 0-426.667-191.027-426.667-426.667 0-235.642 191.025-426.667 426.667-426.667 235.639 0 426.667 191.025 426.667 426.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bluetooth-circle"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1473,
        "id": 547,
        "name": "bluetooth-circle",
        "prevSize": 32,
        "code": 59749
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 102
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BluetoothCircleIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BluetoothCircleIcon = ({ size = 16, ...props }: BluetoothCircleIconProps) => (
  <IcoMoon iconSet={iconSet} icon="bluetooth-circle" size={size} {...props} />
);

BluetoothCircleIcon.displayName = 'BluetoothCircleIcon';

export default BluetoothCircleIcon;
