// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 85.333l37.49 37.49M938.667 938.667l-37.491-37.491M290.133 938.667c-71.687 0-107.53 0-134.911-13.952-24.085-12.271-43.666-31.851-55.938-55.936-13.951-27.383-13.951-63.223-13.951-134.912M938.667 733.867c0 71.689 0 107.529-13.952 134.912-6.135 12.041-14.097 22.959-23.539 32.397M733.867 85.333c71.689 0 107.529 0 134.912 13.951 24.085 12.272 43.665 31.853 55.936 55.938 13.952 27.38 13.952 63.224 13.952 134.911M290.133 85.333c-71.687 0-107.53 0-134.911 13.951-12.042 6.136-22.959 14.099-32.399 23.539M426.667 85.333h170.667M85.333 597.333v-170.667M938.667 426.667v170.667M597.333 938.667h-170.667M122.824 122.824l778.352 778.352"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "selection-slash"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1873,
        "id": 147,
        "name": "selection-slash",
        "prevSize": 32,
        "code": 60149
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 502
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SelectionSlashIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SelectionSlashIcon = ({ size = 16, ...props }: SelectionSlashIconProps) => (
  <IcoMoon iconSet={iconSet} icon="selection-slash" size={size} {...props} />
);

SelectionSlashIcon.displayName = 'SelectionSlashIcon';

export default SelectionSlashIcon;
