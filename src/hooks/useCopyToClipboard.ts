import { useCallback, useEffect, useState } from 'react';

import { copyToClipboard } from '../utils';

export const TEMPORARY_COPY_TIMEOUT = 5000; // 5s
export const COPIED_MESSAGE_TIMEOUT = 1500; // 1.5s

export const useTemporaryCopyToClipboard = (timeoutMs: number = TEMPORARY_COPY_TIMEOUT) => {
  // state
  const [isCopied, setIsCopied] = useState(false);

  // methods
  const temporaryCopyToClipboard = useCallback(async (value: string) => {
    await copyToClipboard(value);
    setIsCopied(true);
  }, []);

  // effects
  useEffect(() => {
    if (!isCopied) {
      // nothing to clear
      return () => {};
    }

    // clear the clipboard after a set time
    const timer = window.setTimeout(async () => {
      await copyToClipboard('');
      setIsCopied(false);
    }, timeoutMs);

    // clean-up on unmount if timer was set
    return () => {
      timer && clearTimeout(timer);
    };
  }, [isCopied, timeoutMs]);

  return {
    temporaryCopyToClipboard,
    isCopied,
  };
};

const useCopyToClipboard = (timeoutMs = COPIED_MESSAGE_TIMEOUT) => {
  // state
  const [isCopied, setIsCopied] = useState(false);

  // methods
  const _copyToClipboard = useCallback(async (value: string) => {
    await copyToClipboard(value);
    setIsCopied(true);
  }, []);

  const resetCopyState = useCallback(() => {
    setIsCopied(false);
  }, []);

  // effects
  useEffect(() => {
    if (!isCopied) {
      // nothing to clear
      return () => {};
    }

    // clear the message after a set time
    const timer = window.setTimeout(async () => {
      resetCopyState();
    }, timeoutMs);

    // clean-up on unmount if timer was set
    return () => {
      timer && clearTimeout(timer);
    };
  }, [isCopied, resetCopyState, timeoutMs]);

  return {
    copyToClipboard: _copyToClipboard,
    resetCopyState,
    isCopied,
  };
};

export default useCopyToClipboard;
