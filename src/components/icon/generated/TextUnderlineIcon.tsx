// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M853.333 85.333v379.26c0 183.275-152.819 331.853-341.333 331.853s-341.333-148.578-341.333-331.853v-379.26M170.667 938.667h682.667"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "text-underline"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1952,
        "id": 68,
        "name": "text-underline",
        "prevSize": 32,
        "code": 60228
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 581
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface TextUnderlineIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const TextUnderlineIcon = ({ size = 16, ...props }: TextUnderlineIconProps) => (
  <IcoMoon iconSet={iconSet} icon="text-underline" size={size} {...props} />
);

TextUnderlineIcon.displayName = 'TextUnderlineIcon';

export default TextUnderlineIcon;
