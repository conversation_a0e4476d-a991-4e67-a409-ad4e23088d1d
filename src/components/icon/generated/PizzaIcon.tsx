// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M225.745 352.606c9.964-10.563 20.54-20.572 31.674-29.975 68.142-57.551 157.17-92.396 254.581-92.396s186.441 34.845 254.583 92.396c11.132 9.403 21.709 19.413 31.671 29.975M225.745 352.606l-50.456-106.134c-9.287-19.536-4.386-43.119 12.982-55.867 90.031-66.080 202.166-105.272 323.73-105.272 121.566 0 233.698 39.192 323.729 105.272 17.37 12.748 22.272 36.331 12.983 55.867l-50.458 106.134M225.745 352.606l103.092 216.853M798.255 352.606l-40.29 84.749M371.001 399.879c7.833-10.034 37.6-40.644 93.997-40.644M328.837 569.459c12.681-6.43 27.003-10.052 42.164-10.052 51.914 0 93.997 42.449 93.997 94.814 0 38.281-22.49 71.262-54.868 86.238M328.837 569.459l81.294 170.999M410.131 740.459l91.028 191.475c4.271 8.977 17.412 8.977 21.683 0l155.738-327.59M678.579 604.343c-41.596-9.779-72.58-47.411-72.58-92.343 0-52.365 42.086-94.815 93.999-94.815 21.871 0 41.997 7.534 57.967 20.169M678.579 604.343l79.386-166.989"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "pizza"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1825,
        "id": 195,
        "name": "pizza",
        "prevSize": 32,
        "code": 60101
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 454
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface PizzaIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const PizzaIcon = ({ size = 16, ...props }: PizzaIconProps) => (
  <IcoMoon iconSet={iconSet} icon="pizza" size={size} {...props} />
);

PizzaIcon.displayName = 'PizzaIcon';

export default PizzaIcon;
