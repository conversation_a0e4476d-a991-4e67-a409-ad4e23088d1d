// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M108.385 562.551v0zM915.614 461.449v0zM87.331 529.425v0zM108.385 461.449v0zM87.331 494.575v0zM915.614 562.551v0zM936.67 529.425v0zM936.67 494.575v0zM237.272 384.291c14.201-10.52 17.184-30.561 6.665-44.762s-30.561-17.184-44.762-6.665l38.097 51.426zM416.491 521.796c-1.775-17.587-17.468-30.4-35.052-28.625-17.584 1.771-30.4 17.468-28.625 35.051l63.677-6.426zM495.778 671.185c17.583 1.775 33.28-11.042 35.051-28.625 1.775-17.583-11.038-33.276-28.625-35.051l-6.426 63.676zM622.942 787.132c17.207-4.045 27.874-21.269 23.834-38.473s-21.265-27.874-38.473-23.834l14.639 62.306zM193.294 148.039c-12.497-12.497-32.758-12.497-45.255 0s-12.497 32.758 0 45.255l45.255-45.255zM830.707 875.959c12.497 12.497 32.755 12.497 45.252 0s12.497-32.755 0-45.252l-45.252 45.252zM305.243 305.243v0zM512 768v-32c-185.321 0-317.428-121.83-379.285-194.236l-48.66 41.574c65.612 76.796 213.991 216.661 427.945 216.661v-32zM512 256v32c185.323 0 317.427 121.83 379.285 194.236l48.661-41.574c-65.613-76.797-213.99-216.661-427.947-216.661v32zM108.385 562.551l24.33-20.787c-11.898-13.926-13.070-15.868-14.676-21.346l-61.413 18.010c5.972 20.365 16.309 31.893 27.43 44.911l24.33-20.787zM108.385 461.449l-24.33-20.787c-11.12 13.018-21.458 24.546-27.43 44.911l61.413 18.010c1.606-5.478 2.778-7.42 14.676-21.346l-24.33-20.787zM87.331 529.425l30.707-9.007c0.016 0.055-0.202-0.747-0.403-2.543-0.188-1.685-0.302-3.712-0.302-5.875s0.114-4.19 0.302-5.875c0.201-1.796 0.419-2.598 0.403-2.543l-61.413-18.010c-2.53 8.623-3.292 18.445-3.292 26.428s0.762 17.805 3.292 26.428l30.706-9.003zM915.614 562.551l24.333 20.787c11.119-13.018 21.457-24.546 27.43-44.911l-61.414-18.010c-1.609 5.478-2.778 7.42-14.677 21.346l24.329 20.787zM915.614 461.449l-24.329 20.787c11.9 13.926 13.069 15.868 14.677 21.346l61.414-18.010c-5.973-20.365-16.311-31.893-27.43-44.911l-24.333 20.787zM936.67 529.425l30.707 9.003c2.526-8.623 3.29-18.445 3.29-26.428s-0.764-17.805-3.29-26.428l-61.414 18.010c-0.017-0.055 0.201 0.747 0.401 2.543 0.188 1.685 0.303 3.712 0.303 5.875s-0.115 4.19-0.303 5.875c-0.201 1.796-0.418 2.598-0.401 2.543l30.707 9.007zM512 384v32c53.018 0 96 42.982 96 96h64c0-88.366-71.633-160-160-160v32zM108.385 461.449l24.33 20.787c23.957-28.045 59.185-64.332 104.557-97.945l-38.097-51.426c-50.188 37.18-88.882 77.087-115.12 107.796l24.33 20.787zM498.991 639.347l3.213-31.838c-45.171-4.561-81.154-40.542-85.713-85.713l-63.677 6.426c7.613 75.435 67.527 135.351 142.964 142.963l3.213-31.838zM615.625 755.977l-7.322-31.151c-30.195 7.095-62.319 11.174-96.303 11.174v64c39.155 0 76.186-4.702 110.942-12.868l-7.317-31.155zM170.667 170.667l-22.627 22.627 134.577 134.577 45.255-45.255-134.577-134.577-22.627 22.627zM305.243 305.243l14.123 28.715c55.396-27.246 119.853-45.958 192.634-45.958v-64c-84.083 0-158.157 21.68-220.879 52.529l14.123 28.715zM718.758 718.758l-22.63 22.626 134.579 134.575 45.252-45.252-134.575-134.579-22.626 22.63zM915.614 562.551l-24.329-20.787c-37.099 43.426-100.693 106.001-186.65 148.279l28.245 57.429c96.576-47.501 166.775-116.975 207.066-164.134l-24.333-20.787zM602.509 602.509l-22.626 22.63 116.245 116.245 45.257-45.257-116.245-116.245-22.63 22.626zM640 512h-32c0 26.517-10.709 50.475-28.117 67.883l45.257 45.257c28.915-28.919 46.861-68.966 46.861-113.139h-32zM305.243 305.243l-22.627 22.627 116.247 116.247 45.254-45.254-116.247-116.247-22.627 22.627zM421.49 421.49l-22.627 22.627 181.020 181.022 45.257-45.257-181.022-181.020-22.627 22.627zM421.49 421.49l22.627 22.627c17.408-17.408 41.365-28.117 67.883-28.117v-64c-44.173 0-84.22 17.943-113.137 46.863l22.627 22.627z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "eye-off"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1625,
        "id": 395,
        "name": "eye-off",
        "prevSize": 32,
        "code": 59901
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 254
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface EyeOffIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const EyeOffIcon = ({ size = 16, ...props }: EyeOffIconProps) => (
  <IcoMoon iconSet={iconSet} icon="eye-off" size={size} {...props} />
);

EyeOffIcon.displayName = 'EyeOffIcon';

export default EyeOffIcon;
