// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M938.667 490.667c0-71.687 0-105.356-13.952-132.736-12.271-24.085-31.851-43.666-55.936-55.938-27.383-13.951-63.223-13.951-134.912-13.951h-84.787c-26.718 0-40.077 0-52.203-3.683-10.735-3.261-20.723-8.606-29.389-15.731-9.792-8.048-17.199-19.165-32.017-41.398l-5.342-8.012c-7.091-10.639-13.052-19.581-18.355-27.218M511.774 192c-11.328-16.303-19.669-26.658-29.67-34.879-13.001-10.687-27.981-18.705-44.083-23.596-18.191-5.524-38.23-5.524-78.308-5.524h-69.579c-71.687 0-107.53 0-134.911 13.951-24.085 12.272-43.666 31.853-55.938 55.938-13.951 27.38-13.951 63.224-13.951 134.911v358.4c0 71.689 0 107.529 13.951 134.912 12.272 24.085 31.853 43.665 55.938 55.936 27.38 13.952 63.224 13.952 134.911 13.952h285.867M511.774 192h298.893c70.694 0 128 57.308 128 128v128M877.845 621.039c0 36.655-30.080 66.368-67.187 66.368s-67.187-29.713-67.187-66.368c0-36.659 30.080-66.372 67.187-66.372s67.187 29.713 67.187 66.372zM772.245 744.286c26.253-1.886 50.667-1.89 76.868-0.009 45.926 3.298 81.199 40.102 87.774 85.12l1.28 8.755c3.793 25.95-14.455 50.167-40.845 52.962-61.931 6.562-111.194 6.485-173.218-0.051-26.428-2.786-44.736-27.025-40.939-53.009l1.259-8.619c6.579-45.039 41.877-81.843 87.821-85.15z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "folder-user"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1670,
        "id": 350,
        "name": "folder-user",
        "prevSize": 32,
        "code": 59946
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 299
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FolderUserIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FolderUserIcon = ({ size = 16, ...props }: FolderUserIconProps) => (
  <IcoMoon iconSet={iconSet} icon="folder-user" size={size} {...props} />
);

FolderUserIcon.displayName = 'FolderUserIcon';

export default FolderUserIcon;
