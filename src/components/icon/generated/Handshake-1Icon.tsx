// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M780.826 243.172l63.398 12.68c2.5 0.5 5.086-0.282 6.886-2.086l86.438-86.435c2.15-2.153 1.037-5.837-1.95-6.435l-60.412-12.082-12.083-60.413c-0.597-2.986-4.284-4.102-6.434-1.948l-86.438 86.435c-1.801 1.803-2.581 4.387-2.082 6.887l12.676 63.397zM780.826 243.172l-39.962 39.965M469.333 554.667l150.848-150.849M620.181 403.817c-38.605-38.606-91.938-62.484-150.848-62.484-117.821 0-213.333 95.514-213.333 213.333s95.513 213.333 213.333 213.333c117.82 0 213.333-95.514 213.333-213.333 0-14.95-1.54-29.547-4.467-43.631M620.181 403.817l120.683-120.68M740.864 283.138c-69.491-69.49-165.491-112.471-271.531-112.471-212.077 0-384 171.923-384 384 0 212.079 171.923 384 384 384 212.079 0 384-171.921 384-384 0-63.471-15.398-123.349-42.667-176.094M490.667 554.667c0 11.78-9.553 21.333-21.333 21.333s-21.333-9.553-21.333-21.333c0-11.78 9.553-21.333 21.333-21.333s21.333 9.553 21.333 21.333z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "handshake-1"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1700,
        "id": 320,
        "name": "handshake-1",
        "prevSize": 32,
        "code": 59976
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 329
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface Handshake-1IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const Handshake-1Icon = ({ size = 16, ...props }: Handshake-1IconProps) => (
  <IcoMoon iconSet={iconSet} icon="handshake-1" size={size} {...props} />
);

Handshake-1Icon.displayName = 'Handshake-1Icon';

export default Handshake-1Icon;
