// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M537.097 680.154l317.969 34.257c57.783 6.225 95.919 61.333 79.945 115.537-11.52 39.095-48.38 66.052-90.321 66.052h-555.002c-45.748 0-87.841-24.226-109.748-63.168l-91.504-162.645c-9.682-17.212 4.615-37.867 24.711-35.703l423.951 45.67zM537.097 680.154v-117.193M537.097 562.961c-145.201-27.738-238.392-28.898-364.643 1.003-24.452 5.794-43.728-22.076-27.391-40.265l347.827-387.283c15.13-16.848 44.207-6.706 44.207 15.42v101.875M537.097 562.961c90.551 14.895 151.471 30.972 217.348 58.756 24.525 10.342 50.172-17.609 34.436-38.221l-251.785-329.785M537.097 562.961v-309.25"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "sailboat"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1860,
        "id": 160,
        "name": "sailboat",
        "prevSize": 32,
        "code": 60136
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 489
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SailboatIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SailboatIcon = ({ size = 16, ...props }: SailboatIconProps) => (
  <IcoMoon iconSet={iconSet} icon="sailboat" size={size} {...props} />
);

SailboatIcon.displayName = 'SailboatIcon';

export default SailboatIcon;
