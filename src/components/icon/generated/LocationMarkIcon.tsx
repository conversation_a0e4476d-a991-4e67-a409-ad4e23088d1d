// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 512h165.926M512 85.333v165.926M512 772.74v165.926M772.74 512h165.926M855.706 512c0 189.824-153.882 343.706-343.706 343.706-189.822 0-343.704-153.882-343.704-343.706 0-189.822 153.882-343.704 343.704-343.704 189.824 0 343.706 153.882 343.706 343.704zM618.667 512c0 58.91-47.757 106.667-106.667 106.667s-106.667-47.757-106.667-106.667c0-58.91 47.757-106.667 106.667-106.667s106.667 47.757 106.667 106.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "location-mark"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1743,
        "id": 277,
        "name": "location-mark",
        "prevSize": 32,
        "code": 60019
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 372
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface LocationMarkIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const LocationMarkIcon = ({ size = 16, ...props }: LocationMarkIconProps) => (
  <IcoMoon iconSet={iconSet} icon="location-mark" size={size} {...props} />
);

LocationMarkIcon.displayName = 'LocationMarkIcon';

export default LocationMarkIcon;
