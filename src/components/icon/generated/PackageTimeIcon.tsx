// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M810.667 714.667c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM778.667 778.667l16.465 27.439 15.535-9.323v-18.116h-32zM708.868 783.228c-15.155 9.092-20.066 28.749-10.974 43.904s28.749 20.066 43.904 10.974l-32.93-54.878zM491.281 90.781v0zM149.946 283.18v0zM149.946 740.821v0zM475.567 961.097c15.394 8.678 34.91 3.23 43.588-12.164s3.234-34.91-12.164-43.588l-31.424 55.753zM874.052 283.18v0zM532.719 90.781v0zM889.869 298.667v0zM543.462 494.238v0zM134.13 298.667v0zM480.538 494.238v0zM864 490.667c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM938.667 768h-32c0 76.582-62.084 138.667-138.667 138.667v64c111.932 0 202.667-90.735 202.667-202.667h-32zM768 938.667v-32c-76.582 0-138.667-62.084-138.667-138.667h-64c0 111.932 90.735 202.667 202.667 202.667v-32zM597.333 768h32c0-76.582 62.084-138.667 138.667-138.667v-64c-111.932 0-202.667 90.735-202.667 202.667h32zM768 597.333v32c76.582 0 138.667 62.084 138.667 138.667h64c0-111.932-90.735-202.667-202.667-202.667v32zM778.667 714.667h-32v64h64v-64h-32zM778.667 778.667l-16.465-27.439-53.333 32 32.93 54.878 53.333-32-16.465-27.439zM491.281 90.781l-15.714-27.876-341.334 192.399 31.426 55.753 341.332-192.399-15.71-27.877zM128 321.023h-32v381.953h64v-381.953h-32zM149.946 740.821l-15.713 27.874 341.334 192.401 31.424-55.753-341.332-192.401-15.713 27.878zM874.052 283.18l15.714-27.876-341.333-192.399-31.424 55.753 341.333 192.399 15.71-27.877zM128 702.976h-32c0 27.115 14.487 52.335 38.233 65.719l31.426-55.753c-3.344-1.886-5.659-5.636-5.659-9.967h-32zM491.281 90.781l15.71 27.877c3.132-1.766 6.886-1.766 10.018 0l31.424-55.753c-22.639-12.762-50.227-12.762-72.866 0l15.714 27.876zM896 321.023h32c0-13.858-3.78-27.196-10.62-38.706l-55.019 32.7c1.045 1.756 1.638 3.819 1.638 6.006h32zM889.869 298.667l27.507-16.35c-6.566-11.049-15.979-20.458-27.61-27.013l-31.424 55.753c1.638 0.923 3.021 2.277 4.019 3.959l27.507-16.35zM543.462 494.238l15.735 27.866 346.406-195.571-31.467-55.731-346.406 195.571 15.731 27.866zM149.946 283.18l-15.713-27.876c-11.628 6.554-21.044 15.964-27.611 27.013l55.016 32.7c1-1.682 2.383-3.036 4.021-3.959l-15.713-27.877zM134.13 298.667l-27.508-16.35c-6.841 11.51-10.622 24.848-10.622 38.706h64c0-2.187 0.594-4.25 1.638-6.006l-27.508-16.35zM480.538 494.238l15.731-27.866-346.407-195.571-31.464 55.731 346.404 195.571 15.735-27.866zM896 490.667h32v-169.644h-64v169.644h32zM543.462 494.238l-15.731-27.866c-9.762 5.513-21.7 5.513-31.462 0l-31.467 55.731c29.295 16.538 65.101 16.538 94.396 0l-15.735-27.866z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "package-time"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1805,
        "id": 215,
        "name": "package-time",
        "prevSize": 32,
        "code": 60081
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 434
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface PackageTimeIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const PackageTimeIcon = ({ size = 16, ...props }: PackageTimeIconProps) => (
  <IcoMoon iconSet={iconSet} icon="package-time" size={size} {...props} />
);

PackageTimeIcon.displayName = 'PackageTimeIcon';

export default PackageTimeIcon;
