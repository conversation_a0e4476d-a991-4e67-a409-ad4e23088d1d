// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 85.333v118.519M512 203.852l71.113-47.407M512 203.852l-71.113-47.407M512 203.852v308.144M512 511.996v308.151M512 511.996l308.147 0.004M512 511.996l-308.148 0.004M512 511.996l217.894 217.899M512 511.996l-217.894-217.889M512 511.996l-217.897 217.899M512 511.996l217.89-217.889M512 820.147v118.519M512 820.147l-71.113 47.407M512 820.147l71.113 47.407M938.667 512h-118.519M820.147 512l47.407 71.113M820.147 512l47.407-71.113M203.852 512h-118.519M203.852 512l-47.407-71.113M203.852 512l-47.407 71.113M813.7 813.7l-83.806-83.806M729.894 729.894l-16.764 83.806M729.894 729.894l83.806-16.764M294.106 294.106l-83.805-83.805M294.106 294.106l16.761-83.805M294.106 294.106l-83.805 16.761M210.298 813.7l83.805-83.806M294.103 729.894l-83.805-16.764M294.103 729.894l16.761 83.806M729.89 294.106l83.806-83.805M729.89 294.106l83.806 16.761M729.89 294.106l-16.759-83.805"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "snow"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1907,
        "id": 113,
        "name": "snow",
        "prevSize": 32,
        "code": 60183
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 536
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SnowIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SnowIcon = ({ size = 16, ...props }: SnowIconProps) => (
  <IcoMoon iconSet={iconSet} icon="snow" size={size} {...props} />
);

SnowIcon.displayName = 'SnowIcon';

export default SnowIcon;
