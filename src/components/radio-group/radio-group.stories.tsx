import type { <PERSON>a, StoryObj } from '@storybook/react-vite';

import { RadioGroup } from '@components/radio-group';

import { radioGroupRootPropDefs } from './radio-group.props';

const meta: Meta<typeof RadioGroup> = {
  title: 'DesignSystem/Radio/RadioGroup',
  component: RadioGroup,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    size: {
      control: 'inline-radio',
      options: radioGroupRootPropDefs.size.values,
    },
    variant: {
      control: 'inline-radio',
      options: radioGroupRootPropDefs.variant.values,
    },
    color: { control: 'text' },
    highContrast: { control: 'boolean' },
    value: { control: 'text' },
    defaultValue: { control: 'text' },
    onValueChange: { action: 'onValueChange' },
    orientation: { control: 'inline-radio', type: 'string', options: ['horizontal', 'vertical'] },
  },
};

export default meta;
type Story = StoryObj<typeof RadioGroup>;

const OPTIONS = [
  { value: 'option-1', label: 'Option 1', caption: 'option 1' },
  { value: 'option-2', label: 'Option 2', caption: 'option 2' },
  { value: 'option-3', label: 'Option 3', caption: 'option 3' },
];

export const _radioGroup: Story = {
  args: {
    size: radioGroupRootPropDefs.size.default,
    variant: radioGroupRootPropDefs.variant.default,
    color: radioGroupRootPropDefs.color.default,
    highContrast: false,
    defaultValue: 'option-1',
    orientation: 'vertical',
    options: OPTIONS,
  },
};
