// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M343.755 469.197c10.642-1.203 18.912-10.236 18.912-21.197s-8.27-19.994-18.912-21.197M343.755 469.197c-0.795 0.090-1.603 0.137-2.422 0.137-11.782 0-21.333-9.553-21.333-21.333s9.551-21.333 21.333-21.333c0.819 0 1.627 0.047 2.422 0.137M343.755 469.197v-42.394M680.243 469.197c0.794 0.090 1.604 0.137 2.423 0.137 11.78 0 21.333-9.553 21.333-21.333s-9.553-21.333-21.333-21.333c-0.819 0-1.63 0.047-2.423 0.137M680.243 469.197c-10.641-1.203-18.91-10.236-18.91-21.197s8.269-19.994 18.91-21.197M680.243 469.197v-42.394M680.243 426.803v-175.511c0-91.657-75.328-165.959-168.243-165.959-92.92 0-168.245 74.302-168.245 165.959v175.511M129.409 749.167c3.814-115.627 15.027-243.337 47.991-359.535 11.899-41.946 49.432-71.114 93.292-76.154 175.324-20.148 309.301-20.252 482.805-0.099 43.759 5.083 81.169 34.229 93.052 76.083 38.771 136.567 47.479 289.057 49.438 419.148 0.913 60.587-45.756 111.612-106.948 116.941-201.348 17.527-348.877 17.498-553.868-0.141-61.284-5.278-108.073-56.333-107.157-117.005 0.291-19.298 0.731-39.083 1.396-59.238zM129.409 749.167h550.834"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bag-alt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1446,
        "id": 574,
        "name": "bag-alt",
        "prevSize": 32,
        "code": 59722
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 75
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BagAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BagAltIcon = ({ size = 16, ...props }: BagAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="bag-alt" size={size} {...props} />
);

BagAltIcon.displayName = 'BagAltIcon';

export default BagAltIcon;
