// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M417.144 759.172c0 52.002-42.453 94.161-94.822 94.161s-94.822-42.159-94.822-94.161M417.144 759.172c0-52.002-42.453-94.161-94.822-94.161s-94.822 42.159-94.822 94.161M417.144 759.172h189.644M227.5 759.172h-14.78c-59.395 0-109.981-43.627-116.172-102.289-5.92-56.094-9.525-106.901-10.746-156.655M796.433 759.172c0 52.002-42.453 94.161-94.822 94.161s-94.822-42.159-94.822-94.161M796.433 759.172c0-52.002-42.453-94.161-94.822-94.161s-94.822 42.159-94.822 94.161M796.433 759.172h23.706c65.459 0 118.528-52.698 118.528-117.7v-141.244M85.802 500.228c-1.334-54.34 0.175-107.426 4.617-164.78 1.58-20.401 3.531-41.343 5.856-63.073 6.253-58.422 56.745-101.709 115.908-101.709h145.697M85.802 500.228h272.079M938.667 500.228v-107.483c0-22.060-6.242-43.675-18.018-62.382l-65.694-104.377c-21.658-34.414-59.644-55.319-100.51-55.319h-147.657M938.667 500.228h-331.878M606.788 170.667v329.562M606.788 170.667h-248.908M606.788 500.228h-248.908M357.88 170.667v329.562"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bus-right"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1499,
        "id": 521,
        "name": "bus-right",
        "prevSize": 32,
        "code": 59775
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 128
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BusRightIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BusRightIcon = ({ size = 16, ...props }: BusRightIconProps) => (
  <IcoMoon iconSet={iconSet} icon="bus-right" size={size} {...props} />
);

BusRightIcon.displayName = 'BusRightIcon';

export default BusRightIcon;
