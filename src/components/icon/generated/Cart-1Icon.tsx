// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M827.379 796.446c-39.659 0-71.808 31.838-71.808 71.108 0 39.275 32.149 71.113 71.808 71.113 39.663 0 71.812-31.838 71.812-71.113 0-39.27-32.149-71.108-71.812-71.108zM827.379 796.446h-439.804c-35.992 0-66.424-26.385-71.181-61.713l-79.129-587.687c-4.757-35.328-35.189-61.713-71.181-61.713h-80.75M252.892 251.259l606.564-62.137c45.231-4.634 83.371 33.003 78.844 77.809l-28.331 280.567c-6.118 60.587-57.604 106.722-119.091 106.722h-466.175M564.075 393.481l215.433-23.703M420.451 867.554c0 39.275-32.151 71.113-71.811 71.113s-71.811-31.838-71.811-71.113c0-39.27 32.151-71.108 71.811-71.108s71.811 31.838 71.811 71.108z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "cart-1"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1530,
        "id": 490,
        "name": "cart-1",
        "prevSize": 32,
        "code": 59806
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 159
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface Cart-1IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const Cart-1Icon = ({ size = 16, ...props }: Cart-1IconProps) => (
  <IcoMoon iconSet={iconSet} icon="cart-1" size={size} {...props} />
);

Cart-1Icon.displayName = 'Cart-1Icon';

export default Cart-1Icon;
