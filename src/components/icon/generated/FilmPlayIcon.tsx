// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M436.937 745.707v0zM636.489 625.118v0zM636.489 612.215v0zM436.937 491.627v0zM99.284 197.889v0zM155.223 141.951v0zM99.284 826.112v0zM155.223 882.048v0zM924.715 826.112v0zM868.779 882.048v0zM924.715 197.889v0zM868.779 141.951v0zM426.667 498.078h-32v241.178h64v-241.178h-32zM436.937 745.707l16.55 27.388 199.552-120.589-33.101-54.775-199.553 120.589 16.551 27.388zM636.489 612.215l16.55-27.388-199.552-120.589-33.102 54.775 199.553 120.589 16.55-27.388zM636.489 625.118l16.55 27.388c25.28-15.279 25.28-52.399 0-67.678l-33.101 54.775c-8.572-5.18-11.938-13.888-11.938-20.937s3.366-15.757 11.938-20.937l16.55 27.388zM426.667 739.255h-32c0 26.411 29.428 51.597 58.82 33.839l-33.102-54.775c8.846-5.346 19.282-4.442 26.719 0.23 7.236 4.548 11.563 12.425 11.563 20.706h-32zM426.667 498.078h32c0 8.282-4.326 16.158-11.563 20.706-7.437 4.672-17.873 5.577-26.719 0.23l33.102-54.775c-29.393-17.758-58.82 7.428-58.82 33.839h32zM938.667 332.8h-32v8.533h64v-8.533h-32zM85.333 341.333h32v-8.533h-64v8.533h32zM512 128v32h213.333v-64h-213.333v32zM725.333 128v32h8.533v-64h-8.533v32zM290.133 128v32h8.533v-64h-8.533v32zM298.667 128v32h213.333v-64h-213.333v32zM725.333 128h-32v213.333h64v-213.333h-32zM938.667 341.333v-32h-213.333v64h213.333v-32zM725.333 341.333v-32h-213.333v64h213.333v-32zM512 128h-32v213.333h64v-213.333h-32zM512 341.333v-32h-213.333v64h213.333v-32zM298.667 341.333v-32h-213.333v64h213.333v-32zM298.667 128h-32v213.333h64v-213.333h-32zM85.333 332.8h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM290.133 128v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM99.284 197.889l28.512 14.528c9.204-18.063 23.89-32.75 41.953-41.953l-29.055-57.024c-30.106 15.34-54.583 39.817-69.923 69.923l28.512 14.528zM85.333 691.2h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM290.133 896v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM99.284 826.112l-28.512 14.528c15.34 30.106 39.817 54.579 69.923 69.922l29.055-57.024c-18.063-9.207-32.75-23.893-41.953-41.954l-28.512 14.528zM938.667 691.2h-32c0 36.373-0.026 61.726-1.638 81.463-1.583 19.366-4.531 30.494-8.823 38.921l57.024 29.056c9.655-18.957 13.683-39.441 15.586-62.763 1.877-22.95 1.852-51.362 1.852-86.677h-32zM733.867 896v32c35.315 0 63.727 0.026 86.677-1.852 23.322-1.903 43.806-5.931 62.763-15.586l-29.056-57.024c-8.427 4.292-19.554 7.241-38.921 8.823-19.738 1.613-45.090 1.638-81.463 1.638v32zM924.715 826.112l-28.51-14.528c-9.207 18.061-23.893 32.747-41.954 41.954l29.056 57.024c30.106-15.343 54.579-39.817 69.922-69.922l-28.514-14.528zM938.667 332.8h32c0-35.315 0.026-63.727-1.852-86.676-1.903-23.323-5.931-43.809-15.586-62.762l-57.024 29.055c4.292 8.428 7.241 19.553 8.823 38.919 1.613 19.739 1.638 45.093 1.638 81.464h32zM733.867 128v32c36.373 0 61.726 0.025 81.463 1.638 19.366 1.582 30.494 4.532 38.921 8.826l29.056-57.024c-18.957-9.658-39.441-13.683-62.763-15.589-22.95-1.875-51.362-1.85-86.677-1.85v32zM924.715 197.889l28.514-14.528c-15.343-30.106-39.817-54.583-69.922-69.923l-29.056 57.024c18.061 9.204 32.747 23.89 41.954 41.953l28.51-14.528zM85.333 691.2h32v-349.867h-64v349.867h32zM938.667 341.333h-32v349.867h64v-349.867h-32zM733.867 896v-32h-443.733v64h443.733v-32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "film-play"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1650,
        "id": 370,
        "name": "film-play",
        "prevSize": 32,
        "code": 59926
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 279
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FilmPlayIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FilmPlayIcon = ({ size = 16, ...props }: FilmPlayIconProps) => (
  <IcoMoon iconSet={iconSet} icon="film-play" size={size} {...props} />
);

FilmPlayIcon.displayName = 'FilmPlayIcon';

export default FilmPlayIcon;
