// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 362.667v213.333M512.427 682.667h-0.427M128 321.023v381.953c0 15.723 8.401 30.208 21.946 37.845l341.335 192.397c12.885 7.266 28.553 7.266 41.438 0l341.333-192.397c13.547-7.637 21.948-22.123 21.948-37.845v-381.953c0-15.721-8.401-30.207-21.948-37.842l-341.333-192.399c-12.885-7.264-28.553-7.264-41.438 0l-341.335 192.399c-13.545 7.635-21.946 22.121-21.946 37.842z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "alert-hexagon"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1387,
        "id": 633,
        "name": "alert-hexagon",
        "prevSize": 32,
        "code": 59663
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 16
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface AlertHexagonIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const AlertHexagonIcon = ({ size = 16, ...props }: AlertHexagonIconProps) => (
  <IcoMoon iconSet={iconSet} icon="alert-hexagon" size={size} {...props} />
);

AlertHexagonIcon.displayName = 'AlertHexagonIcon';

export default AlertHexagonIcon;
