// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M693.333 682.667h149.333M248.765 767.863c4.687-13.308 7.235-27.622 7.235-42.53 0-70.694-57.308-128-128-128-14.909 0-29.223 2.547-42.53 7.236M248.765 767.863c12.327 0.137 26.027 0.137 41.369 0.137h200.533M248.765 767.863c-45.272-0.503-72.021-2.85-93.542-13.815-24.085-12.271-43.666-31.851-55.938-55.936-10.965-21.521-13.312-48.273-13.814-93.542M85.47 604.57c-0.137-12.326-0.137-26.027-0.137-41.37v-187.733c0-15.342 0-29.042 0.137-41.369M85.47 334.098c13.306 4.687 27.62 7.235 42.53 7.235 70.692 0 128-57.308 128-128 0-14.909-2.549-29.223-7.235-42.53M85.47 334.098c0.502-45.272 2.849-72.021 13.814-93.542 12.272-24.085 31.853-43.666 55.938-55.938 21.521-10.965 48.27-13.312 93.542-13.814M248.765 170.804c12.326-0.137 26.027-0.137 41.368-0.137h443.733c15.343 0 29.043 0 41.37 0.137M775.236 170.804c-4.689 13.306-7.236 27.62-7.236 42.53 0 70.692 57.306 128 128 128 14.908 0 29.222-2.549 42.53-7.235M775.236 170.804c45.269 0.502 72.021 2.849 93.542 13.814 24.085 12.272 43.665 31.853 55.936 55.938 10.965 21.521 13.312 48.27 13.815 93.542M938.53 334.098c0.137 12.327 0.137 26.027 0.137 41.369v72.533M256.427 469.333h-0.427M490.667 595.563c-60.54-10.155-106.667-62.805-106.667-126.229 0-70.692 57.306-128 128-128 59.721 0 109.892 40.902 124.023 96.222M938.667 682.667c0 94.255-76.412 170.667-170.667 170.667s-170.667-76.412-170.667-170.667c0-94.255 76.412-170.667 170.667-170.667s170.667 76.412 170.667 170.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "money-minus"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1780,
        "id": 240,
        "name": "money-minus",
        "prevSize": 32,
        "code": 60056
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 409
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MoneyMinusIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MoneyMinusIcon = ({ size = 16, ...props }: MoneyMinusIconProps) => (
  <IcoMoon iconSet={iconSet} icon="money-minus" size={size} {...props} />
);

MoneyMinusIcon.displayName = 'MoneyMinusIcon';

export default MoneyMinusIcon;
