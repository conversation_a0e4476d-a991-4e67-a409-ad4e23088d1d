.rt-SelectTrigger {
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  user-select: none;
  vertical-align: top;
  line-height: var(--height);

  font-family: var(--default-font-family);
  font-weight: var(--font-weight-regular);
  font-style: normal;
  text-align: start;

  &:where(:focus-visible) {
    outline: 2px solid var(--brand-accent-40);
    outline-offset: -1px;
  }
}

.rt-SelectTriggerInner {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.rt-SelectIcon {
  flex-shrink: 0;

  :where(.rt-SelectTrigger:not(.rt-variant-ghost)) & {
    opacity: 0.9;
  }
}

.rt-SelectContent {
  --scrollarea-scrollbar-vertical-margin-top: var(--select-content-padding);
  --scrollarea-scrollbar-vertical-margin-bottom: var(--select-content-padding);
  --scrollarea-scrollbar-horizontal-margin-left: var(--select-content-padding);
  --scrollarea-scrollbar-horizontal-margin-right: var(--select-content-padding);

  overflow: hidden;
  background-color: var(--materials-thin);
  backdrop-filter: blur(50px);
  box-shadow:
    0px 7px 10px 0px rgba(0, 0, 0, 0.09),
    0px 40px 80px 0px rgba(0, 0, 0, 0.3);

  &:where([data-side]) {
    min-width: var(--radix-select-trigger-width);
    max-height: var(--radix-select-content-available-height);
    transform-origin: var(--radix-select-content-transform-origin);
  }
}

.rt-SelectViewport {
  box-sizing: border-box;
  padding: var(--select-content-padding);

  :where(.rt-SelectContent:has(.rt-ScrollAreaScrollbar[data-orientation='vertical'])) & {
    padding-right: var(--spatial-3);
  }
}

.rt-SelectItem {
  display: flex;
  align-items: center;
  height: var(--select-item-height);
  padding-left: var(--select-item-indicator-width);
  padding-right: var(--select-item-indicator-width);
  position: relative;
  box-sizing: border-box;
  outline: none;
  scroll-margin: var(--select-content-padding) 0;

  /* Fix sticky text highlighting after selection in Firefox */
  user-select: none;

  /* Cursors */
  cursor: var(--cursor-menu-item);
  &:where([data-disabled]) {
    cursor: default;
  }
}

.rt-SelectItemIndicator {
  position: absolute;
  left: 0;
  width: var(--select-item-indicator-width);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.rt-SelectSeparator {
  height: 1px;
  margin-top: var(--spatial-2);
  margin-bottom: var(--spatial-2);
  /* margin-left: var(--select-item-indicator-width);
  margin-right: var(--select-separator-margin-right); */
}

.rt-SelectLabel {
  display: flex;
  align-items: center;
  height: var(--select-item-height);
  padding-left: var(--select-item-indicator-width);
  padding-right: var(--select-item-indicator-width);
  color: var(--labels-primary);
  font-weight: var(--font-weight-semibold);
  user-select: none;
  cursor: default;

  :where(.rt-SelectItem) + & {
    margin-top: var(--spatial-2);
  }
}

/***************************************************************************************************
 *                                                                                                 *
 * TRIGGER SIZES                                                                                   *
 *                                                                                                 *
 ***************************************************************************************************/

.rt-SelectTrigger {
  color: var(--labels-primary);

  &:where(:not(.rt-variant-ghost)) {
    box-sizing: border-box;
    height: var(--select-trigger-height);
  }
  &:where(.rt-variant-ghost) {
    box-sizing: content-box;
    height: fit-content;
    padding: var(--select-trigger-ghost-padding-y) var(--select-trigger-ghost-padding-x);

    /* We reset the defined margin variables to avoid inheriting them from a higher component */
    /* If a margin IS defined on the component itself, the utility class will win and reset it */
    --margin-top: 0px;
    --margin-right: 0px;
    --margin-bottom: 0px;
    --margin-left: 0px;

    /* Define the overrides to incorporate the negative margins */
    --margin-top-override: calc(var(--margin-top) - var(--select-trigger-ghost-padding-y));
    --margin-right-override: calc(var(--margin-right) - var(--select-trigger-ghost-padding-x));
    --margin-bottom-override: calc(var(--margin-bottom) - var(--select-trigger-ghost-padding-y));
    --margin-left-override: calc(var(--margin-left) - var(--select-trigger-ghost-padding-x));

    /* Reset the overrides on direct children */
    :where(&) > * {
      --margin-top-override: initial;
      --margin-right-override: initial;
      --margin-bottom-override: initial;
      --margin-left-override: initial;
    }

    margin: var(--margin-top-override) var(--margin-right-override) var(--margin-bottom-override)
      var(--margin-left-override);
  }
}

@breakpoints {
  .rt-SelectTrigger {
    &:where(.rt-r-size-1) {
      --select-trigger-height: var(--spatial-9);
      gap: var(--spatial-3);
      font-size: var(--font-size-3);
      line-height: var(--line-height-3);
      letter-spacing: var(--letter-spacing-2);
      border-radius: var(--radius-5);

      &:where(:not(.rt-variant-ghost)) {
        padding: var(--spatial-4);
      }
      &:where(.rt-variant-ghost) {
        --select-trigger-ghost-padding-x: var(--spatial-2);
        --select-trigger-ghost-padding-y: var(--spatial-1);
      }
    }
    &:where(.rt-r-size-2) {
      --select-trigger-height: var(--spatial-10);
      gap: var(--spatial-4);
      font-size: var(--font-size-4);
      line-height: var(--line-height-4);
      letter-spacing: var(--letter-spacing-3);
      border-radius: var(--radius-6);

      &:where(:not(.rt-variant-ghost)) {
        padding: var(--spatial-5);
      }
      &:where(.rt-variant-ghost) {
        --select-trigger-ghost-padding-x: var(--spatial-2);
        --select-trigger-ghost-padding-y: var(--spatial-1);
      }
    }
    &:where(.rt-r-size-3) {
      --select-trigger-height: var(--spatial-11);
      gap: var(--spatial-5);
      font-size: var(--font-size-5);
      line-height: var(--line-height-5);
      letter-spacing: var(--letter-spacing-4);
      border-radius: var(--radius-7);

      &:where(:not(.rt-variant-ghost)) {
        padding: var(--spatial-6);
      }
      &:where(.rt-variant-ghost) {
        --select-trigger-ghost-padding-x: var(--spatial-3);
        --select-trigger-ghost-padding-y: calc(var(--spatial-1) * 1.5);
      }
      & :where(.rt-SelectIcon) {
        width: 11px;
        height: 11px;
      }
    }
  }
}

/***************************************************************************************************
 *                                                                                                 *
 * CONTENT SIZES                                                                                   *
 *                                                                                                 *
 ***************************************************************************************************/

@breakpoints {
  .rt-SelectContent {
    &:where(.rt-r-size-1) {
      --select-content-padding: var(--spatial-1);
      --select-item-height: var(--spatial-5);
      --select-item-indicator-width: calc(var(--spatial-5) / 1.2);
      --select-separator-margin-right: var(--spatial-2);
      border-radius: var(--radius-3);

      & :where(.rt-SelectLabel) {
        font-size: var(--font-size-1);
        letter-spacing: var(--letter-spacing-1);
        line-height: var(--line-height-1);
      }
      & :where(.rt-SelectItem) {
        font-size: var(--font-size-1);
        line-height: var(--line-height-1);
        letter-spacing: var(--letter-spacing-1);
        border-radius: var(--radius-1);
      }
      & :where(.rt-SelectItemIndicatorIcon) {
        width: calc(8px * var(--scaling));
        height: calc(8px * var(--scaling));
      }
    }
    &:where(.rt-r-size-2, .rt-r-size-3) {
      --select-content-padding: var(--spatial-2);
      --select-item-height: var(--spatial-6);
      --select-item-indicator-width: var(--spatial-5);
      --select-separator-margin-right: var(--spatial-3);
      border-radius: var(--radius-4);

      & :where(.rt-SelectLabel) {
        font-size: var(--font-size-2);
        letter-spacing: var(--letter-spacing-2);
        line-height: var(--line-height-2);
      }
      & :where(.rt-SelectItem) {
        line-height: var(--line-height-2);
        border-radius: var(--radius-2);
      }
    }
    &:where(.rt-r-size-2) {
      & :where(.rt-SelectItem) {
        font-size: var(--font-size-2);
        letter-spacing: var(--letter-spacing-2);
      }
      & :where(.rt-SelectItemIndicatorIcon) {
        width: calc(10px * var(--scaling));
        height: calc(10px * var(--scaling));
      }
    }
    &:where(.rt-r-size-3) {
      & :where(.rt-SelectItem) {
        font-size: var(--font-size-3);
        letter-spacing: var(--letter-spacing-3);
      }
      & :where(.rt-SelectItemIndicatorIcon) {
        width: calc(10px * var(--scaling));
        height: calc(10px * var(--scaling));
      }
    }
  }
}

/***************************************************************************************************
 *                                                                                                 *
 * TRIGGER VARIANTS                                                                                *
 *                                                                                                 *
 ***************************************************************************************************/

/* surface */

.rt-SelectTrigger:where(.rt-variant-surface) {
  --input-border-width: 1px;

  background: var(--fills-tertiary);
  box-shadow: inset 0 0 0 var(--input-border-width) var(--brand-gray700);
  color: var(--labels-primary);

  @media (hover: hover) {
    &:where(:hover) {
      background: linear-gradient(0deg, rgba(0, 0, 0, 0.04) 0%, rgba(0, 0, 0, 0.04) 100%), var(--fills-secondary);
      transition: width 0.3s;
      transition-timing-function: cubic-bezier(0.44, 0, 0.56, 1);
    }
  }
  &:where([data-state='open']) {
    box-shadow: inset 0 0 0 1px var(--labels-secondary);
  }
  &:where(:disabled) {
    color: var(--labels-tertiary);
    background-color: var(--fills-tertiary);
  }
  &:where([data-placeholder]) {
    & :where(.rt-SelectTriggerInner) {
      color: var(--labels-secondary);
    }
  }
}

/* classic */
.rt-SelectTrigger:where(.rt-variant-classic) {
  background: var(--fills-tertiary);
  box-shadow:
    inset 0 0 0 1px var(--brand-gray500),
    inset 0 1.5px 2px 0 var(--brand-gray200),
    inset 0 1.5px 2px 0 hexToRgba(var(--grays-black), 0.2);
  color: var(--labels-primary);
  position: relative;
  z-index: 0;

  &::before {
    content: '';
    position: absolute;
    z-index: -1;
    inset: 0;
    border: 2px solid transparent;
    background-clip: content-box;
    border-radius: inherit;
    pointer-events: none;
    background-image:
      linear-gradient(var(--black-a1) -20%, transparent, var(--white-a1) 130%),
      linear-gradient(var(--color-surface), transparent);
  }

  @media (hover: hover) {
    &:where(:hover) {
      background: linear-gradient(0deg, rgba(0, 0, 0, 0.04) 0%, rgba(0, 0, 0, 0.04) 100%), var(--fills-secondary);
      transition: width 0.3s;
      transition-timing-function: cubic-bezier(0.44, 0, 0.56, 1);
    }
  }

  &:where(:disabled) {
    color: var(--labels-tertiary);
    background-color: var(--fills-tertiary);
    background-image: none;
  }

  &:where([data-placeholder]) {
    & :where(.rt-SelectTriggerInner) {
      color: var(--labels-secondary);
    }
  }
}

/* soft / ghost */

.rt-SelectTrigger:where(.rt-variant-soft),
.rt-SelectTrigger:where(.rt-variant-ghost) {
  background: var(--fills-tertiary);
  color: var(--labels-primary);

  @media (hover: hover) {
    &:where(:hover) {
      background: linear-gradient(0deg, rgba(0, 0, 0, 0.04) 0%, rgba(0, 0, 0, 0.04) 100%), var(--fills-secondary);
      transition: width 0.3s;
      transition-timing-function: cubic-bezier(0.44, 0, 0.56, 1);
    }
  }

  &:where([data-placeholder]) {
    & :where(.rt-SelectTriggerInner) {
      color: var(--labels-secondary);
      opacity: 0.6;
    }
  }
}

.rt-SelectTrigger:where(.rt-variant-soft) {
  &:where(:focus-visible) {
    /* Use gray outline when component color is gray */
    outline-color: var(--brand-accent-40);
  }
  &:where(:disabled) {
    color: var(--labels-tertiary);
  }
}

.rt-SelectTrigger:where(.rt-variant-ghost) {
  &:where(:disabled) {
    color: var(--labels-tertiary);
    background-color: transparent;
  }
}

/* all disabled */
.rt-SelectTrigger {
  &:where(:disabled) {
    & :where(.rt-SelectIcon) {
      color: var(--labels-tertiary);
    }
  }
}

/***************************************************************************************************
 *                                                                                                 *
 * MENU VARIANTS                                                                                   *
 *                                                                                                 *
 ***************************************************************************************************/

.rt-SelectContent {
  box-shadow:
    0 0 0 1px var(--brand-gray300),
    0 12px 60px hexToRgba(var(--grays-black), 0.3),
    0 12px 32px -16px var(--brand-gray500);
}

.rt-SelectItem:where([data-disabled]) {
  color: var(--labels-secondary);
}

.rt-SelectSeparator {
  background-color: var(--brand-gray600);
}

/* solid */

.rt-SelectContent:where(.rt-variant-solid) {
  & :where(.rt-SelectItem[data-highlighted]) {
    background-color: var(--brand-accent-40);
    color: var(--brand-accent);
  }
  &:where(.rt-high-contrast) :where(.rt-SelectItem[data-highlighted]) {
    background-color: var(--brand-accent-40);
    color: var(--brand-accent);
  }
}

/* soft */

.rt-SelectContent:where(.rt-variant-soft) {
  & :where(.rt-SelectItem[data-highlighted]) {
    background-color: var(--brand-accent-40);
  }
}
