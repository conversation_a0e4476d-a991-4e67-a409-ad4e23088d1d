// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M811.998 701.628v0zM725.333 701.628v0zM642.048 794.176v0zM528.875 797.222v0zM315.843 937.873l-2.891 31.868h0l2.89-31.868zM141.014 765.897v0zM149.363 803.076v0zM882.987 765.897v0zM874.637 803.076v0zM708.156 937.873v0zM843.098 701.961v0zM885.299 752.802v0zM180.904 701.961v0zM138.702 752.802v0zM173.906 535.659v0zM128.046 490.364v0zM895.953 490.364v0zM850.095 535.659v0zM618.667 330.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM618.24 266.667c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM555.093 416c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM554.667 352c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM683.093 416c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM682.667 352c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM416 701.628v-32h-204v64h204v-32zM811.998 701.628v-32h-86.665v64h86.665v-32zM642.048 794.176l-23.569-21.645c-8.9 9.694-19.55 15.616-34.479 15.616v64c35.789 0 62.741-15.765 81.621-36.331l-23.573-21.641zM584 820.147v-32c-14.037 0-24.124-5.214-32.546-13.598l-45.158 45.346c18.726 18.65 44.501 32.252 77.705 32.252v-32zM725.333 701.628v-32c-19.238 0-36.356 4.194-50.688 14.438-13.888 9.929-21.687 23.138-26.906 33.877-11.644 23.974-13.918 37.879-29.261 54.588l47.142 43.285c23.774-25.894 34.027-58.261 39.684-69.909 3.439-7.070 5.487-9.011 6.558-9.775 0.631-0.452 3.593-2.505 13.47-2.505v-32zM416 701.628v32c10.159 0 16.674 2.138 21.602 4.89 5.295 2.961 10.56 7.65 16.858 15.3 6.737 8.183 12.561 16.994 21.274 29.158 8.183 11.426 18.001 24.41 30.562 36.919l45.158-45.346c-8.789-8.755-16.175-18.351-23.689-28.838-6.98-9.745-15.334-22.174-23.898-32.572-9.003-10.935-20.156-22.165-35.038-30.485-15.249-8.525-32.666-13.026-52.83-13.026v32zM128 618.667h-32c0 63.868 52.311 114.961 116 114.961v-64c-29.095 0-52-23.189-52-50.961h-32zM896 618.667h-32c0 27.772-22.903 50.961-52.002 50.961v64c63.693 0 116.002-51.093 116.002-114.961h-32zM811.998 535.706v32c29.099 0 52.002 23.189 52.002 50.961h64c0-63.868-52.309-114.961-116.002-114.961v32zM212 535.706v-32c-63.689 0-116 51.093-116 114.961h64c0-27.772 22.905-50.961 52-50.961v-32zM669.606 938.667v-32h-315.212v64h315.212v-32zM354.394 938.667v-32c-20.518 0-28.696-0.034-35.66-0.666l-5.781 63.74c10.557 0.96 22.24 0.926 41.441 0.926v-32zM141.014 765.897l-31.363 6.353c3.761 18.573 6.031 29.952 9.071 40.051l61.284-18.449c-1.975-6.562-3.599-14.413-7.628-34.308l-31.363 6.353zM315.843 937.873l2.891-31.872c-65.369-5.926-120.177-50.517-138.729-112.149l-61.283 18.449c26.192 87.010 103.246 149.188 194.231 157.44l2.891-31.868zM882.987 765.897l-31.364-6.353c-4.028 19.895-5.653 27.746-7.629 34.308l61.286 18.449c3.038-10.099 5.308-21.478 9.071-40.051l-31.364-6.353zM669.606 938.667v32c19.2 0 30.882 0.034 41.442-0.926l-2.893-31.868-2.889-31.872c-6.967 0.631-15.142 0.666-35.661 0.666v32zM874.637 803.076l-30.643-9.225c-18.551 61.632-73.357 106.223-138.726 112.149l2.889 31.872 2.893 31.868c90.982-8.252 168.038-70.43 194.231-157.44l-30.643-9.225zM829.641 701.628v32c3.674 0 5.969 0.004 7.714 0.038 1.707 0.030 2.116 0.085 2.022 0.077l3.721-31.782 3.716-31.787c-5.146-0.602-11.051-0.546-17.173-0.546v32zM882.987 765.897l31.364 6.353c1.19-5.892 2.423-11.686 2.842-16.875l-63.791-5.146c0.013-0.149-0.004 0.213-0.307 1.86-0.303 1.685-0.751 3.895-1.472 7.454l31.364 6.353zM843.098 701.961l-3.721 31.782c8.853 1.033 14.66 8.606 14.025 16.486l63.791 5.146c3.465-42.91-28.002-80.243-70.379-85.201l-3.716 31.787zM194.359 701.628v-32c-6.122 0-12.026-0.055-17.173 0.546l7.437 63.569c-0.094 0.009 0.315-0.047 2.023-0.077 1.743-0.034 4.042-0.038 7.713-0.038v-32zM141.014 765.897l31.363-6.353c-0.721-3.558-1.168-5.773-1.474-7.454-0.3-1.647-0.317-2.010-0.305-1.86l-63.793 5.146c0.419 5.188 1.651 10.978 2.845 16.875l31.363-6.353zM180.904 701.961l-3.718-31.787c-42.375 4.958-73.841 42.291-70.38 85.201l63.793-5.146c-0.635-7.881 5.172-15.454 14.024-16.486l-3.718-31.782zM896 464.593h-32v21.069h64v-21.069h-32zM845.333 535.706v-32h-33.335v64h33.335v-32zM811.998 535.706v-32h-599.998v64h599.998v-32zM212 535.706v-32h-33.333v64h33.333v-32zM128 485.662h32v-21.069h-64v21.069h32zM178.667 535.706v-32c-1.286 0-2.074 0-2.676-0.009-0.576-0.004-0.723-0.009-0.706-0.009l-1.379 31.97-1.379 31.97c1.909 0.081 3.997 0.077 6.139 0.077v-32zM128 485.662h-32c0 2.103-0.008 4.198 0.077 6.114l63.938-2.825c0.002 0.038-0.006-0.094-0.010-0.657-0.004-0.589-0.005-1.361-0.005-2.633h-32zM173.906 535.659l1.379-31.97c-8.617-0.371-14.932-7.083-15.27-14.737l-63.938 2.825c1.833 41.463 35.338 74.078 76.451 75.853l1.379-31.97zM896 485.662h-32c0 1.271 0 2.044-0.004 2.633-0.004 0.563-0.013 0.695-0.009 0.657l63.936 2.825c0.085-1.916 0.077-4.011 0.077-6.114h-32zM845.333 535.706v32c2.142 0 4.228 0.004 6.14-0.077l-2.756-63.94c0.017 0-0.132 0.004-0.708 0.009-0.602 0.009-1.391 0.009-2.675 0.009v32zM895.953 490.364l-31.966-1.412c-0.341 7.654-6.656 14.366-15.27 14.737l2.756 63.94c41.114-1.775 74.615-34.389 76.45-75.853l-31.97-1.412zM512 85.333v32c194.782 0 352 155.847 352 347.26h64c0-227.506-186.624-411.26-416-411.26v32zM512 85.333v-32c-229.374 0-416 183.753-416 411.26h64c0-191.413 157.22-347.26 352-347.26v-32zM829.641 701.628v-32h-17.643v64h17.643v-32zM194.359 701.628v32h17.641v-64h-17.641v32zM618.667 298.667v-32h-0.427v64h0.427v-32zM555.093 384v-32h-0.427v64h0.427v-32zM683.093 384v-32h-0.427v64h0.427v-32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "burger"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1498,
        "id": 522,
        "name": "burger",
        "prevSize": 32,
        "code": 59774
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 127
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BurgerIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BurgerIcon = ({ size = 16, ...props }: BurgerIconProps) => (
  <IcoMoon iconSet={iconSet} icon="burger" size={size} {...props} />
);

BurgerIcon.displayName = 'BurgerIcon';

export default BurgerIcon;
