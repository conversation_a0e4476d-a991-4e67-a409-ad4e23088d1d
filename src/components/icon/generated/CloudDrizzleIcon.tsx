// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M300.544 330.397c-4.537-0.293-9.115-0.442-13.729-0.442-111.275 0-201.481 86.598-201.481 193.424 0 81.609 52.648 151.411 127.116 179.819M300.544 330.397c44.844-94.242 143.885-159.731 258.863-159.731 157.094 0 284.446 122.256 284.446 273.067 0 2.586-0.038 5.163-0.111 7.731M300.544 330.397c15.177-0.147 50.271 4.109 69.234 22.314M843.742 451.465c55.296 18.714 94.925 69.316 94.925 128.802 0 54.084-32.759 100.826-80.265 122.931-10.782 5.018-22.323 8.764-34.411 11.042M843.742 451.465c0.034 12.595-4.629 42.33-23.595 60.535M393.481 648.533l-71.111 113.779M571.26 603.021l-142.221 250.313M701.628 648.533l-71.108 113.779"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "cloud-drizzle"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1565,
        "id": 455,
        "name": "cloud-drizzle",
        "prevSize": 32,
        "code": 59841
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 194
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CloudDrizzleIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CloudDrizzleIcon = ({ size = 16, ...props }: CloudDrizzleIconProps) => (
  <IcoMoon iconSet={iconSet} icon="cloud-drizzle" size={size} {...props} />
);

CloudDrizzleIcon.displayName = 'CloudDrizzleIcon';

export default CloudDrizzleIcon;
