// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M675.469 117.2v0zM852.531 289.2v0zM349.153 798.81v0zM300.207 751.262v0zM824.179 798.81v0zM873.126 751.262v0zM300.207 144.739v0zM349.153 97.192v0zM437.333 470.4c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM736 534.4c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM437.333 597.333c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM624 661.333c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM875.273 321.067v0zM150.324 341.318v0zM196.782 335.923c14.115-10.635 16.937-30.699 6.302-44.814s-30.699-16.937-44.814-6.302l38.512 51.116zM649.293 962.701c16.913-5.133 26.462-23.006 21.329-39.915-5.133-16.913-23.006-26.462-39.915-21.329l18.586 61.244zM706.133 810.667v-32h-238.933v64h238.933v-32zM288 636.587h32v-377.173h-64v377.173h32zM885.333 366.133h-32v270.454h64v-270.454h-32zM675.469 117.2l-22.293 22.953 177.058 172 44.591-45.906-177.058-172-22.298 22.953zM467.2 85.333v32h129.075v-64h-129.075v32zM467.2 810.667v-32c-31.876 0-53.913-0.026-71.030-1.382-16.769-1.331-26.023-3.789-32.822-7.155l-28.391 57.357c17.159 8.495 35.566 11.964 56.149 13.598 20.236 1.609 45.245 1.583 76.093 1.583v-32zM288 636.587h-32c0 29.922-0.026 54.349 1.638 74.142 1.699 20.194 5.324 38.417 14.231 55.394l56.677-29.726c-3.301-6.293-5.779-14.942-7.133-31.031-1.387-16.491-1.413-37.769-1.413-68.779h-32zM349.153 798.81l14.196-28.681c-15.132-7.488-27.26-19.354-34.803-33.732l-56.677 29.726c13.933 26.564 36.072 47.991 63.089 61.363l14.195-28.676zM706.133 810.667v32c30.848 0 55.855 0.026 76.092-1.583 20.587-1.634 38.993-5.103 56.149-13.598l-28.39-57.357c-6.797 3.366-16.051 5.824-32.819 7.155-17.118 1.357-39.155 1.382-71.031 1.382v32zM885.333 636.587h-32c0 31.010-0.026 52.288-1.412 68.779-1.353 16.090-3.831 24.738-7.134 31.031l56.678 29.726c8.905-16.977 12.531-35.2 14.229-55.394 1.664-19.793 1.638-44.22 1.638-74.142h-32zM824.179 798.81l14.195 28.676c27.017-13.372 49.156-34.799 63.091-61.363l-56.678-29.726c-7.543 14.379-19.669 26.244-34.803 33.732l14.195 28.681zM288 259.413h32c0-31.010 0.026-52.287 1.413-68.778 1.353-16.089 3.832-24.739 7.133-31.032l-56.677-29.728c-8.907 16.98-12.532 35.2-14.231 55.397-1.665 19.794-1.638 44.218-1.638 74.141h32zM467.2 85.333v-32c-30.848 0-55.857-0.023-76.093 1.583-20.583 1.634-38.99 5.104-56.149 13.597l28.391 57.358c6.799-3.365 16.053-5.825 32.822-7.156 17.117-1.359 39.153-1.382 71.030-1.382v-32zM300.207 144.739l28.338 14.864c7.543-14.38 19.671-26.243 34.803-33.732l-28.391-57.358c-27.017 13.372-49.156 34.799-63.089 61.362l28.338 14.864zM437.333 502.4v32h298.667v-64h-298.667v32zM437.333 629.333v32h186.667v-64h-186.667v32zM675.469 117.2l22.298-22.953c-12.348-11.997-26.667-21.559-42.163-28.409l-25.873 58.535c8.691 3.843 16.64 9.17 23.445 15.78l22.293-22.953zM642.667 95.106l12.937-29.268c-18.483-8.173-38.69-12.505-59.328-12.505v64c11.682 0 23.078 2.454 33.455 7.040l12.937-29.268zM642.667 95.106h-32v117.161h64v-117.161h-32zM885.333 366.133h32c0-20.461-4.553-40.438-13.082-58.641l-57.954 27.15c4.604 9.835 7.036 20.555 7.036 31.491h32zM875.273 321.067l28.979-13.575c-7.147-15.254-17.075-29.246-29.427-41.245l-44.591 45.906c6.801 6.608 12.198 14.245 16.064 22.489l28.975-13.575zM754.667 321.067v32h120.606v-64h-120.606v32zM642.667 212.267h-32c0 78.63 65.353 140.8 144 140.8v-64c-45.065 0-80-35.252-80-76.8h-32zM138.667 444.181h32c0-27.906 0.030-46.729 1.329-61.246 1.258-14.048 3.514-21.088 6.267-26.017l-55.879-31.201c-8.905 15.948-12.476 33.010-14.132 51.511-1.615 18.031-1.585 40.203-1.585 66.953h32zM138.667 746.667h32v-302.485h-64v302.485h32zM537.941 938.667v-32h-207.275v64h207.275v-32zM138.667 746.667h-32c0 123.712 100.288 224 224 224v-64c-88.366 0-160-71.633-160-160h-32zM150.324 341.318l27.939 15.601c4.424-7.923 10.688-15.096 18.519-20.995l-38.512-51.116c-14.632 11.024-26.936 24.882-35.886 40.911l27.94 15.6zM537.941 938.667v32c48.644 0 83.942 0.354 111.351-7.966l-18.586-61.244c-16 4.855-39.3 5.21-92.766 5.21v32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "file-duplicate"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1633,
        "id": 387,
        "name": "file-duplicate",
        "prevSize": 32,
        "code": 59909
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 262
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FileDuplicateIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FileDuplicateIcon = ({ size = 16, ...props }: FileDuplicateIconProps) => (
  <IcoMoon iconSet={iconSet} icon="file-duplicate" size={size} {...props} />
);

FileDuplicateIcon.displayName = 'FileDuplicateIcon';

export default FileDuplicateIcon;
