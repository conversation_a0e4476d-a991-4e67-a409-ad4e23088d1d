// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 128v768M938.667 150.24v723.523c0 16.981-19.298 27.682-34.914 19.354l-678.477-361.762c-15.923-8.486-15.923-30.221 0-38.707l678.477-361.762c15.616-8.327 34.914 2.371 34.914 19.355z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "previous"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1833,
        "id": 187,
        "name": "previous",
        "prevSize": 32,
        "code": 60109
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 462
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface PreviousIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const PreviousIcon = ({ size = 16, ...props }: PreviousIconProps) => (
  <IcoMoon iconSet={iconSet} icon="previous" size={size} {...props} />
);

PreviousIcon.displayName = 'PreviousIcon';

export default PreviousIcon;
