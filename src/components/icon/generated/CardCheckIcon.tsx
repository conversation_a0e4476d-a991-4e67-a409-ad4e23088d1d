// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M704 735.471l40.145 42.125c1.344 1.412 3.716 1.429 5.086 0.038l82.769-84.301M85.333 384v341.333c0 70.694 57.308 128 128 128h298.667M85.333 384v-85.333c0-70.692 57.308-128 128-128h597.333c70.694 0 128 57.308 128 128v85.333M85.333 384h853.333M938.667 384v106.667M234.667 704h106.667M938.667 725.333c0 94.255-76.412 170.667-170.667 170.667s-170.667-76.412-170.667-170.667c0-94.255 76.412-170.667 170.667-170.667s170.667 76.412 170.667 170.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "card-check"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1521,
        "id": 499,
        "name": "card-check",
        "prevSize": 32,
        "code": 59797
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 150
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CardCheckIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CardCheckIcon = ({ size = 16, ...props }: CardCheckIconProps) => (
  <IcoMoon iconSet={iconSet} icon="card-check" size={size} {...props} />
);

CardCheckIcon.displayName = 'CardCheckIcon';

export default CardCheckIcon;
