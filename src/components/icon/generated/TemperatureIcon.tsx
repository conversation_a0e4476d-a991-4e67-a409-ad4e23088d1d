// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 440.887v284.446M298.667 725.333c0-61.658 26.158-117.205 67.981-156.156 1.974-1.835 3.13-4.399 3.13-7.095v-334.527c0-78.547 63.677-142.222 142.222-142.222s142.221 63.675 142.221 142.222v334.527c0 2.697 1.156 5.261 3.132 7.095 41.822 38.95 67.981 94.498 67.981 156.156 0 117.82-95.514 213.333-213.333 213.333s-213.333-95.514-213.333-213.333z",
          "M512 725.333h0.427"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "temperature"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1934,
        "id": 86,
        "name": "temperature",
        "prevSize": 32,
        "code": 60210
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 563
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface TemperatureIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const TemperatureIcon = ({ size = 16, ...props }: TemperatureIconProps) => (
  <IcoMoon iconSet={iconSet} icon="temperature" size={size} {...props} />
);

TemperatureIcon.displayName = 'TemperatureIcon';

export default TemperatureIcon;
