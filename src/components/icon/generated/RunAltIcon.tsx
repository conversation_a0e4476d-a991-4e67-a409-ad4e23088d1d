// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M196.719 348.988c-11.13 12.536-20.394 30.528-27.406 47.218-10.696 25.46 2.552 53.223 28.084 67.166 55.359 30.234 151.924 84.544 217.258 130.035 100.343 69.867 190.409 175.42 316.898 205.602 68.553 16.363 168.252 15.057 181.193-1.148 6.417-8.038 12.032-16.055 16.38-24.154M196.719 348.988c17.644-19.873 35.261-23.849 54.513-42.409 31.631-30.493 36.389-82.717 64.917-89.967 33.384-8.484 59.155-0.063 86.065 20.136 19.007 14.267 17.794 31.846 34.685 48.22 36.732 35.604 92.907 62.689 124.42 51.587s24.661-54.73 58.116-58.88c17.148-2.127 29.798-1.665 43.443 8.186 31.027 22.398 21.461 68.927 26.598 116.050M196.719 348.988c181.297 101.747 280.375 165.789 391.714 277.162 50.492 44.25 189.316 135.714 340.693 147.558M929.126 773.709c10.598-19.742 13.666-39.949 2.479-62.059-4.356-8.614-13.521-17.306-21.705-23.915-10.048-8.111-21.956-13.875-34.185-18.816l-3.085-1.246c-26.978-10.906-51.563-26.479-70.835-47.031-8.819-9.399-17.967-19.622-24.973-28.553-27.994-35.682-43.793-63.851-57.847-97.122M689.476 401.911c1.331 12.212 3.648 24.465 7.915 36.348 7.671 21.359 14.4 39.701 21.585 56.708M689.476 401.911l-105.075 35.9M718.976 494.967l-60.821 19.268M429.039 796.151h-343.706M251.232 661.653h-165.898"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "run-alt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1856,
        "id": 164,
        "name": "run-alt",
        "prevSize": 32,
        "code": 60132
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 485
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface RunAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const RunAltIcon = ({ size = 16, ...props }: RunAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="run-alt" size={size} {...props} />
);

RunAltIcon.displayName = 'RunAltIcon';

export default RunAltIcon;
