// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M938.667 512c0-235.642-191.027-426.667-426.667-426.667M938.667 512h-426.667M938.667 512c0 117.82-47.757 224.486-124.966 301.7M512 85.333c-235.642 0-426.667 191.025-426.667 426.667 0 235.639 191.025 426.667 426.667 426.667 117.82 0 224.486-47.757 301.7-124.966M512 85.333v426.667M512 512l301.7 301.7"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "chart-alt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1534,
        "id": 486,
        "name": "chart-alt",
        "prevSize": 32,
        "code": 59810
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 163
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ChartAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ChartAltIcon = ({ size = 16, ...props }: ChartAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="chart-alt" size={size} {...props} />
);

ChartAltIcon.displayName = 'ChartAltIcon';

export default ChartAltIcon;
