// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M570.825 122.824v0zM773.175 325.176v0zM197.889 924.715v0zM141.951 868.779v0zM141.951 155.223v0zM197.889 99.284v0zM799.168 362.667v0zM512 970.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM778.667 640c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM621.141 808.286c-11.81-13.15-32.038-14.234-45.188-2.428-13.15 11.81-14.238 32.043-2.428 45.193l47.616-42.765zM694.106 937.425v0zM699.721 937.463v0zM919.488 747.068c12.002-12.975 11.217-33.22-1.754-45.222-12.975-12.002-33.22-11.217-45.222 1.754l46.976 43.469zM128 733.867h32v-443.733h-64v443.733h32zM570.825 122.824l-22.63 22.627 202.355 202.353 45.252-45.255-202.351-202.353-22.626 22.627zM332.8 85.333v32h147.516v-64h-147.516v32zM332.8 938.667v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM128 733.867h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM197.889 924.715l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM128 290.133h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM332.8 85.333v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM141.951 155.223l28.512 14.528c9.204-18.063 23.89-32.75 41.953-41.953l-29.055-57.024c-30.106 15.34-54.583 39.817-69.923 69.923l28.512 14.528zM570.825 122.824l22.626-22.627c-13.679-13.681-29.585-24.629-46.861-32.492l-26.513 58.252c10.368 4.718 19.908 11.285 28.117 19.495l22.63-22.627zM533.333 96.83l13.257-29.126c-20.617-9.381-43.191-14.371-66.274-14.371v64c13.85 0 27.392 2.993 39.761 8.623l13.257-29.126zM533.333 96.83h-32v137.836h64v-137.836h-32zM810.667 415.686h32c0-23.087-4.992-45.66-14.37-66.274l-58.253 26.509c5.632 12.372 8.623 25.913 8.623 39.765h32zM799.168 362.667l29.129-13.254c-7.863-17.277-18.812-33.183-32.495-46.863l-45.252 45.255c8.209 8.209 14.775 17.75 19.494 28.117l29.124-13.254zM661.333 362.667v32h137.835v-64h-137.835v32zM533.333 234.667h-32c0 88.366 71.633 160 160 160v-64c-53.018 0-96-42.981-96-96h-32zM512 938.667v-32h-179.2v64h179.2v-32zM810.667 415.686h-32v224.314h64v-224.314h-32zM597.333 829.666l-23.808 21.385 96.772 107.755 47.616-42.765-96.772-107.755-23.808 21.38zM699.721 937.463l23.488 21.73 196.279-212.126-46.976-43.469-196.279 212.13 23.488 21.734zM694.106 937.425l-23.808 21.38c14.076 15.676 38.63 15.825 52.911 0.393l-46.976-43.469c11.281-12.19 30.554-12.079 41.681 0.311l-23.808 21.385z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "file-check"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1628,
        "id": 392,
        "name": "file-check",
        "prevSize": 32,
        "code": 59904
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 257
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FileCheckIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FileCheckIcon = ({ size = 16, ...props }: FileCheckIconProps) => (
  <IcoMoon iconSet={iconSet} icon="file-check" size={size} {...props} />
);

FileCheckIcon.displayName = 'FileCheckIcon';

export default FileCheckIcon;
