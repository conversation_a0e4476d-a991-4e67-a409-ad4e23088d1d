// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M775.356 299.672c61.218 61.443 99.422 142.188 108.092 228.477 8.67 86.285-12.723 172.779-60.54 244.74-47.812 71.962-119.091 124.937-201.681 149.901-82.594 24.969-171.396 20.373-251.271-12.988-79.877-33.365-145.887-93.44-186.784-169.988s-54.151-164.834-37.503-249.813c16.649-84.978 62.168-161.394 128.804-216.227 65.535-53.926 147.506-83.694 232.357-84.465 2.364-0.022 3.029-1.296 1.075-2.624-24.077-16.383-175.715-101.352-175.715-101.352M343.393 452.407h24.688c21.283 0 38.537 17.075 38.537 38.135v212.143M596.297 452.407c46.366 0 84.301 42.103 84.301 93.559v63.155c0 51.46-37.935 93.564-84.301 93.564s-84.301-42.103-84.301-93.564v-63.155c0-51.925 37.935-93.559 84.301-93.559z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "round",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "forward-10sec"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "round",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1675,
        "id": 345,
        "name": "forward-10sec",
        "prevSize": 32,
        "code": 59951
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 304
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface Forward-10secIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const Forward-10secIcon = ({ size = 16, ...props }: Forward-10secIconProps) => (
  <IcoMoon iconSet={iconSet} icon="forward-10sec" size={size} {...props} />
);

Forward-10secIcon.displayName = 'Forward-10secIcon';

export default Forward-10secIcon;
