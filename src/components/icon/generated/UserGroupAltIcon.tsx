// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M719.164 207.803c63.629 0 115.213 51.589 115.213 115.226s-51.584 115.226-115.213 115.226c-3.132 0-6.234-0.124-9.301-0.371M304.837 207.803c-63.629 0-115.21 51.589-115.21 115.226s51.581 115.226 115.21 115.226c2.804 0 5.586-0.098 8.34-0.299M785.105 536.986c78.75 5.73 139.234 69.619 150.515 147.78l2.193 15.202c6.498 45.047-24.789 87.091-70.037 91.947-4.971 0.533-9.894 1.041-14.771 1.523M238.893 536.986c-78.75 5.73-139.232 69.623-150.511 147.78l-2.194 15.202c-6.501 45.047 24.788 87.091 70.036 91.947 4.97 0.533 9.893 1.041 14.772 1.523M635.443 316.445c0 68.729-55.71 124.443-124.429 124.443s-124.427-55.714-124.427-124.443c0-68.729 55.708-124.445 124.427-124.445s124.429 55.716 124.429 124.445zM350.728 822.746c114.86 12.254 206.094 12.395 320.786 0.094 48.866-5.239 82.662-50.65 75.639-99.302l-2.368-16.418c-12.181-84.412-77.504-153.417-162.556-159.599-48.516-3.529-93.734-3.524-142.349 0.017-85.084 6.199-150.452 75.209-162.639 159.654l-2.331 16.158c-7.032 48.725 26.872 94.174 75.817 99.396z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "user-group-alt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1976,
        "id": 44,
        "name": "user-group-alt",
        "prevSize": 32,
        "code": 60252
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 605
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface UserGroupAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const UserGroupAltIcon = ({ size = 16, ...props }: UserGroupAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="user-group-alt" size={size} {...props} />
);

UserGroupAltIcon.displayName = 'UserGroupAltIcon';

export default UserGroupAltIcon;
