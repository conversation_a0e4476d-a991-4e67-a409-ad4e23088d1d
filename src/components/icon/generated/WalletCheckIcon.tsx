// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M192 756.804l40.145 42.125c1.346 1.412 3.718 1.429 5.085 0.038l82.77-84.301M896 491.076c-6.703-0.41-14.972-0.41-25.6-0.41h-81.067c-47.13 0-85.333 38.204-85.333 85.333s38.204 85.333 85.333 85.333h81.067c10.628 0 18.897 0 25.6-0.41M896 491.076c8.367 0.512 14.302 1.66 19.371 4.241 8.030 4.092 14.554 10.615 18.645 18.645 4.651 9.126 4.651 21.073 4.651 44.971v34.133c0 23.898 0 35.844-4.651 44.971-4.092 8.030-10.615 14.554-18.645 18.645-5.069 2.581-11.004 3.729-19.371 4.241M896 491.076v-51.61c0-71.687 0-107.53-13.952-134.911-12.271-24.085-31.851-43.666-55.936-55.938-27.383-13.951-63.223-13.951-134.912-13.951h-72.533M896 660.924v51.61c0 71.689 0 107.529-13.952 134.912-12.271 24.085-31.851 43.665-55.936 55.936-27.383 13.952-63.223 13.952-134.912 13.952h-200.533M85.708 384c-0.374 15.767-0.374 34.012-0.374 55.467v72.533M85.708 384c0.876-36.917 3.801-60.258 13.577-79.444 12.272-24.085 31.853-43.666 55.938-55.938 27.38-13.951 63.224-13.951 134.911-13.951h328.533M85.708 384v-72.533c0-71.687 0-107.53 13.951-134.911 12.272-24.085 31.853-43.666 55.938-55.938 27.381-13.951 63.224-13.951 134.911-13.951h200.159c70.694 0 128 57.308 128 128M256 405.333h256M426.667 746.667c0 94.255-76.41 170.667-170.667 170.667s-170.667-76.412-170.667-170.667c0-94.255 76.41-170.667 170.667-170.667s170.667 76.412 170.667 170.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "wallet-check"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 2000,
        "id": 20,
        "name": "wallet-check",
        "prevSize": 32,
        "code": 60276
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 629
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface WalletCheckIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const WalletCheckIcon = ({ size = 16, ...props }: WalletCheckIconProps) => (
  <IcoMoon iconSet={iconSet} icon="wallet-check" size={size} {...props} />
);

WalletCheckIcon.displayName = 'WalletCheckIcon';

export default WalletCheckIcon;
