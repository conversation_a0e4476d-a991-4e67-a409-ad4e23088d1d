// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M938.667 321.24c0-2.184-0.678-4.367-2.035-6.034l-189.965-187.206M938.667 321.24c0 2.184-0.678 4.368-2.035 6.034l-189.965 187.205M938.667 321.24h-565.953c-158.716 0-287.38 128.663-287.38 287.379s128.664 287.381 287.38 287.381h373.953"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "arrow-turn-right"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1425,
        "id": 595,
        "name": "arrow-turn-right",
        "prevSize": 32,
        "code": 59701
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 54
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ArrowTurnRightIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ArrowTurnRightIcon = ({ size = 16, ...props }: ArrowTurnRightIconProps) => (
  <IcoMoon iconSet={iconSet} icon="arrow-turn-right" size={size} {...props} />
);

ArrowTurnRightIcon.displayName = 'ArrowTurnRightIcon';

export default ArrowTurnRightIcon;
