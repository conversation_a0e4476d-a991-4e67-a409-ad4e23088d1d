// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M117.649 738.893c21.378 2.091 45.845-19.332 57.91-52.561 13.731-37.815 6.198-76.595-16.823-86.618M117.649 738.893c-2.951-0.29-5.842-1.028-8.634-2.244M117.649 738.893l-8.634-2.244M117.649 738.893l150.967 39.185M109.015 736.649c-23.022-10.022-30.554-48.802-16.823-86.613 13.73-37.815 43.523-60.343 66.545-50.321M158.736 599.714l112.93 33.762M906.351 738.893c-21.376 2.091-45.845-19.332-57.911-52.561-13.73-37.815-6.199-76.595 16.823-86.618M906.351 738.893c2.948-0.29 5.841-1.028 8.636-2.244M906.351 738.893l8.636-2.244M906.351 738.893l-150.967 39.185M914.987 736.649c23.019-10.022 30.554-48.802 16.823-86.613-13.73-37.815-43.524-60.343-66.547-50.321M865.263 599.714l-120.252 35.951M271.666 633.476l240.334 71.842M271.666 633.476c-36.419-44.318-58.333-101.355-58.333-163.593 0-115.943 169.86-312.429 230.396-378.772 9.361-10.26 26.244-6.096 32.222 6.508 15.044 42.921 41.638 81.321 71.808 117.403 6.575 7.864 18.667 7.152 24.026-1.616 10.402-17.026 19.204-34.817 25.506-53.63 4.855-10.714 18.573-14.254 26.18-5.532 49.186 56.392 187.196 223.405 187.196 321.954 0 63.036-25.28 119.799-65.655 159.467M512 705.318l233.011-69.653M512 705.318l243.383 72.759M755.383 778.078l164.335 49.126c18.419 8.021 24.448 39.044 13.461 69.299-10.987 30.251-34.82 48.277-53.239 40.256l-367.94-95.505M512 841.254l-367.94 95.505c-18.419 8.021-42.255-10.005-53.24-40.256-10.985-30.255-4.959-61.278 13.46-69.299l164.336-49.126M512 841.254l-243.384-63.177M516.493 597.333c-49.609 0-89.826-38.123-89.826-85.15 0-38.647 60.663-104.143 82.283-126.257 3.345-3.42 9.374-2.032 11.511 2.17 4.484 11.945 11.844 22.84 20.425 33.111 4.749 5.686 14.319 5.189 17.818-1.19 1.984-3.619 3.708-7.346 5.090-11.203 1.737-3.571 6.635-4.751 9.353-1.844 17.566 18.797 66.854 74.467 66.854 107.316 0 39.974-32.674 72.38-72.981 72.38-0.862 0-1.719-0.013-2.573-0.043-2.517-0.090-5.022 0.41-7.245 1.481-12.22 5.901-26.057 9.229-40.708 9.229z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "campfire"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1517,
        "id": 503,
        "name": "campfire",
        "prevSize": 32,
        "code": 59793
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 146
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CampfireIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CampfireIcon = ({ size = 16, ...props }: CampfireIconProps) => (
  <IcoMoon iconSet={iconSet} icon="campfire" size={size} {...props} />
);

CampfireIcon.displayName = 'CampfireIcon';

export default CampfireIcon;
