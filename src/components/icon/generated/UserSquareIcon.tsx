// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M607.164 701.781v0zM823.885 914.641l-31.671 4.57 0.073 0.499 0.090 0.499 31.509-5.568zM417.379 701.807v0zM200.543 914.735l31.622 4.919 0.050-0.35-31.672-4.57zM155.223 924.715v0zM99.284 868.779v0zM924.715 868.779v0zM868.779 924.715v0zM868.779 99.284v0zM924.715 155.223v0zM155.223 99.284v0zM99.284 155.223v0zM197.226 936.060v0zM654.101 417.213h-32c0 60.88-49.34 110.224-110.191 110.224v64c96.209 0 174.191-78.012 174.191-174.224h-32zM511.91 559.437v-32c-60.851 0-110.193-49.344-110.193-110.224h-64c0 96.212 77.982 174.224 174.193 174.224v-32zM369.717 417.213h32c0-60.88 49.342-110.222 110.193-110.222v-64c-96.211 0-174.193 78.008-174.193 174.222h32zM511.91 274.991v32c60.851 0 110.191 49.342 110.191 110.222h64c0-96.214-77.982-174.222-174.191-174.222v32zM417.379 701.807l2.326 31.915c63.269-4.612 121.999-4.621 185.137-0.026l2.321-31.915 2.325-31.915c-66.24-4.821-128.081-4.813-194.436 0.026l2.326 31.915zM607.164 701.781l-2.321 31.915c96.422 7.014 172.979 85.756 187.371 185.515l63.343-9.139c-18.091-125.402-115.708-230.724-246.067-240.205l-2.325 31.915zM417.379 701.807l-2.326-31.915c-130.394 9.502-228.082 114.816-246.182 240.277l63.344 9.135c14.397-99.793 91.010-178.551 187.49-185.583l-2.326-31.915zM290.133 85.333v32h443.733v-64h-443.733v32zM938.667 290.133h-32v443.733h64v-443.733h-32zM733.867 938.667v-32h-443.733v64h443.733v-32zM85.333 733.867h32v-443.733h-64v443.733h32zM85.333 733.867h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM155.223 924.715l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM938.667 733.867h-32c0 36.373-0.026 61.726-1.638 81.463-1.583 19.366-4.531 30.494-8.823 38.921l57.024 29.056c9.655-18.957 13.683-39.441 15.586-62.763 1.877-22.95 1.852-51.362 1.852-86.677h-32zM924.715 868.779l-28.51-14.528c-9.207 18.061-23.893 32.747-41.954 41.954l29.056 57.024c30.106-15.343 54.579-39.817 69.922-69.922l-28.514-14.528zM733.867 85.333v32c36.373 0 61.726 0.025 81.463 1.638 19.366 1.582 30.494 4.532 38.921 8.826l29.056-57.024c-18.957-9.658-39.441-13.683-62.763-15.589-22.95-1.875-51.362-1.85-86.677-1.85v32zM938.667 290.133h32c0-35.315 0.026-63.727-1.852-86.676-1.903-23.323-5.931-43.809-15.586-62.762l-57.024 29.055c4.292 8.428 7.241 19.553 8.823 38.919 1.613 19.739 1.638 45.093 1.638 81.464h32zM868.779 99.284l-14.528 28.512c18.061 9.204 32.747 23.89 41.954 41.953l57.024-29.055c-15.343-30.106-39.817-54.583-69.922-69.923l-14.528 28.512zM290.133 85.333v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM85.333 290.133h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM155.223 99.284l-14.528-28.512c-30.106 15.34-54.583 39.817-69.923 69.923l57.024 29.055c9.204-18.063 23.89-32.75 41.953-41.953l-14.528-28.512zM290.133 938.667v-32c-41.768 0-68.845-0.051-89.253-2.398l-7.308 63.582c24.926 2.867 56.379 2.816 96.561 2.816v-32zM197.226 936.060l3.654-31.791c-14.695-1.685-23.923-4.395-31.13-8.064l-29.055 57.024c16.254 8.282 33.592 12.407 52.878 14.622l3.654-31.791zM200.543 914.735l-31.619-4.915-3.317 21.325 63.239 9.835 3.317-21.325-31.62-4.919zM733.867 938.667v32c40.683 0 72.435 0.055 97.562-2.931l-7.552-63.552c-20.454 2.428-47.68 2.483-90.010 2.483v32zM827.652 935.957l3.776 31.778c18.889-2.244 35.904-6.37 51.878-14.507l-29.056-57.024c-7.083 3.605-16.119 6.285-30.374 7.979l3.776 31.774zM823.885 914.641l-31.509 5.568 3.763 21.316 63.027-11.132-3.767-21.321-31.514 5.568z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "user-square"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1982,
        "id": 38,
        "name": "user-square",
        "prevSize": 32,
        "code": 60258
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 611
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface UserSquareIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const UserSquareIcon = ({ size = 16, ...props }: UserSquareIconProps) => (
  <IcoMoon iconSet={iconSet} icon="user-square" size={size} {...props} />
);

UserSquareIcon.displayName = 'UserSquareIcon';

export default UserSquareIcon;
