// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 375.467c0-71.687 0-107.53 13.951-134.911 12.272-24.085 31.853-43.666 55.938-55.938 27.38-13.951 63.224-13.951 134.911-13.951h443.733c71.689 0 107.529 0 134.912 13.951 24.085 12.272 43.665 31.853 55.936 55.938 13.952 27.38 13.952 63.224 13.952 134.911v273.067c0 71.689 0 107.529-13.952 134.912-12.271 24.085-31.851 43.665-55.936 55.936-27.383 13.952-63.223 13.952-134.912 13.952h-443.733c-71.687 0-107.53 0-134.911-13.952-24.085-12.271-43.666-31.851-55.938-55.936-13.951-27.383-13.951-63.223-13.951-134.912v-273.067z",
          "M469.333 551.253c0-28.676 0-43.012 5.581-53.965 4.907-9.634 12.74-17.468 22.374-22.374 10.953-5.581 25.289-5.581 53.965-5.581h177.493c28.676 0 43.012 0 53.965 5.581 9.634 4.907 17.468 12.74 22.374 22.374 5.581 10.953 5.581 25.289 5.581 53.965v92.16c0 28.676 0 43.012-5.581 53.965-4.907 9.634-12.74 17.468-22.374 22.374-10.953 5.581-25.289 5.581-53.965 5.581h-177.493c-28.676 0-43.012 0-53.965-5.581-9.634-4.907-17.468-12.74-22.374-22.374-5.581-10.953-5.581-25.289-5.581-53.965v-92.16z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "picture-in-picture"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1822,
        "id": 198,
        "name": "picture-in-picture",
        "prevSize": 32,
        "code": 60098
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 451
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface PictureInPictureIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const PictureInPictureIcon = ({ size = 16, ...props }: PictureInPictureIconProps) => (
  <IcoMoon iconSet={iconSet} icon="picture-in-picture" size={size} {...props} />
);

PictureInPictureIcon.displayName = 'PictureInPictureIcon';

export default PictureInPictureIcon;
