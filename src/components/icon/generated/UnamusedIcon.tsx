// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M298.667 426.667c9.483-5.672 32.459-17.067 64-17.067s54.517 11.395 64 17.067M597.333 426.667c9.485-5.672 32.461-17.067 64-17.067s54.515 11.395 64 17.067M618.667 640l-149.333 64M938.667 512c0 235.639-191.027 426.667-426.667 426.667-235.642 0-426.667-191.027-426.667-426.667 0-235.642 191.025-426.667 426.667-426.667 235.639 0 426.667 191.025 426.667 426.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "unamused"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1969,
        "id": 51,
        "name": "unamused",
        "prevSize": 32,
        "code": 60245
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 598
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface UnamusedIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const UnamusedIcon = ({ size = 16, ...props }: UnamusedIconProps) => (
  <IcoMoon iconSet={iconSet} icon="unamused" size={size} {...props} />
);

UnamusedIcon.displayName = 'UnamusedIcon';

export default UnamusedIcon;
