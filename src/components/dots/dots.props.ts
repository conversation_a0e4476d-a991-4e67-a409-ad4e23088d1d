import type { PropDef } from '@props';

const sizes = ['1', '2', '3', '4', '5'] as const;

const dotsPropDefs = {
  total: { type: 'number', default: 3, required: true },
  current: { type: 'number', default: 0, required: true },
  size: { type: 'enum', className: 'rt-r-size', values: sizes, default: '2', required: false },
} satisfies {
  total: PropDef<number>;
  current: PropDef<number>;
  size: PropDef<(typeof sizes)[number]>;
};

export { dotsPropDefs };
