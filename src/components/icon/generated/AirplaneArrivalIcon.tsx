// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M132.741 853.333h758.519M184.554 713.412l686.264-225.988c22.882-7.535 39.573-26.615 43.396-49.604l24.175-145.353c2.129-12.802-8.196-24.38-21.662-24.297l-63.97 0.395c-26.031 0.161-49.481 15.17-59.708 38.214l-5.329 12.005c-8.247 18.586-25.263 32.229-45.743 36.674l-117.111 25.42c-9.246 2.006-17.566-5.823-15.697-14.765l34.419-164.56c2.441-11.681-5.7-22.974-17.92-24.858l-30.537-4.706c-45.649-7.035-90.94 14.425-112.994 53.539l-116.884 207.293c-1.798 3.191-4.939 5.478-8.616 6.276l-121.888 26.458c-20.48 4.446-37.497 18.086-45.745 36.672-14.255 32.124-27.038 52.105-61.801 67.422-30.291 13.346-36.259 40.738-40.972 69.077-6.107 36.723 19.773 59.972 51.476 73.011 14.864 6.114 31.553 6.707 46.846 1.673z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "airplane-arrival"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1381,
        "id": 639,
        "name": "airplane-arrival",
        "prevSize": 32,
        "code": 59657
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 10
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface AirplaneArrivalIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const AirplaneArrivalIcon = ({ size = 16, ...props }: AirplaneArrivalIconProps) => (
  <IcoMoon iconSet={iconSet} icon="airplane-arrival" size={size} {...props} />
);

AirplaneArrivalIcon.displayName = 'AirplaneArrivalIcon';

export default AirplaneArrivalIcon;
