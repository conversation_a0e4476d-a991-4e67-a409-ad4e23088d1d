// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M213.333 426.667v213.333c0 164.949 133.718 298.667 298.667 298.667s298.667-133.717 298.667-298.667v-213.333M213.333 426.667v-42.667c0-164.949 133.718-298.667 298.667-298.667M213.333 426.667h298.667M810.667 426.667v-42.667c0-164.949-133.717-298.667-298.667-298.667M810.667 426.667h-298.667M512 426.667v-341.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "mouse-alt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1791,
        "id": 229,
        "name": "mouse-alt",
        "prevSize": 32,
        "code": 60067
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 420
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MouseAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MouseAltIcon = ({ size = 16, ...props }: MouseAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="mouse-alt" size={size} {...props} />
);

MouseAltIcon.displayName = 'MouseAltIcon';

export default MouseAltIcon;
