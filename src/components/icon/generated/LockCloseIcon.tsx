// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M691.2 298.667c71.689 0 107.529 0 134.912 13.951 24.085 12.272 43.665 31.853 55.936 55.938 13.952 27.38 13.952 63.222 13.952 134.911v230.4c0 71.689 0 107.529-13.952 134.912-12.271 24.085-31.851 43.665-55.936 55.936-27.383 13.952-63.223 13.952-134.912 13.952h-358.4c-71.687 0-107.53 0-134.911-13.952-24.085-12.271-43.666-31.851-55.938-55.936-13.951-27.383-13.951-63.223-13.951-134.912v-230.4c0-71.689 0-107.53 13.951-134.911 12.272-24.085 31.853-43.666 55.938-55.938 27.38-13.951 63.224-13.951 134.911-13.951M512 661.333c47.13 0 85.333-38.204 85.333-85.333s-38.204-85.333-85.333-85.333c-47.13 0-85.333 38.204-85.333 85.333s38.204 85.333 85.333 85.333zM512 661.333v85.333M682.667 256v42.667h-341.333v-42.667c0-94.257 76.41-170.667 170.667-170.667 94.255 0 170.667 76.41 170.667 170.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "lock-close"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1745,
        "id": 275,
        "name": "lock-close",
        "prevSize": 32,
        "code": 60021
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 374
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface LockCloseIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const LockCloseIcon = ({ size = 16, ...props }: LockCloseIconProps) => (
  <IcoMoon iconSet={iconSet} icon="lock-close" size={size} {...props} />
);

LockCloseIcon.displayName = 'LockCloseIcon';

export default LockCloseIcon;
