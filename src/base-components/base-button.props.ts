import { type PropDef, accentColorPropDef, asChildPropDef, highContrastPropDef, radiusPropDef } from '@props';

const sizes = ['1', '2', '3', '4', '5'] as const;
const variants = ['primary', 'secondary', 'primary-light', 'tertiary', 'ghost', 'quaternary', 'material'] as const;

const baseButtonPropDefs = {
  ...asChildPropDef,
  size: { type: 'enum', className: 'rt-r-size', values: sizes, default: '2', responsive: true },
  variant: { type: 'enum', className: 'rt-variant', values: variants, default: 'primary' },
  glass: { type: 'boolean', className: 'rt-glass', default: false },
  ...accentColorPropDef,
  ...highContrastPropDef,
  ...radiusPropDef,
  loading: { type: 'boolean', className: 'rt-loading', default: false },
} satisfies {
  size: PropDef<(typeof sizes)[number]>;
  variant: PropDef<(typeof variants)[number]>;
  glass: PropDef<boolean>;
  loading: PropDef<boolean>;
};

export { baseButtonPropDefs };
