// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M132.741 549.103l14.647 171.947c5.119 60.092 56.469 106.313 118.109 106.313h493.005c61.641 0 112.99-46.221 118.11-106.313l14.647-171.947M132.741 549.103h758.519M132.741 549.103c-26.182 0-47.407-20.766-47.407-46.379v-11.593c0-32.017 26.531-57.971 59.259-57.971h734.814c32.73 0 59.26 25.954 59.26 57.971v11.593c0 25.613-21.227 46.379-47.407 46.379M606.818 178.087h-4.514c-49.873 0-90.3 39.55-90.3 88.337 0 2.439 2.018 4.417 4.514 4.417h180.599c2.492 0 4.514-1.978 4.514-4.417 0-48.787-40.427-88.337-90.3-88.337h-4.514zM606.818 178.087c0-51.226 42.449-92.753 94.814-92.753s94.814 41.527 94.814 92.753v255.074M274.963 938.667h94.813M654.221 938.667h94.818"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bathtub"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1458,
        "id": 562,
        "name": "bathtub",
        "prevSize": 32,
        "code": 59734
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 87
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BathtubIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BathtubIcon = ({ size = 16, ...props }: BathtubIconProps) => (
  <IcoMoon iconSet={iconSet} icon="bathtub" size={size} {...props} />
);

BathtubIcon.displayName = 'BathtubIcon';

export default BathtubIcon;
