// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M335.171 761.566l81.606-107.345M335.171 761.566l45.17 156.399M335.171 761.566h-169.275M688.828 761.566l-81.604-107.345M688.828 761.566h169.276M688.828 761.566l-45.171 156.399M512 203.852v165.926M512 203.852l-128.678-98.772M512 203.852l129.519-98.505M216.902 417.185l141.024 61.24M216.902 417.185l-49.847-156.348M216.902 417.185l-131.568 94.815M807.1 417.185l-139.264 61.24M807.1 417.185l49.843-156.348M807.1 417.185l131.567 94.815M512 369.778l154.074 108.648-58.85 175.795M512 369.778l-154.074 108.648M607.223 654.221h-190.447M416.777 654.221l-58.851-175.795M938.667 512c0-93.892-30.327-180.7-81.724-251.163M938.667 512c0 93.184-29.871 179.392-80.563 249.566M85.333 512c0-93.892 30.328-180.7 81.722-251.163M85.333 512c0 93.184 29.872 179.392 80.563 249.566M167.055 260.837c53.043-72.724 128.526-128.038 216.267-155.757M383.322 105.080c40.604-12.828 83.831-19.747 128.678-19.747 45.154 0 88.67 7.014 129.519 20.013M641.519 105.347c87.381 27.807 162.551 82.999 215.424 155.491M858.103 761.566c-52.501 72.683-127.339 128.171-214.447 156.399M643.657 917.965c-41.463 13.44-85.713 20.702-131.657 20.702s-90.191-7.262-131.658-20.702M380.342 917.965c-87.108-28.228-161.943-83.716-214.445-156.399"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "football"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1673,
        "id": 347,
        "name": "football",
        "prevSize": 32,
        "code": 59949
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 302
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FootballIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FootballIcon = ({ size = 16, ...props }: FootballIconProps) => (
  <IcoMoon iconSet={iconSet} icon="football" size={size} {...props} />
);

FootballIcon.displayName = 'FootballIcon';

export default FootballIcon;
