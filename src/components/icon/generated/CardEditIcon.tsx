// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 384v341.333c0 70.694 57.308 128 128 128h298.667M85.333 384v-85.333c0-70.692 57.308-128 128-128h597.333c70.694 0 128 57.308 128 128v85.333M85.333 384h853.333M938.667 384v106.667M234.667 704h106.667M469.333 704h85.333M645.606 793.911l-5.589 91.426c-0.367 6.025 4.621 11.012 10.645 10.645l91.426-5.589c1.527-0.094 2.965-0.742 4.049-1.826l172.087-172.087c27.255-27.255 27.255-71.45 0-98.705s-71.45-27.255-98.705 0l-172.087 172.087c-1.084 1.084-1.732 2.522-1.826 4.049z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "card-edit"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1522,
        "id": 498,
        "name": "card-edit",
        "prevSize": 32,
        "code": 59798
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 151
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CardEditIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CardEditIcon = ({ size = 16, ...props }: CardEditIconProps) => (
  <IcoMoon iconSet={iconSet} icon="card-edit" size={size} {...props} />
);

CardEditIcon.displayName = 'CardEditIcon';

export default CardEditIcon;
