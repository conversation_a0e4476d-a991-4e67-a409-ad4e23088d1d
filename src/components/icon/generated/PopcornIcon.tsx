// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M473.178 248.794c13.999 3.532 26.091 10.723 35.469 20.26 2.223 2.262 3.337 3.393 4.309 3.718 0.973 0.323 1.668 0.328 2.645 0.018s2.086-1.409 4.305-3.605c22.332-22.095 55.744-32.259 88.913-23.893 28.873 7.282 51.243 27.012 62.613 51.816 0.725 1.582 1.088 2.373 1.553 2.875 0.457 0.49 0.858 0.771 1.472 1.030 0.631 0.265 1.51 0.336 3.273 0.477 4.194 0.336 8.422 1.025 12.642 2.090M511.991 469.333v469.333M511.991 469.333l-170.665-68.267M511.991 469.333l170.667-68.267M511.991 938.667l-141.393-47.987M511.991 938.667l128.542-47.987M341.326 401.067l29.272 489.613M341.326 401.067l-127.177-50.871M370.598 890.679l-106.024-35.985c-1.455-0.495-2.183-0.742-2.749-1.182-0.501-0.388-0.909-0.883-1.194-1.451-0.323-0.64-0.425-1.404-0.628-2.927l-66.458-496.216c-0.51-3.808-0.765-5.712-0.113-6.927 0.569-1.061 1.559-1.832 2.726-2.124 1.338-0.334 3.121 0.379 6.689 1.806l11.303 4.521M682.658 401.067l-42.125 489.613M682.658 401.067l127.095-50.837M640.533 890.679l96.384-35.985c1.327-0.495 1.993-0.742 2.517-1.156 0.465-0.367 0.849-0.828 1.126-1.353 0.311-0.589 0.439-1.289 0.687-2.684l88.585-496.063c0.704-3.952 1.058-5.928 0.431-7.19-0.55-1.1-1.545-1.911-2.735-2.225-1.361-0.36-3.226 0.386-6.955 1.877l-10.82 4.329M214.15 350.196l-0.823-8.863c0-46.019 28.923-85.42 69.933-101.713 1.865-0.741 2.799-1.111 3.374-1.637 0.56-0.511 0.873-0.978 1.133-1.69 0.267-0.732 0.255-1.768 0.231-3.839l-0.005-0.836c0-28.763 16.948-53.649 41.597-65.596 0.92-0.446 1.38-0.669 1.714-0.921 0.328-0.248 0.537-0.454 0.79-0.777 0.258-0.329 0.489-0.787 0.952-1.703 12.202-24.128 37.597-40.718 66.947-40.718 14.441 0 27.924 4.016 39.35 10.969 2.709 1.649 4.062 2.474 5.086 2.551 1.020 0.076 1.698-0.090 2.569-0.629 0.87-0.541 1.677-1.876 3.294-4.549 16.252-26.886 46.161-44.913 80.367-44.913 29.777 0 56.294 13.658 73.387 34.929 1.088 1.356 1.634 2.035 2.21 2.408 0.559 0.364 1.015 0.538 1.677 0.639 0.678 0.103 1.549-0.044 3.29-0.338 4.151-0.701 8.418-1.066 12.77-1.066 27.558 0 51.631 14.626 64.567 36.386 1.365 2.294 2.048 3.442 2.773 3.962 0.742 0.534 1.267 0.722 2.18 0.788 0.887 0.064 2.18-0.394 4.766-1.309 5.948-2.106 12.361-3.255 19.046-3.255 30.929 0 56 24.56 56 54.857 0 10.901-3.243 21.060-8.841 29.599-1.711 2.61-2.569 3.915-2.692 4.893-0.124 0.986-0.013 1.621 0.448 2.503 0.452 0.875 1.626 1.747 3.977 3.491 26.999 20.032 44.442 51.778 44.442 87.515l-0.905 8.896"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "popcorn"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1831,
        "id": 189,
        "name": "popcorn",
        "prevSize": 32,
        "code": 60107
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 460
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface PopcornIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const PopcornIcon = ({ size = 16, ...props }: PopcornIconProps) => (
  <IcoMoon iconSet={iconSet} icon="popcorn" size={size} {...props} />
);

PopcornIcon.displayName = 'PopcornIcon';

export default PopcornIcon;
