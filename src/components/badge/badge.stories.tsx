import type { Meta, StoryObj } from '@storybook/react-vite';
import { fn } from 'storybook/test';

import { Badge } from '@components/badge';
import { badgePropDefs } from '@components/badge/badge.props';
import Icon from '@components/icon';

const meta: Meta<typeof Badge> = {
  title: 'DesignSystem/Badge',
  component: Badge,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    variant: { control: 'inline-radio', options: badgePropDefs.variant.values },
    size: { control: 'inline-radio', options: badgePropDefs.size.values },
    color: { control: 'select', options: badgePropDefs.color.values },
    status: { control: 'inline-radio', options: badgePropDefs.status.values },
    asChild: { control: 'boolean' },
    count: { control: 'number' },
    overflowCount: { control: 'number' },
    dot: { control: 'boolean' },
    showZero: { control: 'boolean' },
    offset: {
      control: 'object',
      description: 'Offset format: [right, top]',
    },
  },
  args: {
    asChild: false,

    onClick: fn(),
  },
};

export default meta;
type Story = StoryObj<typeof Badge>;

export const _badge: Story = {
  args: {
    variant: badgePropDefs.variant.default,
    size: badgePropDefs.size.default,
    color: badgePropDefs.color.default,
    status: badgePropDefs.status.default,
    count: 0,
    overflowCount: 99,
    dot: false,
    showZero: false,
    offset: [0, 0],
  },

  render: (args) => {
    return (
      <Badge {...args}>
        <Icon icon="home-01" className="mr-1" />
        Notification
        <Icon icon="home-01" className="mr-1" />
      </Badge>
    );
  },
};
