// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M343.755 469.197c10.642-1.203 18.912-10.236 18.912-21.197s-8.27-19.994-18.912-21.197M343.755 469.197c-0.795 0.090-1.603 0.137-2.422 0.137-11.782 0-21.333-9.553-21.333-21.333s9.551-21.333 21.333-21.333c0.819 0 1.627 0.047 2.422 0.137M343.755 469.197v-42.394M680.243 469.197c0.794 0.090 1.604 0.137 2.423 0.137 11.78 0 21.333-9.553 21.333-21.333s-9.553-21.333-21.333-21.333c-0.819 0-1.63 0.047-2.423 0.137M680.243 469.197c-10.641-1.203-18.91-10.236-18.91-21.197s8.269-19.994 18.91-21.197M680.243 469.197v-42.394M680.243 426.803v-175.511c0-91.657-75.328-165.959-168.243-165.959-92.92 0-168.245 74.302-168.245 165.959v175.511M640 618.667c0 70.694-57.306 128-128 128s-128-57.306-128-128M235.171 925.41c204.992 17.638 352.52 17.668 553.868 0.141 61.193-5.329 107.861-56.354 106.948-116.941-1.958-130.091-10.667-282.581-49.438-419.148-11.883-41.854-49.293-71-93.052-76.083-173.504-20.153-307.482-20.050-482.805 0.099-43.86 5.040-81.393 34.208-93.292 76.154-38.709 136.453-47.425 288.777-49.387 418.774-0.916 60.672 45.873 111.727 107.157 117.005z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bag-happy"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1448,
        "id": 572,
        "name": "bag-happy",
        "prevSize": 32,
        "code": 59724
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 77
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BagHappyIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BagHappyIcon = ({ size = 16, ...props }: BagHappyIconProps) => (
  <IcoMoon iconSet={iconSet} icon="bag-happy" size={size} {...props} />
);

BagHappyIcon.displayName = 'BagHappyIcon';

export default BagHappyIcon;
