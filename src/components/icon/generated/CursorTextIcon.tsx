// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M320 85.333h56.771c35.362 0 69.274 12.487 94.281 34.713l40.947 36.398M512 156.445l56.567-50.283c15.006-13.336 35.354-20.828 56.572-20.828h78.861M512 156.445v711.11M512 867.554l-40.947 36.399c-25.007 22.225-58.919 34.714-94.281 34.714h-56.771M512 867.554l40.947 36.399c25.007 22.225 58.918 34.714 94.281 34.714h56.772M378.668 512h266.665"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "cursor-text"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1594,
        "id": 426,
        "name": "cursor-text",
        "prevSize": 32,
        "code": 59870
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 223
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CursorTextIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CursorTextIcon = ({ size = 16, ...props }: CursorTextIconProps) => (
  <IcoMoon iconSet={iconSet} icon="cursor-text" size={size} {...props} />
);

CursorTextIcon.displayName = 'CursorTextIcon';

export default CursorTextIcon;
