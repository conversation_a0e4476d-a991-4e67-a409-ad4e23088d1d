// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M800.457 517.333c-0.619-12.668-1.724-25.186-3.298-37.525-3.183-24.947-25.933-42.475-51.878-42.475h-604.195c-25.947 0-48.693 17.527-51.877 42.475-2.553 20.006-3.875 40.478-3.875 61.308 0 152.265 70.661 285.38 176.017 357.628 19.229 13.184 42.725 18.59 66.284 18.59h232.319c22.758 0 45.444-5.235 64.064-17.911M800.457 517.333c0.38 7.872 0.576 15.799 0.576 23.782 0 152.742-71.108 286.217-177.015 358.306M800.457 517.333h30.43c59.524 0 107.78 46.746 107.78 104.414 0 18.923-6.455 33.801-15.36 53.717-69.973 156.535-299.29 223.957-299.29 223.957M433.015 293.333l-5.901-11.809c-12.841-25.678-10.936-56.266 4.988-80.153l1.83-2.743c15.923-23.887 17.826-54.476 4.988-80.153l-5.905-11.809M273.017 320l-5.905-11.809c-12.839-25.678-10.934-56.266 4.99-80.153l1.829-2.743c15.924-23.887 17.829-54.476 4.99-80.153M593.015 320l-5.901-11.809c-12.843-25.678-10.935-56.266 4.988-80.153l1.83-2.743c15.923-23.887 17.826-54.476 4.988-80.153l-5.905-11.809"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "coffee"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1578,
        "id": 442,
        "name": "coffee",
        "prevSize": 32,
        "code": 59854
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 207
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CoffeeIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CoffeeIcon = ({ size = 16, ...props }: CoffeeIconProps) => (
  <IcoMoon iconSet={iconSet} icon="coffee" size={size} {...props} />
);

CoffeeIcon.displayName = 'CoffeeIcon';

export default CoffeeIcon;
