// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M369.788 654.212l284.424-284.425M324.664 175.267l-31.793 0.33c-64.489 0.669-116.605 52.785-117.274 117.274l-0.33 31.793c-0.317 30.578-12.442 59.849-33.839 81.696l-22.248 22.713c-45.128 46.076-45.128 119.778 0 165.854l22.248 22.711c21.397 21.85 33.522 51.119 33.839 81.698l0.33 31.791c0.669 64.491 52.785 116.608 117.274 117.278l31.793 0.329c30.578 0.316 59.849 12.442 81.696 33.839l22.713 22.246c46.076 45.129 119.778 45.129 165.854 0l22.711-22.246c21.85-21.397 51.119-33.523 81.698-33.839l31.791-0.329c64.491-0.67 116.608-52.787 117.278-117.278l0.329-31.791c0.316-30.579 12.442-59.849 33.839-81.698l22.246-22.711c45.129-46.076 45.129-119.778 0-165.854l-22.246-22.713c-21.397-21.846-33.523-51.118-33.839-81.696l-0.329-31.793c-0.67-64.489-52.787-116.605-117.278-117.274l-31.791-0.33c-30.579-0.317-59.849-12.442-81.698-33.839l-22.711-22.248c-46.076-45.128-119.778-45.128-165.854 0l-22.713 22.248c-21.846 21.397-51.118 33.522-81.696 33.839z",
          "M384 384h0.427",
          "M640 640h0.427"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "discount"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1599,
        "id": 421,
        "name": "discount",
        "prevSize": 32,
        "code": 59875
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 228
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface DiscountIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const DiscountIcon = ({ size = 16, ...props }: DiscountIconProps) => (
  <IcoMoon iconSet={iconSet} icon="discount" size={size} {...props} />
);

DiscountIcon.displayName = 'DiscountIcon';

export default DiscountIcon;
