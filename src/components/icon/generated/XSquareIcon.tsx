// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M384 384l128 128M512 512l128 128M512 512l128-128M512 512l-128 128M290.133 938.667h443.733c71.689 0 107.529 0 134.912-13.952 24.085-12.271 43.665-31.851 55.936-55.936 13.952-27.383 13.952-63.223 13.952-134.912v-443.733c0-71.687 0-107.53-13.952-134.911-12.271-24.085-31.851-43.666-55.936-55.938-27.383-13.951-63.223-13.951-134.912-13.951h-443.733c-71.687 0-107.53 0-134.911 13.951-24.085 12.272-43.666 31.853-55.938 55.938-13.951 27.38-13.951 63.224-13.951 134.911v443.733c0 71.689 0 107.529 13.951 134.912 12.272 24.085 31.853 43.665 55.938 55.936 27.38 13.952 63.224 13.952 134.911 13.952z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "x-square"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 2016,
        "id": 4,
        "name": "x-square",
        "prevSize": 32,
        "code": 60292
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 645
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface XSquareIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const XSquareIcon = ({ size = 16, ...props }: XSquareIconProps) => (
  <IcoMoon iconSet={iconSet} icon="x-square" size={size} {...props} />
);

XSquareIcon.displayName = 'XSquareIcon';

export default XSquareIcon;
