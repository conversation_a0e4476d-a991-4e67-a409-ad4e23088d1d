import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';

import { DropdownMenu as BaseDropdownMenu, Button } from '@components';
import { dropdownMenuContentPropDefs } from '@components/dropdown-menu/dropdown-menu.props';

interface DropdownMenuProps {
  size?: (typeof dropdownMenuContentPropDefs.size.values)[number];
  variant?: (typeof dropdownMenuContentPropDefs.variant.values)[number];
  highContrast?: boolean;
  side?: 'top' | 'bottom' | 'left' | 'right';
  align?: 'start' | 'center' | 'end';
  sideOffset?: number;
  collisionPadding?: number;
}

const DropdownMenu = ({
  size,
  variant,
  highContrast,
  side = 'bottom',
  align = 'start',
  sideOffset = 8,
  collisionPadding = 10,
}: DropdownMenuProps) => (
  <BaseDropdownMenu.Root>
    <BaseDropdownMenu.Trigger>
      <Button>
        Options
        <BaseDropdownMenu.TriggerIcon />
      </Button>
    </BaseDropdownMenu.Trigger>
    <BaseDropdownMenu.Content
      size={size}
      variant={variant}
      highContrast={highContrast}
      side={side}
      align={align}
      sideOffset={sideOffset}
      collisionPadding={collisionPadding}
    >
      <BaseDropdownMenu.Item shortcut="⌘ E">Edit</BaseDropdownMenu.Item>
      <BaseDropdownMenu.Item shortcut="⌘ D">Duplicate</BaseDropdownMenu.Item>
      <BaseDropdownMenu.Separator />
      <BaseDropdownMenu.Item shortcut="⌘ N">Archive</BaseDropdownMenu.Item>

      <BaseDropdownMenu.Sub>
        <BaseDropdownMenu.SubTrigger>More</BaseDropdownMenu.SubTrigger>
        <BaseDropdownMenu.SubContent>
          <BaseDropdownMenu.Item>Move to project…</BaseDropdownMenu.Item>
          <BaseDropdownMenu.Item>Move to folder…</BaseDropdownMenu.Item>
          <BaseDropdownMenu.Separator />
          <BaseDropdownMenu.Item>Advanced options…</BaseDropdownMenu.Item>
        </BaseDropdownMenu.SubContent>
      </BaseDropdownMenu.Sub>

      <BaseDropdownMenu.Separator />
      <BaseDropdownMenu.Item>Share</BaseDropdownMenu.Item>
      <BaseDropdownMenu.Item>Add to favorites</BaseDropdownMenu.Item>
      <BaseDropdownMenu.Separator />
      <BaseDropdownMenu.Item shortcut="⌘ ⌫" color="red">
        Delete
      </BaseDropdownMenu.Item>
    </BaseDropdownMenu.Content>
  </BaseDropdownMenu.Root>
);

const meta = {
  title: 'DesignSystem/DropdownMenu',
  component: DropdownMenu,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    size: { control: 'inline-radio', options: dropdownMenuContentPropDefs.size.values },
    variant: { control: 'inline-radio', options: dropdownMenuContentPropDefs.variant.values },
    highContrast: { control: 'boolean' },
    side: { control: 'inline-radio', options: ['top', 'bottom', 'left', 'right'] },
    align: { control: 'inline-radio', options: ['start', 'center', 'end'] },
    sideOffset: { control: 'number' },
    collisionPadding: { control: 'number' },
  },
} satisfies Meta<typeof DropdownMenu>;

export default meta;
type Story = StoryObj<typeof meta>;

export const _dropdownMenu: Story = {
  args: {
    size: dropdownMenuContentPropDefs.size.default,
    variant: dropdownMenuContentPropDefs.variant.default,
    highContrast: false,
    side: 'bottom',
    align: 'start',
    sideOffset: 8,
    collisionPadding: 10,
  },
  render: DropdownMenu,
};
