// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M811.998 537.097c69.956 0 126.669-101.129 126.669-225.881s-56.713-225.882-126.669-225.882M811.998 537.097c-69.952 0-126.665-101.129-126.665-225.881M811.998 537.097h-126.665M685.333 311.216c0-124.751 56.713-225.882 126.665-225.882M685.333 311.216v225.881M811.998 85.333h-557.331c-69.956 0-126.667 101.131-126.667 225.882v627.451l71.529-23.706 47.686 23.706 47.686-23.706 47.686 23.706 47.686-23.706 47.686 23.706 47.689-23.706 47.684 23.706 47.684-23.706 47.689 23.706 56.627-23.706v-377.865",
          "M128 426.667h512",
          "M810.667 264.531v85.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "toilet-paper"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1956,
        "id": 64,
        "name": "toilet-paper",
        "prevSize": 32,
        "code": 60232
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 585
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ToiletPaperIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ToiletPaperIcon = ({ size = 16, ...props }: ToiletPaperIconProps) => (
  <IcoMoon iconSet={iconSet} icon="toilet-paper" size={size} {...props} />
);

ToiletPaperIcon.displayName = 'ToiletPaperIcon';

export default ToiletPaperIcon;
