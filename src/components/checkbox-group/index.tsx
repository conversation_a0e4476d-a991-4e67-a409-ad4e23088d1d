'use client';

import clsx from 'clsx';
import { Context } from 'radix-ui/internal';
import * as React from 'react';

import * as CheckboxGroupPrimitive from '@base-components/base-checkbox-group.primitive';
import { createCheckboxGroupScope } from '@base-components/base-checkbox-group.primitive';
import { ThickCheckIcon } from '@base-components/icons';
import { checkboxGroupRootPropDefs } from '@components/checkbox-group/checkbox-group.props';
import { Flex } from '@components/flex';
import { Text } from '@components/text';
import { type ComponentPropsWithout, type RemovedProps, extractProps } from '@helpers';
import { type GetPropDefTypes, type MarginProps, type Options, marginPropDefs } from '@props';

const CHECKBOX_GROUP_NAME = 'CheckboxGroup';

type ScopedProps<P> = P & { __scopeCheckboxGroup?: Context.Scope };
const [createCheckboxGroupContext] = Context.createContextScope(CHECKBOX_GROUP_NAME, [createCheckboxGroupScope]);
const useCheckboxGroupScope = createCheckboxGroupScope();

type CheckboxGroupRootOwnProps = GetPropDefTypes<typeof checkboxGroupRootPropDefs>;
type CheckboxGroupContextValue = CheckboxGroupRootOwnProps;

const [CheckboxGroupProvider, useCheckboxGroupContext] =
  createCheckboxGroupContext<CheckboxGroupContextValue>(CHECKBOX_GROUP_NAME);

/**
 * CheckboxGroupRoot
 */
export type CheckBoxGroupOption = Options<string> & {
  caption?: React.ReactNode;
};
type CheckboxGroupRootElement = React.ComponentRef<typeof CheckboxGroupPrimitive.Root>;
interface CheckboxGroupRootProps
  extends ComponentPropsWithout<typeof CheckboxGroupPrimitive.Root, 'asChild' | 'color' | 'defaultChecked'>,
    MarginProps,
    CheckboxGroupRootOwnProps {
  options?: CheckBoxGroupOption[];
  labelAlign?: 'left' | 'right';
}

const CheckboxGroup: React.FC<CheckboxGroupRootProps> = (props) => {
  const { options, labelAlign } = props;
  return (
    <CheckboxGroupRoot {...props}>
      {options?.map((option) => (
        <Flex
          gap="3"
          align={option.caption ? 'start' : 'center'}
          justify={labelAlign === 'left' ? 'end' : 'start'}
          direction={labelAlign === 'left' ? 'row-reverse' : 'row'}
        >
          <CheckboxGroupItem value={option.value} disabled={option.disabled}></CheckboxGroupItem>
          <Flex gap={'1'} direction={'column'} align={labelAlign === 'left' ? 'end' : 'start'}>
            <Text as="label" size={props.size} className="rt-checkbox-group-label">
              {option.label}
            </Text>
            {option.caption && (
              <Text className="rt-checkbox-group-options.caption" size={props.size} color="secondary">
                {option.caption}
              </Text>
            )}
          </Flex>
        </Flex>
      ))}
    </CheckboxGroupRoot>
  );
};

const CheckboxGroupRoot = React.forwardRef<CheckboxGroupRootElement, CheckboxGroupRootProps>(
  (
    {
      color = checkboxGroupRootPropDefs.color.default,
      highContrast = checkboxGroupRootPropDefs.highContrast.default,
      size = checkboxGroupRootPropDefs.size.default,
      variant = checkboxGroupRootPropDefs.variant.default,
      ...props
    }: ScopedProps<CheckboxGroupRootProps>,
    forwardedRef,
  ) => {
    const { __scopeCheckboxGroup, className, ...rootProps } = extractProps(props, marginPropDefs);
    const checkboxGroupScope = useCheckboxGroupScope(__scopeCheckboxGroup);
    return (
      <CheckboxGroupProvider
        scope={__scopeCheckboxGroup}
        color={color}
        size={size}
        highContrast={highContrast}
        variant={variant}
      >
        <CheckboxGroupPrimitive.Root
          {...checkboxGroupScope}
          {...rootProps}
          ref={forwardedRef}
          className={clsx('rt-CheckboxGroupRoot', className)}
        />
      </CheckboxGroupProvider>
    );
  },
);
CheckboxGroupRoot.displayName = 'CheckboxGroup.Root';
/** End */

/** CheckboxGroupItem */
type CheckboxGroupItemElement = React.ComponentRef<typeof CheckboxGroupPrimitive.Item>;
interface CheckboxGroupItemProps
  extends ComponentPropsWithout<typeof CheckboxGroupPrimitive.Item, RemovedProps>,
    MarginProps {}

const CheckboxGroupItem = React.forwardRef<CheckboxGroupItemElement, CheckboxGroupItemProps>(
  (_props: ScopedProps<CheckboxGroupItemProps>, forwardedRef) => {
    const { __scopeCheckboxGroup, children, className, style, ...props } = _props;
    const { size } = useCheckboxGroupContext('CheckboxGroupItem', __scopeCheckboxGroup);

    // Render `<Text as="label">` if children are provided, otherwise render
    // the solo checkbox to allow building out your custom layouts with it.
    if (children) {
      return (
        <Text as="label" size={size} className={clsx('rt-CheckboxGroupItem', className)} style={style}>
          <CheckboxGroupItemCheckbox __scopeCheckboxGroup={__scopeCheckboxGroup} {...props} ref={forwardedRef} />
          {children && <span className="rt-CheckboxGroupItemInner">{children}</span>}
        </Text>
      );
    }

    return (
      <CheckboxGroupItemCheckbox
        __scopeCheckboxGroup={__scopeCheckboxGroup}
        {...props}
        ref={forwardedRef}
        className={className}
        style={style}
      />
    );
  },
);
CheckboxGroupItem.displayName = 'CheckboxGroup.Item';
/** End */

/** CheckboxGroupItemCheckbox */
type CheckboxGroupItemCheckboxElement = React.ComponentRef<typeof CheckboxGroupPrimitive.Item>;
interface CheckboxGroupItemCheckboxProps
  extends ComponentPropsWithout<typeof CheckboxGroupPrimitive.Item, RemovedProps> {}
const CheckboxGroupItemCheckbox = React.forwardRef<
  CheckboxGroupItemCheckboxElement,
  ScopedProps<CheckboxGroupItemCheckboxProps>
>(({ __scopeCheckboxGroup, ...props }, forwardedRef) => {
  const context = useCheckboxGroupContext('CheckboxGroupItemCheckbox', __scopeCheckboxGroup);
  const checkboxGroupScope = useCheckboxGroupScope(__scopeCheckboxGroup);
  const { color, className } = extractProps({ ...props, ...context }, checkboxGroupRootPropDefs, marginPropDefs);
  return (
    <CheckboxGroupPrimitive.Item
      {...checkboxGroupScope}
      data-accent-color={color}
      {...props}
      ref={forwardedRef}
      className={clsx('rt-reset', 'rt-BaseCheckboxRoot', 'rt-CheckboxGroupItemCheckbox', className)}
    >
      <CheckboxGroupPrimitive.Indicator {...checkboxGroupScope} asChild className="rt-BaseCheckboxIndicator">
        <ThickCheckIcon />
      </CheckboxGroupPrimitive.Indicator>
    </CheckboxGroupPrimitive.Item>
  );
});
CheckboxGroupItemCheckbox.displayName = 'CheckboxGroup.ItemCheckbox';
/** End */

export { CheckboxGroup, CheckboxGroupRoot as Root, CheckboxGroupItem as Item };
export type { CheckboxGroupItemProps as ItemProps, CheckboxGroupRootProps as RootProps };
