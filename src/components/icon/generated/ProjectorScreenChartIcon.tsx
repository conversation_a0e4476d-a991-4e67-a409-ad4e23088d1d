// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 669.628c-17.673 0-32 14.327-32 32 0 17.677 14.327 32 32 32v-64zM938.667 733.628c17.673 0 32-14.323 32-32 0-17.673-14.327-32-32-32v64zM314.074 559.407c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM378.074 496.196c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM480 559.407c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM544 432.986c0-17.671-14.327-31.998-32-31.998s-32 14.327-32 31.998h64zM645.926 559.407c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM709.926 369.778c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM571.26 879.407h-32c0 15.057-12.207 27.26-27.26 27.26v64c50.402 0 91.26-40.858 91.26-91.26h-32zM512 938.667v-32c-15.053 0-27.26-12.203-27.26-27.26h-64c0 50.402 40.857 91.26 91.259 91.26v-32zM452.74 879.407h32c0-15.053 12.207-27.26 27.26-27.26v-64c-50.402 0-91.259 40.858-91.259 91.26h32zM512 820.147v32c15.053 0 27.26 12.207 27.26 27.26h64c0-50.402-40.858-91.26-91.26-91.26v32zM512 820.147h32v-118.519h-64v118.519h32zM132.741 85.333v32h758.519v-64h-758.519v32zM938.667 132.741h-32v47.407h64v-47.407h-32zM891.26 227.555v-32h-758.519v64h758.519v-32zM85.333 180.148h32v-47.407h-64v47.407h32zM132.741 227.555v-32c-8.509 0-15.407-6.898-15.407-15.407h-64c0 43.856 35.552 79.407 79.407 79.407v-32zM938.667 180.148h-32c0 8.509-6.899 15.407-15.407 15.407v64c43.857 0 79.407-35.552 79.407-79.407h-32zM891.26 85.333v32c8.508 0 15.407 6.898 15.407 15.407h64c0-43.855-35.55-79.407-79.407-79.407v32zM132.741 85.333v-32c-43.855 0-79.407 35.552-79.407 79.407h64c0-8.509 6.898-15.407 15.407-15.407v-32zM891.26 227.555h-32v474.072h64v-474.072h-32zM891.26 701.628v-32h-379.26v64h379.26v-32zM512 701.628v-32h-379.259v64h379.259v-32zM132.741 701.628h32v-474.072h-64v474.072h32zM132.741 701.628v-32h-47.407v64h47.407v-32zM891.26 701.628v32h47.407v-64h-47.407v32zM346.074 559.407h32v-63.211h-64v63.211h32zM512 559.407h32v-126.421h-64v126.421h32zM677.926 559.407h32v-189.629h-64v189.629h32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "projector-screen-chart"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1836,
        "id": 184,
        "name": "projector-screen-chart",
        "prevSize": 32,
        "code": 60112
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 465
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ProjectorScreenChartIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ProjectorScreenChartIcon = ({ size = 16, ...props }: ProjectorScreenChartIconProps) => (
  <IcoMoon iconSet={iconSet} icon="projector-screen-chart" size={size} {...props} />
);

ProjectorScreenChartIcon.displayName = 'ProjectorScreenChartIcon';

export default ProjectorScreenChartIcon;
