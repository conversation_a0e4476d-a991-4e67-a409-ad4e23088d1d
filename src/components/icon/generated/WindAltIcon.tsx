// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 580.267c301.176-91.021 727.842-68.267 727.842-68.267 26.223 0 51.785-7.445 73.097-21.295 21.312-13.845 37.308-33.399 45.734-55.91s8.866-46.851 1.259-69.6c-7.612-22.749-22.895-42.766-43.695-57.238s-46.080-22.673-72.286-23.451c-26.21-0.778-52.023 5.906-73.826 19.114s-38.494 32.275-47.727 54.525c-5.35 12.888-8.047 26.469-8.047 40.078M160.628 694.046c200.784-68.267 527.057-68.267 552.157-68.267 26.219 0 51.78 7.445 73.097 21.291 21.312 13.85 37.308 33.399 45.734 55.915 8.427 22.511 8.866 46.848 1.254 69.598s-22.891 42.769-43.691 57.237c-20.804 14.473-46.084 22.673-72.29 23.454-26.206 0.777-52.023-5.909-73.822-19.115-21.803-13.21-38.494-32.277-47.731-54.524-5.35-12.89-8.043-26.47-8.043-40.081M110.432 443.733c90.353-42.010 338.823-63.015 338.823-63.015 23.599 0 46.605-6.874 65.788-19.656 19.179-12.782 33.574-30.831 41.161-51.61s7.979-43.247 1.131-64.247c-6.852-20.999-20.604-39.476-39.326-52.835s-41.472-20.929-65.058-21.647c-23.586-0.718-46.82 5.452-66.441 17.644s-34.643 29.792-42.956 50.33c-4.815 11.897-7.239 24.433-7.239 36.995"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "wind-alt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 2011,
        "id": 9,
        "name": "wind-alt",
        "prevSize": 32,
        "code": 60287
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 640
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface WindAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const WindAltIcon = ({ size = 16, ...props }: WindAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="wind-alt" size={size} {...props} />
);

WindAltIcon.displayName = 'WindAltIcon';

export default WindAltIcon;
