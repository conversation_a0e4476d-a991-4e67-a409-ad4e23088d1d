import clsx from 'clsx';
import type React from 'react';
import type { ReactNode } from 'react';

import { Flex } from '@components/flex';
import type { IconType } from '@components/icon';
import Icon from '@components/icon';
import { useSidebar } from '@components/sidebar/sidebar-provider';
import { sidebarPropDefs } from '@components/sidebar/sidebar.props';
import { Tooltip } from '@components/tooltip';
import { extractProps } from '@helpers';
import type { GetPropDefTypes } from '@props';

const SidebarMenu: React.FC<React.ComponentProps<'ul'>> = ({ className, ...props }) => {
  return <ul data-slot="sidebar-menu" data-sidebar="menu" className={clsx('rt-sidebar-menu', className)} {...props} />;
};

const SidebarMenuItem: React.FC<React.ComponentProps<'li'>> = ({ className, ...props }) => {
  return (
    <li
      data-slot="sidebar-menu-item"
      data-sidebar="menu-item"
      className={clsx('rt-sidebar-menu-item', className)}
      {...props}
    />
  );
};

const SidebarMenuButton: React.FC<
  React.ComponentProps<'button'> & {
    tooltip?: string | ReactNode;
    icon?: IconType;
    title?: string;
    collapsible?: boolean;
  }
> = ({ className, tooltip, title, icon, collapsible, ...props }) => {
  const { state } = useSidebar();
  const subItem = (
    <button
      data-slot="sidebar-menu-button"
      data-sidebar="menu-button"
      className={clsx('rt-sidebar-menu-button', className)}
      {...props}
    >
      <Flex className="rt-sidebar-menu-button-icon">{icon && <Icon icon={icon} />}</Flex>
      <span>{title}</span>
      <Flex className="rt-sidebar-menu-button-icon" flexGrow={'1'} justify={'end'}>
        {collapsible && <Icon icon="chevron-right" />}
      </Flex>
    </button>
  );

  if (!tooltip) {
    return subItem;
  }

  return (
    <Tooltip content={tooltip} hidden={state !== 'collapsed'} side="right" align="center">
      {subItem}
    </Tooltip>
  );
};

const SidebarMenuSub: React.FC<React.ComponentProps<'ul'>> = ({ className, ...props }) => {
  return (
    <ul
      data-slot="sidebar-menu-sub"
      data-sidebar="menu-sub"
      className={clsx('rt-sidebar-menu-sub', className)}
      {...props}
    />
  );
};

const SidebarRail: React.FC<Omit<React.ComponentProps<'button'>, 'type'> & GetPropDefTypes<typeof sidebarPropDefs>> = (
  p,
) => {
  const { toggleSidebar, state } = useSidebar();
  const { className, ...props } = extractProps(p, sidebarPropDefs);

  return (
    <button
      data-sidebar="rail"
      data-slot="sidebar-rail"
      aria-label="Toggle Sidebar"
      data-state={state}
      tabIndex={-1}
      onClick={toggleSidebar}
      title="Toggle Sidebar"
      className={clsx('rt-sidebar-rail', className)}
      {...props}
    />
  );
};

export { SidebarMenu, SidebarMenuButton, SidebarMenuItem, SidebarMenuSub, SidebarRail };
