// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M241.178 182.288c-11.555 55.481-13.15 115.308-4.623 176.434 8.913 63.884 28.883 129.188 60.096 192.433 15.273 30.946 33.238 61.397 53.917 90.953 9.625 13.756 19.635 27.025 29.991 39.799M241.178 182.288c-95.158 78.252-155.845 196.895-155.845 329.712 0 5.504 0.104 10.978 0.311 16.431 2.091 55.223 14.678 107.759 35.823 155.674 16.34 37.022 37.792 71.287 63.46 101.897M241.178 182.288c58.228-47.884 129.364-80.644 207.402-92.273M448.58 90.015c20.693-3.084 41.869-4.681 63.42-4.681 36.732 0 72.38 4.641 106.385 13.37 57.847 14.846 110.955 41.517 156.591 77.283 78.246 61.326 134.537 149.391 155.106 250.441M448.58 90.015c-17.489 61.921-21.815 130.34-12.732 200.608M435.849 290.622c9.412 72.855 33.242 147.697 71.761 219.347M435.849 290.622c56.145-7.482 115.597-7.161 176.171 1.752 37.764 5.556 75.968 14.452 114.082 26.879 24.755 8.071 48.661 17.368 71.637 27.762 48.555 21.968 92.937 48.84 132.343 79.412M507.61 509.969c6.583-0.055 13.193-0.013 19.831 0.137M507.61 509.969c3.051 5.675 6.195 11.332 9.429 16.964M527.441 510.106c63.514 1.425 129.459 12.352 195.14 33.766 11.089 3.618 22.007 7.479 32.747 11.571M527.441 510.106c-3.401 5.683-6.869 11.29-10.402 16.828M517.039 526.933c-38.852 60.907-85.41 113.024-136.48 154.974M755.328 555.443c-10.884 28.548-23.889 56.909-39.066 84.813-13.73 25.246-28.745 49.203-44.843 71.795-33.707 47.287-72.192 88.576-113.719 123.115-47.185 39.241-98.295 69.764-150.78 90.466M755.328 555.443c60.173 22.946 114.761 53.244 162.419 88.887M380.559 681.907c-60.64 49.813-127.637 85.282-195.633 104.094M406.921 925.632c33.613 8.512 68.817 13.035 105.079 13.035 68.301 0 132.851-16.047 190.089-44.578 45.107-22.485 85.675-52.723 119.953-88.969 42.876-45.333 75.917-100.070 95.706-160.789M406.921 925.632c-88.134-22.323-165.337-72.073-221.994-139.631M917.747 644.331c13.581-41.66 20.919-86.14 20.919-132.331 0-29.308-2.957-57.924-8.585-85.572"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "volleyball"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1992,
        "id": 28,
        "name": "volleyball",
        "prevSize": 32,
        "code": 60268
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 621
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface VolleyballIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const VolleyballIcon = ({ size = 16, ...props }: VolleyballIconProps) => (
  <IcoMoon iconSet={iconSet} icon="volleyball" size={size} {...props} />
);

VolleyballIcon.displayName = 'VolleyballIcon';

export default VolleyballIcon;
