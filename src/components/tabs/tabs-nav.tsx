import React, { type JSX } from 'react';

import Icon from '@components/icon';
import { TabNavLink, TabNavRoot, type TabNavRootProps } from '@components/tabs/tabs-nav-base';
import type { TabsNavItem } from '@components/tabs/type';

export interface TabsNavProps extends TabNavRootProps {
  activeKey?: string;
  defaultActiveKey?: string;
  onTabChange?: (key: string) => void;
  items: TabsNavItem[];
}

export const TabsNav: React.FC<TabsNavProps> = ({ items, defaultActiveKey, ...props }): JSX.Element => {
  const [activeKey, setActiveKey] = React.useState(defaultActiveKey);
  return (
    <TabNavRoot {...props}>
      {items.map((item) => {
        return (
          <TabNavLink
            key={item.key}
            href={item.href}
            active={activeKey === item.key}
            onClick={() => {
              setActiveKey(item.key);
              props.onTabChange?.(item.key);
            }}
            target={item.newTab ? '_blank' : '_self'}
          >
            {item.prefix && <Icon icon={item.prefix} />}
            {item.title}
            {item.suffix && <Icon icon={item.suffix} />}
          </TabNavLink>
        );
      })}
    </TabNavRoot>
  );
};
