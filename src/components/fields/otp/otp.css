.rt-OTPInputRoot {
  box-sizing: border-box;
  height: var(--input-height);
  padding: var(--input-border-width);
  border-radius: var(--input-border-radius);

  display: flex;
  align-items: center;
  gap: var(--input-gap);
}

.rt-OTPInputSeparator {
  margin-inline: 0.25rem;
  font-weight: bold;
  color: var(--brand-gray700);
}

/* Size */

@breakpoints {
  .rt-OTPInputRoot {
    &:where(.rt-r-size-1) {
      --input-height: var(--spatial-8);
      --input-border-radius: var(--radius-3);
      --input-gap: var(--spatial-1);

      font-size: var(--font-size-3);
      line-height: var(--line-height-3);
      letter-spacing: var(--letter-spacing-2);
      padding: var(--spatial-3);

      & :where(.rt-OTPInput) {
        /* Reset size 2 padding bottom */
        padding-bottom: 0px;

        /* Safari credentials autofill icon */
        &::-webkit-textfield-decoration-container {
          padding-right: 0px;
          margin-right: -2px;
        }
      }
    }
    &:where(.rt-r-size-2) {
      --input-height: var(--spatial-9);
      --input-border-radius: var(--radius-4);
      --input-gap: var(--spatial-2);

      font-size: var(--font-size-4);
      line-height: var(--line-height-4);
      letter-spacing: var(--letter-spacing-3);
      padding: var(--spatial-4);

      & :where(.rt-OTPInput) {
        /* Avoid 1px baseline jitter when layout around the text field is subpixel-sized (e.g. vh units). */
        /* Works because as of Nov 2023, Chrome computes input text bounding box height as 16.5px on @2x screens. */
        padding-bottom: 0.5px;

        /* Safari credentials autofill icon */
        &::-webkit-textfield-decoration-container {
          padding-right: 2px;
          margin-right: 0px;
        }
      }
    }
    &:where(.rt-r-size-3) {
      --input-height: var(--spatial-10);
      --input-border-radius: var(--radius-5);
      --input-gap: var(--spatial-3);

      font-size: var(--font-size-5);
      line-height: var(--line-height-5);
      letter-spacing: var(--letter-spacing-4);
      padding: var(--spatial-5);

      & :where(.rt-OTPInput) {
        /* Reset size 2 padding bottom */
        padding-bottom: 0px;

        /* Safari credentials autofill icon */
        &::-webkit-textfield-decoration-container {
          padding-right: 5px;
          margin-right: 0px;
        }
      }
    }
  }
}

/* surface */

.rt-OTPInputRoot:where(.rt-variant-surface) .rt-OTPInput {
  --input-selection-color: var(--brand-accent-button-bezeled-fill-hover);
  --input-focus-color: var(--brand-accent-40);
  --input-border-width: 1px;

  /* Blend inner shadow with page background */
  background: var(--fills-tertiary);
  box-shadow: inset 0 0 0 var(--input-border-width) var(--brand-gray700);
  color: var(--labels-primary);

  &:where(:disabled, :read-only) {
    /* Blend with grey */
    background: var(--fills-tertiary);
    box-shadow: inset 0 0 0 var(--input-border-width) var(--brand-gray600);
  }
}

/* classic */

.rt-OTPInputRoot:where(.rt-variant-classic) .rt-OTPInput {
  --input-selection-color: var(--brand-accent-button-bezeled-fill-hover);
  --input-focus-color: var(--brand-accent-40);
  --input-border-width: 1px;

  /* Blend inner shadow with page background */
  background: var(--fills-tertiary);
  box-shadow:
    inset 0 0 0 1px var(--brand-gray500),
    inset 0 1.5px 2px 0 var(--brand-gray200),
    inset 0 1.5px 2px 0 hexToRgba(var(--grays-black), 0.2);
  color: var(--labels-primary);

  &:where(:disabled, :read-only) {
    /* Blend with grey */
    background: var(--fills-tertiary);
  }
}

/* soft */

.rt-OTPInputRoot:where(.rt-variant-soft) .rt-OTPInput {
  --input-selection-color: var(--brand-accent-button-bezeled-fill-hover);
  --input-focus-color: var(--brand-accent-40);
  --input-border-width: 0px;

  background: var(--fills-tertiary);
  color: var(--labels-primary);

  &:where(:disabled, :read-only) {
    background: var(--fills-tertiary);
  }
}

.rt-OTPInput {
  height: calc(var(--input-height) + var(--spatial-2));
  width: calc(var(--input-height) + var(--spatial-5));
  text-align: center;
  border-radius: calc(var(--input-border-radius) - var(--input-border-width));

  &:focus {
    border-color: var(--brand-accent-40);
    box-shadow: inset 0 0 0 2px var(--brand-accent-40);
  }

  &:hover {
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.04) 0%, rgba(0, 0, 0, 0.04) 100%), var(--fills-secondary);
    transition: width 0.3s;
    transition-timing-function: cubic-bezier(0.44, 0, 0.56, 1);
  }

  &::placeholder {
    color: var(--labels-secondary);
  }

  &.rt-with-separator-left {
    margin-inline-start: 0;
  }

  /* Add margin when no separator exists before */
  &:not(:first-child):not(.rt-with-separator-left) {
    margin-inline-start: var(--input-gap);
  }

  &:where(:disabled, :read-only) {
    cursor: text;
    background-color: var(--fills-tertiary);
    color: var(--labels-tertiary);

    /* Safari */
    -webkit-text-fill-color: var(--labels-tertiary);

    &::placeholder {
      opacity: 0.5;
    }
    &:where(:placeholder-shown) {
      cursor: var(--cursor-disabled);

      & ~ :where(.rt-InputSlot) {
        cursor: var(--cursor-disabled);
      }
    }

    .rt-OTPInputRoot:where(:has(&)) {
      --input-selection-color: var(--brand-gray500);
      --input-focus-color: var(--brand-accent-40);
    }
  }
}
