// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M669.291 149.333l32 0.119v-0.119h-32zM327.958 149.333h-32l0 0.119 32-0.119zM244.081 922.347v0zM194.946 873.212v0zM802.304 214.79v0zM753.169 165.654v0zM194.946 214.79v0zM244.081 165.654v0zM433.361 970.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM786.624 457.843c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM327.958 352c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM669.291 416c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM327.958 501.333c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM515.806 565.333c17.677 0 32-14.327 32-32s-14.323-32-32-32v64zM327.958 650.667c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM481.775 714.667c17.677 0 32-14.327 32-32s-14.323-32-32-32v64zM639.979 780.343v0zM746.628 780.326v0zM799.181 933.321v0zM587.524 933.265v0zM542.016 873.22v0zM844.646 873.267v0zM391.958 85.333v32h213.332v-64h-213.332v32zM605.291 213.333v-32h-213.332v64h213.332v-32zM605.291 85.333v32c17.673 0 32 14.327 32 32h64c0-53.019-42.978-96-96-96v32zM391.958 85.333v-32c-53.019 0-96 42.981-96 96h64c0-17.673 14.327-32 32-32v-32zM178.625 727.467h32v-366.933h-64v366.933h32zM389.825 938.667v-32c-39.441 0-66.935-0.030-88.276-1.916-20.917-1.847-32.843-5.291-41.832-10.325l-31.272 55.842c20.153 11.285 42.233 16.004 67.468 18.236 24.812 2.193 55.618 2.163 93.911 2.163v-32zM178.625 727.467h-32c0 38.293-0.029 69.099 2.164 93.914 2.231 25.233 6.95 47.313 18.236 67.465l55.84-31.27c-5.034-8.99-8.476-20.915-10.325-41.83-1.886-21.342-1.916-48.836-1.916-88.277h-32zM244.081 922.347l15.636-27.921c-15.447-8.649-28.201-21.406-36.852-36.851l-55.84 31.27c14.418 25.745 35.675 47.002 61.42 61.423l15.636-27.921zM818.624 360.533h32c0-38.293 0.030-69.099-2.163-93.911-2.231-25.235-6.95-47.315-18.236-67.468l-55.842 31.272c5.035 8.989 8.478 20.914 10.325 41.832 1.886 21.34 1.916 48.835 1.916 88.276h32zM753.169 165.654l-15.637 27.92c15.45 8.651 28.203 21.405 36.851 36.852l55.842-31.272c-14.417-25.745-35.674-47.002-61.419-61.42l-15.637 27.92zM178.625 360.533h32c0-39.441 0.029-66.935 1.916-88.276 1.849-20.918 5.291-32.843 10.325-41.832l-55.84-31.272c-11.286 20.153-16.005 42.233-18.236 67.468-2.193 24.812-2.164 55.618-2.164 93.911h32zM244.081 165.654l-15.636-27.92c-25.745 14.418-47.002 35.675-61.42 61.42l55.84 31.272c8.651-15.447 21.405-28.201 36.852-36.852l-15.636-27.92zM327.96 149.817l-0.886-31.988c-39.314 1.088-71.101 4.489-98.629 19.905l31.272 55.84c12.719-7.123 30.454-10.698 69.128-11.769l-0.885-31.988zM391.958 213.333v-32c-17.591 0-31.869-14.197-31.999-31.753l-63.998 0.474c0.39 52.691 43.218 95.279 95.997 95.279v-32zM327.96 149.817l32-0.119-0.002-0.484-64 0.237 0.002 0.483 32-0.118zM669.291 149.817l-0.887 31.988c38.673 1.071 56.41 4.646 69.129 11.769l31.275-55.84c-27.529-15.416-59.315-18.816-98.633-19.905l-0.883 31.988zM669.291 149.333l-32-0.119v0.484l64 0.237v-0.483l-32-0.119zM669.291 149.817l-32-0.237c-0.128 17.556-14.409 31.753-32 31.753v64c52.779 0 95.607-42.589 96-95.279l-32-0.237zM433.361 938.667v-32h-43.536v64h43.536v-32zM818.624 360.533h-32v97.31h64v-97.31h-32zM327.958 384v32h341.332v-64h-341.332v32zM327.958 533.333v32h187.848v-64h-187.848v32zM327.958 682.667v32h153.817v-64h-153.817v32zM773.487 618.031h-32c0 26.598-21.564 48.158-48.162 48.158v64c61.943 0 112.162-50.214 112.162-112.158h-32zM693.325 698.189v-32c-26.598 0-48.158-21.559-48.158-48.158h-64c0 61.943 50.214 112.158 112.158 112.158v-32zM613.167 618.031h32c0-26.598 21.559-48.162 48.158-48.162v-64c-61.943 0-112.158 50.219-112.158 112.162h32zM693.325 537.869v32c26.598 0 48.162 21.564 48.162 48.162h64c0-61.943-50.219-112.162-112.162-112.162v32zM639.979 780.343l2.308 31.919c35.179-2.547 66.953-2.551 102.037-0.021l4.608-63.834c-38.165-2.752-73.024-2.743-111.262 0.021l2.308 31.915zM799.181 933.321l-3.017-31.855c-73.856 6.985-131.588 6.908-205.641-0.060l-2.999 31.859-2.995 31.859c77.982 7.334 139.738 7.428 217.664 0.055l-3.012-31.859zM587.524 933.265l2.999-31.859c-10.645-1.003-18.91-11.132-16.943-22.929l-63.134-10.513c-7.855 47.177 25.207 92.561 74.082 97.161l2.995-31.859zM844.646 873.267l-31.569 5.257c1.971 11.823-6.302 21.939-16.913 22.942l3.017 31.855 3.012 31.859c48.887-4.625 81.865-50.039 74.018-97.169l-31.565 5.257zM746.628 780.326l-2.304 31.915c34.027 2.458 62.647 29.611 68.753 66.283l63.134-10.513c-10.449-62.754-60.774-114.803-127.279-119.603l-2.304 31.919zM639.979 780.343l-2.308-31.915c-66.458 4.804-116.779 56.802-127.223 119.535l63.134 10.513c6.097-36.625 34.697-63.757 68.706-66.214l-2.308-31.919z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "medical-record"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1761,
        "id": 259,
        "name": "medical-record",
        "prevSize": 32,
        "code": 60037
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 390
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MedicalRecordIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MedicalRecordIcon = ({ size = 16, ...props }: MedicalRecordIconProps) => (
  <IcoMoon iconSet={iconSet} icon="medical-record" size={size} {...props} />
);

MedicalRecordIcon.displayName = 'MedicalRecordIcon';

export default MedicalRecordIcon;
