// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M94.457 515.972v0zM85.606 646.195v0zM938.394 646.195v0zM929.566 516.292v0zM821.871 407.366v0zM201.785 407.177v0zM221.137 171.292v0zM802.863 171.292v0zM439.501 562.961c-17.671 0-31.998 14.327-31.998 32 0 17.677 14.327 32 31.998 32v-64zM584.499 626.961c17.673 0 32-14.323 32-32 0-17.673-14.327-32-32-32v64zM544 85.333c0-17.673-14.327-32-32-32s-32 14.327-32 32h64zM439.501 171.852c-17.671 0-31.998 14.327-31.998 32s14.327 32 31.998 32v-64zM584.499 235.852c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM185.761 749.039h-32v106.667h64v-106.667h-32zM354.922 855.706h32v-106.667h-64v106.667h32zM270.342 938.667v32c63.8 0 116.58-50.893 116.58-114.961h-64c0 27.563-22.956 50.961-52.58 50.961v32zM185.761 855.706h-32c0 64.068 52.78 114.961 116.581 114.961v-64c-29.625 0-52.581-23.398-52.581-50.961h-32zM669.077 749.039h-32v106.667h64v-106.667h-32zM838.238 855.706h32v-106.667h-64v106.667h32zM753.66 938.667v32c63.799 0 116.578-50.893 116.578-114.961h-64c0 27.563-22.955 50.961-52.578 50.961v32zM669.077 855.706h-32c0 64.068 52.779 114.961 116.582 114.961v-64c-29.628 0-52.582-23.398-52.582-50.961h-32zM94.457 515.972l-31.926-2.172-8.851 130.223 63.852 4.343 8.851-130.227-31.927-2.167zM206.168 772.595v32h611.663v-64h-611.663v32zM938.394 646.195l31.927-2.172-8.832-129.899-63.851 4.339 8.828 129.903 31.927-2.172zM821.871 407.366l2.709-31.885c-215.573-18.321-408.143-18.943-625.546-0.185l5.501 63.762c213.632-18.432 402.547-17.831 614.621 0.192l2.714-31.884zM929.566 516.292l31.923-2.167c-4.971-73.19-62.775-132.344-136.909-138.644l-2.709 31.885-2.714 31.884c42.726 3.631 75.652 37.606 78.481 79.215l31.927-2.172zM817.83 772.595v32c87.778 0 158.438-73.041 152.491-160.572l-63.855 4.343c3.349 49.28-36.621 92.228-88.636 92.228v32zM85.606 646.195l-31.926-2.172c-5.949 87.531 64.711 160.572 152.488 160.572v-64c-52.014 0-91.985-42.948-88.635-92.228l-31.926-2.172zM94.457 515.972l31.927 2.167c2.822-41.515 35.598-75.409 78.152-79.083l-5.501-63.762c-74 6.385-131.542 65.511-136.503 138.505l31.926 2.172zM149.513 417.185l30.723 8.949 71.625-245.893-61.446-17.898-71.625 245.893 30.723 8.949zM337.318 85.333v32h349.364v-64h-349.364v32zM802.863 171.292l-30.724 8.949 71.625 245.893 61.449-17.898-71.625-245.893-30.724 8.949zM686.682 85.333v32c40.081 0 74.714 26.028 85.457 62.908l61.449-17.898c-18.901-64.88-79.087-109.010-146.906-109.010v32zM221.137 171.292l30.723 8.949c10.743-36.881 45.377-62.908 85.457-62.908v-64c-67.816 0-128.005 44.13-146.903 109.010l30.723 8.949zM439.501 594.961v32h144.998v-64h-144.998v32zM512 85.333h-32v118.519h64v-118.519h-32zM439.501 203.852v32h72.499v-64h-72.499v32zM512 203.852v32h72.499v-64h-72.499v32z",
          "M256 597.333h0.512M767.488 597.333h0.512"
        ],
        "attrs": [
          {},
          {
            "fill": "none",
            "stroke": "rgb(0, 0, 0)",
            "strokeLinejoin": "round",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": true,
        "grid": 0,
        "tags": [
          "car"
        ]
      },
      "attrs": [
        {},
        {
          "fill": "none",
          "stroke": "rgb(0, 0, 0)",
          "strokeLinejoin": "round",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1518,
        "id": 502,
        "name": "car",
        "prevSize": 32,
        "code": 59794
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 147
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CarIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CarIcon = ({ size = 16, ...props }: CarIconProps) => (
  <IcoMoon iconSet={iconSet} icon="car" size={size} {...props} />
);

CarIcon.displayName = 'CarIcon';

export default CarIcon;
