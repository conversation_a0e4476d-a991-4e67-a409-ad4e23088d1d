// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M832 682.667c58.91 0 106.667-124.164 106.667-277.333 0-153.167-47.757-277.333-106.667-277.333M832 682.667c-3.473 0-6.903-0.431-10.291-1.276M832 682.667l-10.291-1.276M821.709 681.391c-54.080-13.461-96.375-131.917-96.375-276.058 0-139.613 39.68-255.13 91.315-274.482M821.709 681.391l-389.487-48.222M816.649 130.851c5.013-1.879 10.138-2.851 15.351-2.851M816.649 130.851l15.351-2.851M816.649 130.851l-626.687 116.385c-60.642 11.262-104.628 64.169-104.628 125.848v104.011c0 64.61 48.151 119.091 112.273 127.027l22.778 2.82M220.384 606.942c-2.979 34.483 6.997 103.441 6.997 103.441l24.868 139.951c3.473 18.871 19.116 33.101 38.23 34.782l123.974 10.884c27.791 2.441 50.408-22.007 45.817-49.523l-19.891-124.105c0 0-10.556-57.596-8.158-89.203M220.384 606.942l211.837 26.227"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "announcement-01"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1398,
        "id": 622,
        "name": "announcement-01",
        "prevSize": 32,
        "code": 59674
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 27
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface Announcement-01IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const Announcement-01Icon = ({ size = 16, ...props }: Announcement-01IconProps) => (
  <IcoMoon iconSet={iconSet} icon="announcement-01" size={size} {...props} />
);

Announcement-01Icon.displayName = 'Announcement-01Icon';

export default Announcement-01Icon;
