.rt-BaseRadioRoot {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: top;
  flex-shrink: 0;
  cursor: var(--cursor-radio);

  /* Unless in a skeleton, align with text line height when possible and fall back to own height */
  height: var(--skeleton-height, var(--line-height, var(--radio-size)));
  --skeleton-height-override: var(--radio-size);

  /* Set root radius when in a skeleton */
  border-radius: var(--skeleton-radius);
  --skeleton-radius-override: 100%;

  &:where(:disabled, [data-disabled]) {
    cursor: var(--cursor-disabled);
  }

  &::before {
    content: '';
    display: block;
    height: var(--radio-size);
    width: var(--radio-size);
    border-radius: 100%;
  }

  &::after {
    pointer-events: none;
    position: absolute;
    height: var(--radio-size);
    width: var(--radio-size);
    border-radius: 100%;
    /* Scale via transform to achieve perfect sub-pixel positioning */
    transform: scale(0.4);
  }

  &:where(:checked, [data-state='checked']) {
    &::after {
      content: '';
    }
  }

  &:where(:focus, :focus-visible)::before {
    outline: 2px solid #dddfe4;
  }
}

/***************************************************************************************************
 *                                                                                                 *
 * SIZES                                                                                           *
 *                                                                                                 *
 ***************************************************************************************************/

@breakpoints {
  .rt-BaseRadioRoot {
    &:where(.rt-r-size-1) {
      --radio-size: var(--spatial-6);
    }
    &:where(.rt-r-size-2) {
      --radio-size: var(--spatial-7);
    }
    &:where(.rt-r-size-3) {
      --radio-size: var(--spatial-8);
    }
  }
}

/***************************************************************************************************
 *                                                                                                 *
 * VARIANTS                                                                                        *
 *                                                                                                 *
 ***************************************************************************************************/

/* surface */

.rt-BaseRadioRoot:where(.rt-variant-surface) {
  &:where(:not(:checked), [data-state='unchecked'])::before {
    background-color: var(--background-primary-base);
    box-shadow: inset 0 0 0 2px var(--separators-non-opaque);
  }
  &:where(:focus, :focus-visible):where(:not(:checked), [data-state='unchecked'])::before {
    box-shadow: inset 0 0 0 2px var(--labels-secondary);
  }
  &:where(:focus, :focus-visible):where(:checked, [data-state='checked'])::before {
    outline: 2px solid var(--brand-accent-40);
    box-shadow: inset 0 0 0 2px var(--brand-accent-40);
  }
  &:hover:where(:not(:checked), [data-state='unchecked'])::before {
    box-shadow: inset 0 0 0 2px var(--brand-accent);
  }
  &:where(:checked, [data-state='checked'])::before {
    background-color: var(--brand-accent);
  }

  &::after {
    background-color: var(--background-primary-base);
  }

  &:where(.rt-high-contrast) {
    &:where(:checked, [data-state='checked'])::before {
      background-color: var(--brand-accent);
    }
    &::after {
      background-color: var(--brand-accent-40);
    }
  }

  &:where(:disabled, [data-disabled])::before {
    box-shadow: inset 0 0 0 2px var(--separators-non-opaque);
    background-color: var(--fills-tertiary);
    outline: none;
  }
  &:where(:disabled, [data-disabled])::after {
    content: '';
    border-radius: var(--spatial-2);
    height: calc(var(--radio-size) / 2.25);
    background-color: var(--labels-tertiary);
  }
}

/* classic */

.rt-BaseRadioRoot:where(.rt-variant-classic) {
  &:where(:not(:checked), [data-state='unchecked'])::before {
    background-color: var(--background-primary-base);
    box-shadow: inset 0 0 0 2px var(--separators-non-opaque);
  }
  &:where(:focus, :focus-visible):where(:not(:checked), [data-state='unchecked'])::before {
    box-shadow: inset 0 0 0 2px var(--labels-secondary);
  }
  &:where(:focus, :focus-visible):where(:checked, [data-state='checked'])::before {
    outline: 2px solid var(--brand-accent-40);
  }
  &:hover:where(:not(:checked), [data-state='unchecked'])::before {
    box-shadow: inset 0 0 0 2px var(--brand-accent);
  }
  &:where(:checked, [data-state='checked'])::before {
    background-color: var(--brand-accent);
    background-image: linear-gradient(to bottom, hexToRgba(#ffffff, 0.2), transparent, hexToRgba(#000000, 0.2));
    box-shadow:
      inset 0 0.5px 0.5px hexToRgba(#ffffff, 0.3),
      inset 0 -0.5px 0.5px hexToRgba(#000000, 0.3);
  }

  &::after {
    background-color: var(--background-primary-base);
  }

  &:where(.rt-high-contrast) {
    &:where(:checked, [data-state='checked'])::before {
      background-color: var(--brand-accent);
    }
    &::after {
      background-color: var(--brand-accent-40);
    }
  }

  &:where(:disabled, [data-disabled])::before {
    box-shadow: var(--shadow-1);
    background-color: var(--fills-tertiary);
    background-image: none;
    outline: none;
  }
  &:where(:disabled, [data-disabled])::after {
    content: '';
    border-radius: var(--spatial-2);
    height: calc(var(--radio-size) / 2.25);
    background-color: var(--labels-tertiary);
  }
}

/* soft */

.rt-BaseRadioRoot:where(.rt-variant-soft) {
  &::before {
    background-color: var(--brand-accent-40);
  }
  &::after {
    background-color: var(--brand-accent);
  }

  &:where(:focus, :focus-visible):where(:not(:checked), [data-state='unchecked'])::before {
    box-shadow: inset 0 0 0 2px var(--labels-secondary);
  }
  &:where(:focus, :focus-visible):where(:checked, [data-state='checked'])::before {
    outline: 2px solid var(--brand-accent-40);
  }
  &:hover:where(:not(:checked), [data-state='unchecked'])::before {
    box-shadow: inset 0 0 0 2px var(--brand-accent);
  }

  &:where(.rt-high-contrast) {
    &::after {
      background-color: var(--brand-accent);
    }
  }

  &:where(:disabled, [data-disabled])::before {
    box-shadow: inset 0 0 0 2px var(--separators-non-opaque);
    background-color: var(--fills-tertiary);
    outline: none;
  }
  &:where(:disabled, [data-disabled])::after {
    content: '';
    border-radius: var(--spatial-2);
    height: calc(var(--radio-size) / 2.25);
    background-color: var(--labels-tertiary);
  }
}
