// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 821.333c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM938.667 885.333c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM367.264 175.7v0zM395.995 175.7v0zM66.121 371.193c-14.133 10.61-16.989 30.669-6.379 44.803 10.61 14.135 30.669 16.989 44.803 6.379l-38.424-51.182zM658.714 422.375c14.135 10.61 34.193 7.756 44.804-6.379 10.611-14.134 7.753-34.193-6.379-44.803l-38.426 51.182zM266.667 853.333c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM432.593 853.333c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM346.074 378.408c-17.673 0-32 14.327-32 32 0 17.671 14.327 31.998 32 31.998v-63.998zM417.185 442.406c17.673 0 32.001-14.327 32.001-31.998 0-17.673-14.327-32-32.001-32v63.998zM85.333 853.333v32h853.333v-64h-853.333v32zM914.961 484.228h-32v98.428h64v-98.428h-32zM772.74 582.656h32v-98.428h-64v98.428h32zM843.853 853.333h32v-196.855h-64v196.855h32zM772.74 582.656h-32c0 57.306 45.047 105.822 103.113 105.822v-64c-20.484 0-39.113-17.587-39.113-41.822h-32zM914.961 582.656h-32c0 24.235-18.624 41.822-39.108 41.822v64c58.065 0 103.108-48.516 103.108-105.822h-32zM843.853 410.408v31.998c20.484 0 39.108 17.587 39.108 41.822h64c0-57.306-45.043-105.82-103.108-105.82v32zM843.853 410.408v-32c-58.065 0-103.113 48.515-103.113 105.82h64c0-24.235 18.628-41.822 39.113-41.822v-31.998zM132.741 828.727h32v-467.534h-64v467.534h32zM630.519 361.194h-32v467.534h64v-467.534h-32zM132.741 361.194l19.851 25.099 234.524-185.494-39.703-50.196-234.523 185.493 19.851 25.098zM395.995 175.7l-19.852 25.098 234.523 185.494 39.701-50.197-234.522-185.493-19.851 25.098zM132.741 361.194l-19.212-25.591-47.407 35.59 38.424 51.182 47.407-35.59-19.212-25.591zM630.519 361.194l-19.213 25.591 47.407 35.59 38.426-51.182-47.407-35.59-19.213 25.591zM156.445 853.333v32h450.369v-64h-450.369v32zM367.264 175.7l19.852 25.098c-3.149 2.49-7.823 2.49-10.972 0l39.703-50.196c-20.12-15.914-48.313-15.914-68.433 0l19.851 25.098zM630.519 828.727h-32c0-2.944 2.594-7.394 8.294-7.394v64c31.885 0 55.706-26.483 55.706-56.606h-32zM132.741 828.727h-32c0 30.123 23.822 56.606 55.704 56.606v-64c5.699 0 8.296 4.45 8.296 7.394h-32zM298.667 853.333h32v-209.161h-64v209.161h32zM464.593 644.173h-32v209.161h64v-209.161h-32zM381.629 558.050v32c27.029 0 50.964 23.095 50.964 54.123h64c0-64.098-50.355-118.123-114.964-118.123v32zM298.667 644.173h32c0-31.027 23.934-54.123 50.963-54.123v-64c-64.61 0-114.963 54.025-114.963 118.123h32zM346.074 410.408v31.998h71.111v-63.998h-71.111v32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "house-alt"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1716,
        "id": 304,
        "name": "house-alt",
        "prevSize": 32,
        "code": 59992
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 345
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface HouseAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const HouseAltIcon = ({ size = 16, ...props }: HouseAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="house-alt" size={size} {...props} />
);

HouseAltIcon.displayName = 'HouseAltIcon';

export default HouseAltIcon;
