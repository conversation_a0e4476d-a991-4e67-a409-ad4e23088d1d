[data-radius] {
  --radius-0: calc(0px * var(--scaling) * var(--radius-factor));
  --radius-1: calc(0.5px * var(--scaling) * var(--radius-factor));
  --radius-2: calc(2px * var(--scaling) * var(--radius-factor));
  --radius-3: calc(8px * var(--scaling) * var(--radius-factor));
  --radius-4: calc(10px * var(--scaling) * var(--radius-factor));
  --radius-5: calc(14px * var(--scaling) * var(--radius-factor));
  --radius-6: calc(16px * var(--scaling) * var(--radius-factor));
  --radius-7: calc(24px * var(--scaling) * var(--radius-factor));
  --radius-8: calc(32px * var(--scaling) * var(--radius-factor));
}

[data-radius='none'] {
  --radius-factor: 0;
  --radius-full: 1000px;
  --radius-thumb: 0.5px;
}

[data-radius='extra-small'] {
  --radius-factor: 0.5;
  --radius-full: 1000px;
  --radius-thumb: 0.5px;
}

[data-radius='small'] {
  --radius-factor: 0.75;
  --radius-full: 1000px;
  --radius-thumb: 0.5px;
}

[data-radius='medium'] {
  --radius-factor: 1;
  --radius-full: 1000px;
  --radius-thumb: 9999px;
}

[data-radius='large'] {
  --radius-factor: 1.5;
  --radius-full: 1000px;
  --radius-thumb: 9999px;
}

[data-radius='extra-large'] {
  --radius-factor: 2;
  --radius-full: 1000px;
  --radius-thumb: 9999px;
}

[data-radius='full'] {
  --radius-factor: 2;
  --radius-full: 1000px;
  --radius-thumb: 9999px;
}
