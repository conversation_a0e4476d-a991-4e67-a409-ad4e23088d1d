'use client';

import clsx from 'clsx';
import { composeRefs } from 'radix-ui/internal';
import * as React from 'react';

import { EyeIcon, EyeOffIcon } from '@base-components/icons';
import { Button } from '@components/button';
import { type ComponentPropsWithout, type NotInputTextualAttributes, type RemovedProps, extractProps } from '@helpers';
import { type GetPropDefTypes, type MarginProps, marginPropDefs } from '@props';

import { inputPropDefs, inputSlotPropDefs } from './input.props';

type InputElement = React.ComponentRef<'input'>;
type InputOwnProps = GetPropDefTypes<typeof inputPropDefs> & {
  defaultValue?: string | number;
  value?: string | number;
  type?: 'email' | 'hidden' | 'number' | 'password' | 'search' | 'text' | 'url';
};
type BaseInputProps = ComponentPropsWithout<
  'input',
  NotInputTextualAttributes | 'color' | 'defaultValue' | 'size' | 'type' | 'value' | 'prefix'
>;
interface InputProps extends BaseInputProps, MarginProps, InputOwnProps {
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  label?: React.ReactNode;
  helperText?: React.ReactNode;
  error?: boolean;
}
const Input = React.forwardRef<InputElement, InputProps>((props, forwardedRef) => {
  const inputRef = React.useRef<HTMLInputElement>(null);
  const { id, label, helperText, prefix, suffix, error, children, className, color, radius, style, ...inputProps } =
    extractProps(props, inputPropDefs, marginPropDefs);

  const inputId = id ?? React.useId();
  const describedById = helperText ? `${inputId}-helper` : undefined;

  return (
    <div className="rt-InputFieldWrapper">
      {label && <label className="rt-InputFieldLabel">{label}</label>}
      <div
        data-accent-color={color}
        data-radius={radius}
        style={style}
        className={clsx('rt-InputRoot', { ['rt-error']: error }, className)}
        onPointerDown={(event) => {
          const target = event.target as HTMLElement;
          if (target.closest('input, button, a')) return;

          const input = inputRef.current;
          if (!input) return;

          // Same selector as in the CSS to find the right slot
          const isRightSlot = target.closest(`
            .rt-InputSlot[data-side='right'],
            .rt-InputSlot:not([data-side='right']) ~ .rt-InputSlot:not([data-side='left'])
          `);

          const cursorPosition = isRightSlot ? input.value.length : 0;

          requestAnimationFrame(() => {
            // Only some input types support this, browsers will throw an error if not supported
            // See: https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/setSelectionRange#:~:text=Note%20that%20according,not%20support%20selection%22.
            try {
              input.setSelectionRange(cursorPosition, cursorPosition);
            } catch {}
            input.focus();
          });
        }}
      >
        {prefix && (
          <div data-side="left" className="rt-InputSlot">
            {prefix}
          </div>
        )}
        <input
          spellCheck="false"
          {...inputProps}
          ref={composeRefs(inputRef, forwardedRef)}
          className="rt-reset rt-Input"
          id={inputId}
          aria-invalid={error || undefined}
          aria-describedby={describedById}
        />
        {children}
        {suffix && (
          <div data-side="right" className="rt-InputSlot">
            {suffix}
          </div>
        )}
      </div>
      {helperText && (
        <div id={describedById} className="rt-InputFieldHelperText">
          {helperText}
        </div>
      )}
    </div>
  );
});
Input.displayName = 'Input';

interface PasswordProps extends InputProps {
  hideIcon?: React.ReactNode;
  showIcon?: React.ReactNode;
}

const Password = React.forwardRef<InputElement, PasswordProps>(
  ({ hideIcon = <EyeOffIcon />, showIcon = <EyeIcon />, ...props }, forwardedRef) => {
    const [visible, setVisible] = React.useState(false);

    return (
      <Input
        {...props}
        type={visible ? 'text' : 'password'}
        ref={forwardedRef}
        suffix={
          <Button
            variant="ghost"
            onClick={() => setVisible((visible) => !visible)}
            tabIndex={-1}
            className="rt-PasswordToggle"
            aria-label={visible ? 'Hide password' : 'Show password'}
          >
            {visible ? hideIcon : showIcon}
          </Button>
        }
      />
    );
  },
);
Password.displayName = 'Input.Password';

type InputSlotElement = React.ComponentRef<'div'>;
type InputSlotOwnProps = GetPropDefTypes<typeof inputSlotPropDefs>;
interface InputSlotProps extends ComponentPropsWithout<'div', RemovedProps>, InputSlotOwnProps {}
const InputSlot = React.forwardRef<InputSlotElement, InputSlotProps>((props, forwardedRef) => {
  const { className, color, side, ...slotProps } = extractProps(props, inputSlotPropDefs);
  return (
    <div
      data-accent-color={color}
      data-side={side}
      {...slotProps}
      ref={forwardedRef}
      className={clsx('rt-InputSlot', className)}
    />
  );
});
InputSlot.displayName = 'Input.Slot';

export { Input, Password, InputSlot as Slot };
export type { InputProps, PasswordProps, InputSlotProps as SlotProps };
