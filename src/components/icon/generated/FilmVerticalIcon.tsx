// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M938.667 341.333v-8.533c0-71.687 0-107.53-13.952-134.911-12.271-24.085-31.851-43.666-55.936-55.938-27.383-13.951-63.223-13.951-134.912-13.951h-8.533M938.667 341.333v341.333M938.667 341.333h-213.333M85.333 341.333v-8.533c0-71.687 0-107.53 13.951-134.911 12.272-24.085 31.853-43.666 55.938-55.938 27.38-13.951 63.224-13.951 134.911-13.951h8.533M85.333 341.333v341.333M85.333 341.333h213.333M85.333 682.667v8.533c0 71.689 0 107.529 13.951 134.912 12.272 24.085 31.853 43.665 55.938 55.936 27.38 13.952 63.224 13.952 134.911 13.952h8.533M85.333 682.667h213.333M938.667 682.667v8.533c0 71.689 0 107.529-13.952 134.912-12.271 24.085-31.851 43.665-55.936 55.936-27.383 13.952-63.223 13.952-134.912 13.952h-8.533M938.667 682.667h-213.333M512 128h213.333M512 128h-213.333M512 128v213.333M512 896h213.333M512 896h-213.333M512 896v-213.333M725.333 128v213.333M725.333 896v-213.333M298.667 128v213.333M298.667 896v-213.333M725.333 341.333h-213.333M725.333 682.667h-213.333M512 341.333v341.333M512 341.333h-213.333M512 682.667h-213.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "film-vertical"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1651,
        "id": 369,
        "name": "film-vertical",
        "prevSize": 32,
        "code": 59927
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 280
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FilmVerticalIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FilmVerticalIcon = ({ size = 16, ...props }: FilmVerticalIconProps) => (
  <IcoMoon iconSet={iconSet} icon="film-vertical" size={size} {...props} />
);

FilmVerticalIcon.displayName = 'FilmVerticalIcon';

export default FilmVerticalIcon;
