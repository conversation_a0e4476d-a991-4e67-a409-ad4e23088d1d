// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M333.401 650.667c26.463 23.966 90.579 72.107 178.595 72.107 88.021 0 152.137-48.141 178.598-72.107M446.908 391.633c-6.558-40.615-41.776-71.633-84.241-71.633s-77.685 31.018-84.239 71.633M446.908 391.633c0.717 4.46 1.092 9.037 1.092 13.7 0 47.13-38.205 85.333-85.333 85.333s-85.333-38.204-85.333-85.333c0-4.663 0.374-9.24 1.094-13.7M446.908 391.633c0 0 39.497-7.633 65.092-7.633s65.092 7.633 65.092 7.633M577.092 391.633c-0.717 4.46-1.092 9.037-1.092 13.7 0 47.13 38.204 85.333 85.333 85.333s85.333-38.204 85.333-85.333c0-4.663-0.375-9.24-1.092-13.7M577.092 391.633c6.558-40.615 41.775-71.633 84.241-71.633s77.683 31.018 84.241 71.633M278.427 391.633h-175.88M102.548 391.633c-11.204 38.174-17.214 78.566-17.214 120.367 0 235.639 191.025 426.667 426.667 426.667 235.639 0 426.667-191.027 426.667-426.667 0-41.801-6.012-82.193-17.216-120.367M102.548 391.633c51.958-177.033 215.61-306.3 409.452-306.3s357.495 129.267 409.451 306.3M745.574 391.633h175.876"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "nerd"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1797,
        "id": 223,
        "name": "nerd",
        "prevSize": 32,
        "code": 60073
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 426
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface NerdIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const NerdIcon = ({ size = 16, ...props }: NerdIconProps) => (
  <IcoMoon iconSet={iconSet} icon="nerd" size={size} {...props} />
);

NerdIcon.displayName = 'NerdIcon';

export default NerdIcon;
