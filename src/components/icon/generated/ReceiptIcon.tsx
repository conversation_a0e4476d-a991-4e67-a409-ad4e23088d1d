// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M805.649 85.333c43.661 0 90.351 23.704 90.351 84.734v204.771c0 13.371-10.112 24.21-22.588 24.21h-135.531M805.649 85.333c-43.665 0-67.767 37.937-67.767 84.734v228.98M805.649 85.333h-564.708c-62.376 0-112.941 54.195-112.941 121.049v670.136c0 20.156 21.909 31.36 36.699 18.769l59.599-50.731c9.344-7.953 22.896-6.775 30.9 2.688l70.6 83.465c8.973 10.611 24.607 10.611 33.58 0l68.070-80.469c8.973-10.611 24.606-10.611 33.579 0l65.852 77.854c9.719 11.49 26.961 10.368 35.294-2.295l48.96-74.415c7.479-11.371 22.43-13.658 32.614-4.988l57.438 48.892c14.788 12.591 36.698 1.387 36.698-18.769v-477.471M274.824 346.074h316.233M274.824 488.294h316.233M274.824 630.519h214.589"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "receipt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1847,
        "id": 173,
        "name": "receipt",
        "prevSize": 32,
        "code": 60123
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 476
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ReceiptIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ReceiptIcon = ({ size = 16, ...props }: ReceiptIconProps) => (
  <IcoMoon iconSet={iconSet} icon="receipt" size={size} {...props} />
);

ReceiptIcon.displayName = 'ReceiptIcon';

export default ReceiptIcon;
