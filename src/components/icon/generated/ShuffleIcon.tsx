// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 292.571h165.001c31.578 0 61.863 12.844 84.192 35.705l179.444 183.724M513.971 512l179.447 183.723c22.327 22.861 52.612 35.708 84.194 35.708h161.054M513.971 512l179.447-183.724c22.327-22.862 52.612-35.705 84.194-35.705h161.054M85.333 731.43h165.001c31.578 0 61.863-12.847 84.192-35.708l78.768-80.644M823.548 170.667l112.329 115.009c1.86 1.904 2.79 4.4 2.79 6.896M823.548 414.476l112.329-115.009c1.86-1.904 2.79-4.4 2.79-6.896M823.548 609.523l112.329 115.008c1.86 1.907 2.79 4.403 2.79 6.899M823.548 853.333l112.329-115.008c1.86-1.903 2.79-4.399 2.79-6.895"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "shuffle"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1887,
        "id": 133,
        "name": "shuffle",
        "prevSize": 32,
        "code": 60163
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 516
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ShuffleIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ShuffleIcon = ({ size = 16, ...props }: ShuffleIconProps) => (
  <IcoMoon iconSet={iconSet} icon="shuffle" size={size} {...props} />
);

ShuffleIcon.displayName = 'ShuffleIcon';

export default ShuffleIcon;
