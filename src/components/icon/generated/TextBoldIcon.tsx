// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M170.667 464.593h341.333c107.721 0 195.046-84.901 195.046-189.63s-87.326-189.629-195.046-189.629h-281.735c-32.915 0-59.598 25.942-59.598 57.942v321.318zM170.667 464.593h438.857c134.652 0 243.81 106.125 243.81 237.035 0 130.914-109.158 237.039-243.81 237.039h-363.005c-41.892 0-75.852-33.015-75.852-73.745v-400.329z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "text-bold"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1946,
        "id": 74,
        "name": "text-bold",
        "prevSize": 32,
        "code": 60222
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 575
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface TextBoldIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const TextBoldIcon = ({ size = 16, ...props }: TextBoldIconProps) => (
  <IcoMoon iconSet={iconSet} icon="text-bold" size={size} {...props} />
);

TextBoldIcon.displayName = 'TextBoldIcon';

export default TextBoldIcon;
