// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M405.333 256h213.333M329.143 85.333h365.713c75.742 0 137.143 58.074 137.143 129.711v712.793c0 9.020-10.991 14.071-18.487 8.499l-287.39-213.572c-8.29-6.157-19.955-6.157-28.245 0l-287.388 213.572c-7.498 5.572-18.489 0.521-18.489-8.499v-712.793c0-71.637 61.401-129.711 137.143-129.711z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bookmark"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1481,
        "id": 539,
        "name": "bookmark",
        "prevSize": 32,
        "code": 59757
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 110
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BookmarkIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BookmarkIcon = ({ size = 16, ...props }: BookmarkIconProps) => (
  <IcoMoon iconSet={iconSet} icon="bookmark" size={size} {...props} />
);

BookmarkIcon.displayName = 'BookmarkIcon';

export default BookmarkIcon;
