// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M298.667 213.333l32.614 2.763c31.526 2.67 51.66 35.124 40.242 64.865l-22.195 57.812M349.329 338.773l-121.773 328.533M349.329 338.773l261.466 65.707M349.329 338.773l150.819 328.533M610.795 404.48l174.98 247.727c4.471 6.327-0.017 15.1-7.723 15.1h-277.905M610.795 404.48l-110.647 262.827M610.795 404.48l45.632-128M656.427 276.48h57.041M656.427 276.48h-57.037M369.778 667.307c0 79.177-63.675 143.36-142.223 143.36s-142.222-64.183-142.222-143.36c0-79.177 63.675-143.36 142.222-143.36s142.223 64.183 142.223 143.36zM938.667 667.307c0 79.177-63.676 143.36-142.221 143.36-78.549 0-142.225-64.183-142.225-143.36s63.676-143.36 142.225-143.36c78.545 0 142.221 64.183 142.221 143.36z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "cycle"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1595,
        "id": 425,
        "name": "cycle",
        "prevSize": 32,
        "code": 59871
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 224
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CycleIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CycleIcon = ({ size = 16, ...props }: CycleIconProps) => (
  <IcoMoon iconSet={iconSet} icon="cycle" size={size} {...props} />
);

CycleIcon.displayName = 'CycleIcon';

export default CycleIcon;
