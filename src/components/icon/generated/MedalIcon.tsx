// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M280.579 661.12c60.83 54.579 142.121 87.919 231.421 87.919 89.301 0 170.59-33.34 231.42-87.919M280.579 661.12c-67.584-60.634-109.913-147.477-109.913-243.935 0-183.277 152.82-331.852 341.333-331.852s341.333 148.575 341.333 331.852c0 96.457-42.33 183.301-109.914 243.935M280.579 661.12l-12.309 224.444c-2.013 36.698 34.271 63.1 67.958 49.451l128.873-52.22c30.016-12.164 63.782-12.164 93.798 0l128.875 52.22c33.685 13.649 69.969-12.753 67.955-49.451l-12.309-224.444"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "medal"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1759,
        "id": 261,
        "name": "medal",
        "prevSize": 32,
        "code": 60035
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 388
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MedalIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MedalIcon = ({ size = 16, ...props }: MedalIconProps) => (
  <IcoMoon iconSet={iconSet} icon="medal" size={size} {...props} />
);

MedalIcon.displayName = 'MedalIcon';

export default MedalIcon;
