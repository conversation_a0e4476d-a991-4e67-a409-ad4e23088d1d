// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M227.555 830.025c75.485 67.563 175.168 108.642 284.445 108.642s208.96-41.079 284.446-108.642M227.555 830.025c-87.289-78.127-142.222-191.659-142.222-318.025 0-235.642 191.025-426.667 426.667-426.667 235.639 0 426.667 191.025 426.667 426.667 0 126.366-54.933 239.898-142.221 318.025M227.555 830.025l37.952-140.736c5.579-20.693 24.343-35.068 45.772-35.068h11.091M796.446 830.025l-37.952-140.736c-5.581-20.693-24.346-35.068-45.773-35.068h-11.093M322.371 654.221v-68.774c0-26.185 21.225-47.407 47.407-47.407h23.703M322.371 654.221h379.257M701.628 654.221v-68.774c0-26.185-21.222-47.407-47.407-47.407h-23.701M393.481 538.039v-175.917c0-9.693 5.901-18.409 14.9-22.008l189.63-75.852c15.569-6.228 32.508 5.239 32.508 22.008v251.769M393.481 538.039h237.038"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "highlighter-circle"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1708,
        "id": 312,
        "name": "highlighter-circle",
        "prevSize": 32,
        "code": 59984
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 337
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface HighlighterCircleIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const HighlighterCircleIcon = ({ size = 16, ...props }: HighlighterCircleIconProps) => (
  <IcoMoon iconSet={iconSet} icon="highlighter-circle" size={size} {...props} />
);

HighlighterCircleIcon.displayName = 'HighlighterCircleIcon';

export default HighlighterCircleIcon;
