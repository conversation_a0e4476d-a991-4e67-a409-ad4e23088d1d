// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M861.018 294.4c6.4 7.77 12.463 15.837 18.167 24.178 37.491 54.827 59.482 121.507 59.482 193.422s-21.99 138.594-59.482 193.421c-5.705 8.341-11.767 16.41-18.167 24.179M440.892 342.755c65.455 0 118.515 37.887 118.515 84.624M440.892 342.755c-65.458 0-118.521 37.887-118.521 84.624 0 46.733 53.063 84.621 118.521 84.621M440.892 342.755v-60.444M440.892 342.755v169.245M440.892 512c65.455 0 118.515 37.888 118.515 84.621 0 46.737-53.060 84.625-118.515 84.625M440.892 512v169.246M440.892 681.246c-65.458 0-118.521-37.888-118.521-84.625M440.892 681.246v60.442M796.446 512c0 200.294-159.189 362.667-355.554 362.667-196.37 0-355.558-162.372-355.558-362.667s159.188-362.667 355.558-362.667c196.365 0 355.554 162.371 355.554 362.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "coin-alt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1579,
        "id": 441,
        "name": "coin-alt",
        "prevSize": 32,
        "code": 59855
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 208
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CoinAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CoinAltIcon = ({ size = 16, ...props }: CoinAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="coin-alt" size={size} {...props} />
);

CoinAltIcon.displayName = 'CoinAltIcon';

export default CoinAltIcon;
