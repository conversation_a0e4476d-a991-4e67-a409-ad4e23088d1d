.rt-AvatarGroup {
  .rt-AvatarRoot:where(:not(.rt-AvatarHidden)) {
    margin-left: calc(var(--avatar-size) * -0.2);
  }
}

.rt-AvatarRoot {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  user-select: none;
  width: var(--avatar-size);
  height: var(--avatar-size);
  flex-shrink: 0;
  position: relative;
}

.rt-AvatarImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: inherit;
}

.rt-AvatarFallback {
  font-family: var(--default-font-family);
  font-weight: var(--font-weight-medium);
  font-style: normal;
  z-index: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  border-radius: inherit;
}

.rt-AvatarVerificationIcon {
  position: absolute;
  top: calc(var(--avatar-size) * -0.05);
  right: calc(var(--avatar-size) * -0.1);
  z-index: 2;
  height: calc(var(--avatar-size) / 2.5);
  width: calc(var(--avatar-size) / 2.5);
}

.rt-AvatarStatus {
  position: absolute;
  right: calc(var(--avatar-size) * -0.08);
  bottom: calc(var(--avatar-size) * -0.08);
  width: calc(var(--avatar-size) / 5);
  height: calc(var(--avatar-size) / 5);
  background-color: #4caf50;
  border-radius: 50%;
  border: 3px solid white;
  z-index: 3;
}

/***************************************************************************************************
 *                                                                                                 *
 * SIZES                                                                                           *
 *                                                                                                 *
 ***************************************************************************************************/

.rt-AvatarFallback {
  text-transform: uppercase;

  &:where(.rt-one-letter) {
    font-size: var(--avatar-fallback-one-letter-font-size);
  }
  &:where(.rt-two-letters) {
    font-size: var(--avatar-fallback-two-letters-font-size, var(--avatar-fallback-one-letter-font-size));
  }
}

@breakpoints {
  .rt-AvatarRoot {
    &:where(.rt-shape-circle) {
      border-radius: var(--radius-full);
    }
    &:where(.rt-r-size-1) {
      --avatar-size: var(--spatial-7);
      --avatar-fallback-one-letter-font-size: var(--font-size-2);
      --avatar-fallback-two-letters-font-size: var(--font-size-1);
      border-radius: var(--radius-2);
      letter-spacing: var(--letter-spacing-1);
    }
    &:where(.rt-r-size-2) {
      --avatar-size: var(--spatial-8);
      --avatar-fallback-one-letter-font-size: var(--font-size-3);
      --avatar-fallback-two-letters-font-size: var(--font-size-2);
      border-radius: var(--radius-2);
      letter-spacing: var(--letter-spacing-2);
    }
    &:where(.rt-r-size-3) {
      --avatar-size: var(--spatial-9);
      --avatar-fallback-one-letter-font-size: var(--font-size-4);
      --avatar-fallback-two-letters-font-size: var(--font-size-3);
      border-radius: var(--radius-3);
      letter-spacing: var(--letter-spacing-3);
    }
    &:where(.rt-r-size-4) {
      --avatar-size: var(--spatial-10);
      --avatar-fallback-one-letter-font-size: var(--font-size-5);
      --avatar-fallback-two-letters-font-size: var(--font-size-4);
      border-radius: var(--radius-3);
      letter-spacing: var(--letter-spacing-4);
    }
    &:where(.rt-r-size-5) {
      --avatar-size: var(--spatial-11);
      --avatar-fallback-one-letter-font-size: var(--font-size-6);
      border-radius: var(--radius-4);
      letter-spacing: var(--letter-spacing-6);
    }
    &:where(.rt-r-size-6) {
      --avatar-size: var(--spatial-12);
      --avatar-fallback-one-letter-font-size: var(--font-size-7);
      border-radius: var(--radius-5);
      letter-spacing: var(--letter-spacing-7);
    }
    &:where(.rt-r-size-7) {
      --avatar-size: var(--spatial-13);
      --avatar-fallback-one-letter-font-size: var(--font-size-7);
      border-radius: var(--radius-5);
      letter-spacing: var(--letter-spacing-7);
    }
    &:where(.rt-r-size-8) {
      --avatar-size: var(--spatial-14);
      --avatar-fallback-one-letter-font-size: var(--font-size-8);
      border-radius: var(--radius-6);
      letter-spacing: var(--letter-spacing-8);
    }
    &:where(.rt-r-size-9) {
      --avatar-size: var(--spatial-15);
      --avatar-fallback-one-letter-font-size: var(--font-size-9);
      border-radius: var(--radius-6);
      letter-spacing: var(--letter-spacing-9);
    }
  }
}

/***************************************************************************************************
 *                                                                                                 *
 * VARIANTS                                                                                        *
 *                                                                                                 *
 ***************************************************************************************************/

/* solid */

.rt-AvatarRoot:where(.rt-variant-solid) {
  & :where(.rt-AvatarFallback) {
    background-color: var(--brand-accent);
    color: var(--grays-white);
  }
  &:where(.rt-high-contrast) :where(.rt-AvatarFallback) {
    background-color: var(--brand-accent-button-bezeled-fill);
    color: var(--brand-accent-button-bezeled-fill-hover);
  }
}

/* soft */

.rt-AvatarRoot:where(.rt-variant-soft) {
  & :where(.rt-AvatarFallback) {
    background-color: var(--brand-accent-40);
    color: var(--brand-accent);
  }
  &:where(.rt-high-contrast) :where(.rt-AvatarFallback) {
    color: var(--brand-accent);
  }
}

/* ghost */

.rt-AvatarRoot:where(.rt-variant-ghost) {
  & :where(.rt-AvatarFallback) {
    background-color: var(--fills-tertiary);
    color: var(--labels-primary);
  }
  &:where(.rt-high-contrast) :where(.rt-AvatarFallback) {
    color: var(--labels-primary);
  }
}
