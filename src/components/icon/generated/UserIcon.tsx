// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M679.953 251.259c0 91.638-75.204 165.926-167.97 165.926-92.769 0-167.973-74.288-167.973-165.926s75.203-165.926 167.973-165.926c92.766 0 167.97 74.288 167.97 165.926z",
          "M196.394 772.258c16.451-112.597 104.695-204.608 219.555-212.873 65.625-4.723 126.668-4.732 192.166-0.026 114.816 8.247 202.995 100.25 219.439 212.8l3.2 21.892c9.476 64.866-36.139 125.414-102.11 132.399-154.829 16.401-277.99 16.213-433.048-0.124-66.074-6.963-111.843-67.563-102.351-132.527l3.148-21.542z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "user"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1985,
        "id": 35,
        "name": "user",
        "prevSize": 32,
        "code": 60261
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 614
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface UserIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const UserIcon = ({ size = 16, ...props }: UserIconProps) => (
  <IcoMoon iconSet={iconSet} icon="user" size={size} {...props} />
);

UserIcon.displayName = 'UserIcon';

export default UserIcon;
