// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M810.667 554.667c70.694 0 128-57.31 128-128.002s-57.306-128-128-128M423.822 255.998l356.901-157.456c14.101-6.221 29.943 4.105 29.943 19.518v617.21c0 15.411-15.842 25.737-29.943 19.516l-356.901-157.453M423.822 255.998h-167.822c-94.257 0-170.667 76.41-170.667 170.667 0 50.972 22.347 96.727 57.778 128.002M423.822 255.998v341.335M423.822 597.333h-167.822c-43.283 0-82.803-16.115-112.888-42.667M423.822 597.333v254.332c0 45.483-40.928 80.021-85.764 72.38-24.56-4.186-45.322-20.54-55.143-43.439l-139.803-325.939"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "announcement-02"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1399,
        "id": 621,
        "name": "announcement-02",
        "prevSize": 32,
        "code": 59675
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 28
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface Announcement02IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const Announcement02Icon = ({ size = 16, ...props }: Announcement02IconProps) => (
  <IcoMoon iconSet={iconSet} icon="announcement-02" size={size} {...props} />
);

Announcement02Icon.displayName = 'Announcement02Icon';

export default Announcement02Icon;
