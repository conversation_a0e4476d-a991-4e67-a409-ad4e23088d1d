// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M845.845 511.313c0.183-77.12-26.47-151.788-75.43-211.285-48.956-59.496-117.184-100.139-193.058-115.004-21.12-4.137-42.462-6.206-63.748-6.25M845.845 511.313c3.307 0 92.821-159.752 92.821-159.752M845.845 511.313c-3.311 0-159.889-91.726-159.889-91.726M513.609 178.774c-55.194-0.115-109.975 13.382-159.299 39.735-48.303 25.807-89.259 62.814-119.643 107.475M513.609 178.774c65.882-0.136 130.517 19.127 185.613 55.735 64.252 42.691 111.552 106.33 133.841 180.075M513.609 178.774c-10.948 0.023-21.926 0.581-32.905 1.684-77.044 7.742-149.137 41.849-203.997 96.51M190.936 609.63c22.29 73.741 69.59 137.382 133.841 180.075 55.094 36.608 119.729 55.868 185.606 55.735M510.383 845.44c10.953-0.026 21.935-0.58 32.913-1.685 77.043-7.74 149.137-41.847 203.998-96.508M510.383 845.44c-21.282-0.047-42.624-2.116-63.74-6.251-75.873-14.865-144.102-55.509-193.059-115.004-48.957-59.499-75.614-134.165-75.429-211.285M510.383 845.44c55.198 0.115 109.982-13.385 159.309-39.735 48.303-25.809 89.259-62.814 119.642-107.477M178.156 512.9c-3.308 0-92.822 159.753-92.822 159.753M178.156 512.9c3.309 0 159.887 91.725 159.887 91.725"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "arrow-path"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1418,
        "id": 602,
        "name": "arrow-path",
        "prevSize": 32,
        "code": 59694
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 47
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ArrowPathIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ArrowPathIcon = ({ size = 16, ...props }: ArrowPathIconProps) => (
  <IcoMoon iconSet={iconSet} icon="arrow-path" size={size} {...props} />
);

ArrowPathIcon.displayName = 'ArrowPathIcon';

export default ArrowPathIcon;
