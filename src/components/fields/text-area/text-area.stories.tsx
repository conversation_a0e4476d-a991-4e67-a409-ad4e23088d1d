import type { Meta, StoryObj } from '@storybook/react-vite';

import { Input } from '@components/fields';
import { textAreaPropDefs } from '@components/fields/text-area/text-area.props';
import { accentColors, radii } from '@props';

const meta: Meta<typeof Input.TextArea> = {
  title: 'DesignSystem/InputFields/TextArea',
  component: Input.TextArea,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    label: { type: 'string' },
    placeholder: { type: 'string' },
    helperText: { type: 'string' },
    value: { type: 'string' },
    defaultValue: { type: 'string' },
    rows: { control: 'number' },
    variant: { control: 'inline-radio', options: textAreaPropDefs.variant.values },
    size: { control: 'inline-radio', options: textAreaPropDefs.size.values },
    resize: { control: 'inline-radio', options: textAreaPropDefs.resize.values },
    error: { type: 'boolean' },
    disabled: { type: 'boolean' },
    readOnly: { type: 'boolean' },
    radius: { control: 'select', type: 'string', options: radii },
    color: { control: 'select', options: accentColors },
  },
};

export default meta;
type Story = StoryObj<typeof Input.TextArea>;

export const _textarea: Story = {
  args: {
    label: 'Label',
    placeholder: 'Type something...',
    helperText: 'Hint',
    rows: 4,
    variant: textAreaPropDefs.variant.default,
    size: textAreaPropDefs.size.default,
    resize: textAreaPropDefs.resize.default,
    error: false,
    disabled: false,
    readOnly: false,
    color: 'accent',
  },
};
