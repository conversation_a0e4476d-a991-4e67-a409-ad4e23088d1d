// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M322.371 272h379.257M85.333 224v96M938.667 224v96M749.035 896l0.004-189.449c0-11.076-1.344-22.063-3.964-32.687M274.963 896l0-189.449c0-11.076 1.342-22.063 3.961-32.687M278.924 673.865c2.451-9.941 6.021-19.563 10.656-28.651l46.447-91.055c20.814-40.798 60.639-66.159 103.9-66.159h144.145c43.26 0 83.085 25.361 103.898 66.159l46.451 91.055c4.634 9.088 8.205 18.709 10.654 28.651M278.924 673.865h466.151M251.259 416c-39.274 0-71.111-32.236-71.111-72v-144c0-39.764 31.837-72 71.111-72s71.111 32.236 71.111 72v144c0 39.764-31.838 72-71.111 72zM772.74 416c-39.275 0-71.113-32.236-71.113-72v-144c0-39.764 31.838-72 71.113-72s71.113 32.236 71.113 72v144c0 39.764-31.838 72-71.113 72z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "weight-bench"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 2007,
        "id": 13,
        "name": "weight-bench",
        "prevSize": 32,
        "code": 60283
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 636
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface WeightBenchIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const WeightBenchIcon = ({ size = 16, ...props }: WeightBenchIconProps) => (
  <IcoMoon iconSet={iconSet} icon="weight-bench" size={size} {...props} />
);

WeightBenchIcon.displayName = 'WeightBenchIcon';

export default WeightBenchIcon;
