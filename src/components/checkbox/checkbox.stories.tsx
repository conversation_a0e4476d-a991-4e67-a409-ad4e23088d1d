import type { Meta, StoryObj } from '@storybook/react-vite';
import { fn } from 'storybook/test';

import { Checkbox } from '@components/checkbox';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: 'DesignSystem/Checkbox/Checkbox',
  component: Checkbox,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  // tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    variant: {
      control: 'inline-radio',
      options: ['square', 'sphere'],
    },
    size: { control: 'inline-radio', options: ['1', '2', '3', '4', '5'] },
    highContrast: { control: 'boolean' },
    disabled: { control: 'boolean' },
    checked: {
      control: 'inline-radio',
      options: [true, false, 'indeterminate'],
    },
    label: { control: 'text', type: 'string' },
    caption: { control: 'text', type: 'string' },
  },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: { onClick: fn() },
} satisfies Meta<typeof Checkbox>;

export default meta;
type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const _checkbox: Story = {
  args: {
    variant: 'square',
    size: '3',
    highContrast: false,
    disabled: false,
    defaultChecked: true,
    label: 'Label',
    caption: 'abc',
  },
};
