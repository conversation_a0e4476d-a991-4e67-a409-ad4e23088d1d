// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M384 384v-192M384 384h-192M384 384l-224-224M384 640v192M384 640h-192M384 640l-224 224M640 384h192M640 384v-192M640 384l224-224M640 640h192M640 640v192M640 640l224 224"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "round",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "arrows-pointing-in"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "round",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1437,
        "id": 583,
        "name": "arrows-pointing-in",
        "prevSize": 32,
        "code": 59713
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 66
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ArrowsPointingInIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ArrowsPointingInIcon = ({ size = 16, ...props }: ArrowsPointingInIconProps) => (
  <IcoMoon iconSet={iconSet} icon="arrows-pointing-in" size={size} {...props} />
);

ArrowsPointingInIcon.displayName = 'ArrowsPointingInIcon';

export default ArrowsPointingInIcon;
