.rt-sidebar-wrapper {
  --sidebar-width: 16rem;
  --sidebar-width-icon: 3rem;
}

.rt-sidebar {
  @media (min-width: 768px) {
    display: block;
  }
}

.rt-sidebar li,
.rt-sidebar ul,
.rt-sidebar-hover-content ul {
  list-style: none;
}

.rt-sidebar ul:not(.rt-sidebar-menu-sub) {
  margin: 0;
  padding: 0;
}

.rt-sidebar:where([data-state='collapsed']):where([data-collapsible='icon']) > .rt-sidebar-gap,
.rt-sidebar:where([data-state='collapsed']):where([data-collapsible='icon']) > .rt-sidebar-container {
  width: var(--sidebar-width-icon);
}

.rt-sidebar:where([data-state='collapsed']):where([data-collapsible='icon']) > .rt-sidebar-gap:where(.rt-type-floating),
.rt-sidebar:where([data-state='collapsed']):where([data-collapsible='icon'])
  > .rt-sidebar-container:where(.rt-type-floating) {
  width: calc(var(--sidebar-width-icon) + var(--spatial-3) * 2 + 1px);
}

.rt-sidebar:where([data-state='collapsed']):where([data-collapsible='offcanvas'])
  > .rt-sidebar-container:where(.rt-side-left) {
  left: calc(var(--sidebar-width) * -1);
}

.rt-sidebar:where([data-state='collapsed']):where([data-collapsible='offcanvas'])
  > .rt-sidebar-container:where(.rt-side-right) {
  right: calc(var(--sidebar-width) * -1);
}

.rt-sidebar:where([data-state='collapsed'], [data-collapsible='icon']) .rt-sidebar-group-label {
  display: none;
}

.rt-sidebar:where([data-state='collapsed'], [data-collapsible='icon']) .rt-sidebar-content {
  overflow: hidden;
}

.rt-sidebar-rail {
  display: flex;
  position: absolute;
  z-index: 20;
  cursor: pointer;
  top: 0;
  bottom: 0;
  background-color: transparent;
  background-image: none;
  text-transform: none;
  width: 1rem;
  margin: 0;
  padding: 0;
  border: none;
  animation-timing-function: linear;
  transition-duration: 150ms;
  transition-timing-function: linear;
  transition-property: all;
  transform: translate(50%, 0) rotate(0) skewX(0) skewY(0) scaleX(1) scaleY(1);
}

.rt-sidebar-rail:where([data-state='collapsed']):where(.rt-collapsible-icon, .rt-collapsible-offcanvas):where(
    .rt-side-left
  ) {
  cursor: e-resize;
}

.rt-sidebar-rail:where([data-state='expanded']):where(.rt-collapsible-icon, .rt-collapsible-offcanvas):where(
    .rt-side-left
  ) {
  cursor: w-resize;
}

.rt-sidebar-rail:where([data-state='collapsed']):where(.rt-collapsible-icon, .rt-collapsible-offcanvas):where(
    .rt-side-right
  ) {
  cursor: w-resize;
}

.rt-sidebar-rail:where([data-state='expanded']):where(.rt-collapsible-icon, .rt-collapsible-offcanvas):where(
    .rt-side-right
  ) {
  cursor: e-resize;
}

.rt-sidebar-rail:where(.rt-side-left) {
  right: 0;
}

.rt-sidebar-rail:where(.rt-side-right) {
  left: calc(var(--spatial-5) * -1);
}

.rt-sidebar-rail:where(.rt-side-left)::after {
  left: 50%;
}

.rt-sidebar-rail:where(.rt-side-right)::after {
  right: 50%;
}

.rt-sidebar-rail::after {
  position: absolute;
  top: 0;
  bottom: 0;
  content: '';
  width: var(--spatial-1);
}

.rt-sidebar-rail:where([data-state='collapsed']):where(.rt-collapsible-offcanvas):where(.rt-side-left)::after {
  left: 100%;
}

.rt-sidebar-rail:where([data-state='expanded']):where(.rt-collapsible-offcanvas):where(.rt-side-left)::after {
  left: 50%;
}

.rt-sidebar-rail:where([data-state='collapsed']):where(.rt-collapsible-offcanvas):where(.rt-side-right)::after {
  right: 100%;
}

.rt-sidebar-rail:where([data-state='expanded']):where(.rt-collapsible-offcanvas):where(.rt-side-right)::after {
  right: 50%;
}

.rt-sidebar-rail:hover::after {
  background-color: var(--fills-quaternary);
}

.rt-sidebar:where([data-state='collapsed'], [data-collapsible='icon']) .rt-sidebar-menu-sub {
  display: none;
}

.rt-sidebar-gap {
  transition-timing-function: linear;
  transition-duration: 0.2s;
  transition-property: width;
  position: relative;
  background: transparent;
  height: 100svh;
  width: var(--sidebar-width);
}

.rt-sidebar:where(.rt-collapsible-none) {
  background-color: var(--background-grouped-primary-high);
}

.rt-sidebar .rt-sidebar-container .rt-sidebar-inner {
  background-color: var(--background-grouped-primary-high);
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.rt-sidebar-container {
  position: fixed;
  top: 0;
  bottom: 0;
  z-index: 10;
  display: none;
  height: 100svh;
  width: var(--sidebar-width);
  transition:
    left 200ms linear,
    right 200ms linear,
    width 200ms linear;
  box-sizing: border-box;
  transition-property: left, right, width;
}

@media (min-width: 768px) {
  .rt-sidebar-container {
    display: flex;
  }
}

.rt-sidebar-container:where(.rt-side-left):not(.rt-type-floating) {
  left: 0;
  border-right: 1px solid var(--fills-quaternary);
}

.rt-sidebar-container:where(.rt-side-right):not(.rt-type-floating) {
  right: 0;
  border-left: 1px solid var(--fills-quaternary);
}

.rt-sidebar-container:where(.rt-side-right) {
  right: 0;
}

.rt-sidebar-container:where(.rt-type-floating) {
  padding: var(--spatial-3);

  > .rt-sidebar-inner {
    border: 1px solid var(--fills-quaternary);
    border-radius: var(--radius-5);
  }
}

.rt-sidebar-group-label {
  display: flex;
  height: 2rem;
  flex-shrink: 0;
  align-items: center;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: var(--font-weight-semibold);
  color: var(--labels-secondary);
  outline: none;
  transition:
    margin 200ms linear,
    opacity 200ms linear;
}

.rt-sidebar-group-label:focus-visible {
  box-shadow: 0 0 0 2px var(--sidebar-ring);
}

/* SVG child styling */
.rt-sidebar-group-label > svg {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

/*
Sidebar menu
*/

.rt-sidebar-menu {
  display: flex;
  flex-direction: column;
  gap: var(--spatial-2);
  width: 100%;
}

.rt-sidebar-menu-item {
  position: relative;

  &:where(.rt-r-size-1) {
    --line-margin: calc(var(--spatial-5) + 1px);
  }

  &:where(.rt-r-size-2) {
    --line-margin: calc(var(--spatial-5) + var(--spatial-1));
  }

  &:where(.rt-r-size-3) {
    --line-margin: calc(var(--spatial-5) + var(--spatial-1));
  }
}

/* Collapsible group styling */
.rt-sidebar-menu-button[data-state='open'] > .rt-sidebar-menu-button-icon:last-child > svg {
  transform: rotate(90deg);
}

.rt-sidebar-menu-item .rt-sidebar-menu-sub-item .rt-sidebar-menu-button-icon:last-child > svg {
  transition: transform 200ms ease;
}

.rt-sidebar-menu-sub {
  display: flex;
  flex-direction: column;
  transform: translateX(var(--spatial-1));
  gap: var(--spatial-2);
  padding-inline: calc(var(--spatial-2) * 2.5);
  border-left: 1px dashed var(--separators-inverted-primary);
  padding-top: var(--spatial-2);
  margin-left: var(--line-margin);
}

.rt-sidebar-menu-sub-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--spatial-2);
  padding: var(--spatial-2);
  border-radius: var(--radius-2);
  cursor: pointer;
  transition: background-color 200ms ease;
}

.rt-sidebar-menu-sub-item:hover {
  background-color: var(--background-grouped-secondary-low);
}

.rt-sidebar-menu-sub-item span {
  flex: 1;
  min-width: 0;
}

.rt-sidebar:where([data-collapsible='icon'], [data-state='collapsed']) .rt-sidebar-menu-button {
  overflow: hidden;
  transition-property: width, height, padding;
  transition-timing-function: linear, cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.2s, 0.15s;
}

.rt-sidebar-menu-button {
  --stroke-stroke-glass-specular: linear-gradient(
    157deg,
    rgba(255, 255, 255, 0.4) 2.12%,
    rgba(255, 255, 255, 0) 39%,
    rgba(255, 255, 255, 0) 54.33%,
    rgba(255, 255, 255, 0.1) 93.02%
  );
  outline-style: none;
  width: 100%;
  text-align: left;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  border-radius: var(--radius-5, 14px);
  border: none;
  font-weight: var(--font-weight-regular, 400);
  line-height: var(--line-height-5, 20px);
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  background: none;
  font-style: normal;
  font-weight: var(--font-weight-regular, 400);
  gap: var(--spatial-5);

  /* 123.077% */
  &:hover {
    background: var(--fills-quaternary);
  }

  &:where(.rt-r-size-1) {
    height: var(--spatial-7, 24px);
    padding: var(--spatial-2, 4px) var(--spatial-4, 12px) var(--spatial-2, 4px) var(--spatial-4, 12px);
    font-size: var(--font-size-4);

    > .rt-sidebar-menu-button-icon > svg {
      width: calc(var(--icon-size-1) + 3px) !important;
      height: var(--icon-size-1) !important;
    }
  }

  &:where(.rt-r-size-2) {
    height: var(--spatial-8, 32px);
    padding: var(--spatial-2, 4px) var(--spatial-4, 12px) var(--spatial-2, 4px) var(--spatial-4, 12px);
    font-size: var(--font-size-4);

    > .rt-sidebar-menu-button-icon > svg {
      width: var(--icon-size-3) !important;
      height: var(--icon-size-3) !important;
    }
  }

  &:where(.rt-r-size-3) {
    height: var(--spatial-9, 40px);
    padding: var(--spatial-4, 12px) var(--spatial-3, 8px) var(--spatial-4, 12px) var(--spatial-3, 8px);
    font-size: var(--font-size-5);

    > .rt-sidebar-menu-button-icon > svg {
      width: var(--icon-size-5) !important;
      height: var(--icon-size-5) !important;
    }
  }

  &:where([data-active='active']) {
    &:where(.rt-variant-active-glass) {
      background: var(--materials-inverted, rgba(0, 0, 0, 0.06));
      border: var(--border-width-thinner, 0.5px) solid var(--stroke-stroke-glass-specular, rgba(255, 255, 255, 0.4));
      box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(50px);

      &:hover {
        background:
          linear-gradient(0deg, rgba(0, 0, 0, 0.02) 0%, rgba(0, 0, 0, 0.02) 100%),
          var(--materials-inverted, rgba(0, 0, 0, 0.06));
      }
    }

    &:where(.rt-variant-active-primary) {
      background: var(--brand-accent, #20cb73);

      color: var(--grays-white);

      &:hover {
        background: linear-gradient(0deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.1) 100%), var(--brand-accent, #20cb73);
      }
    }

    &:where(.rt-variant-active-primary-accent) {
      background: var(--brand-accent-button-bezeled-fill, rgba(47, 217, 129, 0.1));
      color: var(--brand-Accent, #20cb73);

      &:hover {
        background: var(--brand-accent-button-bezeled-fill-hover, rgba(47, 217, 129, 0.2));
      }
    }

    &:where(.rt-variant-active-fill) {
      background: var(--background-grouped-primary-high);
      border: var(--border-width-thinner, 0.5px) solid var(--stroke-stroke-glass-specular, rgba(255, 255, 255, 0.4));
      box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(50px);

      &:hover {
        background:
          linear-gradient(0deg, rgba(0, 0, 0, 0.02) 0%, rgba(0, 0, 0, 0.02) 100%),
          var(--background-grouped-primary-high, #fff);
      }
    }
  }
}

.rt-sidebar-menu-button svg {
  flex-shrink: 0;
}

.rt-sidebar-hover-content {
  border: 1px solid var(--fills-primary);
  border-radius: var(--spatial-4);
  background-color: var(--background-tertiary-elevated);

  > .rt-sidebar-menu-sub {
    border: none;
    margin-top: 0;
  }

  > .rt-sidebar-group-label {
    padding-left: var(--spatial-4);
    border-bottom: 1px solid var(--fills-primary);
    border-radius: 0;
  }
}
