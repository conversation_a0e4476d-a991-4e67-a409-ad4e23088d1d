// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M592.934 85.333l-9.476 194.266c-1.348 27.639 6.78 54.891 22.98 77.030l19.418 26.54c16.090 21.991 24.222 49.032 23.002 76.492l-18.091 407.97c-1.715 38.699 28.429 71.036 66.223 71.036s67.938-32.337 66.223-71.036l-18.091-407.97c-1.22-27.46 6.912-54.501 23.002-76.492l19.418-26.54c16.201-22.139 24.329-49.39 22.98-77.030l-9.476-194.266M696.99 298.667v-213.333M217.757 191.867l-4.279 87.733c-1.348 27.639 6.781 54.891 22.979 77.030l19.418 26.54c16.090 21.991 24.222 49.032 23.005 76.492l-18.090 407.97c-1.716 38.699 28.429 71.036 66.221 71.036s67.937-32.337 66.221-71.036l-18.090-407.97c-1.218-27.46 6.915-54.501 23.005-76.492l19.418-26.54c16.199-22.139 24.327-49.39 22.979-77.030l-4.279-87.733c-2.91-59.678-50.961-106.534-109.252-106.534s-106.342 46.855-109.253 106.534z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "restaurant"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1852,
        "id": 168,
        "name": "restaurant",
        "prevSize": 32,
        "code": 60128
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 481
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface RestaurantIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const RestaurantIcon = ({ size = 16, ...props }: RestaurantIconProps) => (
  <IcoMoon iconSet={iconSet} icon="restaurant" size={size} {...props} />
);

RestaurantIcon.displayName = 'RestaurantIcon';

export default RestaurantIcon;
