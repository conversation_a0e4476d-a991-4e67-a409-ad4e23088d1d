// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M195.317 368.856l-3.317 453.138c0.258 2.505 1.506 4.877 3.317 6.69M655.142 828.685l-453.138 3.315c-2.505-0.256-4.877-1.506-6.688-3.315M195.317 828.685l636.683-636.685"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "arrow-down-left"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1407,
        "id": 613,
        "name": "arrow-down-left",
        "prevSize": 32,
        "code": 59683
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 36
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ArrowDownLeftIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ArrowDownLeftIcon = ({ size = 16, ...props }: ArrowDownLeftIconProps) => (
  <IcoMoon iconSet={iconSet} icon="arrow-down-left" size={size} {...props} />
);

ArrowDownLeftIcon.displayName = 'ArrowDownLeftIcon';

export default ArrowDownLeftIcon;
