// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M261.914 107.365l138.252 188.525c14.082 19.203 9.295 46.273-10.519 59.482-46.253 30.835-65.093 88.421-33.666 134.275 18.945 27.639 45.297 60.74 81.463 96.909 36.169 36.164 69.269 62.519 96.909 81.463 45.854 31.428 103.441 12.587 134.276-33.668 13.21-19.81 40.277-24.597 59.482-10.517l188.523 138.253c30.195 22.144 29.18 67.567-1.975 88.337l-78.374 52.25c-55.505 37.001-123.059 49.967-181.252 17.348-66.756-37.419-168.602-109.12-305.268-245.786s-208.367-238.511-245.786-305.269c-32.618-58.192-19.653-125.744 17.351-181.251l52.25-78.375c20.77-31.155 66.193-32.172 88.335-1.977z",
          "M640 256h128M768 256h128M768 256v-128M768 256v128"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "call-add"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1504,
        "id": 516,
        "name": "call-add",
        "prevSize": 32,
        "code": 59780
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 133
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CallAddIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CallAddIcon = ({ size = 16, ...props }: CallAddIconProps) => (
  <IcoMoon iconSet={iconSet} icon="call-add" size={size} {...props} />
);

CallAddIcon.displayName = 'CallAddIcon';

export default CallAddIcon;
