// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M660.864 464.205v0zM680.384 347.795v0zM670.302 343.759v0zM595.43 452.403v0zM554.948 594.598v0zM362.196 464.205v0zM381.718 347.795v0zM371.636 343.759v0zM296.762 452.403v0zM256.281 594.598v0zM155.223 924.715v0zM99.284 868.779v0zM924.715 868.779v0zM868.779 924.715v0zM868.779 99.284v0zM924.715 155.223v0zM155.223 99.284v0zM99.284 155.223v0zM768 585.869h32v-11.379h-64v11.379h32zM648.013 682.667v32h22.592v-64h-22.592v32zM660.864 464.205l31.561 5.291 19.52-116.409-63.121-10.584-19.52 116.412 31.561 5.291zM670.302 343.759l-26.347-18.159-74.876 108.644 52.698 36.318 74.876-108.644-26.351-18.159zM680.384 347.795l31.561 5.292c6.771-40.382-45.653-59.898-67.989-27.486l52.698 36.318c-15.603 22.641-52.642 9.283-47.829-19.416l31.561 5.292zM682.057 489.071v-32c6.276 0 11.494 5.7 10.368 12.425l-63.121-10.581c-5.491 32.759 19.921 62.157 52.753 62.157v-32zM554.948 594.598l-31.962 1.583c3.29 66.53 58.458 118.485 125.026 118.485v-64c-32.794 0-59.516-25.532-61.103-57.647l-31.962 1.579zM554.948 594.598l31.962-1.579c-2.15-43.49 10.086-86.498 34.867-122.458l-52.698-36.318c-32.738 47.505-48.939 104.384-46.093 161.937l31.962-1.583zM768 574.49h32c0-65.033-52.992-117.419-117.943-117.419v64c29.978 0 53.943 24.098 53.943 53.419h32zM768 585.869h-32c0 35.601-29.094 64.798-65.395 64.798v64c71.279 0 129.395-57.481 129.395-128.798h-32zM469.333 585.869h32v-11.379h-64v11.379h32zM349.348 682.667v32h22.592v-64h-22.592v32zM362.196 464.205l31.56 5.291 19.521-116.409-63.119-10.584-19.521 116.412 31.559 5.291zM371.636 343.759l-26.349-18.159-74.874 108.644 52.698 36.318 74.874-108.644-26.349-18.159zM381.718 347.795l31.559 5.292c6.772-40.382-45.652-59.898-67.989-27.486l52.698 36.318c-15.604 22.641-52.64 9.283-47.827-19.416l31.56 5.292zM383.39 489.071v-32c6.278 0 11.494 5.7 10.366 12.425l-63.119-10.581c-5.493 32.759 19.922 62.157 52.753 62.157v-32zM256.281 594.598l-31.961 1.583c3.289 66.53 58.457 118.485 125.029 118.485v-64c-32.797 0-59.519-25.532-61.107-57.647l-31.961 1.579zM256.281 594.598l31.961-1.579c-2.15-43.49 10.088-86.498 34.87-122.458l-52.698-36.318c-32.739 47.505-48.939 104.384-46.094 161.937l31.961-1.583zM469.333 574.49h32c0-65.033-52.992-117.419-117.943-117.419v64c29.978 0 53.943 24.098 53.943 53.419h32zM469.333 585.869h-32c0 35.601-29.092 64.798-65.393 64.798v64c71.277 0 129.393-57.481 129.393-128.798h-32zM290.133 85.333v32h443.733v-64h-443.733v32zM938.667 290.133h-32v443.733h64v-443.733h-32zM733.867 938.667v-32h-443.733v64h443.733v-32zM85.333 733.867h32v-443.733h-64v443.733h32zM290.133 938.667v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM85.333 733.867h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM155.223 924.715l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM938.667 733.867h-32c0 36.373-0.026 61.726-1.638 81.463-1.583 19.366-4.531 30.494-8.823 38.921l57.024 29.056c9.655-18.957 13.683-39.441 15.586-62.763 1.877-22.95 1.852-51.362 1.852-86.677h-32zM733.867 938.667v32c35.315 0 63.727 0.026 86.677-1.852 23.322-1.903 43.806-5.931 62.763-15.586l-29.056-57.024c-8.427 4.292-19.554 7.241-38.921 8.823-19.738 1.613-45.090 1.638-81.463 1.638v32zM924.715 868.779l-28.51-14.528c-9.207 18.061-23.893 32.747-41.954 41.954l29.056 57.024c30.106-15.343 54.579-39.817 69.922-69.922l-28.514-14.528zM733.867 85.333v32c36.373 0 61.726 0.025 81.463 1.638 19.366 1.582 30.494 4.532 38.921 8.826l29.056-57.024c-18.957-9.658-39.441-13.683-62.763-15.589-22.95-1.875-51.362-1.85-86.677-1.85v32zM938.667 290.133h32c0-35.315 0.026-63.727-1.852-86.676-1.903-23.323-5.931-43.809-15.586-62.762l-57.024 29.055c4.292 8.428 7.241 19.553 8.823 38.919 1.613 19.739 1.638 45.093 1.638 81.464h32zM868.779 99.284l-14.528 28.512c18.061 9.204 32.747 23.89 41.954 41.953l57.024-29.055c-15.343-30.106-39.817-54.583-69.922-69.923l-14.528 28.512zM290.133 85.333v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM85.333 290.133h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM155.223 99.284l-14.528-28.512c-30.106 15.34-54.583 39.817-69.923 69.923l57.024 29.055c9.204-18.063 23.89-32.75 41.953-41.953l-14.528-28.512z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "quote-up-square"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1842,
        "id": 178,
        "name": "quote-up-square",
        "prevSize": 32,
        "code": 60118
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 471
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface QuoteUpSquareIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const QuoteUpSquareIcon = ({ size = 16, ...props }: QuoteUpSquareIconProps) => (
  <IcoMoon iconSet={iconSet} icon="quote-up-square" size={size} {...props} />
);

QuoteUpSquareIcon.displayName = 'QuoteUpSquareIcon';

export default QuoteUpSquareIcon;
