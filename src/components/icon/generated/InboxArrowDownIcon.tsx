// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 643.652v164.284c0 61.879 47.301 113.579 108.759 118.007 238.744 17.212 397.34 16.768 635.557-0.162 61.564-4.373 109.018-56.115 109.018-118.093v-164.036M85.333 643.652l78.376-413.204c9.764-51.477 51.936-90.681 103.907-95.402 31.2-2.834 60.882-5.184 89.571-7.046M85.333 643.652h184.034c32.483 0 62.179 18.432 76.707 47.607s44.223 47.607 76.707 47.607h178.439c32.482 0 62.178-18.432 76.706-47.607s44.224-47.607 76.706-47.607h184.034M938.667 643.652l-78.409-413.379c-9.749-51.394-51.802-90.574-103.684-95.331-30.464-2.793-59.482-5.106-87.543-6.941",
          "M512 85.333v469.333M512 554.667l-128-123.507M512 554.667l128-123.507"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "inbox-arrow-down"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1719,
        "id": 301,
        "name": "inbox-arrow-down",
        "prevSize": 32,
        "code": 59995
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 348
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface InboxArrowDownIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const InboxArrowDownIcon = ({ size = 16, ...props }: InboxArrowDownIconProps) => (
  <IcoMoon iconSet={iconSet} icon="inbox-arrow-down" size={size} {...props} />
);

InboxArrowDownIcon.displayName = 'InboxArrowDownIcon';

export default InboxArrowDownIcon;
