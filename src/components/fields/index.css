@import './input/input.css';
@import './text-area/text-area.css';
@import './select/select.css';
@import './otp/otp.css';

.rt-InputFieldWrapper {
  display: flex;
  flex-direction: column;

  &:has(
      .rt-Input:disabled,
      .rt-Input:read-only,
      .rt-TextAreaInput:disabled,
      .rt-TextAreaInput:read-only,
      .rt-SelectTrigger:disabled
    )
    :is(.rt-InputFieldLabel, .rt-InputFieldHelperText, .rt-InputSlot) {
    color: var(--labels-tertiary);

    &:where([data-accent-color]) {
      color: var(--brand-accent);
    }
  }
}

.rt-error {
  outline: 2px solid var(--brand-red-40);
  outline-offset: -1px;
}

.rt-InputFieldLabel {
  font-size: var(--font-size-4);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-4);
  color: var(--labels-primary);
  margin-bottom: var(--spatial-2);
}

.rt-InputFieldHelperText {
  font-size: var(--font-size-4);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-4);
  color: var(--labels-primary);
  margin-top: var(--spatial-3);
}
