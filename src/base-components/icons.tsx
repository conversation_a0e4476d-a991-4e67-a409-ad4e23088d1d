import React from 'react';

import type { ComponentPropsWithout, RemovedProps } from '@helpers';

type IconElement = React.ComponentRef<'svg'>;
interface IconProps extends ComponentPropsWithout<'svg', RemovedProps | 'children'> {}

const ThickDividerHorizontalIcon = React.forwardRef<IconElement, IconProps>((props, forwardedRef) => {
  return (
    <svg
      width="9"
      height="9"
      viewBox="0 0 9 9"
      fill="currentcolor"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
      ref={forwardedRef}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.75 4.5C0.75 4.08579 1.08579 3.75 1.5 3.75H7.5C7.91421 3.75 8.25 4.08579 8.25 4.5C8.25 4.91421 7.91421 5.25 7.5 5.25H1.5C1.08579 5.25 0.75 4.91421 0.75 4.5Z"
      />
    </svg>
  );
});

ThickDividerHorizontalIcon.displayName = 'ThickDividerHorizontalIcon';

const ThickCheckIcon = React.forwardRef<IconElement, IconProps>((props, forwardedRef) => {
  return (
    <svg
      width="9"
      height="9"
      viewBox="0 0 9 9"
      fill="currentcolor"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
      ref={forwardedRef}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.53547 0.62293C8.88226 0.849446 8.97976 1.3142 8.75325 1.66099L4.5083 8.1599C4.38833 8.34356 4.19397 8.4655 3.9764 8.49358C3.75883 8.52167 3.53987 8.45309 3.3772 8.30591L0.616113 5.80777C0.308959 5.52987 0.285246 5.05559 0.563148 4.74844C0.84105 4.44128 1.31533 4.41757 1.62249 4.69547L3.73256 6.60459L7.49741 0.840706C7.72393 0.493916 8.18868 0.396414 8.53547 0.62293Z"
      />
    </svg>
  );
});
ThickCheckIcon.displayName = 'ThickCheckIcon';

const ChevronDownIcon = React.forwardRef<IconElement, IconProps>((props, forwardedRef) => {
  return (
    <svg
      width="9"
      height="9"
      viewBox="0 0 9 9"
      fill="currentcolor"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
      ref={forwardedRef}
    >
      <path d="M0.135232 3.15803C0.324102 2.95657 0.640521 2.94637 0.841971 3.13523L4.5 6.56464L8.158 3.13523C8.3595 2.94637 8.6759 2.95657 8.8648 3.15803C9.0536 3.35949 9.0434 3.67591 8.842 3.86477L4.84197 7.6148C4.64964 7.7951 4.35036 7.7951 4.15803 7.6148L0.158031 3.86477C-0.0434285 3.67591 -0.0536285 3.35949 0.135232 3.15803Z" />
    </svg>
  );
});
ChevronDownIcon.displayName = 'ChevronDownIcon';

const VerificationIcon = React.forwardRef<IconElement, IconProps>((props, forwardedRef) => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="currentcolor"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
      ref={forwardedRef}
    >
      <path
        d="M13.5486 0.905725L13.6528 1.11381L14.439 3.20462C14.5008 3.36927 14.6307 3.49919 14.7954 3.56096L16.814 4.31829C17.6999 4.65063 18.1759 5.59722 17.9399 6.49282L17.8831 6.67107L16.9439 8.74803C16.8712 8.90813 16.8712 9.09187 16.9439 9.25197L17.8358 11.2149C18.2272 12.0763 17.8945 13.0822 17.0943 13.5486L16.8862 13.6528L14.7954 14.439C14.6307 14.5008 14.5008 14.6307 14.439 14.7954L13.6817 16.814C13.3494 17.6999 12.4028 18.1759 11.5072 17.9399L11.3289 17.8831L9.25197 16.9439C9.09187 16.8712 8.90813 16.8712 8.74803 16.9439L6.78511 17.8358C5.92372 18.2272 4.91778 17.8945 4.45141 17.0943L4.34718 16.8862L3.56096 14.7954C3.49919 14.6307 3.36927 14.5008 3.20462 14.439L1.18597 13.6817C0.300118 13.3494 -0.17591 12.4028 0.0601429 11.5072L0.116938 11.3289L1.05606 9.25197C1.12881 9.09187 1.12881 8.90813 1.05606 8.74803L0.164176 6.78511C-0.227213 5.92372 0.105523 4.91778 0.905725 4.45141L1.11381 4.34718L3.20462 3.56096C3.36927 3.49919 3.49919 3.36927 3.56096 3.20462L4.31829 1.18597C4.65063 0.300118 5.59722 -0.17591 6.49282 0.0601429L6.67107 0.116938L8.74803 1.05606C8.90813 1.12881 9.09187 1.12881 9.25197 1.05606L11.2149 0.164176C12.0763 -0.227213 13.0822 0.105523 13.5486 0.905725Z"
        fill="url(#paint0_linear_1490_785)"
      />
      <path
        d="M7.60196 10.8376L11.9468 5.87208C12.1683 5.61891 12.5532 5.59326 12.8063 5.81478C13.0595 6.0363 13.0852 6.42112 12.8636 6.67429L8.09003 12.1298C7.85801 12.395 7.45007 12.4086 7.20092 12.1594L5.15509 10.1136C4.91722 9.87574 4.91722 9.49007 5.15509 9.25219C5.39296 9.01432 5.77863 9.01432 6.01651 9.25219L7.60196 10.8376Z"
        fill="white"
      />
      <defs>
        <linearGradient id="paint0_linear_1490_785" x1="9" y1="0" x2="9" y2="18" gradientUnits="userSpaceOnUse">
          <stop stop-color="#59B0F3" />
          <stop offset="1" stop-color="#118BE8" />
        </linearGradient>
      </defs>
    </svg>
  );
});
VerificationIcon.displayName = 'VerificationIcon';

const ThickChevronRightIcon = React.forwardRef<IconElement, IconProps>((props, forwardedRef) => {
  return (
    <svg
      width="9"
      height="9"
      viewBox="0 0 9 9"
      fill="currentcolor"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
      ref={forwardedRef}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.23826 0.201711C3.54108 -0.0809141 4.01567 -0.0645489 4.29829 0.238264L7.79829 3.98826C8.06724 4.27642 8.06724 4.72359 7.79829 5.01174L4.29829 8.76174C4.01567 9.06455 3.54108 9.08092 3.23826 8.79829C2.93545 8.51567 2.91909 8.04108 3.20171 7.73826L6.22409 4.5L3.20171 1.26174C2.91909 0.958928 2.93545 0.484337 3.23826 0.201711Z"
      />
    </svg>
  );
});
ThickChevronRightIcon.displayName = 'ThickChevronRightIcon';

const EyeIcon = React.forwardRef<IconElement, IconProps>((props, forwardedRef) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="currentcolor"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
      ref={forwardedRef}
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M21.4597 13.1848C19.966 14.9333 16.679 18 12 18C7.32099 18 4.03405 14.9333 2.54027 13.1848C2.27051 12.869 2.13564 12.7112 2.04683 12.4084C1.98439 12.1955 1.98439 11.8045 2.04683 11.5916C2.13564 11.2888 2.27051 11.131 2.54027 10.8152C4.03405 9.06674 7.32099 6 12 6C16.679 6 19.966 9.06674 21.4597 10.8152C21.7295 11.131 21.8644 11.2888 21.9532 11.5916C22.0156 11.8045 22.0156 12.1955 21.9532 12.4084C21.8644 12.7112 21.7295 12.869 21.4597 13.1848ZM15 12C15 13.6569 13.6569 15 12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12Z"
        fill="currentcolor"
      />
    </svg>
  );
});
EyeIcon.displayName = 'EyeIcon';

const EyeOffIcon = React.forwardRef<IconElement, IconProps>((props, forwardedRef) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="currentcolor"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
      ref={forwardedRef}
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M7.88513 6.82435L3.591 2.53022C3.29811 2.23732 2.82323 2.23732 2.53034 2.53022C2.23745 2.82311 2.23745 3.29798 2.53034 3.59088L20.409 21.4696C20.7019 21.7625 21.1768 21.7625 21.4697 21.4696C21.7626 21.1767 21.7626 20.7018 21.4697 20.4089L17.5384 16.4777C19.3256 15.4555 20.6537 14.1282 21.4597 13.1847C21.7295 12.869 21.8644 12.7111 21.9532 12.4083C22.0156 12.1954 22.0156 11.8045 21.9532 11.5916C21.8644 11.2888 21.7295 11.1309 21.4597 10.8152C19.966 9.06668 16.679 5.99994 12 5.99994C10.4761 5.99994 9.09985 6.32524 7.88513 6.82435ZM10.4761 9.41529L14.5847 13.5239C14.8486 13.0773 15 12.5563 15 11.9999C15 10.3431 13.6569 8.99994 12 8.99994C11.4437 8.99994 10.9227 9.15138 10.4761 9.41529Z"
        fill="currentcolor"
      />
      <path
        d="M12.3318 14.9818L14.936 17.5859C14.0305 17.8454 13.0503 17.9999 12 17.9999C7.32099 17.9999 4.03405 14.9332 2.54027 13.1847C2.27051 12.869 2.13564 12.7111 2.04683 12.4083C1.98439 12.1954 1.98439 11.8045 2.04683 11.5916C2.13564 11.2888 2.27051 11.1309 2.54027 10.8152C3.19218 10.0521 4.18562 9.03794 5.48724 8.13721L9.01814 11.6681C9.00615 11.7771 9 11.8878 9 11.9999C9 13.6568 10.3431 14.9999 12 14.9999C12.1122 14.9999 12.2229 14.9938 12.3318 14.9818Z"
        fill="currentcolor"
      />
    </svg>
  );
});
EyeOffIcon.displayName = 'EyeOffIcon';

export {
  ChevronDownIcon,
  EyeIcon,
  EyeOffIcon,
  ThickCheckIcon,
  ThickChevronRightIcon,
  ThickDividerHorizontalIcon,
  VerificationIcon,
};
export type { IconProps };
