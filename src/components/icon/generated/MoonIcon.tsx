// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M938.035 702.118c2.334-4.267-2.244-8.926-6.656-6.886-57.856 26.726-122.287 41.638-190.204 41.638-250.761 0-454.045-203.285-454.045-454.046 0-67.915 14.911-132.348 41.638-190.202 2.039-4.415-2.622-8.992-6.889-6.659-140.949 77.062-236.547 226.696-236.547 398.657 0 250.765 203.283 454.046 454.046 454.046 171.959 0 321.596-95.599 398.656-236.548z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "moon"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1788,
        "id": 232,
        "name": "moon",
        "prevSize": 32,
        "code": 60064
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 417
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MoonIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MoonIcon = ({ size = 16, ...props }: MoonIconProps) => (
  <IcoMoon iconSet={iconSet} icon="moon" size={size} {...props} />
);

MoonIcon.displayName = 'MoonIcon';

export default MoonIcon;
