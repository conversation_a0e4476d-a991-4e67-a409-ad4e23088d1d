// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M320 311.097c15.143-6.88 32.17-10.743 50.17-10.743h26.681c27.004 0 53.133-8.876 73.745-25.050l134.899-105.846c55.637-43.654 141.184-12.205 147.661 55.11 18.219 189.321 19.661 339.61 4.352 524.318M698.543 868.22c-5.124 1.967-10.441 3.503-15.876 4.582-5.047 0.998-10.197 1.604-15.394 1.796-21.295 0.789-43.345-5.402-62.025-20.058l-134.652-105.655c-20.612-16.175-46.742-25.050-73.745-25.050h-26.681c-63.055 0-114.17-47.398-114.17-105.869v-211.743M128 128l768 768"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "volume-slash"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1996,
        "id": 24,
        "name": "volume-slash",
        "prevSize": 32,
        "code": 60272
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 625
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface VolumeSlashIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const VolumeSlashIcon = ({ size = 16, ...props }: VolumeSlashIconProps) => (
  <IcoMoon iconSet={iconSet} icon="volume-slash" size={size} {...props} />
);

VolumeSlashIcon.displayName = 'VolumeSlashIcon';

export default VolumeSlashIcon;
