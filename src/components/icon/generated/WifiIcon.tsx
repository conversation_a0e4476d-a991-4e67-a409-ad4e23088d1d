// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 359.32c0 0 142.046-167.32 426.667-167.32s426.667 167.32 426.667 167.32M218.667 526.639c0 0 118.107-111.545 293.333-111.545 175.228 0 293.333 111.545 293.333 111.545M352 666.074c0 0 66.667-55.774 160-55.774s160 55.774 160 55.774M512.474 832h-0.474"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "wifi"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 2010,
        "id": 10,
        "name": "wifi",
        "prevSize": 32,
        "code": 60286
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 639
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface WifiIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const WifiIcon = ({ size = 16, ...props }: WifiIconProps) => (
  <IcoMoon iconSet={iconSet} icon="wifi" size={size} {...props} />
);

WifiIcon.displayName = 'WifiIcon';

export default WifiIcon;
