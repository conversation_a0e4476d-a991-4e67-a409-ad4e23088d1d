// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M768 725.333c-47.13 0-85.333-35.819-85.333-80v-88.887c0-44.186 38.204-80 85.333-80s85.333 35.814 85.333 80v88.887c0 44.181-38.204 80-85.333 80zM768 725.333v42.667c0 70.694-57.306 128-128 128h-64M170.667 565.333v-160c0-176.731 152.82-320 341.333-320s341.333 143.269 341.333 320v160M576 896c0 23.565-19.102 42.667-42.667 42.667h-42.667c-23.565 0-42.667-19.102-42.667-42.667s19.102-42.667 42.667-42.667h42.667c23.565 0 42.667 19.102 42.667 42.667zM256 725.333c-47.128 0-85.333-35.819-85.333-80v-88.887c0-44.186 38.205-80 85.333-80s85.333 35.814 85.333 80v88.887c0 44.181-38.205 80-85.333 80z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "headphone-alt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1702,
        "id": 318,
        "name": "headphone-alt",
        "prevSize": 32,
        "code": 59978
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 331
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface HeadphoneAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const HeadphoneAltIcon = ({ size = 16, ...props }: HeadphoneAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="headphone-alt" size={size} {...props} />
);

HeadphoneAltIcon.displayName = 'HeadphoneAltIcon';

export default HeadphoneAltIcon;
