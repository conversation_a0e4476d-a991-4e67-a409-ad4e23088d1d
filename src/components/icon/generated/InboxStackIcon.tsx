// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M128 760.486v-77.82c0-23.565 19.103-42.667 42.667-42.667h122.964c29.235 0 55.962 16.516 69.036 42.667s39.801 42.667 69.035 42.667h160.597c29.235 0 55.962-16.516 69.035-42.667s39.799-42.667 69.035-42.667h122.965c23.565 0 42.667 19.102 42.667 42.667v77.598c0 55.548-42.709 101.918-98.116 105.839-214.391 15.172-357.129 15.573-572 0.145-55.313-3.972-97.883-50.304-97.883-105.762z",
          "M128 384v120.486c0 55.458 42.571 101.79 97.883 105.762 214.872 15.428 357.609 15.027 572-0.145 55.407-3.921 98.116-50.291 98.116-105.839v-120.265M128 384h165.631c29.235 0 55.962 16.518 69.036 42.667 13.074 26.15 39.801 42.667 69.035 42.667h160.597c29.235 0 55.962-16.516 69.035-42.667 13.073-26.149 39.799-42.667 69.035-42.667h165.632M128 384l123.131-193.492c16.638-26.147 43.825-43.901 74.56-47.883 139.32-18.052 234.693-18.115 372.75-0.058 30.665 4.011 57.762 21.752 74.364 47.84l123.196 193.592"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "inbox-stack"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1720,
        "id": 300,
        "name": "inbox-stack",
        "prevSize": 32,
        "code": 59996
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 349
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface InboxStackIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const InboxStackIcon = ({ size = 16, ...props }: InboxStackIconProps) => (
  <IcoMoon iconSet={iconSet} icon="inbox-stack" size={size} {...props} />
);

InboxStackIcon.displayName = 'InboxStackIcon';

export default InboxStackIcon;
