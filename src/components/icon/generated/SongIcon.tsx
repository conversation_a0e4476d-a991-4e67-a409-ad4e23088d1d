// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M369.778 794.709c0 79.505-63.675 143.957-142.223 143.957s-142.222-64.452-142.222-143.957c0-79.505 63.675-143.957 142.222-143.957s142.223 64.452 142.223 143.957zM369.778 794.709v-496.043M938.667 794.709c0 79.505-63.676 143.957-142.221 143.957-78.549 0-142.225-64.452-142.225-143.957s63.676-143.957 142.225-143.957c78.545 0 142.221 64.452 142.221 143.957zM938.667 794.709v-448.635M369.778 298.667v-93.361c0-70.184 59.261-125.378 128.36-119.549l331.853 27.991c61.427 5.181 108.676 57.158 108.676 119.549v112.777M369.778 298.667l568.889 47.407"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "song"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1909,
        "id": 111,
        "name": "song",
        "prevSize": 32,
        "code": 60185
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 538
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SongIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SongIcon = ({ size = 16, ...props }: SongIconProps) => (
  <IcoMoon iconSet={iconSet} icon="song" size={size} {...props} />
);

SongIcon.displayName = 'SongIcon';

export default SongIcon;
