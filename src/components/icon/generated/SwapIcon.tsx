// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M274.289 848v-720M274.289 128l237.711 213.333M274.289 128l-231.622 213.333M749.717 176v720M749.717 896l231.616-213.333M749.717 896l-237.717-213.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "round",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "swap"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "round",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1924,
        "id": 96,
        "name": "swap",
        "prevSize": 32,
        "code": 60200
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 553
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SwapIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SwapIcon = ({ size = 16, ...props }: SwapIconProps) => (
  <IcoMoon iconSet={iconSet} icon="swap" size={size} {...props} />
);

SwapIcon.displayName = 'SwapIcon';

export default SwapIcon;
