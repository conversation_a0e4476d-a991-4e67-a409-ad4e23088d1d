// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M640 931.004c-10.995 5.001-23.031 7.663-35.345 7.663h-305.988c-70.692 0-128-57.306-128-128v-533.333c0-70.692 57.308-128 128-128h426.667c70.694 0 128 57.308 128 128v412.655c0 12.314-2.662 24.35-7.663 35.345M640 931.004c9.216-4.194 17.698-10.035 24.994-17.331l163.345-163.345c7.296-7.296 13.137-15.778 17.331-24.994M640 931.004v-120.337c0-47.13 38.204-85.333 85.333-85.333h120.337M341.333 85.333v128M682.667 85.333v128M341.333 384h341.333M341.333 533.333h341.333M341.333 682.667h170.667"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "note"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1800,
        "id": 220,
        "name": "note",
        "prevSize": 32,
        "code": 60076
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 429
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface NoteIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const NoteIcon = ({ size = 16, ...props }: NoteIconProps) => (
  <IcoMoon iconSet={iconSet} icon="note" size={size} {...props} />
);

NoteIcon.displayName = 'NoteIcon';

export default NoteIcon;
