import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import { useRef } from 'react';

import { Button } from '@components/button';
import { Toast, type ToastRef } from '@components/toast';
import { toastPropsDefs } from '@components/toast/toast.props';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: 'DesignSystem/Toast',
  component: Toast,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
  },

  argTypes: {
    align: { control: 'inline-radio', options: toastPropsDefs.align.values },
    position: { control: 'inline-radio', options: toastPropsDefs.position.values },
    direction: { control: 'inline-radio', options: toastPropsDefs.direction.values },
    variant: { control: 'inline-radio', options: toastPropsDefs.variant.values },
    description: { control: 'text', type: 'string' },
    title: { control: 'text', type: 'string' },
    duration: { control: 'number', type: 'number' },
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  // tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
} satisfies Meta<typeof Toast>;

export default meta;
type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const _toast: Story = {
  args: {
    title: 'Toast Title',
    description: 'This is a description for the toast notification.',
    align: toastPropsDefs.align.default,
    variant: toastPropsDefs.variant.default,
    position: toastPropsDefs.position.default,
    direction: toastPropsDefs.direction.default,
  },
  render: (args) => {
    const toastRef = useRef<ToastRef>(null);
    return (
      <div>
        <Button onClick={() => toastRef.current?.show()}>Click Here</Button>
        <Toast ref={toastRef} {...args} />
      </div>
    );
  },
};
