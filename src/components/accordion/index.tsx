import type {
  AccordionMultipleProps,
  AccordionSingleProps,
  AccordionContentProps as BaseContentProps,
  AccordionHeaderProps as BaseHeaderProps,
  AccordionItemProps as BaseItemProps,
  AccordionTriggerProps as BaseTriggerProps,
} from '@radix-ui/react-accordion';
import clsx from 'clsx';
import { Accordion as BaseAccordion } from 'radix-ui';
import * as React from 'react';

import { accordionContentPropDefs, accordionRootPropDefs } from '@components/accordion/accordion.props';
import { type ComponentPropsWithout, type RemovedProps, extractProps, requireReactElement } from '@helpers';
import type { GetPropDefTypes } from '@props';

type AccordionRootElement = React.ComponentRef<typeof BaseAccordion.Root>;
type AccordionRootOwnProps = GetPropDefTypes<typeof accordionRootPropDefs>;
type AccordionRootProps =
  | (ComponentPropsWithout<typeof BaseAccordion.Root, RemovedProps> & AccordionSingleProps & AccordionRootOwnProps)
  | (ComponentPropsWithout<typeof BaseAccordion.Root, RemovedProps> & AccordionMultipleProps & AccordionRootOwnProps);
const AccordionRoot = React.forwardRef<AccordionRootElement, AccordionRootProps>((props, forwardedRef) => (
  <BaseAccordion.Root {...props} ref={forwardedRef} className={clsx('rt-AccordionRoot', props.className)} />
));
AccordionRoot.displayName = 'Accordion.Root';

type AccordionItemElement = React.ComponentRef<typeof BaseAccordion.Item>;
type AccordionItemProps = BaseItemProps;
const AccordionItem = React.forwardRef<AccordionItemElement, AccordionItemProps>((props, forwardedRef) => (
  <BaseAccordion.Item {...props} ref={forwardedRef} className={clsx('rt-AccordionItem', props.className)} />
));
AccordionItem.displayName = 'Accordion.Item';

type AccordionHeaderElement = React.ComponentRef<typeof BaseAccordion.Header>;
type AccordionHeaderProps = BaseHeaderProps;
const AccordionHeader = React.forwardRef<AccordionHeaderElement, AccordionHeaderProps>((props, forwardedRef) => (
  <BaseAccordion.Header
    {...props}
    ref={forwardedRef}
    className={clsx('rt-reset', 'rt-AccordionHeader', props.className)}
  />
));
AccordionHeader.displayName = 'Accordion.Header';

type AccordionTriggerElement = React.ComponentRef<typeof BaseAccordion.Trigger>;
type AccordionTriggerProps = BaseTriggerProps;
const AccordionTrigger = React.forwardRef<AccordionTriggerElement, AccordionTriggerProps>(
  ({ children, className, ...props }, forwardedRef) => (
    <BaseAccordion.Trigger ref={forwardedRef} asChild {...props} className={clsx('rt-AccordionTrigger', className)}>
      {requireReactElement(children)}
    </BaseAccordion.Trigger>
  ),
);
AccordionTrigger.displayName = 'Accordion.Trigger';

type AccordionContentElement = React.ComponentRef<typeof BaseAccordion.Content>;
type AccordionContentProps = BaseContentProps & GetPropDefTypes<typeof accordionContentPropDefs>;
const AccordionContent = React.forwardRef<AccordionContentElement, AccordionContentProps>((props, forwardedRef) => {
  const { className, children, ...contentProps } = extractProps(props, accordionContentPropDefs);

  return (
    <BaseAccordion.Content ref={forwardedRef} className={clsx('rt-AccordionContent', className)} {...contentProps}>
      {children}
    </BaseAccordion.Content>
  );
});
AccordionContent.displayName = 'Accordion.Content';

export {
  AccordionContent as Content,
  AccordionHeader as Header,
  AccordionItem as Item,
  AccordionRoot as Root,
  AccordionTrigger as Trigger,
};

export type {
  AccordionContentProps as ContentProps,
  AccordionHeaderProps as HeaderProps,
  AccordionItemProps as ItemProps,
  AccordionRootProps as RootProps,
  AccordionTriggerProps as TriggerProps,
};
