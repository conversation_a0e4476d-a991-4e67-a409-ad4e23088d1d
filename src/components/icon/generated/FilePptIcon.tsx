// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M192 874.667h64c16.974 0 33.253-6.741 45.255-18.743s18.745-28.284 18.745-45.257c0-16.973-6.743-33.254-18.745-45.257s-28.281-18.743-45.255-18.743h-64v128zM192 874.667v64M448 874.667h64c16.973 0 33.254-6.741 45.257-18.743s18.743-28.284 18.743-45.257c0-16.973-6.741-33.254-18.743-45.257s-28.284-18.743-45.257-18.743h-64v128zM448 874.667v64M832 746.667h-144M760 938.667v-192",
          "M613.491 122.824v0zM815.842 325.176v0zM184.618 155.223v0zM240.556 99.284v0zM841.835 362.667v0zM138.667 617.442c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM821.333 617.442c0 17.673 14.327 32 32 32s32-14.327 32-32h-64zM613.491 122.824l-22.63 22.627 202.355 202.353 45.252-45.255-202.351-202.353-22.626 22.627zM375.467 85.333v32h147.516v-64h-147.516v32zM170.667 290.133h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM375.467 85.333v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM184.618 155.223l28.512 14.528c9.204-18.063 23.89-32.75 41.953-41.953l-29.055-57.024c-30.106 15.34-54.583 39.817-69.923 69.923l28.512 14.528zM613.491 122.824l22.626-22.627c-13.679-13.681-29.585-24.629-46.861-32.492l-26.513 58.252c10.368 4.718 19.908 11.285 28.117 19.495l22.63-22.627zM576 96.83l13.257-29.126c-20.617-9.381-43.191-14.371-66.274-14.371v64c13.85 0 27.392 2.993 39.761 8.623l13.257-29.126zM576 96.83h-32v137.836h64v-137.836h-32zM853.333 415.686h32c0-23.087-4.992-45.66-14.37-66.274l-58.253 26.509c5.632 12.372 8.623 25.913 8.623 39.765h32zM841.835 362.667l29.129-13.254c-7.863-17.277-18.812-33.183-32.495-46.863l-45.252 45.255c8.209 8.209 14.775 17.75 19.494 28.117l29.124-13.254zM704 362.667v32h137.835v-64h-137.835v32zM576 234.667h-32c0 88.366 71.633 160 160 160v-64c-53.018 0-96-42.981-96-96h-32zM170.667 617.442h32v-327.309h-64v327.309h32zM853.333 415.686h-32v201.756h64v-201.756h-32z"
        ],
        "attrs": [
          {
            "fill": "none",
            "stroke": "rgb(0, 0, 0)",
            "strokeLinejoin": "round",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": true,
        "grid": 0,
        "tags": [
          "file-ppt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "stroke": "rgb(0, 0, 0)",
          "strokeLinejoin": "round",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {}
      ],
      "properties": {
        "order": 1640,
        "id": 380,
        "name": "file-ppt",
        "prevSize": 32,
        "code": 59916
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 269
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FilePptIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FilePptIcon = ({ size = 16, ...props }: FilePptIconProps) => (
  <IcoMoon iconSet={iconSet} icon="file-ppt" size={size} {...props} />
);

FilePptIcon.displayName = 'FilePptIcon';

export default FilePptIcon;
