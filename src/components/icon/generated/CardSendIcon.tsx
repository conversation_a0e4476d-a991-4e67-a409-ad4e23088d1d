// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M682.667 255.999l121.967-121.966c1.664-1.666 3.849-2.499 6.033-2.499M938.667 255.999l-121.967-121.966c-1.664-1.666-3.849-2.499-6.033-2.499M810.667 131.534v359.133M85.333 384v341.333c0 70.694 57.308 128 128 128h597.333c70.694 0 128-57.306 128-128v-298.667M85.333 384v-85.333c0-70.692 57.308-128 128-128h362.667M85.333 384h554.667M234.667 704h106.667M469.333 704h213.333"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "card-send"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1526,
        "id": 494,
        "name": "card-send",
        "prevSize": 32,
        "code": 59802
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 155
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface CardSendIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const CardSendIcon = ({ size = 16, ...props }: CardSendIconProps) => (
  <IcoMoon iconSet={iconSet} icon="card-send" size={size} {...props} />
);

CardSendIcon.displayName = 'CardSendIcon';

export default CardSendIcon;
