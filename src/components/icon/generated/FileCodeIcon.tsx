// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M339.135 633.967v0zM441.062 577.293c12.497-12.497 12.497-32.755 0-45.252s-32.759-12.497-45.256 0l45.256 45.252zM339.135 646.033v0zM395.807 747.959c12.497 12.497 32.759 12.497 45.256 0s12.497-32.755 0-45.252l-45.256 45.252zM680.683 624.913v0zM633.062 532.041c-12.497-12.497-32.759-12.497-45.257 0s-12.497 32.755 0 45.252l45.257-45.252zM587.806 702.707c-12.497 12.497-12.497 32.755 0 45.252s32.759 12.497 45.257 0l-45.257-45.252zM613.491 122.824v0zM815.842 325.176v0zM240.556 924.715v0zM184.618 868.779v0zM783.445 924.715v0zM839.381 868.779v0zM184.618 155.223v0zM240.556 99.284v0zM841.835 362.667v0zM339.135 633.967l22.627 22.626 79.3-79.3-45.256-45.252-79.299 79.296 22.627 22.63zM339.135 646.033l-22.627 22.63 79.299 79.296 45.256-45.252-79.3-79.3-22.627 22.626zM339.135 633.967l-22.627-22.63c-15.829 15.829-15.829 41.498 0 57.327l45.255-45.257c9.164 9.165 9.164 24.021 0 33.186l-22.627-22.626zM680.683 624.913l22.626-22.626-70.246-70.246-45.257 45.252 70.251 70.251 22.626-22.63zM680.683 655.087l-22.626-22.63-70.251 70.251 45.257 45.252 70.246-70.246-22.626-22.626zM680.683 624.913l-22.626 22.63c-4.169-4.169-4.169-10.918 0-15.087l45.252 45.257c20.83-20.83 20.83-54.596 0-75.426l-22.626 22.626zM648.533 938.667v-32h-273.067v64h273.067v-32zM170.667 733.867h32v-443.733h-64v443.733h32zM853.333 415.686h-32v318.181h64v-318.181h-32zM613.491 122.824l-22.63 22.627 202.355 202.353 45.252-45.255-202.351-202.353-22.626 22.627zM375.467 85.333v32h147.516v-64h-147.516v32zM375.467 938.667v-32c-36.371 0-61.725-0.026-81.464-1.638-19.366-1.583-30.491-4.531-38.919-8.823l-29.055 57.024c18.953 9.655 39.439 13.683 62.762 15.586 22.95 1.877 51.361 1.852 86.676 1.852v-32zM170.667 733.867h-32c0 35.315-0.025 63.727 1.85 86.677 1.906 23.322 5.932 43.806 15.589 62.763l57.024-29.056c-4.294-8.427-7.244-19.554-8.826-38.921-1.613-19.738-1.638-45.090-1.638-81.463h-32zM240.556 924.715l14.528-28.51c-18.063-9.207-32.75-23.893-41.953-41.954l-57.024 29.056c15.34 30.106 39.817 54.579 69.923 69.922l14.528-28.514zM648.533 938.667v32c35.315 0 63.727 0.026 86.677-1.852 23.322-1.903 43.806-5.931 62.763-15.586l-29.056-57.024c-8.427 4.292-19.554 7.241-38.921 8.823-19.738 1.613-45.090 1.638-81.463 1.638v32zM853.333 733.867h-32c0 36.373-0.026 61.726-1.638 81.463-1.583 19.366-4.531 30.494-8.823 38.921l57.024 29.056c9.655-18.957 13.683-39.441 15.586-62.763 1.877-22.95 1.852-51.362 1.852-86.677h-32zM783.445 924.715l14.528 28.514c30.106-15.343 54.579-39.817 69.922-69.922l-57.024-29.056c-9.203 18.061-23.893 32.747-41.954 41.954l14.528 28.51zM170.667 290.133h32c0-36.371 0.025-61.725 1.638-81.464 1.582-19.366 4.532-30.491 8.826-38.919l-57.024-29.055c-9.658 18.953-13.683 39.439-15.589 62.762-1.875 22.95-1.85 51.361-1.85 86.676h32zM375.467 85.333v-32c-35.315 0-63.727-0.025-86.676 1.85-23.323 1.906-43.809 5.932-62.762 15.589l29.055 57.024c8.428-4.294 19.553-7.244 38.919-8.826 19.739-1.613 45.093-1.638 81.464-1.638v-32zM184.618 155.223l28.512 14.528c9.204-18.063 23.89-32.75 41.953-41.953l-29.055-57.024c-30.106 15.34-54.583 39.817-69.923 69.923l28.512 14.528zM613.491 122.824l22.626-22.627c-13.679-13.681-29.585-24.629-46.861-32.492l-26.513 58.252c10.368 4.718 19.908 11.285 28.117 19.495l22.63-22.627zM576 96.83l13.257-29.126c-20.617-9.381-43.191-14.371-66.274-14.371v64c13.85 0 27.392 2.993 39.761 8.623l13.257-29.126zM576 96.83h-32v137.836h64v-137.836h-32zM853.333 415.686h32c0-23.087-4.992-45.66-14.37-66.274l-58.253 26.509c5.632 12.372 8.623 25.913 8.623 39.765h32zM841.835 362.667l29.129-13.254c-7.863-17.277-18.812-33.183-32.495-46.863l-45.252 45.255c8.209 8.209 14.775 17.75 19.494 28.117l29.124-13.254zM704 362.667v32h137.835v-64h-137.835v32zM576 234.667h-32c0 88.366 71.633 160 160 160v-64c-53.018 0-96-42.981-96-96h-32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "file-code"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1629,
        "id": 391,
        "name": "file-code",
        "prevSize": 32,
        "code": 59905
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 258
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface FileCodeIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const FileCodeIcon = ({ size = 16, ...props }: FileCodeIconProps) => (
  <IcoMoon iconSet={iconSet} icon="file-code" size={size} {...props} />
);

FileCodeIcon.displayName = 'FileCodeIcon';

export default FileCodeIcon;
