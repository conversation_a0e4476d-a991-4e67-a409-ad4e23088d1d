.rt-BaseTabList {
  display: flex;
  justify-content: flex-start;
  overflow-x: auto;
  white-spatial: nowrap;

  font-family: var(--default-font-family);
  font-style: normal;
  line-height: var(--line-height-2, 14px);
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.rt-BaseTabListTrigger {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
  user-select: none;
}

.rt-BaseTabListTriggerInner,
.rt-BaseTabListTriggerInnerHidden {
  display: flex;
  align-items: center;
  justify-content: center;
}

.rt-BaseTabListTriggerInner {
  :where(.rt-BaseTabListTrigger[data-state='inactive'], .rt-TabNavLink:not([data-active])) & {
    color: var(--labels-secondary);
  }
}

/***************************************************************************************************
 *                                                                                                 *
 * SIZES                                                                                           *
 *                                                                                                 *
 ***************************************************************************************************/

.rt-BaseTabListTrigger {
  box-sizing: border-box;
  height: var(--tab-height);
  padding: var(--tab-padding-top) var(--tab-padding-right) var(--tab-padding-bottom) var(--tab-padding-left);
}

.rt-BaseTabListTriggerInner,
.rt-BaseTabListTriggerInnerHidden {
  box-sizing: border-box;
  padding: var(--tab-inner-padding-y) var(--tab-inner-padding-x);
  gap: var(--tab-gap);
}

@breakpoints {
  .rt-BaseTabList {
    &:where(.rt-r-size-1) {
      font-size: var(--font-size-2);
      line-height: var(--line-height-2);
      --tab-gap: var(--spatial-2);
      --tab-height: var(--spatial-7);
      --tab-padding-top: var(--spatial-1);
      --tab-padding-right: var(--spatial-1);
      --tab-padding-bottom: var(--spatial-2);
      --tab-padding-left: var(--spatial-1);
      --tab-inner-padding-x: var(--spatial-1);
      --tab-inner-padding-y: calc(var(--spatial-1) * 0.5);
      --tab-icon: var(--icon-size-1);
    }

    &:where(.rt-r-size-2) {
      font-size: var(--font-size-3);
      line-height: var(--line-height-3);
      --tab-gap: var(--spatial-3);
      --tab-height: var(--spatial-8);
      --tab-padding-top: var(--spatial-2);
      --tab-padding-right: var(--spatial-1);
      --tab-padding-bottom: var(--spatial-3);
      --tab-padding-left: var(--spatial-1);
      --tab-inner-padding-x: var(--spatial-2);
      --tab-inner-padding-y: var(--spatial-1);
      --tab-icon: var(--icon-size-2);
    }

    &:where(.rt-r-size-3) {
      font-size: var(--font-size-5);
      line-height: var(--line-height-5);
      --tab-gap: var(--spatial-3);
      --tab-height: var(--spatial-9);
      --tab-padding-top: var(--spatial-2);
      --tab-padding-right: var(--spatial-1);
      --tab-padding-bottom: var(--spatial-3);
      --tab-padding-left: var(--spatial-1);
      --tab-inner-padding-x: var(--spatial-2);
      --tab-inner-padding-y: var(--spatial-1);
      --tab-icon: var(--icon-size-3);
    }

    &:where(.rt-r-size-4) {
      font-size: var(--font-size-6);
      line-height: var(--line-height-6);
      --tab-gap: var(--spatial-3);
      --tab-height: var(--spatial-10);
      --tab-padding-top: var(--spatial-3);
      --tab-padding-right: var(--spatial-1);
      --tab-padding-bottom: var(--spatial-3);
      --tab-padding-left: var(--spatial-1);
      --tab-inner-padding-x: var(--spatial-3);
      --tab-inner-padding-y: var(--spatial-1);
      --tab-icon: var(--icon-size-4);
    }

    &:where(.rt-r-size-5) {
      font-size: var(--font-size-6);
      line-height: var(--line-height-6);
      --tab-gap: var(--spatial-4);
      --tab-height: var(--spatial-11);
      --tab-padding-top: var(--spatial-3);
      --tab-padding-right: var(--spatial-1);
      --tab-padding-bottom: var(--spatial-5);
      --tab-padding-left: var(--spatial-1);
      --tab-inner-padding-x: var(--spatial-4);
      --tab-inner-padding-y: var(--spatial-1);
      --tab-icon: var(--icon-size-5);
    }
  }
}

/***************************************************************************************************
 *                                                                                                 *
 * VARIANTS                                                                                        *
 *                                                                                                 *
 ***************************************************************************************************/

.rt-BaseTabListTrigger {
  color: var(--labels-primary);

  @media (hover: hover) {
    &:where(:hover) {
      background-color: var(--fills-quaternary);
    }

    &:where(:focus-visible:hover) {
      background-color: var(--fills-quaternary);
    }
  }

  &:where([data-state='active'], [data-active])::before {
    box-sizing: border-box;
    content: '';
    height: 2px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--accent-indicator);
  }

  :where(.rt-BaseTabList.rt-high-contrast) & {
    &:where([data-state='active'], [data-active])::before {
      background-color: var(--accent-12);
    }
  }
}

.rt-TabsContent {
  position: relative;
  outline: 0;
}

.rt-TabsContent:where(:focus-visible) {
  outline: 2px solid var(--focus-8);
}

.rt-TabsRoot:where([data-orientation='vertical']) {
  display: flex;
  gap: var(--spatial-2);

  > .rt-TabsList {
    flex-direction: column;
    gap: var(--spatial-2);
  }
}

.rt-BaseTabListTrigger:where([data-orientation='vertical']):where([data-state='active']) {
  font-weight: var(--font-weight-semibold);
  border-right: var(--border-width-thin, 1px) solid var(--brand-accent, #20cb73);
  border-bottom: none;
}

.rt-BaseTabListTrigger:where([data-orientation='horizontal']):where([data-state='active'], [data-active]) {
  font-weight: var(--font-weight-semibold);
  border-bottom: var(--border-width-thin, 1px) solid var(--brand-accent, #20cb73);
}

.rt-BaseTabListTriggerInner {
  > svg {
    width: var(--tab-icon) !important;
    height: var(--tab-icon) !important;
  }
}

.rt-TabNavItem {
  display: flex;
}

.rt-TabNavList:where([data-orientation='vertical']) {
  display: flex;
  gap: var(--spatial-2);
  flex-direction: column;

  .rt-TabNavLink:where([data-active]) {
    font-weight: var(--font-weight-semibold);
    border-right: var(--border-width-thin, 1px) solid var(--brand-accent, #20cb73);
    border-bottom: none;
  }
}

.rt-TabNavList:where([data-orientation='horizontal']) {
  .rt-TabNavLink:where([data-active]) {
    font-weight: var(--font-weight-semibold);
    border-bottom: var(--border-width-thin, 1px) solid var(--brand-accent, #20cb73);
  }
}
