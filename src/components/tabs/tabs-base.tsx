import clsx from 'clsx';
import { Tabs as TabsPrimitive } from 'radix-ui';
import React from 'react';

import { type ComponentPropsWithout, type RemovedProps, extractProps } from '@helpers';
import { type GetPropDefTypes, type MarginProps, marginPropDefs } from '@props';

import { tabsContentPropDefs, tabsListPropDefs, tabsRootPropDefs } from './tabs.props.js';

type TabsRootElement = React.ComponentRef<typeof TabsPrimitive.Root>;
type TabsRootOwnProps = GetPropDefTypes<typeof tabsRootPropDefs>;
interface TabsRootProps
  extends ComponentPropsWithout<typeof TabsPrimitive.Root, 'asChild' | 'color' | 'defaultChecked'>,
    MarginProps,
    TabsRootOwnProps {}
const TabsRoot = React.forwardRef<TabsRootElement, TabsRootProps>((props, forwardedRef) => {
  const { className, ...rootProps } = extractProps(props, marginPropDefs);
  return <TabsPrimitive.Root {...rootProps} ref={forwardedRef} className={clsx('rt-TabsRoot', className)} />;
});
TabsRoot.displayName = 'Tabs.Root';

type TabsListElement = React.ComponentRef<typeof TabsPrimitive.List>;
type TabsListOwnProps = GetPropDefTypes<typeof tabsListPropDefs>;
interface TabsListProps
  extends ComponentPropsWithout<typeof TabsPrimitive.List, RemovedProps>,
    MarginProps,
    TabsListOwnProps {}

const TabsList = React.forwardRef<TabsListElement, TabsListProps>((props, forwardedRef) => {
  const { className, color, ...listProps } = extractProps(props, tabsListPropDefs, marginPropDefs);
  return (
    <TabsPrimitive.List
      data-accent-color={color}
      {...listProps}
      asChild={false}
      ref={forwardedRef}
      className={clsx('rt-BaseTabList', 'rt-TabsList', className)}
    />
  );
});
TabsList.displayName = 'Tabs.List';

type TabsTriggerElement = React.ComponentRef<typeof TabsPrimitive.Trigger>;
interface TabsTriggerProps extends ComponentPropsWithout<typeof TabsPrimitive.Trigger, RemovedProps> {}
const TabsTrigger = React.forwardRef<TabsTriggerElement, TabsTriggerProps>((props, forwardedRef) => {
  const { className, children, ...triggerProps } = props;
  return (
    <TabsPrimitive.Trigger
      {...triggerProps}
      asChild={false}
      ref={forwardedRef}
      className={clsx('rt-reset', 'rt-BaseTabListTrigger', 'rt-TabsTrigger', className)}
    >
      <span className="rt-BaseTabListTriggerInner rt-TabsTriggerInner">{children}</span>
    </TabsPrimitive.Trigger>
  );
});
TabsTrigger.displayName = 'Tabs.Trigger';

type TabNavLinkProps = React.ComponentProps<'a'>;
const TabsNavLink: React.FC<TabNavLinkProps> = (props) => {
  const { className, children, ...navProps } = props;
  return (
    <a {...navProps} className={clsx('rt-reset', 'rt-BaseTabListTrigger', 'rt-TabsNavLink', className)}>
      <span className="rt-BaseTabListTriggerInner rt-TabsNavLinkInner">{children}</span>
    </a>
  );
};
TabsTrigger.displayName = 'Tabs.NavLink';

type TabsContentElement = React.ComponentRef<typeof TabsPrimitive.Content>;
type TabsContentOwnProps = GetPropDefTypes<typeof tabsContentPropDefs>;
interface TabsContentProps
  extends ComponentPropsWithout<typeof TabsPrimitive.Content, RemovedProps>,
    MarginProps,
    TabsContentOwnProps {}
const TabsContent = React.forwardRef<TabsContentElement, TabsContentProps>((props, forwardedRef) => {
  const { className, ...contentProps } = extractProps(props, marginPropDefs);
  return <TabsPrimitive.Content {...contentProps} ref={forwardedRef} className={clsx('rt-TabsContent', className)} />;
});
TabsContent.displayName = 'Tabs.Content';

export { TabsContent as Content, TabsList as List, TabsNavLink as NavLink, TabsRoot as Root, TabsTrigger as Trigger };
export type {
  TabsContentProps as ContentProps,
  TabsListProps as ListProps,
  TabNavLinkProps as NavLinkProps,
  TabsRootProps as RootProps,
  TabsTriggerProps as TriggerProps,
};
