// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M320 491.499h71.736c1.766 0 3.348-1.092 3.982-2.748l47.277-123.34c1.51-3.941 7.194-3.54 8.141 0.574l57.348 249.361c0.977 4.25 6.916 4.484 8.226 0.329l56.939-180.932c1.015-3.226 5.188-4.049 7.347-1.451l47.057 56.661c0.811 0.977 2.014 1.545 3.277 1.545h72.67M85.333 357.142c0-126.551 105.064-229.142 234.667-229.142 73.929 0 139.874 33.381 182.886 85.55 4.629 5.613 13.598 5.613 18.227 0 43.012-52.169 108.958-85.55 182.886-85.55 129.604 0 234.667 102.59 234.667 229.142 0 193.527-367.121 494.045-420.279 536.546-3.857 3.085-8.917 3.085-12.774 0-53.158-42.5-420.279-343.019-420.279-536.546z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "heart-beat"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1704,
        "id": 316,
        "name": "heart-beat",
        "prevSize": 32,
        "code": 59980
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 333
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface HeartBeatIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const HeartBeatIcon = ({ size = 16, ...props }: HeartBeatIconProps) => (
  <IcoMoon iconSet={iconSet} icon="heart-beat" size={size} {...props} />
);

HeartBeatIcon.displayName = 'HeartBeatIcon';

export default HeartBeatIcon;
