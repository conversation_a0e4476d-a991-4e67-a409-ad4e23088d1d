// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M450.415 101.925c38.11-22.122 85.060-22.122 123.17 0l260.83 151.413c38.11 22.122 61.585 63.005 61.585 107.249v302.825c0 44.245-23.475 85.129-61.585 107.251l-260.83 151.411c-38.11 22.123-85.060 22.123-123.17 0l-260.831-151.411c-38.109-22.123-61.584-63.006-61.584-107.251v-302.825c0-44.244 23.476-85.127 61.584-107.249l260.831-151.413z",
          "M656 512c0 78.545-64.469 142.221-144 142.221s-144-63.676-144-142.221c0-78.545 64.469-142.222 144-142.222s144 63.677 144 142.222z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "settings-01"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1877,
        "id": 143,
        "name": "settings-01",
        "prevSize": 32,
        "code": 60153
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 506
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface Settings01IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const Settings01Icon = ({ size = 16, ...props }: Settings01IconProps) => (
  <IcoMoon iconSet={iconSet} icon="settings-01" size={size} {...props} />
);

Settings01Icon.displayName = 'Settings01Icon';

export default Settings01Icon;
