// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M333.401 650.667c26.463 23.966 90.579 72.107 178.595 72.107 88.021 0 152.137-48.141 178.598-72.107M320 341.333l53.333 53.333M373.333 394.667l53.333 53.333M373.333 394.667l53.333-53.333M373.333 394.667l-53.333 53.333M597.333 341.333l53.333 53.333M650.667 394.667l53.333 53.333M650.667 394.667l53.333-53.333M650.667 394.667l-53.333 53.333M938.667 512c0 235.639-191.027 426.667-426.667 426.667-235.642 0-426.667-191.027-426.667-426.667 0-235.642 191.025-426.667 426.667-426.667 235.639 0 426.667 191.025 426.667 426.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "smile-dizzy"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1900,
        "id": 120,
        "name": "smile-dizzy",
        "prevSize": 32,
        "code": 60176
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 529
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SmileDizzyIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SmileDizzyIcon = ({ size = 16, ...props }: SmileDizzyIconProps) => (
  <IcoMoon iconSet={iconSet} icon="smile-dizzy" size={size} {...props} />
);

SmileDizzyIcon.displayName = 'SmileDizzyIcon';

export default SmileDizzyIcon;
