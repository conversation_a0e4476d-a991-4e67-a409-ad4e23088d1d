// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M556.262 357.932c23.817 10.092 46.127 24.845 65.545 44.261 80.858 80.857 80.858 211.959 0 292.817l-183.010 183.010c-80.861 80.862-211.959 80.862-292.819 0-80.86-80.858-80.86-211.955 0-292.817l73.204-73.203M467.738 666.069c-23.817-10.095-46.129-24.849-65.544-44.262-80.859-80.858-80.859-211.959 0-292.818l183.010-183.011c80.862-80.86 211.959-80.86 292.817 0 80.862 80.859 80.862 211.958 0 292.819l-73.203 73.203"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "link-alt-02"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1732,
        "id": 288,
        "name": "link-alt-02",
        "prevSize": 32,
        "code": 60008
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 361
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface LinkAlt-02IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const LinkAlt-02Icon = ({ size = 16, ...props }: LinkAlt-02IconProps) => (
  <IcoMoon iconSet={iconSet} icon="link-alt-02" size={size} {...props} />
);

LinkAlt-02Icon.displayName = 'LinkAlt-02Icon';

export default LinkAlt-02Icon;
