// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M768 149.333h21.333M789.333 149.333h21.333M789.333 149.333v128.315M512 746.667v42.667c0 47.13 38.204 85.333 85.333 85.333h106.667c47.13 0 85.333-38.204 85.333-85.333v-136.789M512 746.667h-42.667M512 746.667h42.667M789.333 277.648c-3.524-0.209-7.083-0.315-10.667-0.315h-533.333c-88.366 0-160 64.471-160 144v48c0 106.039 95.513 192 213.333 192 65.683 0 124.433-37.295 163.567-70.302 26.658-22.485 72.875-22.485 99.533 0 39.134 33.007 97.886 70.302 163.567 70.302 22.298 0 43.797-3.081 64-8.789M789.333 277.648c83.392 4.94 149.333 67.382 149.333 143.685v48c0 85.969-62.78 158.746-149.333 183.211"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "diving-mask"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1601,
        "id": 419,
        "name": "diving-mask",
        "prevSize": 32,
        "code": 59877
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 230
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface DivingMaskIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const DivingMaskIcon = ({ size = 16, ...props }: DivingMaskIconProps) => (
  <IcoMoon iconSet={iconSet} icon="diving-mask" size={size} {...props} />
);

DivingMaskIcon.displayName = 'DivingMaskIcon';

export default DivingMaskIcon;
