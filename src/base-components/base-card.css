.rt-BaseCard {
  display: block;
  position: relative;
  overflow: hidden;
  border-radius: var(--base-card-border-radius);

  /* Don't inherit typographic properties */
  font-family: var(--default-font-family);
  font-weight: var(--font-weight-regular);
  font-style: normal;
  text-align: start;

  --inset-border-width: var(--base-card-border-width);
  --inset-border-radius: var(--base-card-border-radius);
  padding-top: var(--base-card-padding-top);
  padding-right: var(--base-card-padding-right);
  padding-bottom: var(--base-card-padding-bottom);
  padding-left: var(--base-card-padding-left);
  box-sizing: border-box;

  /*
   * Some layout acrobatics with `var(--base-card-border-width)` because we want:
   * 1. <Card> with fixed height to clip overflowing content.
   * 2. <Inset> that clips to card’s border-box or padding-box depending on the `clip` value.
   *
   * To have both (1) and (2), we clip the content at the outer edge of `.rt-BaseCard` border, and use
   * a ::before pseudo-element for the background color, which is smaller by the border width on each side.
   */
  --inset-padding-top: calc(var(--base-card-padding-top) - var(--base-card-border-width));
  --inset-padding-right: calc(var(--base-card-padding-right) - var(--base-card-border-width));
  --inset-padding-bottom: calc(var(--base-card-padding-bottom) - var(--base-card-border-width));
  --inset-padding-left: calc(var(--base-card-padding-left) - var(--base-card-border-width));

  /*
   * ::before is used for the background color.
   * ::after is used for the inner border that goes on top the children.
   */
  &::before,
  &::after {
    content: '';
    position: absolute;
    pointer-events: none;
    transition: inherit;
    border-radius: calc(var(--base-card-border-radius) - var(--base-card-border-width));
    inset: var(--base-card-border-width);
  }

  /*
   * Background color:
   * 1. "z-index: -1" so that the background goes below the children
   * 2. "contain: paint" creates a new stacking context so that ::before doesn’t go below the card’s sibling elements
   */
  &::before {
    z-index: -1;
  }
  & {
    contain: paint;
  }
}

/* * * * * * * * * * * * * * * * * * * */
/*                                     */
/*          Shadows / Surface          */
/*                                     */
/* * * * * * * * * * * * * * * * * * * */

:where(.herond-themes) {
  --base-card-surface-box-shadow: 0 0 0 1px color-mix(in oklab, var(--brand-gray500), var(--brand-gray500) 25%);
  --base-card-surface-hover-box-shadow: 00 0 0 1px color-mix(in oklab, var(--brand-gray700), var(--brand-gray700) 25%);
  --base-card-surface-active-box-shadow: 0 0 0 1px color-mix(in oklab, var(--brand-gray600), var(--brand-gray600) 25%);

  @supports (color: color-mix(in oklab, white, black)) {
    --base-card-surface-box-shadow: 0 0 0 1px color-mix(in oklab, var(--brand-gray500), var(--brand-gray500) 25%);
    --base-card-surface-hover-box-shadow: 0 0 0 1px color-mix(in oklab, var(--brand-gray700), var(--brand-gray700) 25%);
    --base-card-surface-active-box-shadow: 0 0 0 1px color-mix(in oklab, var(--brand-gray600), var(--brand-gray600) 25%);
  }
}

/* * * * * * * * * * * * * * * * * * * */
/*                                     */
/*          Shadows / Classic          */
/*                                     */
/* * * * * * * * * * * * * * * * * * * */

/*
 * 1. Outer shadow has to be the same as inner shadow, but with a 1px smaller spread value.
 * 2. Keep the initial state in sync with var(--shadow-2) in `shadow.css`.
 * 3. Make sure that between all states, the length of the shadow list matches:
 *    https://developer.mozilla.org/en-US/docs/Web/CSS/box-shadow#interpolation
 */

/* prettier-ignore */
:where(.herond-themes) {
  --base-card-classic-box-shadow-inner:
    0 0 0 1px var(--base-card-classic-border-color),
    0 0 0 1px var(--color-transparent),
    0 0 0 0.5px rgba(var(--grays-black-a),0.1),
    0 1px 1px 0 var(--brand-gray200),
    0 2px 1px -1px rgba(var(--grays-black-a),0.1),
    0 1px 3px 0 rgba(var(--grays-black-a),0.1);
  --base-card-classic-box-shadow-outer:
    0 0 0 0 var(--base-card-classic-border-color),
    0 0 0 0 var(--color-transparent),
    0 0 0 0 rgba(var(--grays-black-a),0.1),
    0 1px 1px -1px var(--brand-gray200),
    0 2px 1px -2px rgba(var(--grays-black-a),0.1),
    0 1px 3px -1px rgba(var(--grays-black-a),0.1);

  --base-card-classic-hover-box-shadow-inner:
    0 0 0 1px var(--base-card-classic-hover-border-color),
    0 1px 1px 1px rgba(var(--grays-black-a),0.1),
    0 2px 1px -1px var(--brand-gray300),
    0 2px 3px -2px rgba(var(--grays-black-a),0.1),
    0 3px 12px -4px var(--brand-gray300),
    0 4px 16px -8px rgba(var(--grays-black-a),0.1);
  --base-card-classic-hover-box-shadow-outer:
    0 0 0 0 var(--base-card-classic-hover-border-color),
    0 1px 1px 0 rgba(var(--grays-black-a),0.1),
    0 2px 1px -2px var(--brand-gray300),
    0 2px 3px -3px rgba(var(--grays-black-a),0.1),
    0 3px 12px -5px var(--brand-gray300),
    0 4px 16px -9px rgba(var(--grays-black-a),0.1);

  --base-card-classic-active-box-shadow-inner:
    0 0 0 1px var(--base-card-classic-active-border-color),
    0 0 0 1px var(--color-transparent),
    0 0 0 0.5px rgba(var(--grays-black-a),0.1),
    0 1px 1px 0 var(--brand-gray400),
    0 2px 1px -1px rgba(var(--grays-black-a),0.1),
    0 1px 3px 0 rgba(var(--grays-black-a),0.1);
  --base-card-classic-active-box-shadow-outer:
    0 0 0 0 var(--base-card-classic-active-border-color),
    0 0 0 0 var(--color-transparent),
    0 0 0 0 rgba(var(--grays-black-a),0.1),
    0 1px 1px -1px var(--brand-gray400),
    0 2px 1px -2px rgba(var(--grays-black-a),0.1),
    0 1px 3px -1px rgba(var(--grays-black-a),0.1);

  --base-card-classic-border-color: var(--brand-gray300);
  --base-card-classic-hover-border-color: var(--brand-gray300);
  --base-card-classic-active-border-color: var(--brand-gray400);
  @supports (color: color-mix(in oklab, white, black)) {
    --base-card-classic-border-color: color-mix(in oklab, var(--brand-gray300), var(--brand-gray300) 25%);
    --base-card-classic-hover-border-color: color-mix(in oklab, var(--brand-gray400), var(--brand-gray400) 25%);
    --base-card-classic-active-border-color: color-mix(in oklab, var(--brand-gray300), var(--brand-gray300) 25%);
  }
}

/* prettier-ignore */
:is(.dark, .dark-theme),
:is(.dark, .dark-theme) :where(.herond-themes:not(.light, .light-theme)) {
  --base-card-classic-box-shadow-inner:
    0 0 0 1px var(--base-card-classic-border-color),
    0 0 0 1px var(--color-transparent),
    0 0 0 0.5px var(rgba(var(--grays-black-a),0.3)),
    0 1px 1px 0 var(rgba(var(--grays-black-a),0.6)),
    0 2px 1px -1px var(rgba(var(--grays-black-a),0.6)),
    0 1px 3px 0 var(rgba(var(--grays-black-a),0.5));
  --base-card-classic-box-shadow-outer:
    0 0 0 0 var(--base-card-classic-border-color),
    0 0 0 0 var(--color-transparent),
    0 0 0 0 var(rgba(var(--grays-black-a),0.3)),
    0 1px 1px -1px var(rgba(var(--grays-black-a),0.6)),
    0 2px 1px -2px var(rgba(var(--grays-black-a),0.6)),
    0 1px 3px -1px var(rgba(var(--grays-black-a),0.5));

  --base-card-classic-hover-box-shadow-inner:
    0 0 0 1px var(--base-card-classic-hover-border-color),
    0 0 1px 1px var(--brand-gray400),
    0 0 1px -1px var(--brand-gray400),
    0 0 3px -2px var(--brand-gray300),
    0 0 12px -2px var(--brand-gray300),
    0 0 16px -8px var(--brand-gray700);
  --base-card-classic-hover-box-shadow-outer:
    0 0 0 0 var(--base-card-classic-hover-border-color),
    0 0 1px 0 var(--brand-gray400),
    0 0 1px -2px var(--brand-gray400),
    0 0 3px -3px var(--brand-gray300),
    0 0 12px -3px var(--brand-gray300),
    0 0 16px -9px var(--brand-gray700);

  --base-card-classic-active-box-shadow-inner:
    0 0 0 1px var(--base-card-classic-active-border-color),
    0 0 0 1px var(--color-transparent),
    0 0 0 0.5px var(rgba(var(--grays-black-a),0.3)),
    0 1px 1px 0 var(rgba(var(--grays-black-a),0.6)),
    0 2px 1px -1px var(rgba(var(--grays-black-a),0.6)),
    0 1px 3px 0 var(rgba(var(--grays-black-a),0.5));
  --base-card-classic-active-box-shadow-outer:
    0 0 0 0 var(--base-card-classic-active-border-color),
    0 0 0 0 var(--color-transparent),
    0 0 0 0 var(rgba(var(--grays-black-a),0.3)),
    0 1px 1px -1px var(rgba(var(--grays-black-a),0.6)),
    0 2px 1px -2px var(rgba(var(--grays-black-a),0.6)),
    0 1px 3px -1px var(rgba(var(--grays-black-a),0.5));

  --base-card-classic-border-color: var(--brand-gray600);
  --base-card-classic-hover-border-color: var(--brand-gray600);
  --base-card-classic-active-border-color: var(--brand-gray600);
  @supports (color: color-mix(in oklab, white, black)) {
    --base-card-classic-border-color: color-mix(in oklab, var(--brand-gray600), var(--brand-gray600) 25%);
    --base-card-classic-hover-border-color: color-mix(in oklab, var(--brand-gray600), var(--brand-gray600) 25%);
    --base-card-classic-active-border-color: color-mix(in oklab, var(--brand-gray600), var(--brand-gray600) 25%);
  }
}
