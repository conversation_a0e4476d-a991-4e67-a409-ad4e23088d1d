// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 85.333h-221.867c-71.687 0-107.53 0-134.911 13.951-24.085 12.272-43.666 31.853-55.938 55.938-13.951 27.38-13.951 63.224-13.951 134.911v443.733c0 71.689 0 107.529 13.951 134.912 12.272 24.085 31.853 43.665 55.938 55.936 27.38 13.952 63.224 13.952 134.911 13.952h443.733c71.689 0 107.529 0 134.912-13.952 24.085-12.271 43.665-31.851 55.936-55.936 13.952-27.383 13.952-63.223 13.952-134.912v-221.867",
          "M731.396 120.896c47.415-47.416 124.292-47.416 171.708 0s47.415 124.293 0 171.709l-331.443 331.446c-24.866 24.862-57.395 40.602-92.322 44.672l-118.65 13.824c-11.116 1.297-20.531-8.119-19.236-19.234l13.824-118.652c4.069-34.927 19.81-67.456 44.674-92.322l331.446-331.444z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "edit-square"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1615,
        "id": 405,
        "name": "edit-square",
        "prevSize": 32,
        "code": 59891
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 244
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface EditSquareIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const EditSquareIcon = ({ size = 16, ...props }: EditSquareIconProps) => (
  <IcoMoon iconSet={iconSet} icon="edit-square" size={size} {...props} />
);

EditSquareIcon.displayName = 'EditSquareIcon';

export default EditSquareIcon;
