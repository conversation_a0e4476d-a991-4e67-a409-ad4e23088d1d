'use client';

import clsx from 'clsx';
import * as React from 'react';

import { Flex } from '@components/flex';
import { type ComponentPropsWithout, type RemovedProps, extractProps } from '@helpers';
import { type GetPropDefTypes } from '@props';

import { dotsPropDefs } from './dots.props';

type DotsElement = React.ComponentRef<'div'>;
interface DotProps extends ComponentPropsWithout<'div', RemovedProps>, GetPropDefTypes<typeof dotsPropDefs> {}
const Dots = React.forwardRef<DotsElement, DotProps>((props, forwardedRef) => {
  const { className, total = 3, current = 0, ...rest } = extractProps(props, dotsPropDefs);

  const safeTotal = Math.max(1, total);
  const safeCurrent = Math.min(Math.max(current, 0), safeTotal);

  return (
    <Flex ref={forwardedRef} className={clsx('rt-DotWrapper', className)} {...rest}>
      {Array.from({ length: safeTotal }).map((_, idx) => {
        return <div key={idx} className={clsx('rt-Dot', { ['rt-active']: idx < safeCurrent })} />;
      })}
    </Flex>
  );
});

Dots.displayName = 'Dots';

export { Dots };
export type { DotProps };
