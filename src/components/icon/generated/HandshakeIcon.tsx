// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M725.333 632.926l-161.933-140.958c-16.883-14.694-42.645-13.636-62.549-3.174-27.891 14.66-62.464 23.693-96.87 30.080-20.085 3.725-32.259-20.164-17.804-34.317l99.976-97.907M725.333 632.926l128-104.457 3.989-2.931M725.333 632.926c0 25.869-14.033 49.796-36.855 62.835l-133.914 76.493c-50.223 28.693-113.886 20.625-155.004-19.635l-228.894-224.149-3.465-3.639M486.153 386.65l0.853-0.835c16.004-15.671 37.709-24.476 60.339-24.476h229.363M486.153 386.65l-238.613-26.28M247.539 360.37c2.65-10.488 5.216-21.643 7.771-33.642 0.127-0.596 0.347-1.183 0.507-1.771 0.7-2.574-0.658-5.173-3.279-5.949l-67.926-20.12c-2.591-0.768-5.553 0.526-6.886 2.973-35.187 88.293-49.64 121.953-90.756 197.984-0.241 0.448-0.544 0.875-0.81 1.306-1.409 2.291-1 5.158 1.089 6.694l55.185 40.533c2.36 1.737 5.938 1.101 7.887-1.365 5.992-7.723 11.607-15.091 16.881-22.182M247.539 360.37c-16.84 66.638-37.125 106.343-80.337 164.46M857.323 525.538c5.124 6.874 10.564 14.007 16.354 21.474 1.95 2.466 5.53 3.102 7.889 1.365l55.185-40.533c2.091-1.536 2.496-4.403 1.088-6.694-0.265-0.431-0.567-0.858-0.811-1.306-41.114-76.032-55.569-109.692-90.756-197.984-1.331-2.447-4.292-3.74-6.886-2.973l-67.925 20.12c-2.62 0.777-3.977 3.375-3.277 5.949 0.158 0.588 0.38 1.175 0.508 1.771 2.633 12.369 5.278 23.842 8.017 34.612M857.323 525.538c-43.362-58.219-63.757-97.873-80.614-164.198"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "handshake"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1701,
        "id": 319,
        "name": "handshake",
        "prevSize": 32,
        "code": 59977
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 330
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface HandshakeIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const HandshakeIcon = ({ size = 16, ...props }: HandshakeIconProps) => (
  <IcoMoon iconSet={iconSet} icon="handshake" size={size} {...props} />
);

HandshakeIcon.displayName = 'HandshakeIcon';

export default HandshakeIcon;
