// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M203.852 322.371c65.456 0 118.519-53.063 118.519-118.519s-53.063-118.519-118.519-118.519c-65.456 0-118.519 53.062-118.519 118.519s53.062 118.519 118.519 118.519zM203.852 322.371v282.758c0 31.437 12.487 61.581 34.713 83.806l223.25 223.253M274.963 914.961h180.15c5.235 0 9.481-4.241 9.481-9.481v-156.442M820.147 701.628c-65.455 0-118.519 53.065-118.519 118.519s53.065 118.519 118.519 118.519c65.455 0 118.519-53.065 118.519-118.519s-53.065-118.519-118.519-118.519zM820.147 701.628v-282.758c0-31.433-12.484-61.579-34.714-83.805l-223.249-223.25M749.039 109.037l-180.151-0c-5.235 0-9.481 4.245-9.481 9.482v156.444"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "git-diff"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1689,
        "id": 331,
        "name": "git-diff",
        "prevSize": 32,
        "code": 59965
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 318
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface GitDiffIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const GitDiffIcon = ({ size = 16, ...props }: GitDiffIconProps) => (
  <IcoMoon iconSet={iconSet} icon="git-diff" size={size} {...props} />
);

GitDiffIcon.displayName = 'GitDiffIcon';

export default GitDiffIcon;
