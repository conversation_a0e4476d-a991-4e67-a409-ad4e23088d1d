// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M647.812 934.46v0zM376.19 934.46v0zM403.69 188.191v0zM620.309 188.191v0zM264.658 916.416v0zM808.077 865.007v0zM759.343 916.416v0zM804.894 290.483v0zM742.703 219.611v0zM219.107 290.483v0zM281.299 219.611v0zM148.759 829.743v0zM875.243 829.743v0zM417.185 337.777c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM606.814 401.777c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM641.067 143.825v0zM382.935 143.825v0zM647.812 934.46l-1.711-31.957c-44.855 2.398-92.198 4.164-134.101 4.164v64c43.494 0 92.087-1.826 137.519-4.254l-1.707-31.953zM512 938.667v-32c-41.903 0-89.245-1.766-134.103-4.164l-1.707 31.957-1.707 31.953c45.428 2.428 94.022 4.254 137.517 4.254v-32zM403.69 188.191l4.157 31.729c35.793-4.689 71.748-7.773 104.153-7.773v-64c-36.13 0-75.051 3.412-112.467 8.315l4.157 31.729zM512 180.147v32c32.405 0 68.361 3.084 104.154 7.773l8.311-63.458c-37.414-4.902-76.335-8.315-112.465-8.315v32zM203.852 749.265h-32c0 29.837-0.025 54.144 1.617 74.031 1.682 20.361 5.252 38.498 13.711 55.778l57.482-28.139c-3.611-7.373-6.076-16.747-7.411-32.909-1.375-16.644-1.4-37.875-1.4-68.762h-32zM376.19 934.46l1.707-31.957c-29.719-1.587-50.025-2.697-65.954-4.915-15.371-2.142-24.41-5.056-31.702-9.122l-31.165 55.898c16.685 9.306 34.228 13.854 54.036 16.61 19.251 2.684 42.722 3.908 71.371 5.44l1.707-31.953zM215.922 865.007l-28.741 14.067c13.213 26.995 35.646 50.658 61.895 65.289l31.165-55.898c-14.852-8.282-28.102-22.255-35.578-37.53l-28.741 14.071zM820.147 749.265h-32c0 30.886-0.026 52.117-1.399 68.762-1.335 16.162-3.802 25.536-7.411 32.909l57.485 28.139c8.457-17.28 12.028-35.418 13.709-55.778 1.643-19.887 1.617-44.194 1.617-74.031h-32zM647.812 934.46l1.707 31.953c28.646-1.532 52.117-2.756 71.369-5.44 19.81-2.756 37.35-7.305 54.037-16.61l-31.164-55.898c-7.296 4.066-16.333 6.98-31.706 9.122-15.927 2.219-36.233 3.328-65.954 4.915l1.711 31.957zM808.077 865.007l-28.74-14.071c-7.475 15.275-20.723 29.248-35.575 37.53l31.164 55.898c26.249-14.63 48.683-38.293 61.897-65.289l-28.745-14.067zM820.147 431.36h32c0-69.155 0.666-113.387-18.197-154.28l-58.116 26.805c11.648 25.254 12.314 52.889 12.314 127.474h32zM804.894 290.483l29.056-13.403c-14.793-32.077-44.301-65.704-74.185-84.541l-34.129 54.142c19.413 12.236 40.589 36.367 50.197 57.204l29.060-13.402zM203.852 431.36h32c0-74.585 0.665-102.221 12.312-127.474l-58.116-26.805c-18.861 40.893-18.196 85.125-18.196 154.28h32zM219.107 290.483l29.058 13.402c9.611-20.837 30.787-44.968 50.199-57.204l-34.127-54.142c-29.884 18.837-59.392 52.463-74.187 84.541l29.058 13.403zM374.224 192.134l31.492 5.679 8.711-48.309-62.984-11.357-8.711 48.309 31.492 5.679zM403.69 188.191l-4.157-31.729c-5.411 0.709-12.823 1.701-18.855 2.512-3.022 0.407-5.708 0.769-7.639 1.030-0.966 0.13-1.743 0.236-2.278 0.308-0.268 0.036-0.476 0.064-0.617 0.084-0.070 0.009-0.124 0.017-0.16 0.022-0.018 0.003-0.032 0.004-0.041 0.006-0.005 0-0.009 0.001-0.011 0.001s-0.002 0-0.003 0-0.001 0-0.001 0-0 0 4.294 31.711 4.294 31.71 4.295 31.71 0 0 0 0 0.001 0 0.003-0c0.002 0 0.006-0 0.010-0.001 0.009-0.001 0.022-0.003 0.040-0.005 0.035-0.005 0.088-0.012 0.158-0.021 0.139-0.019 0.345-0.047 0.611-0.083 0.532-0.072 1.304-0.177 2.265-0.306 1.922-0.259 4.595-0.62 7.601-1.024 6.026-0.81 13.346-1.79 18.64-2.484l-4.157-31.729zM374.224 192.134c-5.355-31.549-5.358-31.548-5.361-31.548-0.002 0-0.005 0.001-0.008 0.001-0.006 0.001-0.012 0.002-0.020 0.003-0.015 0.003-0.035 0.006-0.058 0.010-0.047 0.008-0.11 0.019-0.189 0.032-0.157 0.027-0.378 0.066-0.659 0.115-0.562 0.099-1.364 0.241-2.38 0.426-2.031 0.369-4.921 0.908-8.455 1.604-7.045 1.387-16.753 3.418-27.351 5.991-19.483 4.728-47.735 12.568-65.507 23.77l34.127 54.142c7.636-4.814 25.847-10.712 46.474-15.718 9.456-2.295 18.216-4.129 24.623-5.391 3.193-0.629 5.776-1.11 7.537-1.43 0.88-0.16 1.553-0.279 1.993-0.357 0.22-0.038 0.381-0.066 0.48-0.084 0.049-0.009 0.084-0.014 0.102-0.017 0.009-0.002 0.015-0.003 0.016-0.003s0 0-0.001 0c-0.001 0-0.003 0-0.003 0.001-0.002 0-0.004 0-5.359-31.548zM620.309 188.191l-4.156 31.729c5.295 0.694 12.612 1.673 18.641 2.484 3.004 0.404 5.679 0.765 7.599 1.024 0.96 0.13 1.737 0.234 2.266 0.306 0.269 0.036 0.474 0.064 0.61 0.083 0.073 0.009 0.124 0.016 0.158 0.021 0.021 0.002 0.034 0.004 0.043 0.005 0.004 0.001 0.009 0.001 0.009 0.001s0 0 0.004 0c0 0 0 0 0 0s0 0 4.292-31.71c4.297-31.71 4.297-31.711 4.297-31.711s0 0 0 0-0.004 0-0.004-0-0.004-0.001-0.009-0.001c-0.013-0.001-0.026-0.003-0.043-0.006-0.038-0.005-0.090-0.012-0.162-0.022-0.141-0.019-0.346-0.047-0.614-0.083-0.538-0.073-1.314-0.178-2.278-0.308-1.933-0.261-4.617-0.623-7.637-1.030-6.033-0.811-13.444-1.803-18.859-2.512l-4.156 31.729zM649.775 192.134c-5.355 31.549-5.355 31.548-5.359 31.548 0-0 0-0 0-0.001-0.004 0-0.004 0-0.004 0s0.009 0.001 0.017 0.003 0.051 0.009 0.102 0.017c0.098 0.017 0.26 0.045 0.478 0.084 0.439 0.077 1.114 0.197 1.993 0.357 1.762 0.32 4.343 0.801 7.539 1.43 6.409 1.262 15.168 3.097 24.623 5.392 20.625 5.006 38.835 10.904 46.473 15.718l34.129-54.142c-17.775-11.203-46.025-19.043-65.506-23.77-10.603-2.572-20.309-4.604-27.354-5.991-3.533-0.696-6.426-1.235-8.457-1.604-1.015-0.185-1.818-0.327-2.377-0.426-0.282-0.049-0.503-0.087-0.661-0.115-0.077-0.014-0.141-0.024-0.188-0.032-0.026-0.004-0.043-0.007-0.060-0.010-0.009-0.001-0.013-0.003-0.017-0.003s-0.009-0.001-0.009-0.001-0.009-0.001-5.363 31.548zM820.147 431.36h-32v32.068h64v-32.068h-32zM203.852 463.428h32v-32.068h-64v32.068h32zM85.333 581.952h-32v142.857h64v-142.857h-32zM148.759 829.743l-14.875 28.335 67.163 35.26 29.75-56.666-67.163-35.26-14.875 28.331zM938.667 581.952h-32v142.857h64v-142.857h-32zM875.243 829.743l-14.878-28.331-67.162 35.26 29.751 56.666 67.162-35.26-14.874-28.335zM938.667 724.809h-32c0 32.154-17.83 61.658-46.302 76.604l29.751 56.666c49.527-26.005 80.55-77.329 80.55-133.269h-32zM820.147 463.428l-0.311 31.996c47.855 0.474 86.831 39.479 86.831 86.528h64c0-82.957-68.062-149.717-150.204-150.524l-0.316 32zM85.333 724.809h-32c0 55.94 31.022 107.264 80.55 133.269l29.75-56.666c-28.469-14.946-46.301-44.45-46.301-76.604h-32zM203.852 463.428l-0.314-32c-82.141 0.806-150.204 67.567-150.204 150.524h64c0-47.049 38.978-86.054 86.833-86.528l-0.314-31.996zM417.185 559.407v32h189.629v-64h-189.629v32zM677.926 630.519h-32v94.814h64v-94.814h-32zM606.814 796.446v-32h-189.629v64h189.629v-32zM346.074 725.333h32v-94.814h-64v94.814h32zM417.185 796.446v-32c-21.6 0-39.111-17.51-39.111-39.113h-64c0 56.947 46.164 103.113 103.111 103.113v-32zM677.926 725.333h-32c0 21.602-17.51 39.113-39.113 39.113v64c56.947 0 103.113-46.165 103.113-103.113h-32zM606.814 559.407v32c21.602 0 39.113 17.51 39.113 39.113h64c0-56.947-46.165-103.113-103.113-103.113v32zM417.185 559.407v-32c-56.947 0-103.111 46.165-103.111 103.113h64c0-21.602 17.511-39.113 39.111-39.113v-32zM417.185 369.777v32h189.629v-64h-189.629v32zM203.852 749.265h32v-285.837h-64v285.837h32zM820.147 463.428h-32v285.837h64v-285.837h-32zM452.915 85.333v32h118.17v-64h-118.17v32zM641.067 143.825l-31.492 5.679 8.708 48.309 62.985-11.357-8.708-48.309-31.492 5.679zM571.085 85.333v32c18.923 0 35.132 13.548 38.49 32.171l62.985-11.357c-8.853-49.097-51.588-84.814-101.474-84.814v32zM382.935 143.825l31.492 5.679c3.358-18.623 19.566-32.171 38.489-32.171v-64c-49.887 0-92.62 35.717-101.473 84.814l31.492 5.679z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "backpack"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1443,
        "id": 577,
        "name": "backpack",
        "prevSize": 32,
        "code": 59719
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 72
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BackpackIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BackpackIcon = ({ size = 16, ...props }: BackpackIconProps) => (
  <IcoMoon iconSet={iconSet} icon="backpack" size={size} {...props} />
);

BackpackIcon.displayName = 'BackpackIcon';

export default BackpackIcon;
