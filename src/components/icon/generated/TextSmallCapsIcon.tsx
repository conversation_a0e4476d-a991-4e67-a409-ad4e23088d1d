// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 232.823v-5.268c0-78.547 63.675-142.222 142.222-142.222h237.038M843.853 232.823v-5.268c0-78.547-63.676-142.222-142.225-142.222h-237.035M464.593 85.333v758.519M464.593 843.853h-126.42M464.593 843.853h71.113M938.667 556.774v-3.294c0-49.092-39.795-88.887-88.887-88.887h-148.151M701.628 464.593v474.074M701.628 464.593h-94.814M701.628 938.667h-79.010M701.628 938.667h79.014"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "text-small-caps"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1951,
        "id": 69,
        "name": "text-small-caps",
        "prevSize": 32,
        "code": 60227
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 580
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface TextSmallCapsIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const TextSmallCapsIcon = ({ size = 16, ...props }: TextSmallCapsIconProps) => (
  <IcoMoon iconSet={iconSet} icon="text-small-caps" size={size} {...props} />
);

TextSmallCapsIcon.displayName = 'TextSmallCapsIcon';

export default TextSmallCapsIcon;
