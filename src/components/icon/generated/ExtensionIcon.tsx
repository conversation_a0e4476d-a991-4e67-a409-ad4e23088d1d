// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M566.946 41.609c-30.345 0-59.447 12.055-80.905 33.513-21.461 21.458-33.515 50.561-33.515 80.907 0 7.119 0 10.679-0.61 13.578-1.178 5.912-4.083 11.342-8.346 15.605s-9.694 7.167-15.607 8.347c-2.948 0.61-6.508 0.61-13.577 0.61h-63.566c-26.974 0-52.844 10.715-71.917 29.789s-29.789 44.943-29.789 71.917v63.567c0 7.119 0 10.679-0.61 13.578-1.18 5.912-4.084 11.342-8.347 15.605s-9.693 7.167-15.605 8.347c-2.95 0.61-6.509 0.61-13.578 0.61-30.346 0-59.449 12.055-80.907 33.511-21.457 21.461-33.513 50.564-33.513 80.909s12.055 59.447 33.513 80.905c21.458 21.461 50.561 33.515 80.907 33.515 7.119 0 10.628 0 13.578 0.559 5.919 1.182 11.354 4.096 15.617 8.367 4.263 4.275 7.164 9.715 8.335 15.637 0.61 2.948 0.61 6.507 0.61 13.577v63.565c0 26.974 10.715 52.843 29.789 71.919 19.074 19.072 44.943 29.79 71.917 29.79h63.566c7.119 0 10.679 0 13.577 0.61 5.905 1.173 11.332 4.070 15.595 8.324s7.168 9.673 8.358 15.573c0.61 3.004 0.61 6.562 0.61 13.632 0 30.345 12.053 59.447 33.515 80.905 21.457 21.457 50.56 33.515 80.905 33.515s59.447-12.058 80.905-33.515c21.461-21.457 33.515-50.56 33.515-80.905 0-7.121 0-10.679 0.559-13.581 1.182-5.918 4.096-11.354 8.367-15.616 4.275-4.262 9.715-7.164 15.637-8.333 2.948-0.61 6.507-0.61 13.577-0.61h63.565c26.974 0 52.847-10.718 71.919-29.79 19.072-19.076 29.79-44.945 29.79-71.919v-63.565c0-7.121 0-10.679-0.61-13.577-1.173-5.922-4.070-11.362-8.337-15.637-4.262-4.271-9.698-7.185-15.616-8.367-2.948-0.559-6.511-0.559-13.577-0.559-30.349 0-59.452-12.053-80.909-33.515-21.457-21.457-33.51-50.56-33.51-80.905s12.053-59.447 33.51-80.909c21.457-21.456 50.56-33.511 80.909-33.511 7.117 0 10.628 0 13.577-0.61 5.914-1.18 11.341-4.084 15.603-8.347s7.168-9.693 8.35-15.605c0.61-2.95 0.61-6.509 0.61-13.578v-63.567c0-26.974-10.718-52.843-29.79-71.917s-44.945-29.789-71.919-29.789h-63.565c-7.121 0-10.679 0-13.577-0.61-5.922-1.171-11.362-4.071-15.637-8.335-4.271-4.264-7.185-9.699-8.367-15.617-0.559-2.899-0.559-6.458-0.559-13.578 0-30.346-12.053-59.449-33.515-80.907-21.457-21.457-50.56-33.513-80.905-33.513z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "round",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "extension"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "round",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1622,
        "id": 398,
        "name": "extension",
        "prevSize": 32,
        "code": 59898
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 251
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ExtensionIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ExtensionIcon = ({ size = 16, ...props }: ExtensionIconProps) => (
  <IcoMoon iconSet={iconSet} icon="extension" size={size} {...props} />
);

ExtensionIcon.displayName = 'ExtensionIcon';

export default ExtensionIcon;
