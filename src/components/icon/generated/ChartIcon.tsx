// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M384 180.181c-171.007 38.804-298.667 191.736-298.667 374.485 0 212.075 171.923 384 384 384 182.75 0 335.68-127.659 374.485-298.667M520.563 85.417c228.843 4.505 413.513 189.178 418.018 418.020 0.094 4.71-3.733 8.563-8.448 8.563h-409.6c-4.715 0-8.533-3.823-8.533-8.533v-409.6c0-4.713 3.853-8.542 8.563-8.449z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "chart"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1536,
        "id": 484,
        "name": "chart",
        "prevSize": 32,
        "code": 59812
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 165
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ChartIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ChartIcon = ({ size = 16, ...props }: ChartIconProps) => (
  <IcoMoon iconSet={iconSet} icon="chart" size={size} {...props} />
);

ChartIcon.displayName = 'ChartIcon';

export default ChartIcon;
