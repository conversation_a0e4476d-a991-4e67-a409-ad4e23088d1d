// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M300.544 337.983c-4.537-0.307-9.115-0.463-13.729-0.463-111.275 0-201.481 90.709-201.481 202.607 0 20.74 3.098 40.751 8.855 59.593M300.544 337.983c44.844-98.717 143.885-167.316 258.863-167.316 157.094 0 284.446 128.061 284.446 286.033 0 2.709-0.038 5.41-0.111 8.098M300.544 337.983c15.177-0.154 50.271 4.304 69.234 23.373M843.742 464.798c55.296 19.601 94.925 72.606 94.925 134.921M843.742 464.798c0.034 13.193-4.629 44.343-23.595 63.411M132.741 718.899l95.611-24.038c61.488-15.458 126.023-13.577 186.519 5.431 63.401 19.925 131.177 21.013 195.177 3.136l11.081-3.098c68.143-19.034 140.19-18.765 208.192 0.772l61.939 17.796M227.555 853.333l71.567-19.191c46.091-12.361 94.774-10.863 140.029 4.305 47.42 15.898 98.551 16.764 146.475 2.483l8.222-2.449c50.974-15.189 105.267-14.976 156.126 0.61l46.473 14.242"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "sun-fog"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1922,
        "id": 98,
        "name": "sun-fog",
        "prevSize": 32,
        "code": 60198
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 551
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SunFogIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SunFogIcon = ({ size = 16, ...props }: SunFogIconProps) => (
  <IcoMoon iconSet={iconSet} icon="sun-fog" size={size} {...props} />
);

SunFogIcon.displayName = 'SunFogIcon';

export default SunFogIcon;
