// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M683.473 53.462c-17.6-1.584-33.156 11.401-34.739 29.002s11.401 33.156 29.001 34.74l5.739-63.742zM834.569 167.632v0zM863.996 332.846c-0.559 17.664 13.303 32.438 30.972 32.998 17.664 0.56 32.435-13.305 32.994-30.97l-63.966-2.028zM676.531 162.639c-17.472-2.664-33.796 9.34-36.459 26.812s9.34 33.794 26.812 36.457l9.647-63.269zM753.583 241.071v0zM757.197 330.702c0.478 17.667 15.189 31.599 32.858 31.12 17.664-0.48 31.599-15.19 31.117-32.856l-63.974 1.737zM134.447 633.139v0zM128.193 727.266v0zM730.773 727.266v0zM724.531 633.37v0zM648.435 554.637v0zM210.285 554.5v0zM223.959 384v0zM635.004 384v0zM258.727 658.236c-17.673 0-32 14.323-32 32 0 17.673 14.327 32 32 32v-64zM259.068 722.236c17.673 0 32-14.327 32-32 0-17.677-14.327-32-32-32v64zM599.898 658.236c-17.677 0-32 14.323-32 32 0 17.673 14.323 32 32 32v-64zM600.239 722.236c17.673 0 32-14.327 32-32 0-17.677-14.327-32-32-32v64zM378.256 658.236c-17.673 0-32 14.323-32 32 0 17.673 14.327 32 32 32v-64zM480.708 722.236c17.673 0 32-14.327 32-32 0-17.677-14.327-32-32-32v64zM680.606 85.333c-2.871 31.871-2.88 31.87-2.893 31.869-0.004 0-0.013-0.001-0.021-0.002-0.013-0.001-0.026-0.003-0.034-0.003-0.021-0.002-0.038-0.003-0.047-0.004-0.021-0.002-0.021-0.002 0 0 0.043 0.004 0.162 0.017 0.363 0.040 0.405 0.046 1.118 0.133 2.112 0.275 1.997 0.283 5.116 0.782 9.143 1.602 8.077 1.645 19.674 4.556 33.105 9.539 26.978 10.008 60.365 27.99 88.051 59.94l48.367-41.914c-36.642-42.28-80.239-65.447-114.159-78.031-17.015-6.313-31.851-10.059-42.59-12.246-5.385-1.096-9.766-1.807-12.928-2.255-1.579-0.224-2.854-0.383-3.797-0.491-0.469-0.054-0.858-0.096-1.161-0.127-0.154-0.015-0.282-0.028-0.388-0.038-0.055-0.005-0.102-0.010-0.145-0.014-0.021-0.002-0.043-0.004-0.060-0.006-0.009-0.001-0.021-0.002-0.026-0.003-0.013-0.001-0.026-0.002-2.893 31.869zM834.569 167.632l-24.183 20.957c27.725 31.992 41.267 68.212 47.804 97.063 3.247 14.345 4.698 26.571 5.329 35.060 0.32 4.233 0.431 7.503 0.465 9.602 0.021 1.049 0.017 1.802 0.017 2.233-0.004 0.216-0.004 0.351-0.009 0.402 0 0.026 0 0.030 0 0.013 0-0.009 0-0.022 0.004-0.041 0-0.010 0-0.021 0-0.033 0-0.006 0-0.016 0-0.019 0-0.011 0-0.022 31.983 0.992s31.987 1.003 31.987 0.991c0-0.005 0-0.017 0-0.026 0-0.018 0-0.037 0-0.058 0.004-0.042 0.004-0.089 0.004-0.142 0.004-0.106 0.009-0.235 0.009-0.385 0.009-0.302 0.013-0.692 0.017-1.169 0.009-0.952 0.009-2.248-0.021-3.86-0.055-3.222-0.218-7.72-0.631-13.271-0.832-11.081-2.675-26.494-6.737-44.427-8.085-35.705-25.25-82.6-61.854-124.838l-24.183 20.957zM671.706 194.273c-4.821 31.634-4.83 31.633-4.843 31.631-0.004-0-0.013-0.002-0.021-0.003-0.013-0.002-0.026-0.004-0.034-0.006-0.026-0.004-0.047-0.007-0.064-0.010-0.034-0.006-0.060-0.010-0.077-0.012-0.030-0.005-0.013-0.003 0.038 0.007 0.111 0.019 0.38 0.068 0.798 0.152 0.836 0.169 2.253 0.476 4.134 0.966 3.789 0.984 9.348 2.671 15.834 5.377 13.107 5.472 28.945 14.668 41.933 29.652l48.363-41.914c-21.278-24.554-46.379-38.757-65.643-46.799-9.702-4.051-18.189-6.65-24.397-8.262-3.115-0.809-5.687-1.377-7.586-1.76-0.951-0.192-1.741-0.337-2.347-0.444-0.303-0.053-0.559-0.097-0.772-0.132-0.107-0.017-0.201-0.032-0.282-0.045-0.038-0.006-0.077-0.012-0.115-0.018-0.017-0.003-0.034-0.005-0.047-0.008-0.009-0.001-0.021-0.003-0.026-0.004-0.013-0.002-0.021-0.003-4.847 31.631zM753.583 241.071l-24.179 20.957c13.018 15.022 20.122 32.342 23.919 46.526 1.873 6.999 2.859 12.936 3.371 16.979 0.252 2.012 0.384 3.524 0.452 4.428 0.034 0.451 0.047 0.748 0.055 0.877 0 0.064 0.004 0.086 0 0.064 0-0.011 0-0.033 0-0.067 0-0.017 0-0.036-0.004-0.058 0-0.011 0-0.023 0-0.035 0-0.006 0-0.017 0-0.020 0-0.010 0-0.020 31.987-0.889s31.987-0.879 31.987-0.89c0-0.004 0-0.015 0-0.023 0-0.016 0-0.032 0-0.049 0-0.035-0.004-0.072-0.004-0.112-0.004-0.081-0.004-0.175-0.009-0.28-0.009-0.211-0.021-0.47-0.034-0.776-0.030-0.611-0.077-1.411-0.149-2.383-0.141-1.942-0.38-4.584-0.785-7.805-0.811-6.422-2.3-15.261-5.043-25.512-5.44-20.313-16.132-47.374-37.38-71.89l-24.183 20.957zM198.962 801.6h-32v77.099h64v-77.099h-32zM318.491 878.699h32v-77.099h-64v77.099h32zM258.727 938.667v32c50.782 0 91.764-41.276 91.764-91.968h-64c0 15.548-12.532 27.968-27.764 27.968v32zM198.962 878.699h-32c0 50.692 40.983 91.968 91.764 91.968v-64c-15.232 0-27.764-12.42-27.764-27.968h-32zM540.471 801.6h-32v77.099h64v-77.099h-32zM660.002 878.699h32v-77.099h-64v77.099h32zM600.239 938.667v32c50.782 0 91.763-41.276 91.763-91.968h-64c0 15.548-12.531 27.968-27.763 27.968v32zM540.471 878.699h-32c0 50.692 40.986 91.968 91.767 91.968v-64c-15.232 0-27.767-12.42-27.767-27.968h-32zM134.447 633.139l-31.93-2.125-6.254 94.131 63.859 4.241 6.254-94.127-31.93-2.121zM213.382 818.628v32h432.199v-64h-432.199v32zM730.773 727.266l31.927-2.121-6.238-93.897-63.859 4.245 6.238 93.892 31.932-2.121zM648.435 554.637l2.773-31.881c-152.875-13.291-289.524-13.743-443.737-0.132l5.627 63.753c150.356-13.269 283.264-12.843 432.564 0.141l2.773-31.881zM724.531 633.37l31.932-2.121c-3.772-56.734-47.735-103.492-105.254-108.493l-5.547 63.761c25.054 2.176 45.197 22.733 46.942 48.977l31.927-2.125zM645.581 818.628v32c68.011 0 121.626-57.685 117.12-125.483l-63.859 4.241c2.065 31.091-22.49 57.242-53.261 57.242v32zM128.193 727.266l-31.93-2.121c-4.505 67.797 49.11 125.483 117.118 125.483v-64c-30.768 0-55.325-26.15-53.259-57.242l-31.93-2.121zM134.447 633.139l31.93 2.121c1.74-26.189 21.792-46.682 46.722-48.883l-5.627-63.753c-57.427 5.069-101.195 51.81-104.954 108.39l31.93 2.125zM173.349 561.732l30.777 8.764 50.61-177.732-61.553-17.527-50.61 177.732 30.777 8.764zM306.052 321.868v32h246.861v-64h-246.861v32zM635.004 384l-30.775 8.763 50.611 177.732 61.551-17.527-50.607-177.732-30.78 8.764zM552.913 321.868v32c23.757 0 44.74 15.809 51.315 38.895l61.555-17.527c-14.37-50.467-60.39-85.368-112.87-85.368v32zM223.959 384l30.777 8.763c6.574-23.086 27.558-38.895 51.316-38.895v-64c-52.48 0-98.499 34.901-112.87 85.368l30.777 8.764zM258.727 690.236v32h0.341v-64h-0.341v32zM599.898 690.236v32h0.341v-64h-0.341v32zM378.256 690.236v32h102.452v-64h-102.452v32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "smart-car"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1898,
        "id": 122,
        "name": "smart-car",
        "prevSize": 32,
        "code": 60174
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 527
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SmartCarIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SmartCarIcon = ({ size = 16, ...props }: SmartCarIconProps) => (
  <IcoMoon iconSet={iconSet} icon="smart-car" size={size} {...props} />
);

SmartCarIcon.displayName = 'SmartCarIcon';

export default SmartCarIcon;
