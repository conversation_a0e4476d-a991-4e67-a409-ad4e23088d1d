// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M230.398 843.853c51.841 0 93.867-42.449 93.867-94.814s-42.025-94.818-93.867-94.818c-51.841 0-93.867 42.453-93.867 94.818s42.025 94.814 93.867 94.814zM230.398 843.853v94.814M512 180.148c-51.844 0-93.869 42.45-93.869 94.815s42.025 94.815 93.869 94.815c51.84 0 93.867-42.45 93.867-94.815s-42.027-94.815-93.867-94.815zM512 180.148v-94.815M512 938.667v-438.519M230.398 93.786v430.067M793.6 85.333v320M793.6 938.667v-213.333M793.6 725.333c51.84 0 93.867-42.449 93.867-94.814s-42.027-94.814-93.867-94.814c-51.844 0-93.867 42.449-93.867 94.814s42.022 94.814 93.867 94.814z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "sliders"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1897,
        "id": 123,
        "name": "sliders",
        "prevSize": 32,
        "code": 60173
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 526
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SlidersIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SlidersIcon = ({ size = 16, ...props }: SlidersIconProps) => (
  <IcoMoon iconSet={iconSet} icon="sliders" size={size} {...props} />
);

SlidersIcon.displayName = 'SlidersIcon';

export default SlidersIcon;
