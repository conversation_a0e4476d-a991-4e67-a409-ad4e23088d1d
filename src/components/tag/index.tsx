import clsx from 'clsx';
import { Slot } from 'radix-ui';
import * as React from 'react';

import { tagPropDefs } from '@components/tag/tag.props';
import { type ComponentPropsWithout, type RemovedProps, extractProps } from '@helpers';
import { type GetPropDefTypes, type MarginProps, marginPropDefs } from '@props';

type TagElement = React.ComponentRef<'span'>;
type TagOwnProps = GetPropDefTypes<typeof tagPropDefs>;
interface TagProps extends ComponentPropsWithout<'span', RemovedProps>, MarginProps, TagOwnProps {}
const Tag = React.forwardRef<TagElement, TagProps>((props, forwardedRef) => {
  const { asChild, className, color, radius, ...tagProps } = extractProps(props, tagPropDefs, marginPropDefs);
  const Comp = asChild ? Slot.Root : 'span';

  return (
    <Comp
      data-accent-color={color}
      data-radius={radius}
      {...tagProps}
      ref={forwardedRef}
      className={clsx('rt-reset', 'rt-Tag', className)}
    />
  );
});
Tag.displayName = 'Tag';

export { Tag };
export type { TagProps };
