// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M234.667 512h0.427M512 512h0.427M788.907 512h0.427"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 85.33333333333333
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "dots-horizontal"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 85.33333333333333
        }
      ],
      "properties": {
        "order": 1606,
        "id": 414,
        "name": "dots-horizontal",
        "prevSize": 32,
        "code": 59882
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 235
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface DotsHorizontalIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const DotsHorizontalIcon = ({ size = 16, ...props }: DotsHorizontalIconProps) => (
  <IcoMoon iconSet={iconSet} icon="dots-horizontal" size={size} {...props} />
);

DotsHorizontalIcon.displayName = 'DotsHorizontalIcon';

export default DotsHorizontalIcon;
