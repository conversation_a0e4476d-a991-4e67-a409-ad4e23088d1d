// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M94.457 515.972v0zM85.606 646.195v0zM938.394 646.195v0zM929.566 516.292v0zM821.871 407.366v0zM201.785 407.177v0zM196.177 264.611v0zM829.171 265.365v0zM270.341 562.961c-17.673 0-32 14.327-32 32 0 17.677 14.327 32 32 32v-64zM342.839 626.961c17.673 0 32-14.323 32-32 0-17.673-14.327-32-32-32v64zM681.161 562.961c-17.673 0-32 14.327-32 32 0 17.677 14.327 32 32 32v-64zM753.655 626.961c17.677 0 32-14.323 32-32 0-17.673-14.323-32-32-32v64zM311.91 180.148v0zM406.417 121.635v0zM633.702 180.148v0zM713.207 180.148v0zM618.044 120.854v0zM185.761 749.039h-32v21.879h64v-21.879h-32zM185.761 770.918h-32v84.787h64v-84.787h-32zM354.922 855.706h32v-83.11h-64v83.11h32zM354.922 772.595h32v-23.556h-64v23.556h32zM270.342 938.667v32c63.8 0 116.58-50.893 116.58-114.961h-64c0 27.563-22.956 50.961-52.58 50.961v32zM185.761 855.706h-32c0 64.068 52.78 114.961 116.581 114.961v-64c-29.625 0-52.581-23.398-52.581-50.961h-32zM669.077 749.039h-32v23.556h64v-23.556h-32zM669.077 772.595h-32v83.11h64v-83.11h-32zM838.238 855.706h32v-84.787h-64v84.787h32zM838.238 770.918h32v-21.879h-64v21.879h32zM753.66 938.667v32c63.799 0 116.578-50.893 116.578-114.961h-64c0 27.563-22.955 50.961-52.578 50.961v32zM669.077 855.706h-32c0 64.068 52.779 114.961 116.582 114.961v-64c-29.628 0-52.582-23.398-52.582-50.961h-32zM94.457 515.972l-31.926-2.172-8.851 130.223 63.852 4.343 8.851-130.227-31.927-2.167zM206.168 772.595v32h148.754v-64h-148.754v32zM354.922 772.595v32h314.155v-64h-314.155v32zM669.077 772.595v32h148.753v-64h-148.753v32zM938.394 646.195l31.927-2.172-8.832-129.899-63.851 4.339 8.828 129.903 31.927-2.172zM821.871 407.366l2.709-31.885c-215.573-18.321-408.143-18.943-625.546-0.185l5.501 63.762c213.632-18.432 402.547-17.831 614.621 0.192l2.714-31.884zM929.566 516.292l31.923-2.167c-4.971-73.19-62.775-132.344-136.909-138.644l-2.709 31.885-2.714 31.884c42.726 3.631 75.652 37.606 78.481 79.215l31.927-2.172zM817.83 772.595v32c8.73 0 17.306-0.721 25.664-2.112l-10.513-63.13c-4.894 0.815-9.954 1.242-15.151 1.242v32zM838.238 770.918l5.257 31.565c75.383-12.553 132.181-79.659 126.827-158.46l-63.855 4.343c3.017 44.386-29.043 83.584-73.485 90.987l5.257 31.565zM85.606 646.195l-31.926-2.172c-5.356 78.801 51.441 145.907 126.825 158.46l10.513-63.13c-44.442-7.403-76.502-46.601-73.485-90.987l-31.926-2.172zM185.761 770.918l-5.257 31.565c8.36 1.391 16.936 2.112 25.663 2.112v-64c-5.196 0-10.255-0.427-15.15-1.242l-5.257 31.565zM94.457 515.972l31.927 2.167c2.822-41.515 35.598-75.409 78.152-79.083l-5.501-63.762c-74 6.385-131.542 65.511-136.503 138.505l31.926 2.172zM149.513 417.185l30.601 9.359 46.664-152.574-61.201-18.718-46.664 152.574 30.601 9.359zM829.171 265.365l-30.665 9.152 45.316 151.82 61.329-18.305-45.316-151.82-30.665 9.153zM270.341 594.961v32h72.497v-64h-72.497v32zM681.161 594.961v32h72.495v-64h-72.495v32zM311.91 180.148v32h80.132v-64h-80.132v32zM392.042 180.148l31.076 7.635 14.377-58.513-62.154-15.27-14.376 58.514 31.076 7.634zM453.406 85.333v32h117.85v-64h-117.85v32zM392.042 180.148v32h241.66v-64h-241.66v32zM633.702 180.148v32h79.505v-64h-79.505v32zM618.044 120.854l-30.942 8.17 15.659 59.295 61.879-16.34-15.659-59.294-30.938 8.17zM571.255 85.333v32c7.876 0 14.127 5.182 15.846 11.69l61.879-16.34c-9.327-35.32-41.553-59.35-77.726-59.35v32zM406.417 121.635l31.078 7.634c1.621-6.613 7.919-11.936 15.91-11.936v-64c-36.676 0-69.226 24.69-78.064 60.666l31.076 7.635zM829.171 265.365l30.665-9.153c-19.226-64.402-79.177-108.064-146.628-108.064v64c39.863 0 74.372 25.752 85.299 62.369l30.665-9.152zM196.177 264.611l30.601 9.359c11.117-36.348 45.493-61.822 85.133-61.822v-64c-67.079 0-126.786 43.189-146.334 107.104l30.601 9.359z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "taxi"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1932,
        "id": 88,
        "name": "taxi",
        "prevSize": 32,
        "code": 60208
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 561
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface TaxiIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const TaxiIcon = ({ size = 16, ...props }: TaxiIconProps) => (
  <IcoMoon iconSet={iconSet} icon="taxi" size={size} {...props} />
);

TaxiIcon.displayName = 'TaxiIcon';

export default TaxiIcon;
