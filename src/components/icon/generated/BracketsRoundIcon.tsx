// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M341.333 129.687c-126.44 69.817-213.333 214.792-213.333 382.317 0 167.522 86.893 312.499 213.333 382.315M682.667 129.687c126.438 69.817 213.333 214.792 213.333 382.317 0 167.522-86.895 312.499-213.333 382.315"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "brackets-round"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1485,
        "id": 535,
        "name": "brackets-round",
        "prevSize": 32,
        "code": 59761
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 114
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BracketsRoundIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BracketsRoundIcon = ({ size = 16, ...props }: BracketsRoundIconProps) => (
  <IcoMoon iconSet={iconSet} icon="brackets-round" size={size} {...props} />
);

BracketsRoundIcon.displayName = 'BracketsRoundIcon';

export default BracketsRoundIcon;
