import { type PropDef } from '@props';

const orientationValues = ['horizontal', 'vertical'] as const;
const sizes = ['1', '2', '3', '4'] as const;
const colors = ['none-opaque', 'opaque', 'inverted-primary', 'inverted-secondary', 'card-photo-stroke'] as const;

const dividerPropDefs = {
  orientation: {
    type: 'enum',
    className: 'rt-r-orientation',
    values: orientationValues,
    default: 'horizontal',
    responsive: true,
  },
  size: { type: 'enum', className: 'rt-r-size', values: sizes, default: '1', responsive: true },
  color: { type: 'enum', className: 'rt-color', values: colors, default: 'none-opaque' },
} satisfies {
  orientation: PropDef<(typeof orientationValues)[number]>;
  size: PropDef<(typeof sizes)[number]>;
  color: PropDef<(typeof colors)[number]>;
};

export { dividerPropDefs };
