// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M395.736 649.783l120.68 120.678c16.661 16.661 43.678 16.661 60.339 0l67.674-67.674c14.891-14.891 25.041-33.856 29.171-54.507l17.502-87.509c4.13-20.651 14.281-39.616 29.171-54.507l40.346-40.35c8.107-8.107 17.468-14.852 27.725-19.981l52.25-26.124c26.048-13.024 31.68-47.739 11.089-68.332l-157.645-157.642c-20.591-20.593-55.305-14.959-68.331 11.089l-26.125 52.25c-5.129 10.255-11.874 19.615-19.981 27.722l-40.35 40.348c-14.891 14.891-33.856 25.041-54.502 29.17l-87.513 17.502c-20.65 4.13-39.615 14.28-54.506 29.171l-67.673 67.672c-16.663 16.661-16.663 43.678 0 60.339l120.679 120.683zM395.736 649.783l-193.070 193.071"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "pin"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1824,
        "id": 196,
        "name": "pin",
        "prevSize": 32,
        "code": 60100
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 453
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface PinIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const PinIcon = ({ size = 16, ...props }: PinIconProps) => (
  <IcoMoon iconSet={iconSet} icon="pin" size={size} {...props} />
);

PinIcon.displayName = 'PinIcon';

export default PinIcon;
