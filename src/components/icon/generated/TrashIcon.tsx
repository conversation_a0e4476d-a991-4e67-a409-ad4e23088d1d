// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M128 213.333h85.333M213.333 213.333h128M213.333 213.333v597.333c0 70.694 35.974 128 106.667 128h362.667c70.694 0 128-57.306 128-128v-597.333M810.667 213.333h85.333M810.667 213.333h-128M341.333 213.333h341.333M341.333 213.333v-42.667c0-47.128 38.205-85.333 85.333-85.333h170.667c47.13 0 85.333 38.205 85.333 85.333v42.667M426.667 448v256M597.333 448v256"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "trash"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1961,
        "id": 59,
        "name": "trash",
        "prevSize": 32,
        "code": 60237
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 590
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface TrashIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const TrashIcon = ({ size = 16, ...props }: TrashIconProps) => (
  <IcoMoon iconSet={iconSet} icon="trash" size={size} {...props} />
);

TrashIcon.displayName = 'TrashIcon';

export default TrashIcon;
