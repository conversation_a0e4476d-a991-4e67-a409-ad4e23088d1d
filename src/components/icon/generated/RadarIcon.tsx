// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M680.201 344.98c-42.944-43.242-102.443-70.017-168.201-70.017-130.912 0-237.037 106.125-237.037 237.037 0 130.91 106.125 237.039 237.037 237.039 130.91 0 237.039-106.129 237.039-237.039 0-11.063-0.759-21.948-2.227-32.61M680.201 344.98l134.089-134.089c-77.261-77.558-184.166-125.558-302.289-125.558-235.642 0-426.667 191.025-426.667 426.667 0 235.639 191.025 426.667 426.667 426.667 235.639 0 426.667-191.027 426.667-426.667 0-63.057-13.679-122.918-38.229-176.78M680.201 344.98l-167.023 167.020"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "radar"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1844,
        "id": 176,
        "name": "radar",
        "prevSize": 32,
        "code": 60120
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 473
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface RadarIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const RadarIcon = ({ size = 16, ...props }: RadarIconProps) => (
  <IcoMoon iconSet={iconSet} icon="radar" size={size} {...props} />
);

RadarIcon.displayName = 'RadarIcon';

export default RadarIcon;
