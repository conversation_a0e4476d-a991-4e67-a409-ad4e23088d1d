'use client';

import clsx from 'clsx';
import * as React from 'react';

import { type ComponentPropsWithout, type NotInputTextualAttributes, extractProps } from '@helpers';
import type { GetPropDefTypes } from '@props';

import { otpInputPropDefs } from './otp.props';

const defaultLength = 6;

type OTPInputOwnProps = GetPropDefTypes<typeof otpInputPropDefs>;
type BaseInputProps = ComponentPropsWithout<
  'input',
  NotInputTextualAttributes | 'color' | 'defaultValue' | 'size' | 'type' | 'value' | 'prefix' | 'onChange'
>;

interface OTPInputProps extends BaseInputProps, OTPInputOwnProps {
  length?: number;
  onChange?: (value: string) => void;
  onActiveChange?: (value: string, index: number) => void;
  error?: boolean;
  disabled?: boolean;
  className?: string;
  separator?: React.ReactNode;
  separatorAlign?: 'between' | 'symmetric';
}

const OTPInput = React.forwardRef<HTMLDivElement, OTPInputProps>((props, forwardedRef) => {
  const {
    color,
    radius,
    length = defaultLength,
    onChange,
    onActiveChange,
    error,
    disabled,
    separator,
    separatorAlign = 'between',
    className,
    style,
    ...rest
  } = extractProps(props, otpInputPropDefs);

  const [values, setValues] = React.useState<string[]>(Array(length).fill(''));
  const inputsRef = React.useRef<(HTMLInputElement | null)[]>([]);

  const updateValue = (val: string, idx: number) => {
    if (!/^[0-9a-zA-Z]*$/.test(val)) return;

    const updated = [...values];
    updated[idx] = val;
    setValues(updated);

    onChange?.(updated.join(''));
    onActiveChange?.(val, idx);

    if (val && idx < length - 1) {
      inputsRef.current[idx + 1]?.focus();
    }
  };

  const handleBackspace = (e: React.KeyboardEvent<HTMLInputElement>, idx: number) => {
    if (e.key === 'Backspace' && !values[idx] && idx > 0) {
      setValues((prev) => {
        const updated = [...prev];
        updated[idx - 1] = '';
        return updated;
      });
      inputsRef.current[idx - 1]?.focus();
    }
  };

  const shouldRenderSeparator = (index: number) => {
    if (!separator) return false;
    if (separatorAlign === 'between') return index > 0;
    if (separatorAlign === 'symmetric') return index === Math.floor(length / 2);
    return false;
  };

  const renderInput = (i: number) => (
    <input
      {...rest}
      key={`input-${i}`}
      spellCheck="false"
      value={values[i]}
      onChange={(e) => updateValue(e.target.value, i)}
      onKeyDown={(e) => handleBackspace(e, i)}
      className={clsx(
        'rt-reset rt-OTPInput',
        { 'rt-error': error },
        { 'rtr-with-separator-left': shouldRenderSeparator(i) },
      )}
      maxLength={1}
      aria-invalid={error || undefined}
      disabled={disabled}
      ref={(el) => {
        inputsRef.current[i] = el;
      }}
    />
  );

  const renderSeparator = (i: number) =>
    shouldRenderSeparator(i) ? (
      <span key={`sep-${i}`} className="rt-OTPInputSeparator">
        {separator}
      </span>
    ) : null;

  return (
    <div
      className={clsx('rt-OTPInputRoot', className)}
      data-accent-color={color}
      data-radius={radius}
      style={style}
      ref={forwardedRef}
    >
      {Array.from({ length }).flatMap((_, i) => [renderSeparator(i), renderInput(i)])}
    </div>
  );
});

OTPInput.displayName = 'Input.OTP';

export { OTPInput };
export type { OTPInputProps };
