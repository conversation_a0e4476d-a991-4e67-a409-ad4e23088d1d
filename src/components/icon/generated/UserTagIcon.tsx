// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M606.814 298.667c0 52.365-42.449 94.815-94.814 94.815s-94.815-42.45-94.815-94.815c0-52.365 42.45-94.815 94.815-94.815s94.814 42.45 94.814 94.815z",
          "M333.025 600.512c9.784-58.761 56.457-105.557 115.875-109.854 43.418-3.14 82.829-3.149 126.148-0.021 59.456 4.288 106.146 51.132 115.934 109.931 5.807 34.867-18.586 67.708-53.777 71.036-89.766 8.491-160.431 8.393-250.347-0.068-35.2-3.315-59.64-36.147-53.833-71.023z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          },
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "user-tag"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        },
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1983,
        "id": 37,
        "name": "user-tag",
        "prevSize": 32,
        "code": 60259
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 612
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface UserTagIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const UserTagIcon = ({ size = 16, ...props }: UserTagIconProps) => (
  <IcoMoon iconSet={iconSet} icon="user-tag" size={size} {...props} />
);

UserTagIcon.displayName = 'UserTagIcon';

export default UserTagIcon;
