// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M320 772.74c74.142 26.505 117.858 27.567 192 0M641.25 298.444c-32.981-2.124-103.893-15.847-103.893-94.592 0-70.834 53.978-118.519 109.833-118.519 27.93 0 89.903 24.058 111.718 64.128 102.195 187.723 129.604 485.35 136.951 631.483 2.603 51.712-31.373 97.707-82.086 111.441-228.681 61.931-383.71 61.653-605.482-0.495-48.156-13.495-80.291-57.557-80.291-106.846v-67.61c0-34.714 15.985-67.627 45.287-86.942 55.352-36.48 151.347-91.204 229.911-91.827 72.284-0.572 171.108 74.249 211.804 107.844 6.737 5.559 17.229 0.29 16.329-8.316-10.854-103.539-30.874-210.466 19.503-324.695 2.94-6.671-2.21-14.581-9.583-15.056z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bicep"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1471,
        "id": 549,
        "name": "bicep",
        "prevSize": 32,
        "code": 59747
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 100
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface BicepIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const BicepIcon = ({ size = 16, ...props }: BicepIconProps) => (
  <IcoMoon iconSet={iconSet} icon="bicep" size={size} {...props} />
);

BicepIcon.displayName = 'BicepIcon';

export default BicepIcon;
