// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M384 917.333h256M428.791 213.333h166.413c52.74 0 100.079 32.348 119.245 81.481l82.743 321.467c5.35 13.722 13.879 25.975 24.887 35.759l5.888 5.235c16.132 14.34 25.365 34.897 25.365 56.482 0 41.741-33.835 75.575-75.575 75.575h-531.517c-41.738 0-75.574-33.835-75.574-75.575 0-21.585 9.231-42.142 25.365-56.482l5.89-5.235c11.006-9.783 19.535-22.037 24.887-35.759l82.736-321.467c19.167-49.134 66.508-81.481 119.246-81.481zM576 149.333c0 35.346-28.655 64-64 64-35.349 0-64-28.654-64-64s28.651-64 64-64c35.345 0 64 28.654 64 64z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "bell-02"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1463,
        "id": 557,
        "name": "bell-02",
        "prevSize": 32,
        "code": 59739
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 92
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface Bell02IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const Bell02Icon = ({ size = 16, ...props }: Bell02IconProps) => (
  <IcoMoon iconSet={iconSet} icon="bell-02" size={size} {...props} />
);

Bell02Icon.displayName = 'Bell02Icon';

export default Bell02Icon;
