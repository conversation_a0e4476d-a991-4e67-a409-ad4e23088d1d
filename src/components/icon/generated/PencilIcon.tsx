// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M554.974 227.555l92.211-92.212c66.679-66.681 174.793-66.681 241.472 0s66.679 174.791 0 241.471l-92.211 92.211M554.974 227.555l-398.529 398.527M554.974 227.555c70.101 25.278 118.613 50.795 155.157 86.312M796.446 469.026l-398.53 398.528M796.446 469.026c-23.023-70.047-48.137-118.059-86.315-155.159M156.445 626.082l-30.305 30.306c-1.444 1.442-2.38 3.315-2.669 5.338l-38.039 266.167c-0.89 6.225 4.449 11.567 10.678 10.675l266.164-38.037c2.022-0.29 3.895-1.229 5.338-2.671l30.305-30.306M156.445 626.082l120.736 120.738M397.916 867.554l-120.736-120.734M277.18 746.82l432.951-432.953"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "pencil"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1820,
        "id": 200,
        "name": "pencil",
        "prevSize": 32,
        "code": 60096
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 449
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface PencilIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const PencilIcon = ({ size = 16, ...props }: PencilIconProps) => (
  <IcoMoon iconSet={iconSet} icon="pencil" size={size} {...props} />
);

PencilIcon.displayName = 'PencilIcon';

export default PencilIcon;
