// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M384 464.593l125.214-122.356c0.772-0.602 1.779-0.904 2.786-0.904M640 464.593l-125.214-122.356c-0.772-0.602-1.779-0.904-2.786-0.904M512 341.333v341.333M938.667 512c0 235.639-191.027 426.667-426.667 426.667-235.642 0-426.667-191.027-426.667-426.667 0-235.642 191.025-426.667 426.667-426.667 235.639 0 426.667 191.025 426.667 426.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "arrow-up-circle"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1427,
        "id": 593,
        "name": "arrow-up-circle",
        "prevSize": 32,
        "code": 59703
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 56
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ArrowUpCircleIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ArrowUpCircleIcon = ({ size = 16, ...props }: ArrowUpCircleIconProps) => (
  <IcoMoon iconSet={iconSet} icon="arrow-up-circle" size={size} {...props} />
);

ArrowUpCircleIcon.displayName = 'ArrowUpCircleIcon';

export default ArrowUpCircleIcon;
