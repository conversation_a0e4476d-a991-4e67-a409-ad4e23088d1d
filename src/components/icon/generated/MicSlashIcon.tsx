// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M328.205 464.593c0 104.73 82.288 189.628 183.795 189.628 1.92 0 3.836-0.030 5.743-0.090M810.667 346.074v118.519c0 95.625-42.219 181.077-108.476 237.598M512 772.74c-164.949 0-298.667-137.963-298.667-308.147v-114.872M512 772.74c36.89 0 72.218-6.899 104.836-19.516M512 772.74v165.926M512 938.667h-137.846M512 938.667h137.847M443.076 369.778c54.332-13.984 84.676-12.746 137.847 0M489.024 488.294c17.907-3.9 28.015-3.635 45.952 0M85.333 85.333l242.872 242.872M938.667 938.667l-236.476-236.476M702.191 702.191l-83.311-83.311M618.88 618.88c46.571-34.402 76.915-90.688 76.915-154.287v-189.63c0-104.729-82.287-189.629-183.795-189.629-101.507 0-183.795 84.9-183.795 189.629v53.242M618.88 618.88l-290.675-290.675"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "mic-slash"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1771,
        "id": 249,
        "name": "mic-slash",
        "prevSize": 32,
        "code": 60047
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 400
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MicSlashIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MicSlashIcon = ({ size = 16, ...props }: MicSlashIconProps) => (
  <IcoMoon iconSet={iconSet} icon="mic-slash" size={size} {...props} />
);

MicSlashIcon.displayName = 'MicSlashIcon';

export default MicSlashIcon;
