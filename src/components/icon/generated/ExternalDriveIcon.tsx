// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M180.204 912.973v0zM89.695 822.464v0zM934.306 822.464v0zM843.797 912.973v0zM843.797 580.361v0zM934.306 670.869v0zM180.204 580.361v0zM89.695 670.869v0zM178.061 267.628v0zM845.939 267.628v0zM758.293 117.098v0zM811.686 160.060v0zM212.312 160.060v0zM265.708 117.098v0zM277.333 714.667c-17.673 0-32 14.327-32 32s14.327 32 32 32v-64zM490.24 778.667c17.677 0 32-14.327 32-32s-14.323-32-32-32v64zM618.671 778.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM618.244 714.667c-17.677 0-32 14.327-32 32s14.323 32 32 32v-64zM746.671 778.667c17.673 0 32-14.327 32-32s-14.327-32-32-32v64zM746.244 714.667c-17.677 0-32 14.327-32 32s14.323 32 32 32v-64zM256 576v32h512v-64h-512v32zM768 917.333v-32h-512v64h512v-32zM256 917.333v-32c-41.739 0-56.331-0.273-67.513-3.273l-16.564 61.82c21.372 5.726 46.459 5.453 84.078 5.453v-32zM85.333 746.667h-32c0 37.619-0.275 62.707 5.452 84.079l61.819-16.567c-2.996-11.179-3.271-25.771-3.271-67.511h-32zM180.204 912.973l8.282-30.912c-33.129-8.875-59.005-34.752-67.882-67.883l-61.819 16.567c14.795 55.215 57.922 98.342 113.137 113.135l8.282-30.908zM938.667 746.667h-32c0 41.741-0.273 56.333-3.273 67.511l61.82 16.567c5.726-21.372 5.453-46.46 5.453-84.079h-32zM768 917.333v32c37.619 0 62.707 0.273 84.079-5.453l-16.567-61.82c-11.179 2.999-25.771 3.273-67.511 3.273v32zM934.306 822.464l-30.912-8.286c-8.875 33.131-34.752 59.008-67.883 67.883l16.567 61.82c55.215-14.793 98.342-57.92 113.135-113.135l-30.908-8.282zM768 576v32c41.741 0 56.333 0.273 67.511 3.273l16.567-61.82c-21.372-5.726-46.46-5.453-84.079-5.453v32zM938.667 746.667h32c0-37.619 0.273-62.707-5.453-84.079l-61.82 16.567c2.999 11.179 3.273 25.771 3.273 67.511h32zM843.797 580.361l-8.286 30.912c33.131 8.875 59.008 34.752 67.883 67.883l61.82-16.567c-14.793-55.215-57.92-98.342-113.135-113.135l-8.282 30.908zM256 576v-32c-37.619 0-62.705-0.273-84.078 5.453l16.564 61.82c11.182-2.999 25.774-3.273 67.513-3.273v-32zM85.333 746.667h32c0-41.741 0.275-56.333 3.271-67.511l-61.819-16.567c-5.727 21.372-5.452 46.46-5.452 84.079h32zM180.204 580.361l-8.282-30.908c-55.215 14.793-98.342 57.92-113.137 113.135l61.819 16.567c8.877-33.131 34.753-59.008 67.882-67.883l-8.282-30.912zM89.695 670.869l31.258 6.852 88.366-403.244-62.516-13.699-88.366 403.244 31.258 6.848zM378.114 106.667v32h267.774v-64h-267.774v32zM845.939 267.628l-31.258 6.85 88.367 403.244 62.515-13.7-88.367-403.244-31.258 6.85zM645.888 106.667v32c29.854 0 50.53 0.020 66.812 1.218 15.876 1.168 25.361 3.344 32.939 6.606l25.306-58.784c-16.653-7.17-33.98-10.21-53.551-11.65-19.17-1.41-42.581-1.39-71.505-1.39v32zM845.939 267.628l31.258-6.85c-6.191-28.254-11.183-51.129-16.666-69.552-5.594-18.81-12.271-35.083-22.839-49.817l-52.006 37.303c4.809 6.703 8.964 15.503 13.504 30.763 4.655 15.647 9.101 35.837 15.492 65.003l31.258-6.85zM758.293 117.098l-12.655 29.392c16.030 6.901 29.875 18.040 40.047 32.221l52.006-37.303c-16.956-23.635-40.030-42.201-66.748-53.702l-12.651 29.392zM178.061 267.628l31.258 6.85c6.391-29.165 10.836-49.356 15.491-65.002 4.54-15.259 8.696-24.060 13.504-30.763l-52.004-37.303c-10.569 14.734-17.247 31.007-22.843 49.817-5.481 18.423-10.473 41.297-16.665 69.553l31.258 6.85zM378.114 106.667v-32c-28.925 0-52.338-0.020-71.508 1.39-19.572 1.44-36.897 4.48-53.552 11.65l25.307 58.784c7.577-3.262 17.063-5.438 32.941-6.606 16.28-1.198 36.954-1.218 66.812-1.218v-32zM212.312 160.060l26.002 18.652c10.172-14.181 24.017-25.321 40.047-32.221l-25.307-58.784c-26.716 11.501-49.791 30.067-66.744 53.702l26.002 18.652zM277.333 746.667v32h212.907v-64h-212.907v32zM618.671 746.667v-32h-0.427v64h0.427v-32zM746.671 746.667v-32h-0.427v64h0.427v-32z"
        ],
        "attrs": [
          {}
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "external-drive"
        ]
      },
      "attrs": [
        {}
      ],
      "properties": {
        "order": 1623,
        "id": 397,
        "name": "external-drive",
        "prevSize": 32,
        "code": 59899
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 252
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface ExternalDriveIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const ExternalDriveIcon = ({ size = 16, ...props }: ExternalDriveIconProps) => (
  <IcoMoon iconSet={iconSet} icon="external-drive" size={size} {...props} />
);

ExternalDriveIcon.displayName = 'ExternalDriveIcon';

export default ExternalDriveIcon;
