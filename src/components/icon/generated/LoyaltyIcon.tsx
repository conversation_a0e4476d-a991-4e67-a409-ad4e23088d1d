// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M192 653.53v-17.638c0-2.436-1.91-4.412-4.267-4.412h-98.133c-2.356 0-4.267 1.975-4.267 4.412v255.765c0 2.432 1.91 4.407 4.267 4.407h98.133c2.356 0 4.267-1.975 4.267-4.407v-17.643M192 653.53v220.484M192 653.53c48.914-22.468 128-88.196 192.905-88.196h197.823c19.849 0 35.938 16.631 35.938 37.146 0 17.045-11.226 31.902-27.221 36.036l-124.369 32.132c-11.209 2.897-19.076 13.308-19.076 25.254 0 13.466 9.933 24.704 22.908 25.924l162.099 15.232c32.461 3.051 56.38-0.166 85.534-15.232l161.609-82.155c26.884-13.892 58.517 6.31 58.517 37.376 0 12.975-5.833 25.216-15.782 33.126l-178.138 141.623c-53.44 42.487-120.939 61.47-187.84 52.826l-236.907-30.609h-128M341.333 219.174c0-56.245 47.279-101.841 105.6-101.841 33.267 0 62.942 14.836 82.3 38.022 2.082 2.495 6.118 2.495 8.201 0 19.358-23.186 49.033-38.022 82.3-38.022 58.321 0 105.6 45.596 105.6 101.841 0 86.012-165.205 219.576-189.129 238.464-1.732 1.37-4.011 1.37-5.743 0-23.923-18.889-189.129-152.452-189.129-238.464z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "butt",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "loyalty"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "butt",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1751,
        "id": 269,
        "name": "loyalty",
        "prevSize": 32,
        "code": 60027
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 380
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface LoyaltyIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const LoyaltyIcon = ({ size = 16, ...props }: LoyaltyIconProps) => (
  <IcoMoon iconSet={iconSet} icon="loyalty" size={size} {...props} />
);

LoyaltyIcon.displayName = 'LoyaltyIcon';

export default LoyaltyIcon;
