// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M282.256 512v-343.704c0-45.819 36.001-82.963 80.41-82.963s80.41 37.144 80.41 82.963v201.482c0 39.273 30.857 71.11 68.924 71.11s68.924-31.836 68.924-71.11v-154.074c0-45.819 36.002-82.963 80.41-82.963s80.41 37.144 80.41 82.963v296.296M213.333 654.221h137.846M672.819 654.221h137.847M282.256 938.667c-38.065 0-68.923-31.838-68.923-71.113v-331.849c0-13.094 10.286-23.706 22.974-23.706h91.898c12.688 0 22.974 10.611 22.974 23.706v331.849c0 39.275-30.858 71.113-68.923 71.113zM741.743 938.667c-38.063 0-68.924-31.838-68.924-71.113v-331.849c0-13.094 10.287-23.706 22.976-23.706h91.895c12.689 0 22.976 10.611 22.976 23.706v331.849c0 39.275-30.857 71.113-68.924 71.113z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "skipping-rope"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1896,
        "id": 124,
        "name": "skipping-rope",
        "prevSize": 32,
        "code": 60172
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 525
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SkippingRopeIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SkippingRopeIcon = ({ size = 16, ...props }: SkippingRopeIconProps) => (
  <IcoMoon iconSet={iconSet} icon="skipping-rope" size={size} {...props} />
);

SkippingRopeIcon.displayName = 'SkippingRopeIcon';

export default SkippingRopeIcon;
