// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M132.741 853.333h758.519M839.445 713.412l-686.263-225.988c-22.883-7.535-39.576-26.615-43.398-49.604l-24.172-145.353c-2.129-12.802 8.193-24.38 21.66-24.297l63.972 0.395c26.030 0.161 49.481 15.17 59.706 38.214l5.327 12.005c8.247 18.586 25.265 32.229 45.745 36.674l117.112 25.42c9.243 2.006 17.566-5.823 15.695-14.765l-34.417-164.56c-2.443-11.681 5.698-22.974 17.92-24.858l30.535-4.706c45.649-7.035 90.94 14.425 112.994 53.539l116.885 207.293c1.796 3.191 4.937 5.478 8.614 6.276l121.886 26.458c20.484 4.446 37.5 18.086 45.747 36.672 14.255 32.124 27.038 52.105 61.798 67.422 30.293 13.346 36.262 40.738 40.973 69.077 6.11 36.723-19.772 59.972-51.473 73.011-14.865 6.114-31.556 6.707-46.848 1.673z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "airplane-departure"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1382,
        "id": 638,
        "name": "airplane-departure",
        "prevSize": 32,
        "code": 59658
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 11
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface AirplaneDepartureIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const AirplaneDepartureIcon = ({ size = 16, ...props }: AirplaneDepartureIconProps) => (
  <IcoMoon iconSet={iconSet} icon="airplane-departure" size={size} {...props} />
);

AirplaneDepartureIcon.displayName = 'AirplaneDepartureIcon';

export default AirplaneDepartureIcon;
