// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M85.333 380.654c0 0 28.429-33.487 85.333-70.499M938.667 380.654c0 0-142.046-167.32-426.667-167.32-101.456 0-184.795 21.26-250.043 48.624M218.667 547.972c0 0 43.381-40.969 116.567-73.25M805.333 547.972c0 0-118.106-111.543-293.333-111.543-24.393 0-47.676 2.159-69.692 5.879M352 687.407c0 0 57.712-48.282 140.919-55.002M512.474 853.333h-0.474M85.333 85.333l176.624 176.624M938.667 938.667l-496.358-496.358M261.957 261.957l180.351 180.351"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "wifi-1"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 2009,
        "id": 11,
        "name": "wifi-1",
        "prevSize": 32,
        "code": 60285
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 638
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface Wifi-1IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const Wifi-1Icon = ({ size = 16, ...props }: Wifi-1IconProps) => (
  <IcoMoon iconSet={iconSet} icon="wifi-1" size={size} {...props} />
);

Wifi-1Icon.displayName = 'Wifi-1Icon';

export default Wifi-1Icon;
