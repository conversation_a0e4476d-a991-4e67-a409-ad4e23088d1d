// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M192 873.762v-723.523c0-16.985 18.161-27.682 32.86-19.355l638.569 361.762c14.985 8.486 14.985 30.221 0 38.707l-638.569 361.762c-14.698 8.329-32.86-2.372-32.86-19.354z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "play"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1827,
        "id": 193,
        "name": "play",
        "prevSize": 32,
        "code": 60103
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 456
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface PlayIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const PlayIcon = ({ size = 16, ...props }: PlayIconProps) => (
  <IcoMoon iconSet={iconSet} icon="play" size={size} {...props} />
);

PlayIcon.displayName = 'PlayIcon';

export default PlayIcon;
