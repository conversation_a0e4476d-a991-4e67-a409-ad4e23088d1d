// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M512 85.333v118.519M512 203.852l71.113-47.407M512 203.852l-71.113-47.407M512 203.852v165.926M512 820.147v118.519M512 820.147l-71.113 47.407M512 820.147l71.113 47.407M512 820.147v-165.926M938.667 512h-118.519M820.147 512l47.407 71.113M820.147 512l47.407-71.113M820.147 512h-165.926M203.852 512h-118.519M203.852 512l-47.407-71.113M203.852 512l-47.407 71.113M203.852 512h165.926M813.7 813.7l-83.806-83.806M729.894 729.894l-16.764 83.806M729.894 729.894l83.806-16.764M729.894 729.894l-117.329-117.329M294.106 294.106l-83.805-83.805M294.106 294.106l16.761-83.805M294.106 294.106l-83.805 16.761M294.106 294.106l117.327 117.327M210.298 813.7l83.805-83.806M294.103 729.894l-83.805-16.764M294.103 729.894l16.761 83.806M294.103 729.894l117.33-117.329M729.89 294.106l83.806-83.805M729.89 294.106l83.806 16.761M729.89 294.106l-16.759-83.805M729.89 294.106l-117.325 117.327M512 369.778l100.565 41.656M512 369.778l-100.566 41.656M612.565 411.434l41.655 100.566M654.221 512l-41.655 100.565M612.565 612.565l-100.565 41.655M512 654.221l-100.566-41.655M411.434 612.565l-41.656-100.565M369.778 512l41.656-100.566"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "snow-alt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1906,
        "id": 114,
        "name": "snow-alt",
        "prevSize": 32,
        "code": 60182
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 535
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SnowAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SnowAltIcon = ({ size = 16, ...props }: SnowAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="snow-alt" size={size} {...props} />
);

SnowAltIcon.displayName = 'SnowAltIcon';

export default SnowAltIcon;
