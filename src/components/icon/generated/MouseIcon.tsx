// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M523.947 369.778c39.586 0 71.68 31.837 71.68 71.11v94.818c0 39.27-32.094 71.108-71.68 71.108s-71.68-31.838-71.68-71.108v-94.818c0-39.272 32.094-71.11 71.68-71.11zM523.947 369.778v-174.243c0-60.862-49.732-110.201-111.083-110.201-47.814 0-91.079 31.493-124.701 65.218-18.491 18.548-43.092 37.558-74.83 53.3M523.947 938.667c-158.351 0-286.72-127.351-286.72-284.446v-165.926c0-157.092 128.369-284.442 286.72-284.442 158.353 0 286.72 127.35 286.72 284.442v165.926c0 157.094-128.367 284.446-286.72 284.446z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "mouse"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1792,
        "id": 228,
        "name": "mouse",
        "prevSize": 32,
        "code": 60068
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 421
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface MouseIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const MouseIcon = ({ size = 16, ...props }: MouseIconProps) => (
  <IcoMoon iconSet={iconSet} icon="mouse" size={size} {...props} />
);

MouseIcon.displayName = 'MouseIcon';

export default MouseIcon;
