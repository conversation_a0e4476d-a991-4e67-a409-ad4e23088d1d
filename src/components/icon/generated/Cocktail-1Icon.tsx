// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M696.887 213.333v-93.867c0-11.948 0-17.922-2.517-22.485-2.214-4.014-5.751-7.278-10.099-9.323-4.945-2.325-11.418-2.325-24.358-2.325h-295.824c-12.943 0-19.415 0-24.359 2.325-4.349 2.045-7.884 5.309-10.1 9.323-2.519 4.564-2.519 10.537-2.519 22.485v93.867M696.887 213.333h-369.776M696.887 213.333l-88.324 163.060c-1.459 2.694-2.47 5.111-3.127 7.607M696.887 213.333l88.324 163.060c3.012 5.558 4.122 9.936 4.122 16.257v511.883c0 11.947 0 17.92-2.517 22.485-2.219 4.015-5.751 7.279-10.103 9.323-4.941 2.325-11.413 2.325-24.358 2.325h-147.908M327.111 213.333l-88.324 163.060c-1.46 2.694-2.472 5.111-3.128 7.607M604.446 938.667h-332.801c-12.944 0-19.415 0-24.359-2.325-4.349-2.044-7.884-5.308-10.1-9.323-2.519-4.565-2.519-10.539-2.519-22.485v-511.883c0-3.257 0.294-5.998 0.992-8.651M604.446 938.667v-546.016c0-3.257 0.294-5.998 0.99-8.651M235.659 384h369.777M362.667 597.333h113.92M362.667 725.333h113.92"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "cocktail-1"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1575,
        "id": 445,
        "name": "cocktail-1",
        "prevSize": 32,
        "code": 59851
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 204
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface Cocktail-1IconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const Cocktail-1Icon = ({ size = 16, ...props }: Cocktail-1IconProps) => (
  <IcoMoon iconSet={iconSet} icon="cocktail-1" size={size} {...props} />
);

Cocktail-1Icon.displayName = 'Cocktail-1Icon';

export default Cocktail-1Icon;
