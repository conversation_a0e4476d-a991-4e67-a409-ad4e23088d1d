// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M290.133 938.667c-71.687 0-107.53 0-134.911-13.952-24.085-12.271-43.666-31.851-55.938-55.936-13.951-27.383-13.951-63.223-13.951-134.912M938.667 733.867c0 71.689 0 107.529-13.952 134.912-12.271 24.085-31.851 43.665-55.936 55.936-27.383 13.952-63.223 13.952-134.912 13.952M733.867 85.333c71.689 0 107.529 0 134.912 13.951 24.085 12.272 43.665 31.853 55.936 55.938 13.952 27.38 13.952 63.224 13.952 134.911M290.133 85.333c-71.687 0-107.53 0-134.911 13.951-24.085 12.272-43.666 31.853-55.938 55.938-13.951 27.38-13.951 63.224-13.951 134.911M426.667 938.667h170.667M426.667 85.333h170.667M85.333 426.667v170.667M938.667 426.667v170.667M366.933 298.667h290.133c23.898 0 35.844 0 44.971 4.65 8.030 4.091 14.554 10.618 18.645 18.646 4.651 9.127 4.651 21.075 4.651 44.97v290.133c0 23.898 0 35.844-4.651 44.971-4.092 8.030-10.615 14.554-18.645 18.645-9.126 4.651-21.073 4.651-44.971 4.651h-290.133c-23.895 0-35.843 0-44.97-4.651-8.028-4.092-14.555-10.615-18.646-18.645-4.65-9.126-4.65-21.073-4.65-44.971v-290.133c0-23.895 0-35.843 4.65-44.97 4.091-8.028 10.618-14.555 18.646-18.646 9.127-4.65 21.075-4.65 44.97-4.65z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "selection-all"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1868,
        "id": 152,
        "name": "selection-all",
        "prevSize": 32,
        "code": 60144
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 497
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface SelectionAllIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const SelectionAllIcon = ({ size = 16, ...props }: SelectionAllIconProps) => (
  <IcoMoon iconSet={iconSet} icon="selection-all" size={size} {...props} />
);

SelectionAllIcon.displayName = 'SelectionAllIcon';

export default SelectionAllIcon;
