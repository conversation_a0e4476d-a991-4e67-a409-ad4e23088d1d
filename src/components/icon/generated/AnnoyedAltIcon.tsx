// Auto-generated icon component - do not edit manually
import IcoMoon, { type IconProps } from 'react-icomoon';

const iconSet = {
  "IcoMoonType": "selection",
  "icons": [
    {
      "icon": {
        "paths": [
          "M298.667 409.6c9.483 5.672 32.459 17.067 64 17.067s54.517-11.395 64-17.067M597.333 409.6c9.485 5.672 32.461 17.067 64 17.067s54.515-11.395 64-17.067M618.667 640l-149.333 64M938.667 512c0 235.639-191.027 426.667-426.667 426.667-235.642 0-426.667-191.027-426.667-426.667 0-235.642 191.025-426.667 426.667-426.667 235.639 0 426.667 191.025 426.667 426.667z"
        ],
        "attrs": [
          {
            "fill": "none",
            "strokeLinejoin": "miter",
            "strokeLinecap": "round",
            "strokeMiterlimit": "4",
            "strokeWidth": 64
          }
        ],
        "isMulticolor": false,
        "isMulticolor2": false,
        "grid": 0,
        "tags": [
          "annoyed-alt"
        ]
      },
      "attrs": [
        {
          "fill": "none",
          "strokeLinejoin": "miter",
          "strokeLinecap": "round",
          "strokeMiterlimit": "4",
          "strokeWidth": 64
        }
      ],
      "properties": {
        "order": 1400,
        "id": 620,
        "name": "annoyed-alt",
        "prevSize": 32,
        "code": 59676
      },
      "setIdx": 0,
      "setId": 2,
      "iconIdx": 29
    }
  ],
  "height": 1024,
  "metadata": {
    "name": "icomoon"
  },
  "preferences": {
    "showGlyphs": true,
    "showQuickUse": true,
    "showQuickUse2": true,
    "showSVGs": true,
    "fontPref": {
      "prefix": "icon-",
      "metadata": {
        "fontFamily": "icomoon",
        "majorVersion": 1,
        "minorVersion": 0
      },
      "metrics": {
        "emSize": 1024,
        "baseline": 6.25,
        "whitespace": 50
      },
      "embed": false
    },
    "imagePref": {
      "prefix": "icon-",
      "png": true,
      "useClassSelector": true,
      "color": 0,
      "bgColor": 16777215,
      "classSelector": ".icon"
    },
    "historySize": 50,
    "showCodes": true,
    "gridSize": 16
  }
};

export interface AnnoyedAltIconProps extends Omit<IconProps, 'iconSet' | 'icon'> {
  size?: number;
}

export const AnnoyedAltIcon = ({ size = 16, ...props }: AnnoyedAltIconProps) => (
  <IcoMoon iconSet={iconSet} icon="annoyed-alt" size={size} {...props} />
);

AnnoyedAltIcon.displayName = 'AnnoyedAltIcon';

export default AnnoyedAltIcon;
